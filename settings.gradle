pluginManagement {
    repositories {
        maven {
            url 'http://nexus.devops.cmft/repository/maven-public/'
            allowInsecureProtocol = true
        }
        gradlePluginPortal()
    }
}
include 'app:dceppay'
include 'app:dceppay-merchant'
include 'app:dceppay-batch'
include 'app:dceppay-schedule'
include 'constants:dceppay-constants'
include 'interface:dceppay-batch-interface'
include 'interface:dceppay-igw-interface'
include 'interface:dceppay-interface'
include 'interface:dceppay-merchant-interface'
include 'service:dceppay-base'
include 'service:dceppay-base-db'
include 'service:dceppay-check-service'
include 'service:dceppay-merchant-service'
include 'service:dceppay-schedule-service'
include 'service:dceppay-payment-service'

