# 专线管理表重新设计总结

## 概述

按照您的要求，重新设计了专线管理表，表名改为 `dcep_line_info`，严格按照您提供的字段进行设计，没有添加额外字段。

## 表结构变化

### 1. 主表：dcep_line_info

**字段列表**（严格按照您的需求）：
- `id` - 主键ID
- `line_mapping_address` - 专线映射地址，格式：IP:端口
- `line_name` - 专线名称
- `institution_line_address` - 机构专线地址，仅展示信息
- `pboc_line_address` - 央行专线地址
- `pboc_idc_code` - 央行IDC标识，如bj-1
- `pboc_real_address` - 央行真实地址，用于关联专线地址进一步请求央行，主要适用于SFTP业务
- `is_default` - 默认标识，Y-是/N-否，默认专线仅适用于API业务，有且仅有一条
- `business_type` - 业务类型，API/SFTP
- `health_check_enabled` - 探活开关，Y-启用/N-禁用当前专线的探活
- `network_status` - 网络状态，NORMAL-正常/WARNING-告警/FAULT-故障
- `service_status` - 服务状态，NORMAL-正常/ABNORMAL-异常
- `probe_result` - 探测结果，最近一次接口探测返回的机房信息
- `last_health_check_time` - 最近一次探活时间
- `health_check_fail_start_time` - 探活异常开始时间，当探活失败时登记，探活成功后清空
- `create_user_no` - 创建人账号
- `create_time` - 创建时间
- `update_user_no` - 更新人账号
- `update_time` - 更新时间
- `tm_smp` - 时间戳

**移除的字段**：
- `consecutive_fail_count` - 连续失败次数（移除）
- `response_time_avg` - 平均响应时间（移除）
- `status` - 记录状态（移除）
- `remark` - 备注（移除）

### 2. 探活记录表：dcep_line_health_record

保持原有结构，关联字段改为 `dcep_line_info.id`

## 代码变更

### 1. 实体类变更

**文件重命名**：
- `DcepLineManagementDO.java` → `DcepLineInfoDO.java`

**字段调整**：
- 移除了 `consecutiveFailCount`、`responseTimeAvg`、`status`、`remark` 字段
- 保留了您要求的所有字段

### 2. DAO接口变更

**文件重命名**：
- `IDcepLineManagementDao.java` → `IDcepLineInfoDao.java`

**方法调整**：
- `updateNetworkStatus` 方法简化，移除了不存在的字段参数
- 所有返回类型从 `DcepLineManagementDO` 改为 `DcepLineInfoDO`

### 3. MyBatis映射文件变更

**文件重命名**：
- `IDcepLineManagementDao.xml` → `IDcepLineInfoDao.xml`

**SQL调整**：
- 表名从 `dcep_line_management` 改为 `dcep_line_info`
- 移除了不存在字段的映射
- 简化了查询和更新语句

### 4. 服务层变更

**接口调整**：
- `ILineHealthCheckService` 和 `ILineManagementService` 中所有 `DcepLineManagementDO` 改为 `DcepLineInfoDO`

**实现类调整**：
- `LineHealthCheckServiceImpl` 和 `LineManagementServiceImpl` 中的DAO引用和方法调用全部更新
- 移除了对不存在字段的操作
- 简化了状态更新逻辑

## 主要影响

### 1. 探活逻辑调整

由于移除了 `consecutive_fail_count` 和 `response_time_avg` 字段：

**之前的逻辑**：
- 在数据库中记录连续失败次数
- 在数据库中记录平均响应时间
- 基于数据库字段进行状态判断

**现在的逻辑**：
- 连续失败次数通过缓存管理（Redis中的HealthCheckCache）
- 响应时间统计通过缓存计算
- 状态判断基于缓存中的统计数据
- 每5次探活后批量更新数据库状态

### 2. 最优专线选择调整

**之前**：基于 `response_time_avg` 字段排序选择最快专线
**现在**：基于 `line_name` 排序选择专线（或可以基于缓存中的响应时间数据）

### 3. 数据管理调整

**删除操作**：
- 之前：逻辑删除（设置status='I'）
- 现在：物理删除（直接删除记录）

## 建表语句

```sql
-- 专线管理表
CREATE TABLE `dcep_line_info` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `line_mapping_address` varchar(100) NOT NULL COMMENT '专线映射地址，格式：IP:端口',
  `line_name` varchar(100) NOT NULL COMMENT '专线名称',
  `institution_line_address` varchar(100) DEFAULT NULL COMMENT '机构专线地址，仅展示信息',
  `pboc_line_address` varchar(100) DEFAULT NULL COMMENT '央行专线地址',
  `pboc_idc_code` varchar(50) DEFAULT NULL COMMENT '央行IDC标识，如bj-1',
  `pboc_real_address` varchar(100) DEFAULT NULL COMMENT '央行真实地址，用于关联专线地址进一步请求央行，主要适用于SFTP业务',
  `is_default` char(1) DEFAULT 'N' COMMENT '默认标识，Y-是/N-否，默认专线仅适用于API业务，有且仅有一条',
  `business_type` varchar(20) NOT NULL COMMENT '业务类型，API/SFTP',
  `health_check_enabled` char(1) DEFAULT 'Y' COMMENT '探活开关，Y-启用/N-禁用当前专线的探活',
  `network_status` varchar(20) DEFAULT 'NORMAL' COMMENT '网络状态，NORMAL-正常/WARNING-告警/FAULT-故障',
  `service_status` varchar(20) DEFAULT 'NORMAL' COMMENT '服务状态，NORMAL-正常/ABNORMAL-异常',
  `probe_result` varchar(200) DEFAULT NULL COMMENT '探测结果，最近一次接口探测返回的机房信息',
  `last_health_check_time` datetime DEFAULT NULL COMMENT '最近一次探活时间',
  `health_check_fail_start_time` datetime DEFAULT NULL COMMENT '探活异常开始时间，当探活失败时登记，探活成功后清空',
  `create_user_no` varchar(50) DEFAULT NULL COMMENT '创建人账号',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_user_no` varchar(50) DEFAULT NULL COMMENT '更新人账号',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `tm_smp` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '时间戳',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_line_mapping_address` (`line_mapping_address`),
  KEY `idx_business_type` (`business_type`),
  KEY `idx_health_check_enabled` (`health_check_enabled`),
  KEY `idx_network_status` (`network_status`),
  KEY `idx_is_default` (`is_default`),
  KEY `idx_last_health_check_time` (`last_health_check_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='专线管理表';
```

## 示例数据

```sql
INSERT INTO `dcep_line_info` (
  `id`, `line_mapping_address`, `line_name`, `institution_line_address`, 
  `pboc_line_address`, `pboc_idc_code`, `pboc_real_address`, 
  `is_default`, `business_type`, `health_check_enabled`, 
  `network_status`, `service_status`, `create_user_no`
) VALUES 
('1', '*************:8080', '主专线-API', '*************:8080', '**********:8080', 'bj-1', '**********:8080', 'Y', 'API', 'Y', 'NORMAL', 'NORMAL', 'system'),
('2', '*************:22', '备用专线-SFTP', '*************:22', '**********:22', 'bj-2', '**********:22', 'N', 'SFTP', 'Y', 'NORMAL', 'NORMAL', 'system'),
('3', '*************:8080', '备用专线-API', '*************:8080', '**********:8080', 'sh-1', '**********:8080', 'N', 'API', 'Y', 'NORMAL', 'NORMAL', 'system');
```

## 注意事项

1. **数据迁移**：如果已有旧表数据，需要进行数据迁移
2. **缓存依赖**：探活统计功能现在更依赖Redis缓存
3. **监控调整**：需要调整相关的监控和告警逻辑
4. **测试验证**：建议进行充分的功能测试

## 总结

重新设计后的表结构更加简洁，严格按照您的字段要求设计，移除了额外的统计字段。探活的统计功能通过缓存机制实现，既保持了功能完整性，又符合了表结构的简化要求。
