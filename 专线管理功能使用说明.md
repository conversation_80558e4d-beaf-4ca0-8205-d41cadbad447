# 专线管理功能使用说明

## 功能概述

专线管理功能提供了对央行专线的统一管理和自动探活能力，包括：

1. **专线信息管理**：维护专线的基本信息、地址映射、业务类型等
2. **自动探活**：定时对专线进行网络连通性检测
3. **状态监控**：实时监控专线的网络状态和服务状态
4. **智能路由**：根据响应时间自动选择最优专线
5. **告警机制**：连续失败时自动告警

## 数据库表结构

### 1. 专线管理表 (dcep_line_management)

主要字段说明：
- `line_mapping_address`: 专线映射地址，格式为 IP:端口
- `line_name`: 专线名称
- `business_type`: 业务类型，API/SFTP
- `is_default`: 是否为默认专线，Y/N
- `health_check_enabled`: 探活开关，Y/N
- `network_status`: 网络状态，NORMAL/WARNING/FAULT
- `consecutive_fail_count`: 连续失败次数
- `response_time_avg`: 平均响应时间

### 2. 专线探活记录表 (dcep_line_health_record)

记录每次探活的详细信息：
- `line_id`: 关联的专线ID
- `check_time`: 探活时间
- `check_type`: 探活类型，PING/TELNET/API
- `response_time`: 响应时间
- `check_result`: 探活结果，SUCCESS/FAIL/TIMEOUT

## 核心功能

### 1. 定时探活任务

**任务名称**: `lineHealthCheckTask`
**执行频率**: 每5秒执行一次
**主要逻辑**:
- 查询所有启用探活的专线
- 对每个专线执行Telnet连接测试
- 将探活结果缓存到Redis中
- 每5次探测后统计成功/失败次数
- 根据统计结果更新专线状态
- 重新计算平均响应时间
- 刷新最优专线缓存

### 2. 状态判断规则（基于5次探测批次）

- **正常状态**: 5次探测中成功次数 > 失败次数
- **告警状态**: 累计失败次数达到3次
- **故障状态**: 累计失败次数达到5次
- **状态恢复**: 当5次探测中成功次数多于失败次数时，重置失败计数并恢复正常状态

**探活缓存机制**:
- 每次探活结果先存储在Redis缓存中
- 缓存包含最近5次的响应时间和成功/失败状态
- 每5次探测后进行一次状态评估和数据库更新
- 缓存有效期30分钟

### 3. 最优专线选择

系统会根据以下规则选择最优专线：
1. 网络状态为NORMAL的专线
2. 按平均响应时间升序排列
3. 选择响应最快的专线
4. 结果缓存10分钟
5. 只有当有专线完成5次探测后才会更新最优专线缓存

**平均响应时间计算**:
- 基于最近5次成功探活的响应时间
- 每5次探测后重新计算
- 只统计成功的探活响应时间

## 使用方式

### 1. 获取最优专线

```java
@Autowired
private ILineHealthCheckService lineHealthCheckService;

// 获取API业务类型的最优专线
DcepLineManagementDO bestApiLine = lineHealthCheckService.getBestLine("API");

// 获取SFTP业务类型的最优专线
DcepLineManagementDO bestSftpLine = lineHealthCheckService.getBestLine("SFTP");
```

### 2. 专线管理操作

```java
@Autowired
private ILineManagementService lineManagementService;

// 获取所有启用探活的专线
List<DcepLineManagementDO> lines = lineManagementService.getEnabledHealthCheckLines();

// 获取默认专线
DcepLineManagementDO defaultLine = lineManagementService.getDefaultLine("API");

// 启用/禁用探活
lineManagementService.toggleHealthCheck(lineId, true);
```

### 3. XXL-Job配置

在XXL-Job管理界面中配置定时任务：
- **任务名称**: lineHealthCheckTask
- **Cron表达式**: `0/5 * * * * ?` (每5秒执行一次)
- **执行器**: dceppay-schedule应用对应的执行器

## 监控和告警

### 1. 日志监控

系统会输出以下关键日志：
- 专线探活开始和结束
- 专线状态变化（正常→告警→故障）
- 最优专线缓存更新

### 2. 告警信息

当专线状态发生变化时，会输出相应级别的日志：
- **INFO**: 专线恢复正常
- **WARN**: 专线进入告警状态
- **ERROR**: 专线进入故障状态

### 3. 缓存监控

可以通过Redis监控工具查看专线缓存：

**最优专线缓存**:
- 缓存Key: `dcep:line:best:API` 和 `dcep:line:best:SFTP`
- 缓存时间: 10分钟

**探活结果缓存**:
- 缓存Key: `dcep:line:health:{lineId}`
- 缓存时间: 30分钟
- 存储内容: 最近5次探活的响应时间和成功/失败状态

## 配置说明

### 1. 探活配置

在 `LineManagementConstants` 中可以调整以下参数：
- `PING_TIMEOUT_MS`: 探活超时时间（默认5秒）
- `WARNING_FAIL_COUNT`: 告警失败次数阈值（默认3次）
- `FAULT_FAIL_COUNT`: 故障失败次数阈值（默认5次）
- `AVG_CALC_COUNT`: 平均响应时间计算次数（默认5次）
- `BATCH_CHECK_COUNT`: 批量检查次数（默认5次）

### 2. 缓存配置

- `CACHE_EXPIRE_MINUTES`: 最优专线缓存过期时间（默认10分钟）
- `HEALTH_CHECK_CACHE_EXPIRE_MINUTES`: 探活结果缓存过期时间（默认30分钟）

## 注意事项

1. **默认专线设置**: 每个业务类型只能有一个默认专线
2. **探活频率**: 建议根据实际网络环境调整探活频率
3. **数据清理**: 建议定期清理历史探活记录，避免数据量过大
4. **网络环境**: 确保应用服务器能够访问专线地址
5. **Redis依赖**: 最优专线选择功能依赖Redis缓存

## 扩展功能

### 1. 支持更多探活方式

可以扩展支持HTTP、HTTPS等探活方式：
```java
// 在LineHealthCheckServiceImpl中添加新的探活方法
private boolean httpCheck(String url) {
    // HTTP探活实现
}
```

### 2. 支持更复杂的路由策略

可以根据业务需求实现更复杂的专线选择策略：
- 按地域选择
- 按负载均衡
- 按业务优先级

### 3. 集成监控系统

可以将专线状态信息推送到监控系统：
- Prometheus指标
- 钉钉/企业微信告警
- 邮件通知
