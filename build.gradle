buildscript {
    ext {
        requiredHttps = false
        cmpayVersion = '3.2.x'
        versionMngUrl = "http://gitlab.devops.cmft/publicProject/lemon/snippets/34/raw"
    }
    apply from: resources.text.fromInsecureUri(versionMngUrl)
}

plugins {
    id 'com.cmpay.platform' version "${cmpayVersion}" apply false
    id 'io.freefair.maven-optional' version '6.0.0-m2' apply false
    id 'io.freefair.lombok' version '6.0.0-m2' apply false
}

allprojects {
    repositories {
        maven {
            url 'http://nexus.devops.cmft/repository/maven-public/'
            allowInsecureProtocol = true
        }
    }
}

subprojects{
    apply plugin: 'java-library'
    apply plugin: 'idea'
    apply plugin: 'com.cmpay.platform'
    apply plugin: 'io.freefair.maven-optional'
    apply plugin: 'io.freefair.lombok'

    version = '1.0.1'
    group = 'com.cmpay.dceppay'

    repositories {
        maven {
            url 'http://nexus.devops.cmft/repository/maven-public/'
            allowInsecureProtocol = true
        }
    }

    dependencies {
        testImplementation("org.springframework.boot:spring-boot-starter-test")
    }

    compileJava {
        sourceCompatibility = '1.8'
        targetCompatibility = '1.8'
        options.encoding = 'UTF-8'
    }

    java {
        withSourcesJar()
    }

    configurations {
        all*.exclude group: 'com.cmpay', module: 'lemon-api-gateway-client'
        all*.exclude group: 'com.cmpay', module: 'lemon-api-gateway-autoconfigure'
        all*.exclude group: 'com.cmpay', module: 'lemon-api-gateway-client-starter'
    }
}

configure(subprojects.findAll {it.name.matches('\\S*(-interface|-constants)\\S*')}) {
    apply plugin: 'maven-publish'
    publishing {
        publications {
            mavenJava(MavenPublication) {
                versionMapping {
                    usage('java-runtime') {
                        fromResolutionResult()
                    }
                }
                from components.java
            }
        }
        repositories {
            maven {
                allowInsecureProtocol = true
                def releasesRepoUrl = 'http://nexus.devops.cmft/repository/cmpay-interface-releases/'
                def snapshotsRepoUrl = 'http://nexus.devops.cmft/repository/cmpay-interface-snapshots/'
                def nexusUserName = version.endsWith('SNAPSHOT') ? appMavenUserSnap : appMavenUserRelease
                def nexusPassword = version.endsWith('SNAPSHOT') ? appMavenPasswordSnap : appMavenPasswordRelease
                url = version.endsWith('SNAPSHOT') ? snapshotsRepoUrl : releasesRepoUrl
                credentials {
                    username nexusUserName
                    password nexusPassword
                }
            }
        }
    }
}

description = 'dceppay'
