-- 专线管理信息表
CREATE TABLE `dcep_line_info` (
  `line_id` varchar(8) NOT NULL COMMENT '专线编号',
  `line_mapping_address` varchar(64) NOT NULL COMMENT '专线映射地址',
  `line_name` varchar(128) DEFAULT NULL COMMENT '专线名称',
  `institution_line_address` varchar(64) NOT NULL COMMENT '机构专线地址',
  `pboc_line_address` varchar(64) NOT NULL COMMENT '央行专线地址',
  `pboc_idc_code` varchar(32) DEFAULT NULL COMMENT '央行IDC标识',
  `pboc_real_address` varchar(32) DEFAULT NULL COMMENT '央行真实地址',
  `business_type` varchar(32) NOT NULL COMMENT '业务类型',
  `is_default` char(1) NOT NULL DEFAULT 'N' COMMENT '默认标识',
  `health_check_enabled` char(1) DEFAULT 'Y' COMMENT '探活开关',
  `network_status` varchar(16) DEFAULT 'NORMAL' COMMENT '网络状态，NORMAL-正常/WARNING-告警/FAULT-故障',
  `service_status` varchar(16) DEFAULT 'NORMAL' COMMENT '服务状态，NORMAL-正常/ABNORMAL-异常',
  `probe_result` varchar(512) DEFAULT NULL COMMENT '探测结果',
  `last_check_time` varchar(14) DEFAULT NULL COMMENT '最近一次探测时间',
  `fail_start_time` varchar(14) DEFAULT NULL COMMENT '探测异常开始时间',
  `fail_count` int DEFAULT 0 COMMENT '连续失败次数',
  `rsp_time_avg` bigint DEFAULT NULL COMMENT '平均响应时长',
  `del_flag` char(1) NOT NULL DEFAULT 'N' COMMENT '删除标识，默认N，删除时置为Y',
  `remark` varchar(128) DEFAULT NULL COMMENT '备注',
  `create_user_no` varchar(32) NOT NULL COMMENT '创建者',
  `create_time` varchar(14) NOT NULL COMMENT '创建时间',
  `update_user_no` varchar(32) DEFAULT NULL COMMENT '更新者',
  `update_time` varchar(14) DEFAULT NULL COMMENT '更新时间',
  `tm_smp` varchar(14) NOT NULL COMMENT '时间戳',
  PRIMARY KEY (`line_id`),
  UNIQUE KEY `uk_line_mapping_address` (`line_mapping_address`),
  KEY `idx_business_type` (`business_type`),
  KEY `idx_health_check_enabled` (`health_check_enabled`),
  KEY `idx_network_status` (`network_status`),
  KEY `idx_is_default` (`is_default`),
  KEY `idx_del_flag` (`del_flag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='专线管理信息表';

-- 专线探活记录表
CREATE TABLE `dcep_line_health_record` (
  `record_id` varchar(8) NOT NULL COMMENT '记录编号',
  `line_id` varchar(8) NOT NULL COMMENT '专线编号',
  `check_date` varchar(8) NOT NULL COMMENT '探活日期',
  `check_time` varchar(14) NOT NULL COMMENT '探活时间',
  `check_type` varchar(16) NOT NULL COMMENT '探活类型，网络：NET/接口：APT',
  `response_time` bigint DEFAULT NULL COMMENT '响应时间',
  `check_result` varchar(16) DEFAULT NULL COMMENT '探活结果，SUCCESS-成功/FAIL-失败/TIMEOUT-超时',
  `error_message` varchar(512) DEFAULT NULL COMMENT '错误信息',
  `tm_smp` varchar(14) NOT NULL COMMENT '时间戳',
  PRIMARY KEY (`record_id`),
  KEY `idx_line_id` (`line_id`),
  KEY `idx_check_date` (`check_date`),
  KEY `idx_check_time` (`check_time`),
  KEY `idx_check_result` (`check_result`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='专线探活记录表';

-- 插入示例数据
INSERT INTO `dcep_line_info` (
  `line_id`, `line_mapping_address`, `line_name`, `institution_line_address`, 
  `pboc_line_address`, `pboc_idc_code`, `pboc_real_address`, 
  `business_type`, `is_default`, `health_check_enabled`, 
  `network_status`, `service_status`, `create_user_no`, `create_time`, `tm_smp`
) VALUES 
('00000001', '*************:8080', '主专线-API', '*************:8080', '**********:8080', 'bj-1', '**********:8080', 'API', 'Y', 'Y', 'NORMAL', 'NORMAL', 'system', '20241223100000', '20241223100000'),
('00000002', '*************:22', '备用专线-SFTP', '*************:22', '**********:22', 'bj-2', '**********:22', 'SFTP', 'N', 'Y', 'NORMAL', 'NORMAL', 'system', '20241223100000', '20241223100000'),
('00000003', '*************:8080', '备用专线-API', '*************:8080', '**********:8080', 'sh-1', '**********:8080', 'API', 'N', 'Y', 'NORMAL', 'NORMAL', 'system', '20241223100000', '20241223100000');
