package com.cmpay.dceppay.service.cache.impl;

import com.cmpay.dceppay.config.rediscache.DcepCacheRedis;
import com.cmpay.dceppay.constant.redis.RedisKeyPrefixConstants;
import com.cmpay.dceppay.service.cache.IdcInformationCacheService;
import com.cmpay.lemon.common.utils.JudgeUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024/10/8 15:29
 */
@Slf4j
@Service
public class IdcInformationCacheServiceImpl implements IdcInformationCacheService {
    @Autowired
    private DcepCacheRedis cacheRedis;

    @Override
    public void addCityZoneIdcInformationCache(String value) {
        String key = RedisKeyPrefixConstants.IDC_CZONE_INFORMATION;
        cacheRedis.getRedisTemplate().opsForValue().set(key, value,30, TimeUnit.SECONDS);
    }

    @Override
    public void addGlobalZoneIdcInformationCache(String value) {
        String key = RedisKeyPrefixConstants.IDC_GZONE_INFORMATION;
        cacheRedis.getRedisTemplate().opsForValue().set(key, value,30, TimeUnit.SECONDS);
    }

    @Override
    public String queryCityZoneIdcInformation() {
        String key = RedisKeyPrefixConstants.IDC_CZONE_INFORMATION;
        Object obj = cacheRedis.getRedisTemplate().opsForValue().get(key);
        if (JudgeUtils.isNotNull(obj)) {
            return obj.toString();
        } else {
            return null;
        }
    }


    @Override
    public String queryGlobalZoneIdcInformation() {
        String key = RedisKeyPrefixConstants.IDC_GZONE_INFORMATION;
        Object obj = cacheRedis.getRedisTemplate().opsForValue().get(key);
        if (JudgeUtils.isNotNull(obj)) {
            return obj.toString();
        } else {
            return null;
        }
    }
}
