package com.cmpay.dceppay.utils;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/9/5 10:22
 * 金额校验
 */
public class AmountUtils {
    /**
     * 金额校验
     *
     * @param amount
     * @return
     */
    public static boolean isValidAmount(BigDecimal amount) {
        // 检查金额是否为null、大于0且小数位为2
        return amount != null
                && amount.compareTo(BigDecimal.ZERO) > 0
                && amount.scale() == 2;
    }
}
