package com.cmpay.dceppay.util;

import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.net.InetAddress;
import java.net.InetSocketAddress;
import java.net.Socket;

/**
 * 网络连通性检测工具类
 * <AUTHOR>
 * @date 2024/12/23
 */
@Slf4j
public class NetworkConnectivityUtil {
    
    private static final int TIMEOUT_MS = 200; // 超时时间200ms
    
    /**
     * 连通性检测结果
     */
    public static class ConnectivityResult {
        private boolean pingSuccess;
        private boolean telnetSuccess;
        private long pingTime;
        private long telnetTime;
        private long totalTime;
        private String errorMessage;
        
        public ConnectivityResult() {}
        
        public ConnectivityResult(boolean pingSuccess, boolean telnetSuccess, 
                                long pingTime, long telnetTime, long totalTime, String errorMessage) {
            this.pingSuccess = pingSuccess;
            this.telnetSuccess = telnetSuccess;
            this.pingTime = pingTime;
            this.telnetTime = telnetTime;
            this.totalTime = totalTime;
            this.errorMessage = errorMessage;
        }
        
        // Getters and Setters
        public boolean isPingSuccess() { return pingSuccess; }
        public void setPingSuccess(boolean pingSuccess) { this.pingSuccess = pingSuccess; }
        
        public boolean isTelnetSuccess() { return telnetSuccess; }
        public void setTelnetSuccess(boolean telnetSuccess) { this.telnetSuccess = telnetSuccess; }
        
        public long getPingTime() { return pingTime; }
        public void setPingTime(long pingTime) { this.pingTime = pingTime; }
        
        public long getTelnetTime() { return telnetTime; }
        public void setTelnetTime(long telnetTime) { this.telnetTime = telnetTime; }
        
        public long getTotalTime() { return totalTime; }
        public void setTotalTime(long totalTime) { this.totalTime = totalTime; }
        
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
        
        @Override
        public String toString() {
            return String.format("ConnectivityResult{ping=%s(%dms), telnet=%s(%dms), total=%dms, error='%s'}", 
                    pingSuccess, pingTime, telnetSuccess, telnetTime, totalTime, errorMessage);
        }
    }
    
    /**
     * 对IP:端口进行连通性校验
     * 先ping，ping通后再telnet
     * 
     * @param address IP:端口格式的地址
     * @return 连通性检测结果
     */
    public static ConnectivityResult checkConnectivity(String address) {
        if (address == null || address.trim().isEmpty()) {
            return new ConnectivityResult(false, false, 0, 0, 0, "地址为空");
        }
        
        String[] parts = address.split(":");
        if (parts.length != 2) {
            return new ConnectivityResult(false, false, 0, 0, 0, "地址格式错误，应为IP:端口");
        }
        
        String ip = parts[0].trim();
        int port;
        try {
            port = Integer.parseInt(parts[1].trim());
            if (port < 1 || port > 65535) {
                return new ConnectivityResult(false, false, 0, 0, 0, "端口号超出范围(1-65535)");
            }
        } catch (NumberFormatException e) {
            return new ConnectivityResult(false, false, 0, 0, 0, "端口号格式错误");
        }
        
        return checkConnectivity(ip, port);
    }
    
    /**
     * 对IP和端口进行连通性校验
     * 先ping，ping通后再telnet
     * 
     * @param ip IP地址
     * @param port 端口号
     * @return 连通性检测结果
     */
    public static ConnectivityResult checkConnectivity(String ip, int port) {
        long startTime = System.currentTimeMillis();
        ConnectivityResult result = new ConnectivityResult();
        
        try {
            // 第一步：Ping检测
            log.debug("开始ping检测: {}:{}", ip, port);
            long pingStartTime = System.currentTimeMillis();
            boolean pingSuccess = ping(ip);
            long pingEndTime = System.currentTimeMillis();
            long pingTime = pingEndTime - pingStartTime;
            
            result.setPingSuccess(pingSuccess);
            result.setPingTime(pingTime);
            
            if (!pingSuccess) {
                result.setTelnetSuccess(false);
                result.setTelnetTime(0);
                result.setTotalTime(pingTime);
                result.setErrorMessage("Ping失败");
                log.debug("Ping失败: {}:{}, 耗时: {}ms", ip, port, pingTime);
                return result;
            }
            
            log.debug("Ping成功: {}:{}, 耗时: {}ms", ip, port, pingTime);
            
            // 第二步：Telnet检测
            log.debug("开始telnet检测: {}:{}", ip, port);
            long telnetStartTime = System.currentTimeMillis();
            boolean telnetSuccess = telnet(ip, port);
            long telnetEndTime = System.currentTimeMillis();
            long telnetTime = telnetEndTime - telnetStartTime;
            
            result.setTelnetSuccess(telnetSuccess);
            result.setTelnetTime(telnetTime);
            
            if (!telnetSuccess) {
                result.setErrorMessage("Telnet失败");
                log.debug("Telnet失败: {}:{}, 耗时: {}ms", ip, port, telnetTime);
            } else {
                log.debug("Telnet成功: {}:{}, 耗时: {}ms", ip, port, telnetTime);
            }
            
        } catch (Exception e) {
            result.setPingSuccess(false);
            result.setTelnetSuccess(false);
            result.setErrorMessage("检测异常: " + e.getMessage());
            log.error("连通性检测异常: {}:{}, 错误: {}", ip, port, e.getMessage(), e);
        }
        
        long endTime = System.currentTimeMillis();
        result.setTotalTime(endTime - startTime);
        
        log.debug("连通性检测完成: {}:{}, 结果: {}", ip, port, result);
        return result;
    }
    
    /**
     * Ping检测
     * 
     * @param ip IP地址
     * @return 是否ping通
     */
    private static boolean ping(String ip) {
        try {
            InetAddress address = InetAddress.getByName(ip);
            return address.isReachable(TIMEOUT_MS);
        } catch (IOException e) {
            log.debug("Ping异常: {}, 错误: {}", ip, e.getMessage());
            return false;
        }
    }
    
    /**
     * Telnet检测
     * 
     * @param ip IP地址
     * @param port 端口号
     * @return 是否telnet通
     */
    private static boolean telnet(String ip, int port) {
        try (Socket socket = new Socket()) {
            socket.connect(new InetSocketAddress(ip, port), TIMEOUT_MS);
            return true;
        } catch (IOException e) {
            log.debug("Telnet异常: {}:{}, 错误: {}", ip, port, e.getMessage());
            return false;
        }
    }
}
