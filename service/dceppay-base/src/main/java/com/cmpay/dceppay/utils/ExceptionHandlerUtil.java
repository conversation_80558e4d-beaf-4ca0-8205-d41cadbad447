package com.cmpay.dceppay.utils;

import com.cmpay.dceppay.constant.common.CommonErrorConstants;
import com.cmpay.dceppay.enums.MsgCodeEnum;
import com.cmpay.lemon.common.exception.BusinessException;
import feign.RetryableException;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024/9/9 17:53
 */
@Slf4j
public class ExceptionHandlerUtil {
    public static void throwRequestDcepError(Exception e) {
        String msg;
        if (e instanceof BusinessException) {
            log.warn(e.toString());
            BusinessException.throwBusinessException((BusinessException) e);
        } else if ((e instanceof RetryableException) && e.getMessage().contains(CommonErrorConstants.READ_TIMED_OUT)) {
            BusinessException.throwBusinessException(MsgCodeEnum.TRANSACTION_REQUEST_ERROR_TIME_OUT);
        } else {
            msg = MsgCodeEnum.TRANSACTION_REQUEST_SYS_ERROR.getMsgCd();
            log.error(msg + e.getCause());
            BusinessException.throwBusinessException(MsgCodeEnum.TRANSACTION_REQUEST_SYS_ERROR);
        }

    }

    public static String getExceptionMsg(Exception e) {
        String msg;
        if (e instanceof BusinessException) {
            msg = ((BusinessException) e).getMsgCd() + ((BusinessException) e).getMsgInfo();
        } else if ((e instanceof RetryableException) && e.getMessage().contains(CommonErrorConstants.READ_TIMED_OUT)) {
            msg = MsgCodeEnum.TRANSACTION_REQUEST_ERROR_TIME_OUT.getMsgCd();
        } else {
            msg = MsgCodeEnum.TRANSACTION_REQUEST_SYS_ERROR.getMsgCd();
            log.error(msg, e.getCause());
        }
        return msg;
    }
}
