package com.cmpay.dceppay.service.mng;

import com.cmpay.dceppay.constant.dcep.DcepIDCconstants;
import com.cmpay.dceppay.service.cache.IdcInformationCacheService;
import com.cmpay.lemon.common.utils.JudgeUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/10/8 16:06
 */
@Slf4j
@Service
public class DcepRequestUrlServiceImpl implements IDcepRequestUrlService {
    @Autowired
    private IIDCParamService busParamService;
    @Autowired
    private IdcInformationCacheService idcInformationCacheService;

    @Override
    public String getRequestUrl(String idcInfo) {
        if (JudgeUtils.equals(idcInfo, DcepIDCconstants.CITY_ZNOE)) {
            String idcInformation = idcInformationCacheService.queryCityZoneIdcInformation();
            if (JudgeUtils.isNotNull(idcInformation)) {
                return idcInformation;
            }
            return busParamService.getCityZoneDefaultIdcInfo();
        }
        if (JudgeUtils.equals(idcInfo, DcepIDCconstants.GLOBAL_ZONE)) {
            String idcInformation = idcInformationCacheService.queryGlobalZoneIdcInformation();
            if (JudgeUtils.isNotNull(idcInformation)) {
                return idcInformation;
            }
            return busParamService.getGlobalZoneDefaultIdcInfo();
        }
        return null;
    }
}
