package com.cmpay.dceppay.service.cache;

import com.cmpay.dceppay.bo.LineCheckRecordCacheBO;

/**
 * <AUTHOR>
 * @date 2025/9/29 16:06
 */
public interface ILineCheckRecordCacheService {
    boolean addCheckCache(LineCheckRecordCacheBO lineCheckRecordCacheBO);

    boolean updateCheckCache(LineCheckRecordCacheBO lineCheckRecordCacheBO);

    LineCheckRecordCacheBO queryCheckCache(String lineId);

    boolean deleteCheckCache(String lineId);

}
