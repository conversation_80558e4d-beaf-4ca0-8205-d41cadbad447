package com.cmpay.dceppay.bo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className BusinessLicenseCacheBO
 * @date 2025/9/5
 * @description
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BusinessLicenseCacheBO {

    /**
     * 证书颁发机构编码
     */
    private String orgCode;

    /**
     * 证书类型
     */
    private String licenseType;

    /**
     * 证书序号-用于缓存添加参与key拼接
     */
    private String seqNo;

    /**
     * NcrptnSn/SignSn 证书序列号
     */
    private String sn;

    /**
     * 公钥
     */
    private String publicKey;

    /**
     * 私钥
     */
    private String privateKey;

    /**
     * 证书过期时间
     */
    private String expireTime;
}
