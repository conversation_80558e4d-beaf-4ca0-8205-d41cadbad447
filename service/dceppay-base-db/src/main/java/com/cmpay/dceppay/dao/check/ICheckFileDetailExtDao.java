package com.cmpay.dceppay.dao.check;

import com.cmpay.dceppay.entity.check.CheckFileDetailDO;
import com.cmpay.dceppay.entity.check.CheckSummaryExtDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/3 13:46
 */
@Mapper
public interface ICheckFileDetailExtDao extends ICheckFileDetailDao {
    List<CheckFileDetailDO> listWaitDetail(String checkDate);

    void updatePaymentLongNoOrder(CheckFileDetailDO checkFileDetailDO);

    void updatePaymentLongStsError(CheckFileDetailDO checkFileDetailDO);

    void updateCheckComplete(CheckFileDetailDO checkFileDetailDO);

    void updateAmountError(CheckFileDetailDO checkFileDetailDO);

    void updateRefundShortNoOrder(CheckFileDetailDO checkFileDetailDO);

    void updateRefundShortStsError(CheckFileDetailDO checkFileDetailDO);

    CheckFileDetailDO getPaymentShortDoubt(CheckFileDetailDO queryDO);

    CheckFileDetailDO getRefundLogDoubt(CheckFileDetailDO queryDO);

    CheckSummaryExtDO summaryInfo(CheckFileDetailDO queryDO);

    void updateCheckErrorComplete(CheckFileDetailDO checkFileDetailDO);

    void updateCheckErrorAmount(CheckFileDetailDO checkFileDetailDO);
}
