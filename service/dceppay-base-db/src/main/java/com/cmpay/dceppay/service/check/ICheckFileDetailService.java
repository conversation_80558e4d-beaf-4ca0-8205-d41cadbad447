package com.cmpay.dceppay.service.check;

import com.cmpay.dceppay.bo.check.CheckFileDetailBO;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/23 17:50
 */
public interface ICheckFileDetailService {
    /**
     * 批量插入对账文件表数
     * @param list
     */
    void addCheckFileCount(List<CheckFileDetailBO> list);

    /**
     * 查询等待对账数据
     * @param checkDate
     * @return
     */
    List<CheckFileDetailBO> listWaitDetailBO(String checkDate);

    void updatePaymentLongNoOrder(CheckFileDetailBO checkFileDetailBO, String checkDate);

    void updatePaymentLongStsError(CheckFileDetailBO checkFileDetailBO, String checkDate);

    void updateCheckComplete(CheckFileDetailBO checkFileDetailBO, String checkDate);

    void updateAmountError(CheckFileDetailBO checkFileDetailBO, String checkDate);

    void updateRefundShortNoOrder(CheckFileDetailBO checkFileDetailBO, String checkDate);

    void updateRefundShortStsError(CheckFileDetailBO checkFileDetailBO, String checkDate);

    /**
     * 充值短款存疑数据查询
     * @param checkFileDetailBO
     * @return
     */
    CheckFileDetailBO getPaymentShortDoubt(CheckFileDetailBO checkFileDetailBO);

    /**
     * 退款长款存疑数据查询
     * @param checkFileDetailBO
     * @return
     */

    CheckFileDetailBO getRefundLogDoubt(CheckFileDetailBO checkFileDetailBO);

    /**
     * 差错取消对账明细对平
     * @param outOrderNo
     * @param refundOrderNo
     */

    void updateCheckErrorComplete(String outOrderNo, String refundOrderNo);

    void updateCheckErrorAmount(String outOrderNo, String refundOrderNo, BigDecimal amount);
}
