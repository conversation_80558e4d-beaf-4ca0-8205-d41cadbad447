/*
 * @ClassName DcepLineHealthRecordDO
 * @Description 专线探活记录实体类
 * @version 1.0
 * @Date 2024-12-23 10:00:00
 */
package com.cmpay.dceppay.entity.mng;

import com.cmpay.framework.data.BaseDO;
import com.cmpay.lemon.framework.annotation.DataObject;

import java.util.Date;

@DataObject
public class DcepLineHealthRecordDO extends BaseDO {
    /**
     * @Fields lineId 专线ID，关联dcep_line_management.id
     */
    private String lineId;
    
    /**
     * @Fields checkTime 探活时间
     */
    private Date checkTime;
    
    /**
     * @Fields checkType 探活类型，PING/TELNET/API
     */
    private String checkType;
    
    /**
     * @Fields responseTime 响应时间（毫秒）
     */
    private Long responseTime;
    
    /**
     * @Fields checkResult 探活结果，SUCCESS-成功/FAIL-失败/TIMEOUT-超时
     */
    private String checkResult;
    
    /**
     * @Fields errorMessage 错误信息
     */
    private String errorMessage;
    
    /**
     * @Fields createTime 创建时间
     */
    private Date createTime;

    public String getLineId() {
        return lineId;
    }

    public void setLineId(String lineId) {
        this.lineId = lineId;
    }

    public Date getCheckTime() {
        return checkTime;
    }

    public void setCheckTime(Date checkTime) {
        this.checkTime = checkTime;
    }

    public String getCheckType() {
        return checkType;
    }

    public void setCheckType(String checkType) {
        this.checkType = checkType;
    }

    public Long getResponseTime() {
        return responseTime;
    }

    public void setResponseTime(Long responseTime) {
        this.responseTime = responseTime;
    }

    public String getCheckResult() {
        return checkResult;
    }

    public void setCheckResult(String checkResult) {
        this.checkResult = checkResult;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
