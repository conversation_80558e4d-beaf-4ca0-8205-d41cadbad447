package com.cmpay.dceppay.service.pay;

import com.cmpay.dceppay.bo.notify.NotifyKeyBO;
import com.cmpay.dceppay.bo.notify.NotifySyncQueryBO;
import com.cmpay.dceppay.bo.pay.PayNotifyBO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/3 17:02
 */
public interface IPayNotifyDBService {
    int addNotifyRecord(PayNotifyBO payNotifyBO);

    /**
     * 查询通知失败的订单
     * @param notifySyncQueryBO
     * @return
     */
    List<PayNotifyBO> queryWaitNotifyRecord(NotifySyncQueryBO notifySyncQueryBO);

    PayNotifyBO getByKey(NotifyKeyBO notifyKeyBO);

    int  updateNotifyCount(PayNotifyBO payNotifyBO);

    int updateNotifySuccess(PayNotifyBO payNotifyBO);
}
