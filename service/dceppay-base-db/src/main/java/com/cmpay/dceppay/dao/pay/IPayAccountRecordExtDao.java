package com.cmpay.dceppay.dao.pay;

import com.cmpay.dceppay.entity.pay.AccountQueryDO;
import com.cmpay.dceppay.entity.pay.PayAccountRecordDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/2 15:35
 */
@Mapper
public interface IPayAccountRecordExtDao extends IPayAccountRecordDao {
    List<PayAccountRecordDO> queryWaitHandleAccount(AccountQueryDO accountQueryDO);

    void updateAccountSuccess(PayAccountRecordDO payAccountRecordDO);

    int updateWaitDeal(PayAccountRecordDO payAccountRecordDO);

    void updateAccountCancelReserve(PayAccountRecordDO payAccountRecordDO);

    void updateNoRecordAccount(PayAccountRecordDO payAccountRecordDO);

    PayAccountRecordDO getByUk(PayAccountRecordDO payAccountRecordUk);
}
