package com.cmpay.dceppay.service.check.ext.impl;

import com.cmpay.dceppay.bo.check.CheckGenFileStatusBO;
import com.cmpay.dceppay.enums.check.CheckFileProcessEnums;
import com.cmpay.dceppay.enums.check.CheckFileStatusEnum;
import com.cmpay.dceppay.service.check.ICheckGenFileStatusDBService;
import com.cmpay.dceppay.service.check.ext.ICheckGenFileStatusService;
import com.cmpay.lemon.common.utils.JudgeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/9/18 9:42
 */
@Service
public class CheckGenFileStatusServiceImpl implements ICheckGenFileStatusService {
    @Autowired
    private ICheckGenFileStatusDBService checkGenFileStatusDBService;

    @Override
    public void addCheckGenFileStatus(String accountDate, String channelCode) {
        if (JudgeUtils.isNotNull(checkGenFileStatusDBService.getByKey(accountDate, channelCode))) {
            return;
        }
        CheckGenFileStatusBO checkGenFileStatusBO = new CheckGenFileStatusBO();
        checkGenFileStatusBO.setCheckFileDate(accountDate);
        checkGenFileStatusBO.setChannelCode(channelCode);
        checkGenFileStatusDBService.addCheckGenFileStatus(checkGenFileStatusBO);
    }

    @Override
    public boolean fileSendSuccess(String accountDate, String channelCode) {
        CheckGenFileStatusBO checkGenFileStatusBO = checkGenFileStatusDBService.getByKey(accountDate, channelCode);
        if (JudgeUtils.isNull(checkGenFileStatusBO)) {
            return false;
        }
        return JudgeUtils.equals(checkGenFileStatusBO.getFileStatus(), CheckFileStatusEnum.FILE_SEND_SUCCESS.name());
    }

}
