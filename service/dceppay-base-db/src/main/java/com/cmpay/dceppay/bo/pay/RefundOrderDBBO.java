package com.cmpay.dceppay.bo.pay;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/9/5 10:52
 */
@Data
public class RefundOrderDBBO {
    /**
     * @Fields refundOrderNo 退款请求号
     */
    private String refundOrderNo;
    /**
     * @Fields outOrderNo 原交易订单号
     */
    private String outOrderNo;
    /**
     * @Fields refundDate 退款提交日期
     */
    private String refundDate;
    /**
     * @Fields refundTime 退款提交时间
     */
    private String refundTime;
    /**
     * @Fields channelCode 支付渠道号
     */
    private String channelCode;
    /**
     * @Fields payWay 支付方式
     */
    private String payWay;
    /**
     * @Fields scene 支付场景
     */
    private String scene;
    /**
     * @Fields busType 业务类型
     */
    private String busType;
    /**
     * @Fields merchantNo 商户编号
     */
    private String merchantNo;
    /**
     * @Fields orgMerchantNo 运营机构商户号
     */
    private String orgMerchantNo;
    /**
     * @Fields walletId 商户钱包id
     */
    private String walletId;
    /**
     * @Fields accountDate 会计日期
     */
    private String accountDate;
    /**
     * @Fields orderAmount 原订单金额
     */
    private BigDecimal orderAmount;
    /**
     * @Fields refundAmount 退款金额
     */
    private BigDecimal refundAmount;
    /**
     * @Fields cancelFlag 撤单标识
     */
    private String cancelFlag;
    /**
     * @Fields notifyUrl 通知url
     */
    private String notifyUrl;
    /**
     * @Fields status 订单状态
     */
    private String status;
    /**
     * @Fields bankOrderNo 支付机构订单号
     */
    private String bankOrderNo;
    /**
     * @Fields errMsgCd 错误码
     */
    private String errMsgCd;
    /**
     * @Fields errMsgInfo 错误码信息
     */
    private String errMsgInfo;
    /**
     * @Fields orderCompleteTime 订单完成时间
     */
    private String orderCompleteTime;
    /**
     * @Fields receiveNotifyTime 接收通知时间
     */
    private String receiveNotifyTime;
    /**
     * @Fields refundReason 退款原因
     */
    private String refundReason;
    /**
     * @Fields bankRefundNo 支付机退款订单号
     */
    private String bankRefundNo;

    /**
     * @Fields extra 保留字段
     */
    private String extra;
    /**
     * @Fields messageIdentification 报文标识号
     */
    private String messageIdentification;
    /**
     * @Fields bizType 业务类型
     */
    private String bizType;
    /**
     * @Fields bizCategory 业务种类
     */
    private String bizCategory;
    /**
     * @Fields originalMessageIdentification 支付订单报文标识号
     */
    private String originalMessageIdentification;
}
