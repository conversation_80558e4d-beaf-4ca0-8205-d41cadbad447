package com.cmpay.dceppay.bo.pay;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/9/13 17:11
 * 结算表对象
 */
@Data
public class PaySettlementBO {
    /**
     * @Fields tradeJrnNo 交易流水号
     */
    private String tradeJrnNo;
    /**
     * @Fields outOrderNo 交易订单号
     */
    private String outOrderNo;
    /**
     * @Fields refundOrderNo 退款订单号
     */
    private String refundOrderNo;
    /**
     * @Fields settlementDate 结算日期
     */
    private String settlementDate;
    /**
     * @Fields orderDate 订单提交日期
     */
    private String orderDate;
    /**
     * @Fields orderTime 订单提交时间
     */
    private String orderTime;
    /**
     * @Fields channelCode 支付渠道号
     */
    private String channelCode;
    /**
     * @Fields payWay 支付方式
     */
    private String payWay;
    /**
     * @Fields scene 支付场景
     */
    private String scene;
    /**
     * @Fields busType 业务类型
     */
    private String busType;
    /**
     * @Fields merchantNo 商户编号
     */
    private String merchantNo;
    /**
     * @Fields orgMerchantNo 运营机构商户号
     */
    private String orgMerchantNo;
    /**
     * @Fields walletId 商户钱包id
     */
    private String walletId;
    /**
     * @Fields accountDate 会计日期
     */
    private String accountDate;
    /**
     * @Fields orderAmount 交易金额
     */
    private BigDecimal orderAmount;
    /**
     * @Fields bankOrderNo 支付机构订单号
     */
    private String bankOrderNo;
    private String orderCompleteTime;
    /**
     * @Fields settlementType 订单结算类型
     */
    private String settlementType;
    /**
     * @Fields checkStatus 对账状态
     */
    private String checkStatus;
    /**
     * @Fields checkDate 账期
     */
    private String checkDate;
    /**
     * @Fields checkCompleteTime 对账完成时间
     */
    private String checkCompleteTime;
    private String remark;

}
