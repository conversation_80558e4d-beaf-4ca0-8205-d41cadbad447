package com.cmpay.dceppay.dao.pay;

import com.cmpay.dceppay.entity.pay.PayNotifyDO;
import com.cmpay.dceppay.entity.pay.PayNotifyQueryDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/2 15:35
 */
@Mapper
public interface IPayNotifyExtDao extends IPayNotifyDao {
    List<PayNotifyDO> queryWaitNotifyRecord(PayNotifyQueryDO notifyQueryDO);

    int updateNotifyCount(PayNotifyDO payNotifyDO);

    int updateNotifySuccess(PayNotifyDO payNotifyDO);
}
