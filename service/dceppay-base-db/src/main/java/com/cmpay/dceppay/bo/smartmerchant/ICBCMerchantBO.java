package com.cmpay.dceppay.bo.smartmerchant;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/8/28 17:43
 */
@Data
public class ICBCMerchantBO {
    /**
     * @Fields merchantId 商户编号
     */
    private String merchantId;
    /**
     * @Fields operatorMerchantId 运营机构商户号
     */
    private String operatorMerchantId;
    /**
     * @Fields serviceMerchantFlag 是否服务商商户
     */
    private String serviceMerchantFlag;
    /**
     * @Fields 服务商编号
     */
    private String serviceId;
    /**
     * @Fields serviceProviderId 运营机构服务商编号
     */
    private String serviceProviderId;
    /**
     * @Fields merchantName 商户名称
     */
    private String merchantName;
//    /**
//     * @Fields merchantShowName 商户对外经营名称
//     */
//    private String merchantShowName;
    /**
     * @Fields merchantShortName 商户简称
     */
    private String merchantShortName;
    /**
     * @Fields merchantCategory 经营类目（MCC）
     */
    private String merchantCategory;
//    /**
//     * @Fields bussCode 业务类型
//     */
//    private String bussCode;
//    /**
//     * @Fields bussType 业务种类
//     */
//    private String bussType;
//    /**
//     * @Fields merchantType 商户类型
//     */
//    private String merchantType;
    /**
     * @Fields merchantLicenseType 证件类型
     */
    private String merchantLicenseType;
    /**
     * @Fields merchantLicense 证件编号
     */
    private String merchantLicense;
//    /**
//     * @Fields contactName 商户联系人
//     */
//    private String contactName;
//    /**
//     * @Fields servicePhone 商户联系人电话
//     */
//    private String servicePhone;
//    /**
//     * @Fields contactPhone 联系电话
//     */
//    private String contactPhone;
//    /**
//     * @Fields contactEmail 联系邮箱
//     */
//    private String contactEmail;
//    /**
//     * @Fields unifyEntryFlag 统一入账标识
//     */
//    private String unifyEntryFlag;
//    /**
//     * @Fields settleCycle 结算周期
//     */
//    private String settleCycle;
//    /**
//     * @Fields accType 账户类型
//     */
//    private String accType;
//    /**
//     * @Fields accName 账户名称
//     */
//    private String accName;
//    /**
//     * @Fields accNo 银行账号
//     */
//    private String accNo;
//    /**
//     * @Fields accBankName 开户银行(含支行)
//     */
//    private String accBankName;
//    /**
//     * @Fields accBankCode 开户行联行号
//     */
//    private String accBankCode;


//    /**
//     * @Fields remark 备注信息
//     */
//    private String remark;
    /**
     * @Fields onboardStatus 进件状态
     */
    private String onboardStatus;
    /**
     * @Fields serviceStatus 商户状态
     */
    private String serviceStatus;
//    /**
//     * @Fields onboardTime 进件时间
//     */
//    private String onboardTime;
    /**
     * @Fields walletId 结算钱包id
     */
    private String walletId;
    /**
     * @Fields walletName 结算钱包名称
     */
    private String walletName;
    /**
     * @Fields merchantWalletName 商户伞底钱包名称
     */
    private String merchantWalletName;
    /**
     * @Fields merchantWalletId 商户伞底钱包ID
     */
    private String merchantWalletId;
    /**
     * @Fields protocolWalletName 协议伞顶钱包名称
     */
    private String protocolWalletName;
    /**
     * @Fields protocolWalletId 协议伞顶钱包ID
     */
    private String protocolWalletId;
    /**
     * 操作者
     */
    private String operatorId;
}
