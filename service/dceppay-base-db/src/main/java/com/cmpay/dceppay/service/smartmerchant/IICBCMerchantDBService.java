package com.cmpay.dceppay.service.smartmerchant;

import com.cmpay.dceppay.bo.smartmerchant.ICBCMerchantBO;
import com.cmpay.dceppay.bo.smartmerchant.ICBCMerchantUpdateStatusBO;

/**
 * <AUTHOR>
 * @date 2024/8/28 16:47
 * 提供工行商户信息表增删改成操作业务处理接口类
 */
public interface IICBCMerchantDBService {
    /**
     * 商户信息查询
     * @param merchantId
     * @return
     */
    ICBCMerchantBO getMerchantByMerchantID(String merchantId);

    /**
     * 更新进件等待中
     * @param updateBO
     * @return
     */
    int updateOnboarding(ICBCMerchantUpdateStatusBO updateBO);

    /**
     * 更新进件失败
     * @param updateBO
     * @return
     */
    int updateOnboardFail(ICBCMerchantUpdateStatusBO updateBO);

    /**
     * 更新进件成功
     * @param updateBO
     * @return
     */
    int updateOnboardSuccess(ICBCMerchantUpdateStatusBO updateBO);

    int updateOnboardCanceling(ICBCMerchantUpdateStatusBO updateBO);

    int updateOnboardCancelFail(ICBCMerchantUpdateStatusBO updateBO);

    /**
     * 更新进件作废
     * @param updateBO
     * @return
     */
    int updateOnboardCancel(ICBCMerchantUpdateStatusBO updateBO);


}
