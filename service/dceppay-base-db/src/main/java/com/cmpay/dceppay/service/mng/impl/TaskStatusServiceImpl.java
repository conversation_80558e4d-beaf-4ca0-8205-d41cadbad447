package com.cmpay.dceppay.service.mng.impl;

import com.cmpay.dceppay.bo.mng.TaskStatusBO;
import com.cmpay.dceppay.dao.mng.ITaskStatusExtDao;
import com.cmpay.dceppay.entity.mng.TaskStatusDO;
import com.cmpay.dceppay.entity.mng.TaskStatusDOKey;
import com.cmpay.dceppay.enums.mng.TaskKeyEnum;
import com.cmpay.dceppay.enums.mng.TaskStatusEnum;
import com.cmpay.dceppay.service.mng.ITaskStatusService;
import com.cmpay.dceppay.util.DateTimeUtil;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.datasource.TargetDataSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/9/26 13:29
 */
@Service
public class TaskStatusServiceImpl implements ITaskStatusService {
    @Autowired
    private ITaskStatusExtDao taskStatusExtDao;

    @Override
    @TargetDataSource("dcepmng")
    public int addCheckAccountTaskInfo(String taskDate) {
        TaskStatusDO taskStatusDO = new TaskStatusDO();
        taskStatusDO.setTaskDate(taskDate);
        taskStatusDO.setTaskKey(TaskKeyEnum.CHECK_ACC_TASK.name());
        taskStatusDO.setTaskDesc(TaskKeyEnum.CHECK_ACC_TASK.getDesc());
        taskStatusDO.setStatus(TaskStatusEnum.SUCCESS.name());
        taskStatusDO.setCreatedTime(DateTimeUtil.getCurrentDateTimeStr());
        taskStatusDO.setUpdatedTime(DateTimeUtil.getCurrentDateTimeStr());
        return taskStatusExtDao.insert(taskStatusDO);

    }

    @Override
    @TargetDataSource("dcepmng")
    public TaskStatusBO getByKey(String taskDate, String taskKey) {
        TaskStatusDOKey taskStatusDOKey = new TaskStatusDOKey();
        taskStatusDOKey.setTaskDate(taskDate);
        taskStatusDOKey.setTaskKey(taskKey);
        TaskStatusDO taskStatusDO = taskStatusExtDao.get(taskStatusDOKey);
        if (JudgeUtils.isNull(taskStatusDO)) {
            return null;
        }
        return BeanUtils.copyPropertiesReturnDest(new TaskStatusBO(), taskStatusDO);
    }
}
