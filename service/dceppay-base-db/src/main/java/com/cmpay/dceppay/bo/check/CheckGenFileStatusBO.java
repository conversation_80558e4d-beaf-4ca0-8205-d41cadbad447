package com.cmpay.dceppay.bo.check;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/9/18 9:17
 */
@Data
public class CheckGenFileStatusBO {
    /**
     * @Fields checkFileDate 对账日期
     */
    private String checkFileDate;
    /**
     * @Fields channelCode 渠道号
     */
    private String channelCode;
    /**
     * @Fields totalCount 总笔数
     */
    private Integer totalCount;
    /**
     * @Fields totalAmount 总金额
     */
    private BigDecimal totalAmount;
    /**
     * @Fields paymentCount 支付总笔数
     */
    private Integer paymentCount;
    /**
     * @Fields paymentAmount 支付总金额
     */
    private BigDecimal paymentAmount;
    /**
     * @Fields refundCount 退款总笔数
     */
    private Integer refundCount;
    /**
     * @Fields refundAmount 退款总金额
     */
    private BigDecimal refundAmount;
    /**
     * @Fields fileStartTime 文件生成开始时间
     */
    private String fileStartTime;
    /**
     * @Fields fileEndTime 文件生成结束时间
     */
    private String fileEndTime;
    /**
     * @Fields fileSendTime 文件推送时间
     */
    private String fileSendTime;
    /**
     * @Fields fileStatus 文件状态 文件生成中：file_process, 对账文件生成完成：file_success, 对账文件生成失败：file_fail, 文件已发送：file_send_success, 文件发送失败：file_send_fail
     */
    private String fileStatus;
}
