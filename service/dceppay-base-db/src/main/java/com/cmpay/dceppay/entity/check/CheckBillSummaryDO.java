/*
 * @ClassName CheckBillSummaryDO
 * @Description 
 * @version 1.0
 * @Date 2024-11-29 08:52:11
 */
package com.cmpay.dceppay.entity.check;

import com.cmpay.framework.data.BaseDO;
import com.cmpay.lemon.framework.annotation.DataObject;
import java.math.BigDecimal;

@DataObject
public class CheckBillSummaryDO extends BaseDO {
    /**
     * @Fields checkFileDate 对账日期
     */
    private String checkFileDate;
    /**
     * @Fields institutionCode 运营机构编码
     */
    private String institutionCode;
    /**
     * @Fields totalCount 总笔数
     */
    private Integer totalCount;
    /**
     * @Fields totalAmount 总金额
     */
    private BigDecimal totalAmount;
    /**
     * @Fields paymentCount 支付总笔数
     */
    private Integer paymentCount;
    /**
     * @Fields paymentAmount 支付总金额
     */
    private BigDecimal paymentAmount;
    /**
     * @Fields refundCount 退款总笔数
     */
    private Integer refundCount;
    /**
     * @Fields refundAmount 退款总金额
     */
    private BigDecimal refundAmount;
    /**
     * @Fields totalSuccessCount 成功总笔数
     */
    private Integer totalSuccessCount;
    /**
     * @Fields totalSuccessAmount 成功总金额
     */
    private BigDecimal totalSuccessAmount;
    /**
     * @Fields paymentSuccessCount 成功支付总笔数
     */
    private Integer paymentSuccessCount;
    /**
     * @Fields paymentSuccessAmount 成功支付总金额
     */
    private BigDecimal paymentSuccessAmount;
    /**
     * @Fields refundSuccessCount 成功退款总笔数
     */
    private Integer refundSuccessCount;
    /**
     * @Fields refundSuccessAmount 成功退款总金额
     */
    private BigDecimal refundSuccessAmount;
    /**
     * @Fields fileCount 文件总数
     */
    private Integer fileCount;
    /**
     * @Fields filePath 文件路径
     */
    private String filePath;
    /**
     * @Fields fileNameList 文件名列表
     */
    private String fileNameList;
    /**
     * @Fields digitalEnv SM2加密后SM4秘钥
     */
    private String digitalEnv;

    public String getCheckFileDate() {
        return checkFileDate;
    }

    public void setCheckFileDate(String checkFileDate) {
        this.checkFileDate = checkFileDate;
    }

    public String getInstitutionCode() {
        return institutionCode;
    }

    public void setInstitutionCode(String institutionCode) {
        this.institutionCode = institutionCode;
    }

    public Integer getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public Integer getPaymentCount() {
        return paymentCount;
    }

    public void setPaymentCount(Integer paymentCount) {
        this.paymentCount = paymentCount;
    }

    public BigDecimal getPaymentAmount() {
        return paymentAmount;
    }

    public void setPaymentAmount(BigDecimal paymentAmount) {
        this.paymentAmount = paymentAmount;
    }

    public Integer getRefundCount() {
        return refundCount;
    }

    public void setRefundCount(Integer refundCount) {
        this.refundCount = refundCount;
    }

    public BigDecimal getRefundAmount() {
        return refundAmount;
    }

    public void setRefundAmount(BigDecimal refundAmount) {
        this.refundAmount = refundAmount;
    }

    public Integer getTotalSuccessCount() {
        return totalSuccessCount;
    }

    public void setTotalSuccessCount(Integer totalSuccessCount) {
        this.totalSuccessCount = totalSuccessCount;
    }

    public BigDecimal getTotalSuccessAmount() {
        return totalSuccessAmount;
    }

    public void setTotalSuccessAmount(BigDecimal totalSuccessAmount) {
        this.totalSuccessAmount = totalSuccessAmount;
    }

    public Integer getPaymentSuccessCount() {
        return paymentSuccessCount;
    }

    public void setPaymentSuccessCount(Integer paymentSuccessCount) {
        this.paymentSuccessCount = paymentSuccessCount;
    }

    public BigDecimal getPaymentSuccessAmount() {
        return paymentSuccessAmount;
    }

    public void setPaymentSuccessAmount(BigDecimal paymentSuccessAmount) {
        this.paymentSuccessAmount = paymentSuccessAmount;
    }

    public Integer getRefundSuccessCount() {
        return refundSuccessCount;
    }

    public void setRefundSuccessCount(Integer refundSuccessCount) {
        this.refundSuccessCount = refundSuccessCount;
    }

    public BigDecimal getRefundSuccessAmount() {
        return refundSuccessAmount;
    }

    public void setRefundSuccessAmount(BigDecimal refundSuccessAmount) {
        this.refundSuccessAmount = refundSuccessAmount;
    }

    public Integer getFileCount() {
        return fileCount;
    }

    public void setFileCount(Integer fileCount) {
        this.fileCount = fileCount;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public String getFileNameList() {
        return fileNameList;
    }

    public void setFileNameList(String fileNameList) {
        this.fileNameList = fileNameList;
    }

    public String getDigitalEnv() {
        return digitalEnv;
    }

    public void setDigitalEnv(String digitalEnv) {
        this.digitalEnv = digitalEnv;
    }
}