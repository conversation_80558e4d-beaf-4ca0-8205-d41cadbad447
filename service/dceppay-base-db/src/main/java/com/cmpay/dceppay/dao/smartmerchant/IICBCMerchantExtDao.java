package com.cmpay.dceppay.dao.smartmerchant;

import com.cmpay.dceppay.entity.smartmerchant.ICBCMerchantDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR>
 * @date 2024/8/28 16:44
 * 工行商户信息表扩展dao
 */
@Mapper
public interface IICBCMerchantExtDao extends IICBCMerchantDao {
    /**
     * 根据商户号查询商户数据
     *
     * @param merchantId 商户号
     * @return 商户信息
     */
    ICBCMerchantDO getMerchantByMerchantID(String merchantId);


    /**
     * 商户进件状态--进件中
     *
     * @param updateDO
     * @return 更新记录数
     */
    int updateOnboarding(ICBCMerchantDO updateDO);

    /**
     * 商户进件状态--进件失败
     *
     * @param updateDO
     * @return 更新记录数
     */
    int updateOnboardFail(ICBCMerchantDO updateDO);

    /**
     * 商户进件状态--进件成功
     *
     * @param updateDO
     * @return 更新记录数
     */
    int updateOnboardSuccess(ICBCMerchantDO updateDO);


    /**
     * 商户进件状态--进件作废
     *
     * @param updateDO
     * @return 更新记录数
     */
    int updateOnboardCancel(ICBCMerchantDO updateDO);

    /**
     * 进件作废中
     * @param merchantDO
     * @return
     */
    int updateOnboardCanceling(ICBCMerchantDO merchantDO);

    /**
     * 作废失败
     * @param merchantDO
     * @return
     */
    int updateOnboardCancelFail(ICBCMerchantDO merchantDO);
}
