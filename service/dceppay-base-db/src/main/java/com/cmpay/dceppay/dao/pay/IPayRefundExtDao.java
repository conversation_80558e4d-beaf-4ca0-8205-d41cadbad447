package com.cmpay.dceppay.dao.pay;

import com.cmpay.dceppay.entity.pay.PayRefundDO;
import com.cmpay.dceppay.entity.pay.RefundQueryDO;
import org.apache.ibatis.annotations.Mapper;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/2 15:27
 */
@Mapper
public interface IPayRefundExtDao extends IPayRefundDao{

    PayRefundDO getByOutRefundNo(String refundOrderNo);


    BigDecimal sumRefundAmount(String outOrderNo);


    int updateRefundSuccess(PayRefundDO refundDO);

    int updateRefundFail(PayRefundDO refundDO);

    BigDecimal sumRefundSuccessAmount(String outOrderNo);

    void updateAccountDate(PayRefundDO refundDO);

    void updateRefundWait(PayRefundDO refundDO);

    List<PayRefundDO> findWaitRefundList(RefundQueryDO refundQueryDO);

    List<PayRefundDO> findWaitPendList(RefundQueryDO refundQueryDO);
}
