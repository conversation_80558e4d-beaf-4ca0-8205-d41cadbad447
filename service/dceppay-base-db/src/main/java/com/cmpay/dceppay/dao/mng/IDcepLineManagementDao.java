/*
 * @ClassName IDcepLineManagementDao
 * @Description 专线管理DAO接口
 * @version 1.0
 * @Date 2024-12-23 10:00:00
 */
package com.cmpay.dceppay.dao.mng;

import com.cmpay.dceppay.entity.mng.DcepLineManagementDO;
import com.cmpay.lemon.framework.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface IDcepLineManagementDao extends BaseDao<DcepLineManagementDO, String> {
    
    /**
     * 查询启用探活的专线列表
     * @return 启用探活的专线列表
     */
    List<DcepLineManagementDO> selectEnabledHealthCheckLines();
    
    /**
     * 根据业务类型查询默认专线
     * @param businessType 业务类型
     * @return 默认专线
     */
    DcepLineManagementDO selectDefaultLineByBusinessType(@Param("businessType") String businessType);
    
    /**
     * 根据业务类型查询响应最快的专线
     * @param businessType 业务类型
     * @return 响应最快的专线
     */
    DcepLineManagementDO selectFastestLineByBusinessType(@Param("businessType") String businessType);
    
    /**
     * 更新专线网络状态
     * @param id 专线ID
     * @param networkStatus 网络状态
     * @param consecutiveFailCount 连续失败次数
     * @param healthCheckFailStartTime 探活异常开始时间
     * @param responseTimeAvg 平均响应时间
     * @return 更新行数
     */
    int updateNetworkStatus(@Param("id") String id, 
                           @Param("networkStatus") String networkStatus,
                           @Param("consecutiveFailCount") Integer consecutiveFailCount,
                           @Param("healthCheckFailStartTime") java.util.Date healthCheckFailStartTime,
                           @Param("responseTimeAvg") Long responseTimeAvg);
}
