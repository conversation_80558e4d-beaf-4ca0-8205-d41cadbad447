package com.cmpay.dceppay.service.check;

import com.cmpay.dceppay.bo.check.CheckGenFileStatusBO;

/**
 * <AUTHOR>
 * @date 2024/9/18 9:11
 */
public interface ICheckGenFileStatusDBService {
    /**
     * 添加对账文件生成记录
     * @param checkGenFileStatusBO
     * @return
     */
    int addCheckGenFileStatus(CheckGenFileStatusBO checkGenFileStatusBO);


    CheckGenFileStatusBO getByKey(String checkDate,String channelCode);

    /**
     * 更新对账文件生成完成
     * @param checkGenFileStatusBO
     */
    void updateFileGenFinish(CheckGenFileStatusBO checkGenFileStatusBO);

    /**
     * 上传成功
     * @param checkDate
     * @param channelCode
     */
    void updateFileSendFinish(String checkDate ,String channelCode);

    /**
     * 上传失败
     * @param checkDate
     * @param channelCode
     */
    void updateFileSendFail(String checkDate ,String channelCode);
}
