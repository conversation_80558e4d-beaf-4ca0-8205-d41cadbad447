/*
 * @ClassName CheckFileDetailDO
 * @Description 
 * @version 1.0
 * @Date 2024-09-03 16:03:09
 */
package com.cmpay.dceppay.entity.check;

import com.cmpay.framework.data.BaseDO;
import com.cmpay.lemon.framework.annotation.DataObject;
import java.math.BigDecimal;

@DataObject
public class CheckFileDetailDO extends BaseDO {
    /**
     * @Fields outOrderNo 交易订单号
     */
    private String outOrderNo;
    /**
     * @Fields refundOrderNo 退款订单号
     */
    private String refundOrderNo;
    /**
     * @Fields checkFileDate 对账日期
     */
    private String checkFileDate;
    /**
     * @Fields orderCompleteTime 订单完成时间
     */
    private String orderCompleteTime;
    /**
     * @Fields messageNumber 报文编号
     */
    private String messageNumber;
    /**
     * @Fields messageIdentifier 报文标识号
     */
    private String messageIdentifier;
    /**
     * @Fields acceptInstitutionCode 受理服务机构编码
     */
    private String acceptInstitutionCode;
    /**
     * @Fields payInstitutionCode 付款运营机构编码
     */
    private String payInstitutionCode;
    /**
     * @Fields receiveInstitutionCode 收款运营机构编码
     */
    private String receiveInstitutionCode;
    /**
     * @Fields currencyCode 货币代码
     */
    private String currencyCode;
    /**
     * @Fields orderAmount 订单金额
     */
    private BigDecimal orderAmount;
    /**
     * @Fields checkType 对账类型
     */
    private String checkType;
    /**
     * @Fields bankOrderNo 机构交易订单号
     */
    private String bankOrderNo;
    /**
     * @Fields bankRefundNo 机构退款订单号
     */
    private String bankRefundNo;
    /**
     * @Fields orderStatus 业务状态
     */
    private String orderStatus;
    /**
     * @Fields tradeDesc 交易描述信息
     */
    private String tradeDesc;
    /**
     * @Fields checkStatus 对账状态
     */
    private String checkStatus;
    /**
     * @Fields checkCompleteTime 对账完成时间
     */
    private String checkCompleteTime;
    /**
     * @Fields tmSmp 最后更新时间
     */
    private String tmSmp;

    public String getOutOrderNo() {
        return outOrderNo;
    }

    public void setOutOrderNo(String outOrderNo) {
        this.outOrderNo = outOrderNo;
    }

    public String getRefundOrderNo() {
        return refundOrderNo;
    }

    public void setRefundOrderNo(String refundOrderNo) {
        this.refundOrderNo = refundOrderNo;
    }

    public String getCheckFileDate() {
        return checkFileDate;
    }

    public void setCheckFileDate(String checkFileDate) {
        this.checkFileDate = checkFileDate;
    }

    public String getOrderCompleteTime() {
        return orderCompleteTime;
    }

    public void setOrderCompleteTime(String orderCompleteTime) {
        this.orderCompleteTime = orderCompleteTime;
    }

    public String getMessageNumber() {
        return messageNumber;
    }

    public void setMessageNumber(String messageNumber) {
        this.messageNumber = messageNumber;
    }

    public String getMessageIdentifier() {
        return messageIdentifier;
    }

    public void setMessageIdentifier(String messageIdentifier) {
        this.messageIdentifier = messageIdentifier;
    }

    public String getAcceptInstitutionCode() {
        return acceptInstitutionCode;
    }

    public void setAcceptInstitutionCode(String acceptInstitutionCode) {
        this.acceptInstitutionCode = acceptInstitutionCode;
    }

    public String getPayInstitutionCode() {
        return payInstitutionCode;
    }

    public void setPayInstitutionCode(String payInstitutionCode) {
        this.payInstitutionCode = payInstitutionCode;
    }

    public String getReceiveInstitutionCode() {
        return receiveInstitutionCode;
    }

    public void setReceiveInstitutionCode(String receiveInstitutionCode) {
        this.receiveInstitutionCode = receiveInstitutionCode;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public BigDecimal getOrderAmount() {
        return orderAmount;
    }

    public void setOrderAmount(BigDecimal orderAmount) {
        this.orderAmount = orderAmount;
    }

    public String getCheckType() {
        return checkType;
    }

    public void setCheckType(String checkType) {
        this.checkType = checkType;
    }

    public String getBankOrderNo() {
        return bankOrderNo;
    }

    public void setBankOrderNo(String bankOrderNo) {
        this.bankOrderNo = bankOrderNo;
    }

    public String getBankRefundNo() {
        return bankRefundNo;
    }

    public void setBankRefundNo(String bankRefundNo) {
        this.bankRefundNo = bankRefundNo;
    }

    public String getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(String orderStatus) {
        this.orderStatus = orderStatus;
    }

    public String getTradeDesc() {
        return tradeDesc;
    }

    public void setTradeDesc(String tradeDesc) {
        this.tradeDesc = tradeDesc;
    }

    public String getCheckStatus() {
        return checkStatus;
    }

    public void setCheckStatus(String checkStatus) {
        this.checkStatus = checkStatus;
    }

    public String getCheckCompleteTime() {
        return checkCompleteTime;
    }

    public void setCheckCompleteTime(String checkCompleteTime) {
        this.checkCompleteTime = checkCompleteTime;
    }

    public String getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(String tmSmp) {
        this.tmSmp = tmSmp;
    }
}