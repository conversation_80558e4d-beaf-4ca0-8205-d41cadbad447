package com.cmpay.dceppay.service.bus.impl;

import com.cmpay.dceppay.bo.bus.BusMessageBO;
import com.cmpay.dceppay.dao.bus.IBusMessageExtDao;
import com.cmpay.dceppay.entity.bus.BusMessageDO;
import com.cmpay.dceppay.enums.common.IdGenKeyEnum;
import com.cmpay.dceppay.service.bus.IBusMessageDBService;
import com.cmpay.dceppay.util.DateTimeUtil;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.framework.utils.IdGenUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/10/30 17:13
 */
@Service
public class BusMessageDBServiceImpl implements IBusMessageDBService {
    @Autowired
    private IBusMessageExtDao busMessageExtDao;

    @Override
    public int addBusMessage(BusMessageBO busMessageBO) {
        BusMessageDO busMessageDO = new BusMessageDO();
        BeanUtils.copyProperties(busMessageDO,busMessageBO);
        busMessageDO.setMessageId(IdGenUtils.generateIdWithShortDateTime(IdGenKeyEnum.BUS_MESSAGE_ID.name(), IdGenKeyEnum.BUS_MESSAGE_ID_LENGTH));
        String messageType=busMessageBO.getMessageType();
        busMessageDO.setMessageType( messageType.substring(0, messageType.lastIndexOf('.')));
        busMessageDO.setTmSmp(DateTimeUtil.getCurrentDateTimeStr());
        return busMessageExtDao.insert(busMessageDO);
    }
}
