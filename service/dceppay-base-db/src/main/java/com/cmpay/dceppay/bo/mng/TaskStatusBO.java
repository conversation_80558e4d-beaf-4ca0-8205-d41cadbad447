package com.cmpay.dceppay.bo.mng;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/9/26 13:26
 *
 */
@Data
public class TaskStatusBO {
    /**
     * @Fields taskDate 执行日期
     */
    private String taskDate;
    /**
     * @Fields taskKey 任务key
     */
    private String taskKey;
    /**
     * @Fields taskDesc 任务说明
     */
    private String taskDesc;
    /**
     * @Fields status 任务状态PENDING,RUNNING,SUCCESS,FAILED,CANCELLED
     */
    private String status;
    /**
     * @Fields result 执行结果，可以存储错误信息或者成功信息
     */
    private String result;
    /**
     * @Fields createdTime 创建时间
     */
    private String createdTime;
    /**
     * @Fields updatedTime 更新时间
     */
    private String updatedTime;
}
