/*
 * @ClassName BusMessageDO
 * @Description 
 * @version 1.0
 * @Date 2024-10-30 17:07:40
 */
package com.cmpay.dceppay.entity.bus;

import com.cmpay.framework.data.BaseDO;
import com.cmpay.lemon.framework.annotation.DataObject;

@DataObject
public class BusMessageDO extends BaseDO {
    /**
     * @Fields messageId 消息号
     */
    private String messageId;
    /**
     * @Fields messageType 消息类型
     */
    private String messageType;
    /**
     * @Fields messageDate 消息日期
     */
    private String messageDate;
    /**
     * @Fields messageTime 消息时间
     */
    private String messageTime;
    /**
     * @Fields sender 发起方
     */
    private String sender;
    /**
     * @Fields receiver 接收方
     */
    private String receiver;
    /**
     * @Fields messageContent 消息内容
     */
    private String messageContent;
    /**
     * @Fields remark 备注信息
     */
    private String remark;
    /**
     * @Fields operateId 操作者
     */
    private String operateId;

    public String getMessageId() {
        return messageId;
    }

    public void setMessageId(String messageId) {
        this.messageId = messageId;
    }

    public String getMessageType() {
        return messageType;
    }

    public void setMessageType(String messageType) {
        this.messageType = messageType;
    }

    public String getMessageDate() {
        return messageDate;
    }

    public void setMessageDate(String messageDate) {
        this.messageDate = messageDate;
    }

    public String getMessageTime() {
        return messageTime;
    }

    public void setMessageTime(String messageTime) {
        this.messageTime = messageTime;
    }

    public String getSender() {
        return sender;
    }

    public void setSender(String sender) {
        this.sender = sender;
    }

    public String getReceiver() {
        return receiver;
    }

    public void setReceiver(String receiver) {
        this.receiver = receiver;
    }

    public String getMessageContent() {
        return messageContent;
    }

    public void setMessageContent(String messageContent) {
        this.messageContent = messageContent;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getOperateId() {
        return operateId;
    }

    public void setOperateId(String operateId) {
        this.operateId = operateId;
    }
}