package com.cmpay.dceppay.service.check.ext.impl;


import com.cmpay.dceppay.bo.check.CheckFileSummaryBO;
import com.cmpay.dceppay.service.check.ICheckBillSummaryDBService;
import com.cmpay.dceppay.service.check.ICheckFileProcessDBService;
import com.cmpay.dceppay.service.check.ext.ICheckFileSummaryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 2024/9/23 10:16
 */
@Service
public class CheckFileSummaryServiceImpl implements ICheckFileSummaryService {
    @Autowired
    private ICheckBillSummaryDBService checkBillSummaryDBService;
    @Autowired
    private ICheckFileProcessDBService checkFileProcessDBService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void registerSummaryInfo(CheckFileSummaryBO checkFileSummaryBO) {
        int addNumber = checkBillSummaryDBService.initCheckBillInfo(checkFileSummaryBO);
        if (addNumber == 1) {
            checkFileProcessDBService.initCheckFileProcess(checkFileSummaryBO.getCheckFileDate(), checkFileSummaryBO.getInstitutionCode());
        }
    }
}
