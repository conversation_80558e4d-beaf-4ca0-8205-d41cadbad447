package com.cmpay.dceppay.entity.check;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/9/25 18:40
 */
@Data
public class CheckSummaryExtDO {
    /**
     * @Fields totalCount 总笔数
     */
    private Integer totalCount;
    /**
     * @Fields totalAmount 总金额
     */
    private BigDecimal totalAmount;
    /**
     * @Fields paymentCount 支付总笔数
     */
    private Integer paymentCount;
    /**
     * @Fields paymentAmount 支付总金额
     */
    private BigDecimal paymentAmount;
    /**
     * @Fields refundCount 退款总笔数
     */
    private Integer refundCount;
    /**
     * @Fields refundAmount 退款总金额
     */
    private BigDecimal refundAmount;
    /**
     * @Fields totalSuccessCount 成功总笔数
     */
    private Integer totalSuccessCount;
    /**
     * @Fields totalSuccessAmount 成功总金额
     */
    private BigDecimal totalSuccessAmount;
    /**
     * @Fields paymentSuccessCount 成功支付总笔数
     */
    private Integer paymentSuccessCount;
    /**
     * @Fields paymentSuccessAmount 成功支付总金额
     */
    private BigDecimal paymentSuccessAmount;
    /**
     * @Fields refundSuccessCount 成功退款总笔数
     */
    private Integer refundSuccessCount;
    /**
     * @Fields refundSuccessAmount 成功退款总金额
     */
    private BigDecimal refundSuccessAmount;
}
