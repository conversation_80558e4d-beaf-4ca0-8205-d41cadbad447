/*
 * @ClassName PayAccountRecordDO
 * @Description 
 * @version 1.0
 * @Date 2024-11-12 14:11:49
 */
package com.cmpay.dceppay.entity.pay;

import com.cmpay.framework.data.BaseDO;
import com.cmpay.lemon.framework.annotation.DataObject;
import java.math.BigDecimal;

@DataObject
public class PayAccountRecordDO extends BaseDO {
    /**
     * @Fields jrnNo 流水号
     */
    private String jrnNo;
    /**
     * @Fields orderDate 交易日期
     */
    private String orderDate;
    /**
     * @Fields orderNo 订单编号
     */
    private String orderNo;
    /**
     * @Fields orderType 订单类型 P1 数字人民币支付, P2 数字人民币退款
     */
    private String orderType;
    /**
     * @Fields busType 业务类型 P101
     */
    private String busType;
    /**
     * @Fields busChannel 业务渠道
     */
    private String busChannel;
    /**
     * @Fields tradeType 交易类型 P1
     */
    private String tradeType;
    /**
     * @Fields busCode 业务交易码 DCEP0001-数币支付, DCEP0002-数币退款, DCEP0003-数币对账, DCEP0004-数币补单, DCEP0005-数币退款（撤单）
     */
    private String busCode;
    /**
     * @Fields accountFrom 账务来源方
     */
    private String accountFrom;
    /**
     * @Fields createTime 创建时间
     */
    private String createTime;
    /**
     * @Fields finishTime 处理时间
     */
    private String finishTime;
    /**
     * @Fields handleStatus 账务处理结果
     */
    private String handleStatus;
    /**
     * @Fields accountDate 会计日期
     */
    private String accountDate;
    /**
     * @Fields remark 备注
     */
    private String remark;
    /**
     * @Fields amount 金额
     */
    private BigDecimal amount;

    public String getJrnNo() {
        return jrnNo;
    }

    public void setJrnNo(String jrnNo) {
        this.jrnNo = jrnNo;
    }

    public String getOrderDate() {
        return orderDate;
    }

    public void setOrderDate(String orderDate) {
        this.orderDate = orderDate;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public String getBusType() {
        return busType;
    }

    public void setBusType(String busType) {
        this.busType = busType;
    }

    public String getBusChannel() {
        return busChannel;
    }

    public void setBusChannel(String busChannel) {
        this.busChannel = busChannel;
    }

    public String getTradeType() {
        return tradeType;
    }

    public void setTradeType(String tradeType) {
        this.tradeType = tradeType;
    }

    public String getBusCode() {
        return busCode;
    }

    public void setBusCode(String busCode) {
        this.busCode = busCode;
    }

    public String getAccountFrom() {
        return accountFrom;
    }

    public void setAccountFrom(String accountFrom) {
        this.accountFrom = accountFrom;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getFinishTime() {
        return finishTime;
    }

    public void setFinishTime(String finishTime) {
        this.finishTime = finishTime;
    }

    public String getHandleStatus() {
        return handleStatus;
    }

    public void setHandleStatus(String handleStatus) {
        this.handleStatus = handleStatus;
    }

    public String getAccountDate() {
        return accountDate;
    }

    public void setAccountDate(String accountDate) {
        this.accountDate = accountDate;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }
}