/*
 * @ClassName ICBCServiceProviderDO
 * @Description 
 * @version 1.0
 * @Date 2024-10-18 09:56:05
 */
package com.cmpay.dceppay.entity.smartmerchant;

import com.cmpay.framework.data.BaseDO;
import com.cmpay.lemon.framework.annotation.DataObject;

@DataObject
public class ICBCServiceProviderDO extends BaseDO {
    /**
     * @Fields serviceId 服务商编号
     */
    private String serviceId;
    /**
     * @Fields serviceProviderId 运营机构服务商编号
     */
    private String serviceProviderId;
    /**
     * @Fields serviceName 服务商名称
     */
    private String serviceName;
    /**
     * @Fields licenseType 证件类型
     */
    private String licenseType;
    /**
     * @Fields licenseNo 证件编号
     */
    private String licenseNo;
    /**
     * @Fields sceneDesc 场景ID
     */
    private String sceneDesc;
    /**
     * @Fields sceneId 场景描述
     */
    private String sceneId;
    /**
     * @Fields onboardStatus 进件状态
     */
    private String onboardStatus;
    /**
     * @Fields serviceStatus 商户状态
     */
    private String serviceStatus;
    /**
     * @Fields onboardId 进件id
     */
    private String onboardId;
    /**
     * @Fields msgCode 错误码
     */
    private String msgCode;
    /**
     * @Fields msgInfo 错误信息
     */
    private String msgInfo;
    /**
     * @Fields remark 备注信息
     */
    private String remark;
    /**
     * @Fields onboardTime 进件时间
     */
    private String onboardTime;
    /**
     * @Fields createId 创建者
     */
    private String createId;
    /**
     * @Fields createTime 创建时间
     */
    private String createTime;
    /**
     * @Fields updateId 更新者
     */
    private String updateId;
    /**
     * @Fields updateTime 更新时间
     */
    private String updateTime;

    public String getServiceId() {
        return serviceId;
    }

    public void setServiceId(String serviceId) {
        this.serviceId = serviceId;
    }

    public String getServiceProviderId() {
        return serviceProviderId;
    }

    public void setServiceProviderId(String serviceProviderId) {
        this.serviceProviderId = serviceProviderId;
    }

    public String getServiceName() {
        return serviceName;
    }

    public void setServiceName(String serviceName) {
        this.serviceName = serviceName;
    }

    public String getLicenseType() {
        return licenseType;
    }

    public void setLicenseType(String licenseType) {
        this.licenseType = licenseType;
    }

    public String getLicenseNo() {
        return licenseNo;
    }

    public void setLicenseNo(String licenseNo) {
        this.licenseNo = licenseNo;
    }

    public String getSceneDesc() {
        return sceneDesc;
    }

    public void setSceneDesc(String sceneDesc) {
        this.sceneDesc = sceneDesc;
    }

    public String getSceneId() {
        return sceneId;
    }

    public void setSceneId(String sceneId) {
        this.sceneId = sceneId;
    }

    public String getOnboardStatus() {
        return onboardStatus;
    }

    public void setOnboardStatus(String onboardStatus) {
        this.onboardStatus = onboardStatus;
    }

    public String getServiceStatus() {
        return serviceStatus;
    }

    public void setServiceStatus(String serviceStatus) {
        this.serviceStatus = serviceStatus;
    }

    public String getOnboardId() {
        return onboardId;
    }

    public void setOnboardId(String onboardId) {
        this.onboardId = onboardId;
    }

    public String getMsgCode() {
        return msgCode;
    }

    public void setMsgCode(String msgCode) {
        this.msgCode = msgCode;
    }

    public String getMsgInfo() {
        return msgInfo;
    }

    public void setMsgInfo(String msgInfo) {
        this.msgInfo = msgInfo;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getOnboardTime() {
        return onboardTime;
    }

    public void setOnboardTime(String onboardTime) {
        this.onboardTime = onboardTime;
    }

    public String getCreateId() {
        return createId;
    }

    public void setCreateId(String createId) {
        this.createId = createId;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateId() {
        return updateId;
    }

    public void setUpdateId(String updateId) {
        this.updateId = updateId;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }
}