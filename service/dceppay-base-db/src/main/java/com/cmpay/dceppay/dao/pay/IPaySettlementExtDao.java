package com.cmpay.dceppay.dao.pay;

import com.cmpay.dceppay.entity.pay.PaySettlementDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/2 15:28
 */
@Mapper
public interface IPaySettlementExtDao extends IPaySettlementDao {

    PaySettlementDO getPaymentSettlement(PaySettlementDO queryDO);

    PaySettlementDO getRefundSettlement(PaySettlementDO queryDO);

    void updateCheckComplete(PaySettlementDO paySettlementDO);

    void updateAmountError(PaySettlementDO paySettlementDO);

    List<PaySettlementDO> findWaitOrderList(String checkDate);

    PaySettlementDO getPaymentShortDoubt(PaySettlementDO queryDO);

    PaySettlementDO getRefundLongDoubt(PaySettlementDO queryDO);

    void updateCheckErrorComplete(PaySettlementDO paySettlementDO);

    void updateCheckErrorAmount(PaySettlementDO paySettlementDO);

    int updateAccountDate(PaySettlementDO paySettlementDO);

    void updatePaymentShort(PaySettlementDO paySettlementDO);

    void updateRefundLog(PaySettlementDO paySettlementDO);
}
