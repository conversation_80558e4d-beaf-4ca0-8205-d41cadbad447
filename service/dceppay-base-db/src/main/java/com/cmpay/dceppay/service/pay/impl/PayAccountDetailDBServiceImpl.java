package com.cmpay.dceppay.service.pay.impl;

import com.cmpay.dceppay.bo.pay.PayAccountBO;
import com.cmpay.dceppay.bo.pay.PayAccountDetailBO;
import com.cmpay.dceppay.dao.pay.IPayAccountDetailExtDao;
import com.cmpay.dceppay.entity.pay.PayAccountDetailDO;
import com.cmpay.dceppay.service.pay.IPayAccountDetailDBService;
import com.cmpay.dceppay.util.DateTimeUtil;
import com.cmpay.lemon.common.utils.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/3 17:09
 */
@Service
public class PayAccountDetailDBServiceImpl implements IPayAccountDetailDBService {
    @Autowired
    private IPayAccountDetailExtDao accountDetailExtDao;

    @Override
    public void savePayAccountDetail(List<PayAccountDetailBO> payAccountDetail, PayAccountBO accountBO) {
        for (PayAccountDetailBO accountDetailBO : payAccountDetail) {
            PayAccountDetailDO payAccountDetailDO = BeanUtils.copyPropertiesReturnDest(new PayAccountDetailDO(), accountDetailBO);
            payAccountDetailDO.setOrderDate(accountBO.getOrderDate());
            payAccountDetailDO.setJrnNo(accountBO.getBusJrnNo());
            payAccountDetailDO.setTmSmp(DateTimeUtil.getCurrentDateTimeStr());
            accountDetailExtDao.insert(payAccountDetailDO);
        }

    }
}
