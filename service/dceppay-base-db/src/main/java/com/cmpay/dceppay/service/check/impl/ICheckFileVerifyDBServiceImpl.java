package com.cmpay.dceppay.service.check.impl;

import com.cmpay.dceppay.dao.check.ICheckFileVerifyExtDao;
import com.cmpay.dceppay.entity.check.CheckFileVerifyDO;
import com.cmpay.dceppay.entity.check.CheckFileVerifyDOKey;
import com.cmpay.dceppay.enums.check.CheckFileHandleEnum;
import com.cmpay.dceppay.service.check.ICheckFileVerifyDBService;
import com.cmpay.dceppay.util.DateTimeUtil;
import com.cmpay.lemon.common.utils.JudgeUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/9/23 19:55
 */
@Service
@Slf4j
public class ICheckFileVerifyDBServiceImpl implements ICheckFileVerifyDBService {
    @Autowired
    private ICheckFileVerifyExtDao checkFileVerifyExtDao;

    @Override
    public String queryCheckDiffFlag(String checkFileDate, String institutionCode) {
        CheckFileVerifyDOKey checkFileVerifyDOKey = getCheckFileVerifyDOKey(checkFileDate, institutionCode);
        CheckFileVerifyDO checkFileVerifyDO = checkFileVerifyExtDao.get(checkFileVerifyDOKey);
        if (JudgeUtils.isNull(checkFileVerifyExtDao.get(checkFileVerifyDOKey))) {
            CheckFileVerifyDO insertCheckVerifyDO=new CheckFileVerifyDO();
            insertCheckVerifyDO.setCheckFileDate(checkFileDate);
            insertCheckVerifyDO.setInstitutionCode(institutionCode);
            insertCheckVerifyDO.setHandleFlag(CheckFileHandleEnum.DO_NOTHING.name());
            insertCheckVerifyDO.setDownloadEndTime(DateTimeUtil.getCurrentDateTimeStr());
            insertCheckVerifyDO.setTmSmp(DateTimeUtil.getCurrentDateTimeStr());
            checkFileVerifyExtDao.insert(insertCheckVerifyDO);
            return CheckFileHandleEnum.DO_NOTHING.name();
        }
        return checkFileVerifyDO.getHandleFlag();
    }

    private static CheckFileVerifyDOKey getCheckFileVerifyDOKey(String checkFileDate, String institutionCode) {
        CheckFileVerifyDOKey checkFileVerifyDOKey = new CheckFileVerifyDOKey();
        checkFileVerifyDOKey.setCheckFileDate(checkFileDate);
        checkFileVerifyDOKey.setInstitutionCode(institutionCode);
        return checkFileVerifyDOKey;
    }

    @Override
    public void resetHandleFlag(String checkFileDate, String institutionCode) {
        CheckFileVerifyDO checkFileVerifyDO = new CheckFileVerifyDO();
        checkFileVerifyDO.setCheckFileDate(checkFileDate);
        checkFileVerifyDO.setInstitutionCode(institutionCode);
        checkFileVerifyDO.setHandleFlag(CheckFileHandleEnum.DO_NOTHING.name());
        checkFileVerifyDO.setTmSmp(DateTimeUtil.getCurrentDateTimeStr());
        checkFileVerifyExtDao.resetHandleFlag(checkFileVerifyDO);
    }


}
