package com.cmpay.dceppay.service.mng.impl;

import com.cmpay.dceppay.bo.mng.BusParamBO;
import com.cmpay.dceppay.constant.mng.BusParamTypeConstants;
import com.cmpay.dceppay.dao.mng.IBusParamExtDao;
import com.cmpay.dceppay.entity.mng.BusParamDO;
import com.cmpay.dceppay.enums.dcep.FinancialStatusEnum;
import com.cmpay.dceppay.service.mng.IFinancialBusParamService;
import com.cmpay.dceppay.util.DateTimeUtil;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.datasource.TargetDataSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/10/31 11:26
 */
@Service
public class IFinancialBusParamServiceImpl implements IFinancialBusParamService {
    @Autowired
    private IBusParamExtDao paramExtDao;

    @Override
    @TargetDataSource("dcepmng")
    public BusParamBO getFinancialStatus() {
        BusParamDO busParamDO = paramExtDao.getByParamType(BusParamTypeConstants.FINANCIAL_STATUS);
        if (JudgeUtils.isNotNull(busParamDO)) {
          return  BeanUtils.copyPropertiesReturnDest(new BusParamBO(), busParamDO);
        }
        return null;
    }

    @Override
    @TargetDataSource("dcepmng")
    public void updateFinancialStatusOff(BusParamBO busParamBO) {
        updateFinancialStatus(busParamBO, FinancialStatusEnum.OFF);
    }


    @Override
    @TargetDataSource("dcepmng")
    public void updateFinancialStatusOn(BusParamBO busParamBO) {
        updateFinancialStatus(busParamBO, FinancialStatusEnum.ON);

    }

    private void updateFinancialStatus(BusParamBO busParamBO, FinancialStatusEnum statusEnum) {
        BusParamDO busParamDO = new BusParamDO();
        busParamDO.setParamNo(busParamBO.getParamNo());
        busParamDO.setParamCode(statusEnum.name());
        busParamDO.setParamLabel(statusEnum.getDesc());
        if (JudgeUtils.isNotNull(busParamBO.getOperateId())) {
            busParamDO.setUpdateUserNo(busParamBO.getOperateId());
        }
        busParamDO.setUpdateTime(DateTimeUtil.getCurrentDateTimeStr());
        paramExtDao.updateFinancialStatus(busParamDO);
    }
}
