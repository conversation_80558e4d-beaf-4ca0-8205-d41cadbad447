package com.cmpay.dceppay.service.smartmerchant;

import com.cmpay.dceppay.bo.smartmerchant.ICBCServiceProviderBO;
import com.cmpay.dceppay.bo.smartmerchant.ICBCServiceProviderUpdateStatusBO;

/**
 * <AUTHOR>
 * @date 2024/8/28 16:48
 */
public interface IICBCServiceProviderDBService {
    /**
     * 服务商信息查询
     * @param serviceId
     * @return
     */
    ICBCServiceProviderBO getServiceProviderInfoByServiceID(String serviceId);

    /**
     * 更新进件等待中
     * @param updateBO
     * @return
     */
    int updateOnboarding(ICBCServiceProviderUpdateStatusBO updateBO);

    /**
     * 更新进件失败
     * @param updateBO
     * @return
     */
    int updateOnboardFail(ICBCServiceProviderUpdateStatusBO updateBO);

    /**
     * 更新进件成功
     * @param updateBO
     * @return
     */
    int updateOnboardSuccess(ICBCServiceProviderUpdateStatusBO updateBO);

    /**
     * 更新进件作废
     * @param updateBO
     * @return
     */
    int updateOnboardCancel(ICBCServiceProviderUpdateStatusBO updateBO);
}
