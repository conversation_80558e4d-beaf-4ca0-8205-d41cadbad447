package com.cmpay.dceppay.jdbc;

import com.cmpay.dceppay.util.DateTimeUtil;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;

import java.math.BigDecimal;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/23 10:15
 */
public class CustomBatchPreparedStatementSetter implements BatchPreparedStatementSetter {
    private final List<Object[]> params;

    public CustomBatchPreparedStatementSetter(List<Object[]> params) {
        this.params = params;
    }

    @Override
    public void setValues(PreparedStatement ps, int i) throws SQLException {
        Object[] param = params.get(i);
        // 设置参数，例如：
        ps.setString(1,String.valueOf(param[0]));
        ps.setString(2,String.valueOf(param[1]));
        ps.setString(3,String.valueOf(param[2]));
        ps.setString(4,String.valueOf(param[3]));
        ps.setString(5,String.valueOf(param[4]));
        ps.setString(6,String.valueOf(param[5]));
        ps.setString(7,String.valueOf(param[6]));
        ps.setString(8, String.valueOf(param[7]));
        ps.setString(9, String.valueOf(param[8]));
        ps.setString(10, String.valueOf(param[9]));
        ps.setBigDecimal(11, new BigDecimal(String.valueOf(param[10])));
        ps.setString(12,String.valueOf(param[11]));
        ps.setString(13,String.valueOf(param[12]));
        ps.setString(14,String.valueOf(param[13]));
        ps.setString(15,String.valueOf(param[14]));
        ps.setString(16,String.valueOf(param[15]));
        ps.setString(17,String.valueOf(param[16]));
        ps.setString(18,String.valueOf(param[17]));
    }

    @Override
    public int getBatchSize() {
        return params.size();
    }
}