package com.cmpay.dceppay.bo.pay;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/9/4 18:44
 */
@Data
public class MerchantRouteBO {
    /**
     * @Fields bussCode 业务类型标识
     */
    private String bussCode;
    /**
     * @Fields bussType 业务类型名称
     */
    private String bussType;
    /**
     * @Fields merchantNo 商户号
     */
    private String merchantNo;
    /**
     * @Fields routeStatus 路由状态 disable enable
     */
    private String routeStatus;

    /**
     * @Fields operatorMerchantId 运营机构商户号
     */
    private String operatorMerchantId;
    /**
     * @Fields serviceMerchantFlag 是否服务商商户
     */
    private String serviceMerchantFlag;
    /**
     * @Fields 服务商编号
     */
    private String serviceId;
    /**
     * @Fields serviceProviderId 运营机构服务商编号
     */
    private String serviceProviderId;
    /**
     * @Fields merchantName 商户名称
     */
    private String merchantName;
    /**
     * @Fields merchantShowName 商户对外经营名称
     */
    private String merchantShowName;
    /**
     * @Fields merchantShortName 商户简称
     */
    private String merchantShortName;
    /**
     * @Fields merchantCategory 经营类目（MCC）
     */
    private String merchantCategory;
    /**
     * @Fields merchantLicenseType 证件类型
     */
    private String merchantLicenseType;
    /**
     * @Fields merchantLicense 证件编号
     */
    private String merchantLicense;
    /**
     * @Fields walletId 钱包id
     */
    private String walletId;
    private String channelCode;
    //报文标识号
    private String messageIdentification ;

}
