/*
 * @ClassName ICBCMerchantDO
 * @Description 
 * @version 1.0
 * @Date 2024-10-18 09:56:05
 */
package com.cmpay.dceppay.entity.smartmerchant;

import com.cmpay.framework.data.BaseDO;
import com.cmpay.lemon.framework.annotation.DataObject;

@DataObject
public class ICBCMerchantDO extends BaseDO {
    /**
     * @Fields merchantId 商户编号
     */
    private String merchantId;
    /**
     * @Fields operatorMerchantId 运营机构商户号
     */
    private String operatorMerchantId;
    /**
     * @Fields serviceMerchantFlag 是否服务商商户
     */
    private String serviceMerchantFlag;
    /**
     * @Fields serviceId 运营机构服务商编号
     */
    private String serviceId;
    /**
     * @Fields merchantName 商户名称
     */
    private String merchantName;
    /**
     * @Fields merchantShowName 商户对外经营名称
     */
    private String merchantShowName;
    /**
     * @Fields merchantShortName 商户简称
     */
    private String merchantShortName;
    /**
     * @Fields merchantCategory 经营类目（MCC）
     */
    private String merchantCategory;
    /**
     * @Fields bussCode 业务类型
     */
    private String bussCode;
    /**
     * @Fields bussType 业务种类
     */
    private String bussType;
    /**
     * @Fields merchantType 商户类型
     */
    private String merchantType;
    /**
     * @Fields merchantLicenseType 证件类型
     */
    private String merchantLicenseType;
    /**
     * @Fields merchantLicense 证件编号
     */
    private String merchantLicense;
    /**
     * @Fields contactName 商户联系人
     */
    private String contactName;
    /**
     * @Fields servicePhone 商户联系人电话
     */
    private String servicePhone;
    /**
     * @Fields contactPhone 联系电话
     */
    private String contactPhone;
    /**
     * @Fields contactEmail 联系邮箱
     */
    private String contactEmail;
    /**
     * @Fields unifyEntryFlag 统一入账标识
     */
    private String unifyEntryFlag;
    /**
     * @Fields settleCycle 结算周期
     */
    private String settleCycle;
    /**
     * @Fields accType 账户类型
     */
    private String accType;
    /**
     * @Fields accName 账户名称
     */
    private String accName;
    /**
     * @Fields accNo 银行账号
     */
    private String accNo;
    /**
     * @Fields accBankName 开户银行(含支行)
     */
    private String accBankName;
    /**
     * @Fields accBankCode 开户行联行号
     */
    private String accBankCode;
    /**
     * @Fields walletId 结算钱包id
     */
    private String walletId;
    /**
     * @Fields walletName 结算钱包名称
     */
    private String walletName;
    /**
     * @Fields merchantWalletName 商户伞底钱包名称
     */
    private String merchantWalletName;
    /**
     * @Fields merchantWalletId 商户伞底钱包ID
     */
    private String merchantWalletId;
    /**
     * @Fields protocolWalletName 协议伞顶钱包名称
     */
    private String protocolWalletName;
    /**
     * @Fields protocolWalletId 协议伞顶钱包ID
     */
    private String protocolWalletId;
    /**
     * @Fields remark 备注信息
     */
    private String remark;
    /**
     * @Fields onboardStatus 进件状态
     */
    private String onboardStatus;
    /**
     * @Fields serviceStatus 商户状态
     */
    private String serviceStatus;
    /**
     * @Fields onboardId 进件id
     */
    private String onboardId;
    /**
     * @Fields onboardTime 进件时间
     */
    private String onboardTime;
    /**
     * @Fields msgCode 错误码
     */
    private String msgCode;
    /**
     * @Fields msgInfo 错误信息
     */
    private String msgInfo;
    /**
     * @Fields createId 创建者
     */
    private String createId;
    /**
     * @Fields createTime 创建时间
     */
    private String createTime;
    /**
     * @Fields updateId 更新者
     */
    private String updateId;
    /**
     * @Fields updateTime 更新时间
     */
    private String updateTime;

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getOperatorMerchantId() {
        return operatorMerchantId;
    }

    public void setOperatorMerchantId(String operatorMerchantId) {
        this.operatorMerchantId = operatorMerchantId;
    }

    public String getServiceMerchantFlag() {
        return serviceMerchantFlag;
    }

    public void setServiceMerchantFlag(String serviceMerchantFlag) {
        this.serviceMerchantFlag = serviceMerchantFlag;
    }

    public String getServiceId() {
        return serviceId;
    }

    public void setServiceId(String serviceId) {
        this.serviceId = serviceId;
    }

    public String getMerchantName() {
        return merchantName;
    }

    public void setMerchantName(String merchantName) {
        this.merchantName = merchantName;
    }

    public String getMerchantShowName() {
        return merchantShowName;
    }

    public void setMerchantShowName(String merchantShowName) {
        this.merchantShowName = merchantShowName;
    }

    public String getMerchantShortName() {
        return merchantShortName;
    }

    public void setMerchantShortName(String merchantShortName) {
        this.merchantShortName = merchantShortName;
    }

    public String getMerchantCategory() {
        return merchantCategory;
    }

    public void setMerchantCategory(String merchantCategory) {
        this.merchantCategory = merchantCategory;
    }

    public String getBussCode() {
        return bussCode;
    }

    public void setBussCode(String bussCode) {
        this.bussCode = bussCode;
    }

    public String getBussType() {
        return bussType;
    }

    public void setBussType(String bussType) {
        this.bussType = bussType;
    }

    public String getMerchantType() {
        return merchantType;
    }

    public void setMerchantType(String merchantType) {
        this.merchantType = merchantType;
    }

    public String getMerchantLicenseType() {
        return merchantLicenseType;
    }

    public void setMerchantLicenseType(String merchantLicenseType) {
        this.merchantLicenseType = merchantLicenseType;
    }

    public String getMerchantLicense() {
        return merchantLicense;
    }

    public void setMerchantLicense(String merchantLicense) {
        this.merchantLicense = merchantLicense;
    }

    public String getContactName() {
        return contactName;
    }

    public void setContactName(String contactName) {
        this.contactName = contactName;
    }

    public String getServicePhone() {
        return servicePhone;
    }

    public void setServicePhone(String servicePhone) {
        this.servicePhone = servicePhone;
    }

    public String getContactPhone() {
        return contactPhone;
    }

    public void setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone;
    }

    public String getContactEmail() {
        return contactEmail;
    }

    public void setContactEmail(String contactEmail) {
        this.contactEmail = contactEmail;
    }

    public String getUnifyEntryFlag() {
        return unifyEntryFlag;
    }

    public void setUnifyEntryFlag(String unifyEntryFlag) {
        this.unifyEntryFlag = unifyEntryFlag;
    }

    public String getSettleCycle() {
        return settleCycle;
    }

    public void setSettleCycle(String settleCycle) {
        this.settleCycle = settleCycle;
    }

    public String getAccType() {
        return accType;
    }

    public void setAccType(String accType) {
        this.accType = accType;
    }

    public String getAccName() {
        return accName;
    }

    public void setAccName(String accName) {
        this.accName = accName;
    }

    public String getAccNo() {
        return accNo;
    }

    public void setAccNo(String accNo) {
        this.accNo = accNo;
    }

    public String getAccBankName() {
        return accBankName;
    }

    public void setAccBankName(String accBankName) {
        this.accBankName = accBankName;
    }

    public String getAccBankCode() {
        return accBankCode;
    }

    public void setAccBankCode(String accBankCode) {
        this.accBankCode = accBankCode;
    }

    public String getWalletId() {
        return walletId;
    }

    public void setWalletId(String walletId) {
        this.walletId = walletId;
    }

    public String getWalletName() {
        return walletName;
    }

    public void setWalletName(String walletName) {
        this.walletName = walletName;
    }

    public String getMerchantWalletName() {
        return merchantWalletName;
    }

    public void setMerchantWalletName(String merchantWalletName) {
        this.merchantWalletName = merchantWalletName;
    }

    public String getMerchantWalletId() {
        return merchantWalletId;
    }

    public void setMerchantWalletId(String merchantWalletId) {
        this.merchantWalletId = merchantWalletId;
    }

    public String getProtocolWalletName() {
        return protocolWalletName;
    }

    public void setProtocolWalletName(String protocolWalletName) {
        this.protocolWalletName = protocolWalletName;
    }

    public String getProtocolWalletId() {
        return protocolWalletId;
    }

    public void setProtocolWalletId(String protocolWalletId) {
        this.protocolWalletId = protocolWalletId;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getOnboardStatus() {
        return onboardStatus;
    }

    public void setOnboardStatus(String onboardStatus) {
        this.onboardStatus = onboardStatus;
    }

    public String getServiceStatus() {
        return serviceStatus;
    }

    public void setServiceStatus(String serviceStatus) {
        this.serviceStatus = serviceStatus;
    }

    public String getOnboardId() {
        return onboardId;
    }

    public void setOnboardId(String onboardId) {
        this.onboardId = onboardId;
    }

    public String getOnboardTime() {
        return onboardTime;
    }

    public void setOnboardTime(String onboardTime) {
        this.onboardTime = onboardTime;
    }

    public String getMsgCode() {
        return msgCode;
    }

    public void setMsgCode(String msgCode) {
        this.msgCode = msgCode;
    }

    public String getMsgInfo() {
        return msgInfo;
    }

    public void setMsgInfo(String msgInfo) {
        this.msgInfo = msgInfo;
    }

    public String getCreateId() {
        return createId;
    }

    public void setCreateId(String createId) {
        this.createId = createId;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateId() {
        return updateId;
    }

    public void setUpdateId(String updateId) {
        this.updateId = updateId;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }
}