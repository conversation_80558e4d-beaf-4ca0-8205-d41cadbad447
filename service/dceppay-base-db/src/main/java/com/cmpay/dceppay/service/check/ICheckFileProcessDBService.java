package com.cmpay.dceppay.service.check;

import com.cmpay.dceppay.bo.check.CheckFileProcessBO;

/**
 * <AUTHOR>
 * @date 2024/9/23 10:13
 */
public interface ICheckFileProcessDBService {
    /**
     * 获取状态
     * @param checkDate
     * @param institutionCode
     * @return
     */
    CheckFileProcessBO getProcessBO(String checkDate, String institutionCode);
    /**
     * 初始化对账文件状态
     * @param checkFileDate
     * @param institutionCode
     */
    void initCheckFileProcess(String checkFileDate, String institutionCode);

    /**
     * 对账文件下载开始
     * @param checkFileDate
     * @param institutionCode
     */
    void updateCheckFileDownloadBegin(String checkFileDate, String institutionCode);

    /**
     * 对账文件下载结束
     * @param checkFileDate
     * @param institutionCode
     */
    void updateCheckFileDownloadEnd(String checkFileDate, String institutionCode);

    /**
     * 入库开始
     * @param checkFileDate
     * @param institutionCode
     */

    void updateImportBegin(String checkFileDate, String institutionCode);
    /**
     * 入库结束
     * @param checkFileDate
     * @param institutionCode
     */
    void updateImportEnd(String checkFileDate, String institutionCode);
    /**
     * 对账凯斯
     * @param checkFileDate
     * @param institutionCode
     */

    void updateCheckBegin(String checkFileDate, String institutionCode);

    /**
     * 对账结束
     * @param checkFileDate
     * @param institutionCode
     */

    void updateCheckEnd(String checkFileDate, String institutionCode);

    /**
     * 待核实
     * @param checkFileDate
     * @param institutionCode
     */

    void updateWaitConfirmInfo(String checkFileDate, String institutionCode);


}
