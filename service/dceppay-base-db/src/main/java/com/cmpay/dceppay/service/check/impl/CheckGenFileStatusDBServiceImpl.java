package com.cmpay.dceppay.service.check.impl;

import com.cmpay.dceppay.bo.check.CheckGenFileStatusBO;
import com.cmpay.dceppay.dao.check.ICheckGenFileStatusExtDao;
import com.cmpay.dceppay.entity.check.CheckGenFileStatusDO;
import com.cmpay.dceppay.entity.check.CheckGenFileStatusDOKey;
import com.cmpay.dceppay.enums.check.CheckFileStatusEnum;
import com.cmpay.dceppay.service.check.ICheckGenFileStatusDBService;
import com.cmpay.dceppay.util.DateTimeUtil;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/9/18 9:12
 */
@Service
public class CheckGenFileStatusDBServiceImpl implements ICheckGenFileStatusDBService {
    @Autowired
    private ICheckGenFileStatusExtDao checkGenFileStatusExtDao;


    @Override
    public int addCheckGenFileStatus(CheckGenFileStatusBO checkGenFileStatusBO) {
        CheckGenFileStatusDO genFileStatusDO = new CheckGenFileStatusDO();
        genFileStatusDO.setCheckFileDate(checkGenFileStatusBO.getCheckFileDate());
        genFileStatusDO.setChannelCode(checkGenFileStatusBO.getChannelCode());
        genFileStatusDO.setFileStatus(CheckFileStatusEnum.FILE_PROCESS.name());
        genFileStatusDO.setFileStartTime(DateTimeUtil.getCurrentDateTimeStr());
        genFileStatusDO.setTmSmp(DateTimeUtil.getCurrentDateTimeStr());
        return checkGenFileStatusExtDao.insert(genFileStatusDO);
    }

    @Override
    public CheckGenFileStatusBO getByKey(String checkDate, String channelCode) {
        CheckGenFileStatusDOKey genFileStatusDOKey = new CheckGenFileStatusDOKey();
        genFileStatusDOKey.setCheckFileDate(checkDate);
        genFileStatusDOKey.setChannelCode(channelCode);
        CheckGenFileStatusDO checkGenFileStatusDO = checkGenFileStatusExtDao.get(genFileStatusDOKey);
        if (JudgeUtils.isNull(checkGenFileStatusDO)) {
            return null;
        }
        return BeanUtils.copyPropertiesReturnDest(new CheckGenFileStatusBO(), checkGenFileStatusDO);
    }

    @Override
    public void updateFileGenFinish(CheckGenFileStatusBO checkGenFileStatusBO) {
        CheckGenFileStatusDO genFileStatusDO = new CheckGenFileStatusDO();
        BeanUtils.copyProperties(genFileStatusDO,checkGenFileStatusBO);
        genFileStatusDO.setFileStatus(CheckFileStatusEnum.FILE_SUCCESS.name());
        genFileStatusDO.setFileEndTime(DateTimeUtil.getCurrentDateTimeStr());
        genFileStatusDO.setTmSmp(DateTimeUtil.getCurrentDateTimeStr());
        checkGenFileStatusExtDao.updateFileGenFinish(genFileStatusDO);
    }

    @Override
    public void updateFileSendFinish(String checkDate, String channelCode) {
        CheckGenFileStatusDO genFileStatusDO = new CheckGenFileStatusDO();
        genFileStatusDO.setCheckFileDate(checkDate);
        genFileStatusDO.setChannelCode(channelCode);
        genFileStatusDO.setFileStatus(CheckFileStatusEnum.FILE_SEND_SUCCESS.name());
        genFileStatusDO.setFileSendTime(DateTimeUtil.getCurrentDateTimeStr());
        genFileStatusDO.setTmSmp(DateTimeUtil.getCurrentDateTimeStr());
        checkGenFileStatusExtDao.updateFileSendFinish(genFileStatusDO);
    }

    @Override
    public void updateFileSendFail(String checkDate, String channelCode) {
        CheckGenFileStatusDO genFileStatusDO = new CheckGenFileStatusDO();
        genFileStatusDO.setCheckFileDate(checkDate);
        genFileStatusDO.setChannelCode(channelCode);
        genFileStatusDO.setFileStatus(CheckFileStatusEnum.FILE_SEND_FAIL.name());
        genFileStatusDO.setFileSendTime(DateTimeUtil.getCurrentDateTimeStr());
        genFileStatusDO.setTmSmp(DateTimeUtil.getCurrentDateTimeStr());
        checkGenFileStatusExtDao.updateFileSendFail(genFileStatusDO);
    }




}
