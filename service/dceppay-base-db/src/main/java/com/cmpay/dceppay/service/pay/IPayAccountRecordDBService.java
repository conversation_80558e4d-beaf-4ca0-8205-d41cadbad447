package com.cmpay.dceppay.service.pay;

import com.cmpay.dceppay.bo.pay.AccountQueryBO;
import com.cmpay.dceppay.bo.pay.PayAccountBO;
import com.cmpay.dceppay.bo.pay.PayAccountRecordBO;
import com.cmpay.dceppay.bo.pay.PayOrderDBBO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/3 17:03
 */
public interface IPayAccountRecordDBService {
    int saveWaitPayAccountRecord(PayAccountBO payAccountBO);

    List<PayAccountRecordBO> queryWaitHandleAccount(AccountQueryBO accountQueryBO);

    void updateAccountSuccess(PayAccountRecordBO accountRecordBO);

    PayAccountRecordBO getByJrnNo(String  jrnNo);

    int updateWaitDeal(String jrnNo);

    void updateAccountCancelReserve(PayAccountRecordBO accountRecordBO);

    void updateNoRecordAccount(PayAccountRecordBO accountRecordBO);

    PayAccountRecordBO getByUk(PayAccountRecordBO accountRecordBO);

}
