package com.cmpay.dceppay.service.pay.impl;

import com.cmpay.dceppay.bo.notify.NotifyKeyBO;
import com.cmpay.dceppay.bo.notify.NotifySyncQueryBO;
import com.cmpay.dceppay.bo.pay.PayNotifyBO;
import com.cmpay.dceppay.bo.pay.RefundOrderDBBO;
import com.cmpay.dceppay.constant.pay.NotifyConstants;
import com.cmpay.dceppay.dao.pay.IPayNotifyExtDao;
import com.cmpay.dceppay.entity.pay.*;
import com.cmpay.dceppay.service.pay.IPayNotifyDBService;
import com.cmpay.dceppay.util.BeanConvertUtils;
import com.cmpay.dceppay.util.DateTimeUtil;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/9/3 17:08
 */
@Service
public class PayNotifyDBServiceImpl implements IPayNotifyDBService {
    @Autowired
    private IPayNotifyExtDao payNotifyExtDao;

    @Override
    public int addNotifyRecord(PayNotifyBO payNotifyBO) {
        PayNotifyDO payNotifyDO = new PayNotifyDO();
        BeanUtils.copyProperties(payNotifyDO, payNotifyBO);
        payNotifyDO.setNotifyDate(DateTimeUtil.getCurrentDateStr());
        payNotifyDO.setNotifyTime(DateTimeUtil.getCurrentTimeStr());
        payNotifyDO.setTmSmp(DateTimeUtil.getCurrentDateTimeStr());
        return payNotifyExtDao.insert(payNotifyDO);
    }

    @Override
    public List<PayNotifyBO> queryWaitNotifyRecord(NotifySyncQueryBO notifySyncQueryBO) {
        PayNotifyQueryDO notifyQueryDO = new PayNotifyQueryDO();
        BeanUtils.copyProperties(notifyQueryDO, notifySyncQueryBO);
        List<PayNotifyDO> notifyDOList = payNotifyExtDao.queryWaitNotifyRecord(notifyQueryDO);
        if (JudgeUtils.isNull(notifyDOList)) {
            return new ArrayList<>();
        }
        notifyDOList = notifyDOList.stream()
                .filter(order -> calculateShardId(order.getRefundOrderNo(), notifySyncQueryBO.getShardTotal()) == notifySyncQueryBO.getShardIndex())
                .collect(Collectors.toList());
        return BeanConvertUtils.convertList(notifyDOList, PayNotifyBO.class);
    }

    @Override
    public PayNotifyBO getByKey(NotifyKeyBO notifyKeyBO) {
        PayNotifyDOKey payNotifyDOKey = new PayNotifyDOKey();
        BeanUtils.copyProperties(payNotifyDOKey, notifyKeyBO);
        PayNotifyDO payNotifyDO = payNotifyExtDao.get(payNotifyDOKey);
        if (JudgeUtils.isNotNull(payNotifyDO)) {
            return BeanUtils.copyPropertiesReturnDest(new PayNotifyBO(), payNotifyDO);
        }
        return null;
    }

    @Override
    public int updateNotifyCount(PayNotifyBO payNotifyBO) {
        PayNotifyDO payNotifyDO = new PayNotifyDO();
        BeanUtils.copyProperties(payNotifyDO, payNotifyBO);
        payNotifyDO.setTmSmp(DateTimeUtil.getCurrentDateTimeStr());
        return payNotifyExtDao.updateNotifyCount(payNotifyDO);
    }

    @Override
    public int updateNotifySuccess(PayNotifyBO payNotifyBO) {
        PayNotifyDO payNotifyDO = new PayNotifyDO();
        BeanUtils.copyProperties(payNotifyDO, payNotifyBO);
        payNotifyDO.setNotifyCompleteTime(DateTimeUtil.getCurrentDateTimeStr());
        payNotifyDO.setNotifyStatus(NotifyConstants.NOTIFY_STATUS_SUCCESS);
        payNotifyDO.setTmSmp(DateTimeUtil.getCurrentDateTimeStr());
        return payNotifyExtDao.updateNotifySuccess(payNotifyDO);
    }


    private int calculateShardId(String orderNo, int shardTotal) {
        return Math.abs(orderNo.hashCode()) % shardTotal;
    }
}
