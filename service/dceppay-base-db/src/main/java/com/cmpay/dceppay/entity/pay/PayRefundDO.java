/*
 * @ClassName PayRefundDO
 * @Description 
 * @version 1.0
 * @Date 2024-11-12 18:18:31
 */
package com.cmpay.dceppay.entity.pay;

import com.cmpay.framework.data.BaseDO;
import com.cmpay.lemon.framework.annotation.DataObject;
import java.math.BigDecimal;

@DataObject
public class PayRefundDO extends BaseDO {
    /**
     * @Fields refundOrderNo 退款请求号
     */
    private String refundOrderNo;
    /**
     * @Fields outOrderNo 原交易订单号
     */
    private String outOrderNo;
    /**
     * @Fields refundDate 退款提交日期
     */
    private String refundDate;
    /**
     * @Fields refundTime 退款提交时间
     */
    private String refundTime;
    /**
     * @Fields channelCode 支付渠道号
     */
    private String channelCode;
    /**
     * @Fields payWay 支付方式
     */
    private String payWay;
    /**
     * @Fields scene 支付场景
     */
    private String scene;
    /**
     * @Fields busType 业务类型
     */
    private String busType;
    /**
     * @Fields merchantNo 商户编号
     */
    private String merchantNo;
    /**
     * @Fields orgMerchantNo 运营机构商户号
     */
    private String orgMerchantNo;
    /**
     * @Fields walletId 商户钱包id
     */
    private String walletId;
    /**
     * @Fields accountDate 会计日期
     */
    private String accountDate;
    /**
     * @Fields orderAmount 原订单金额
     */
    private BigDecimal orderAmount;
    /**
     * @Fields refundAmount 退款金额
     */
    private BigDecimal refundAmount;
    /**
     * @Fields notifyUrl 通知url
     */
    private String notifyUrl;
    /**
     * @Fields cancelFlag 撤单标识
     */
    private String cancelFlag;
    /**
     * @Fields status 订单状态
     */
    private String status;
    /**
     * @Fields bankOrderNo 支付机构订单号
     */
    private String bankOrderNo;
    /**
     * @Fields errMsgCd 错误码
     */
    private String errMsgCd;
    /**
     * @Fields errMsgInfo 错误码信息
     */
    private String errMsgInfo;
    /**
     * @Fields orderCompleteTime 订单完成时间
     */
    private String orderCompleteTime;
    /**
     * @Fields receiveNotifyTime 接收通知时间
     */
    private String receiveNotifyTime;
    /**
     * @Fields refundReason 退款原因
     */
    private String refundReason;
    /**
     * @Fields bizType 业务类型
     */
    private String bizType;
    /**
     * @Fields bizCategory 业务种类
     */
    private String bizCategory;
    /**
     * @Fields bankRefundNo 支付机退款订单号
     */
    private String bankRefundNo;
    /**
     * @Fields notifyStatus 通知状态
     */
    private String notifyStatus;
    /**
     * @Fields sendNotifyTime 下发通知时间
     */
    private String sendNotifyTime;
    /**
     * @Fields extra 保留字段
     */
    private String extra;
    /**
     * @Fields messageIdentification 报文标识号
     */
    private String messageIdentification;

    public String getRefundOrderNo() {
        return refundOrderNo;
    }

    public void setRefundOrderNo(String refundOrderNo) {
        this.refundOrderNo = refundOrderNo;
    }

    public String getOutOrderNo() {
        return outOrderNo;
    }

    public void setOutOrderNo(String outOrderNo) {
        this.outOrderNo = outOrderNo;
    }

    public String getRefundDate() {
        return refundDate;
    }

    public void setRefundDate(String refundDate) {
        this.refundDate = refundDate;
    }

    public String getRefundTime() {
        return refundTime;
    }

    public void setRefundTime(String refundTime) {
        this.refundTime = refundTime;
    }

    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }

    public String getPayWay() {
        return payWay;
    }

    public void setPayWay(String payWay) {
        this.payWay = payWay;
    }

    public String getScene() {
        return scene;
    }

    public void setScene(String scene) {
        this.scene = scene;
    }

    public String getBusType() {
        return busType;
    }

    public void setBusType(String busType) {
        this.busType = busType;
    }

    public String getMerchantNo() {
        return merchantNo;
    }

    public void setMerchantNo(String merchantNo) {
        this.merchantNo = merchantNo;
    }

    public String getOrgMerchantNo() {
        return orgMerchantNo;
    }

    public void setOrgMerchantNo(String orgMerchantNo) {
        this.orgMerchantNo = orgMerchantNo;
    }

    public String getWalletId() {
        return walletId;
    }

    public void setWalletId(String walletId) {
        this.walletId = walletId;
    }

    public String getAccountDate() {
        return accountDate;
    }

    public void setAccountDate(String accountDate) {
        this.accountDate = accountDate;
    }

    public BigDecimal getOrderAmount() {
        return orderAmount;
    }

    public void setOrderAmount(BigDecimal orderAmount) {
        this.orderAmount = orderAmount;
    }

    public BigDecimal getRefundAmount() {
        return refundAmount;
    }

    public void setRefundAmount(BigDecimal refundAmount) {
        this.refundAmount = refundAmount;
    }

    public String getNotifyUrl() {
        return notifyUrl;
    }

    public void setNotifyUrl(String notifyUrl) {
        this.notifyUrl = notifyUrl;
    }

    public String getCancelFlag() {
        return cancelFlag;
    }

    public void setCancelFlag(String cancelFlag) {
        this.cancelFlag = cancelFlag;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getBankOrderNo() {
        return bankOrderNo;
    }

    public void setBankOrderNo(String bankOrderNo) {
        this.bankOrderNo = bankOrderNo;
    }

    public String getErrMsgCd() {
        return errMsgCd;
    }

    public void setErrMsgCd(String errMsgCd) {
        this.errMsgCd = errMsgCd;
    }

    public String getErrMsgInfo() {
        return errMsgInfo;
    }

    public void setErrMsgInfo(String errMsgInfo) {
        this.errMsgInfo = errMsgInfo;
    }

    public String getOrderCompleteTime() {
        return orderCompleteTime;
    }

    public void setOrderCompleteTime(String orderCompleteTime) {
        this.orderCompleteTime = orderCompleteTime;
    }

    public String getReceiveNotifyTime() {
        return receiveNotifyTime;
    }

    public void setReceiveNotifyTime(String receiveNotifyTime) {
        this.receiveNotifyTime = receiveNotifyTime;
    }

    public String getRefundReason() {
        return refundReason;
    }

    public void setRefundReason(String refundReason) {
        this.refundReason = refundReason;
    }

    public String getBizType() {
        return bizType;
    }

    public void setBizType(String bizType) {
        this.bizType = bizType;
    }

    public String getBizCategory() {
        return bizCategory;
    }

    public void setBizCategory(String bizCategory) {
        this.bizCategory = bizCategory;
    }

    public String getBankRefundNo() {
        return bankRefundNo;
    }

    public void setBankRefundNo(String bankRefundNo) {
        this.bankRefundNo = bankRefundNo;
    }

    public String getNotifyStatus() {
        return notifyStatus;
    }

    public void setNotifyStatus(String notifyStatus) {
        this.notifyStatus = notifyStatus;
    }

    public String getSendNotifyTime() {
        return sendNotifyTime;
    }

    public void setSendNotifyTime(String sendNotifyTime) {
        this.sendNotifyTime = sendNotifyTime;
    }

    public String getExtra() {
        return extra;
    }

    public void setExtra(String extra) {
        this.extra = extra;
    }

    public String getMessageIdentification() {
        return messageIdentification;
    }

    public void setMessageIdentification(String messageIdentification) {
        this.messageIdentification = messageIdentification;
    }
}