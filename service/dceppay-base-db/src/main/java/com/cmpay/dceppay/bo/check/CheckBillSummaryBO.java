package com.cmpay.dceppay.bo.check;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/9/23 10:22
 */
@Data
public class CheckBillSummaryBO  {
    /**
     * @Fields checkFileDate 对账日期
     */
    private String checkFileDate;
    /**
     * @Fields institutionCode 运营机构编码
     */
    private String institutionCode;
    /**
     * @Fields totalCount 总笔数
     */
    private Integer totalCount;
    /**
     * @Fields totalAmount 总金额
     */
    private BigDecimal totalAmount;
    /**
     * @Fields paymentCount 支付总笔数
     */
    private Integer paymentCount;
    /**
     * @Fields paymentAmount 支付总金额
     */
    private BigDecimal paymentAmount;
    /**
     * @Fields refundCount 退款总笔数
     */
    private Integer refundCount;
    /**
     * @Fields refundAmount 退款总金额
     */
    private BigDecimal refundAmount;
    /**
     * @Fields totalSuccessCount 成功总笔数
     */
    private Integer totalSuccessCount;
    /**
     * @Fields totalSuccessAmount 成功总金额
     */
    private BigDecimal totalSuccessAmount;
    /**
     * @Fields paymentSuccessCount 成功支付总笔数
     */
    private Integer paymentSuccessCount;
    /**
     * @Fields paymentSuccessAmount 成功支付总金额
     */
    private BigDecimal paymentSuccessAmount;
    /**
     * @Fields refundSuccessCount 成功退款总笔数
     */
    private Integer refundSuccessCount;
    /**
     * @Fields refundSuccessAmount 成功退款总金额
     */
    private BigDecimal refundSuccessAmount;
    /**
     * @Fields fileCount 文件总数
     */
    private Integer fileCount;
    /**
     * @Fields filePath 文件路径
     */
    private String filePath;
    /**
     * @Fields fileNameList 文件名列表
     */
    private String fileNameList;
    private String digitalEnv;
}
