package com.cmpay.dceppay.util;

import com.cmpay.dceppay.constant.common.DateTimeFormatterConstant;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import lombok.extern.slf4j.Slf4j;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/9/6 15:54
 */
@Slf4j
public class DateTimeUtil extends DateTimeUtils {

    public static String getCurrentISODateTime() {
        DateTimeFormatter formatter = DateTimeFormatterConstant.ISO_DATETIME_FORMATTER;
        LocalDateTime now = LocalDateTime.now();
        String isoDateTime = now.format(formatter);
        return isoDateTime;
    }

    /**
     * yyyyMMddHHmmss 转isodate
     *
     * @param dateTime
     * @return
     */
    public static String getISODateTime(String dateTime) {
        // 输出日期格式
        DateTimeFormatter outputFormat = DateTimeFormatterConstant.ISO_DATETIME_FORMATTER;
        LocalDateTime date = LocalDateTime.parse(dateTime, DEFAULT_DATETIME_FORMATTER);
        // 解析输入的日期字符串
        return date.format(outputFormat);
    }

    public static String getCheckDate() {
        LocalDate localDate = LocalDate.now().minusDays(1);
        return localDate.format(DEFAULT_DATE_FORMATTER);
    }

    /**
     * isodate转 yyyyMMddHHmmss
     *
     * @param dateTime
     * @return
     */

    public static String changeISODateTime(String dateTime) {
        // 输出日期格式
        DateTimeFormatter outputFormat = DateTimeFormatterConstant.ISO_DATETIME_FORMATTER;
        LocalDateTime date = LocalDateTime.parse(dateTime, outputFormat);
        // 解析输入的日期字符串
        return date.format(DEFAULT_DATETIME_FORMATTER);
    }

    /**
     * isodate转 yyyyMMdd
     *
     * @param dateTime
     * @return
     */

    public static String changeISODate(String dateTime) {
        // 输出日期格式
        DateTimeFormatter outputFormat = DateTimeFormatterConstant.ISO_DATETIME_FORMATTER;
        LocalDateTime date = LocalDateTime.parse(dateTime, outputFormat);
        // 解析输入的日期字符串
        return date.format(DEFAULT_DATE_FORMATTER);
    }


}
