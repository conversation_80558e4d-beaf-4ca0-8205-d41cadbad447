package com.cmpay.dceppay.bo.pay;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/13 15:55
 */
@Data
public class PayAccountBO {
    /**
     * @Fields orderDate 交易日期
     */
    private String orderDate;
    /**
     * @Fields orderNo 订单编号
     */
    private String orderNo;
    /**
     * @Fields orderType 订单类型 P1 数字人民币支付, P2 数字人民币退款
     */
    private String orderType;
    /**
     * @Fields busType 业务类型 P101
     */
    private String busType;
    /**
     * @Fields busChannel 业务渠道
     */
    private String busChannel;
    /**
     * @Fields tradeType 交易类型 P1
     */
    private String tradeType;
    /**
     * @Fields busCode 业务交易码 DCEP0001-数币支付, DCEP0002-数币退款, DCEP0003-数币对账, DCEP0004-数币补单, DCEP0005-数币退款（撤单）
     */
    private String busCode;
    /**
     * @Fields accountFrom 账务来源方
     */
    private String accountFrom;
    //业务流水号
    private String busJrnNo;

    //业务流水号
    private BigDecimal orderAmount;;

    private List<PayAccountDetailBO> payAccountDetailBOList;

    private String accountDate;
}
