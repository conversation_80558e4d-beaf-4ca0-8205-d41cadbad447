/*
 * @ClassName PayAccountDetailDO
 * @Description 
 * @version 1.0
 * @Date 2024-09-03 16:50:36
 */
package com.cmpay.dceppay.entity.pay;

import com.cmpay.framework.data.BaseDO;
import com.cmpay.lemon.framework.annotation.DataObject;
import java.math.BigDecimal;

@DataObject
public class PayAccountDetailDO extends BaseDO {
    /**
     * @Fields jrnNo 流水号
     */
    private String jrnNo;
    /**
     * @Fields dcFlag 借贷方向，D 表示借方，C 表示贷方
     */
    private String dcFlag;
    /**
     * @Fields accountNo 账号
     */
    private String accountNo;
    /**
     * @Fields orderDate 交易日期
     */
    private String orderDate;
    /**
     * @Fields accountName 账户名称
     */
    private String accountName;
    /**
     * @Fields amount 金额
     */
    private BigDecimal amount;

    public String getJrnNo() {
        return jrnNo;
    }

    public void setJrnNo(String jrnNo) {
        this.jrnNo = jrnNo;
    }

    public String getDcFlag() {
        return dcFlag;
    }

    public void setDcFlag(String dcFlag) {
        this.dcFlag = dcFlag;
    }

    public String getAccountNo() {
        return accountNo;
    }

    public void setAccountNo(String accountNo) {
        this.accountNo = accountNo;
    }

    public String getOrderDate() {
        return orderDate;
    }

    public void setOrderDate(String orderDate) {
        this.orderDate = orderDate;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }
}