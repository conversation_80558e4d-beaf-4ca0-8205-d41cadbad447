package com.cmpay.dceppay.bo.pay;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/9/27 18:07
 */
@Data
public class PayNotifyBO {
    private String tradeJrnNo;
    /**
     * @Fields outOrderNo 交易订单号
     */
    private String outOrderNo;
    /**
     * @Fields refundOrderNo 退款订单号
     */
    private String refundOrderNo;
    /**
     * @Fields notifyDate 登记通知日期
     */
    private String notifyDate;
    /**
     * @Fields notifyTime 登记通知时间
     */
    private String notifyTime;
    /**
     * @Fields orderDate 订单提交日期
     */
    private String orderDate;
    /**
     * @Fields orderTime 订单提交时间
     */
    private String orderTime;
    /**
     * @Fields channelCode 支付渠道号
     */
    private String channelCode;
    /**
     * @Fields payWay 支付方式
     */
    private String payWay;
    /**
     * @Fields scene 支付场景
     */
    private String scene;
    /**
     * @Fields busType 业务类型
     */
    private String busType;
    /**
     * @Fields accountDate 会计日期
     */
    private String accountDate;
    /**
     * @Fields orderAmount 订单总金额
     */
    private BigDecimal orderAmount;
    /**
     * @Fields notifyUrl 通知url
     */
    private String notifyUrl;
    /**
     * @Fields bankOrderNo 支付机构订单号
     */
    private String bankOrderNo;
    /**
     * @Fields orderCompleteTime 订单完成时间
     */
    private String orderCompleteTime;
    /**
     * @Fields notifyType 通知类型
     */
    private String notifyType;
    /**
     * @Fields notifyStatus 通知状态
     */
    private String notifyStatus;
}
