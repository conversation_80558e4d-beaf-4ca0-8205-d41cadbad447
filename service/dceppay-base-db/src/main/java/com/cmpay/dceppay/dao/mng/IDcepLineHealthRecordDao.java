/*
 * @ClassName IDcepLineHealthRecordDao
 * @Description 专线探活记录DAO接口
 * @version 1.0
 * @Date 2024-12-23 10:00:00
 */
package com.cmpay.dceppay.dao.mng;

import com.cmpay.dceppay.entity.mng.DcepLineHealthRecordDO;
import com.cmpay.lemon.framework.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@Mapper
public interface IDcepLineHealthRecordDao extends BaseDao<DcepLineHealthRecordDO, String> {
    
    /**
     * 查询指定专线最近N次的探活记录
     * @param lineId 专线ID
     * @param limit 记录数量
     * @return 探活记录列表
     */
    List<DcepLineHealthRecordDO> selectRecentRecords(@Param("lineId") String lineId, @Param("limit") int limit);
    
    /**
     * 计算指定专线最近N次成功探活的平均响应时间
     * @param lineId 专线ID
     * @param limit 记录数量
     * @return 平均响应时间
     */
    Long calculateAvgResponseTime(@Param("lineId") String lineId, @Param("limit") int limit);
    
    /**
     * 删除指定时间之前的探活记录
     * @param beforeTime 时间点
     * @return 删除行数
     */
    int deleteRecordsBefore(@Param("beforeTime") Date beforeTime);
}
