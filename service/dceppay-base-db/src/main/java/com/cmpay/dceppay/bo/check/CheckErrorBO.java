package com.cmpay.dceppay.bo.check;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/9/24 19:04
 */
@Data
public class CheckErrorBO {
    /**
     * @Fields outOrderNo 交易订单号
     */
    private String outOrderNo;
    /**
     * @Fields refundOrderNo 退款订单号
     */
    private String refundOrderNo;
    /**
     * @Fields checkFileDate 对账日期
     */
    private String checkFileDate;
    /**
     * @Fields errorType 差错类型
     */
    private String errorType;
    /**
     * @Fields errorTime 差错时间
     */
    private String errorTime;
    /**
     * @Fields errorHandleType 差错处理方式
     */
    private String errorHandleType;
    /**
     * @Fields tradeAmount 交易金额
     */
    private BigDecimal tradeAmount;
    /**
     * @Fields checkAmount 对账金额
     */
    private BigDecimal checkAmount;
    /**
     * @Fields errorStatus 差错状态
     */
    private String errorStatus;
    /**
     * @Fields tradeType 交易类型
     */
    private String tradeType;
    /**
     * @Fields merchantNo 商户编号
     */
    private String merchantNo;
    /**
     * @Fields orgMerchantNo 运营机构商户号
     */
    private String orgMerchantNo;
    /**
     * @Fields doubtFlag 存疑表示
     */
    private String doubtFlag;
    private String errorProcessTime;
    //操作人id
    private String updateId;
}
