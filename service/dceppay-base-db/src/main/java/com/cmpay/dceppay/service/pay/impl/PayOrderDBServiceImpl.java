package com.cmpay.dceppay.service.pay.impl;

import com.cmpay.dceppay.bo.pay.PayOrderCloseUpdateBO;
import com.cmpay.dceppay.bo.pay.PayOrderDBBO;
import com.cmpay.dceppay.bo.pay.PayOrderQueryBO;
import com.cmpay.dceppay.constant.pay.NotifyConstants;
import com.cmpay.dceppay.dao.pay.IPayOrderExtDao;
import com.cmpay.dceppay.entity.pay.*;
import com.cmpay.dceppay.enums.pay.OrderStatusEnum;
import com.cmpay.dceppay.service.pay.IPayOrderDBService;
import com.cmpay.dceppay.util.BeanConvertUtils;
import com.cmpay.dceppay.util.DateTimeUtil;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/9/3 17:01
 */
@Slf4j
@Service
public class PayOrderDBServiceImpl implements IPayOrderDBService {
    @Autowired
    private IPayOrderExtDao payOrderExtDao;

    @Override
    public PayOrderDBBO getByOutOrderNo(String orderNo) {
        PayOrderDO payOrderDO = payOrderExtDao.getByOutOrderNo(orderNo);
        PayOrderDBBO orderBO = new PayOrderDBBO();
        if (JudgeUtils.isNotNull(payOrderDO)) {
            BeanUtils.copyProperties(orderBO, payOrderDO);
            return orderBO;
        }
        return null;
    }

    @Override
    public int insertOrder(PayOrderDBBO order) {
        PayOrderDO payOrderDO = new PayOrderDO();
        BeanUtils.copyProperties(payOrderDO, order);
        payOrderDO.setTmSmp(DateTimeUtils.getCurrentDateTimeStr());
        return payOrderExtDao.insert(payOrderDO);
    }

    @Override
    //只有等待付款状态的订单允许更新成订单关闭，更新订单关闭状态时订单状态必须为等待支付
    public int updateTradeClose(PayOrderCloseUpdateBO closeUpdateBO) {
        PayOrderDO payOrderDO = new PayOrderDO();
        payOrderDO.setOutOrderNo(closeUpdateBO.getOutOrderNo());
        payOrderDO.setStatus(OrderStatusEnum.TRADE_CLOSED.name());
        payOrderDO.setTmSmp(DateTimeUtils.getCurrentDateTimeStr());
        return payOrderExtDao.updateTradeClose(payOrderDO);
    }

    @Override
    public List<PayOrderDBBO> findWaitPayList(PayOrderQueryBO payOrderQueryBO) {
        PayOrderExtDO payOrderExtDO = new PayOrderExtDO();
        BeanUtils.copyProperties(payOrderExtDO, payOrderQueryBO);
        List<PayOrderDO> payOrderDOList = payOrderExtDao.findWaitPayList(payOrderExtDO);
        if (JudgeUtils.isNull(payOrderDOList)) {
            return new ArrayList<>();
        }
        payOrderDOList = payOrderDOList.stream()
                .filter(order -> calculateShardId(order.getOutOrderNo(), payOrderQueryBO.getShardTotal()) == payOrderQueryBO.getShardIndex())
                .collect(Collectors.toList());
        return BeanConvertUtils.convertList(payOrderDOList, PayOrderDBBO.class);
    }

    @Override
    public int updateTradeSuccess(PayOrderDBBO payOrderDBBO) {
        PayOrderExtDO payOrderExtDO = new PayOrderExtDO();
        BeanUtils.copyProperties(payOrderExtDO, payOrderDBBO);
        payOrderExtDO.setStatus(OrderStatusEnum.TRADE_SUCCESS.name());
        payOrderExtDO.setTmSmp(DateTimeUtils.getCurrentDateTimeStr());
        return payOrderExtDao.updateTradeSuccess(payOrderExtDO);
    }

    @Override
    public int updateTradeFail(PayOrderDBBO payOrderDBBO) {
        PayOrderExtDO payOrderExtDO = new PayOrderExtDO();
        BeanUtils.copyProperties(payOrderExtDO, payOrderDBBO);
        payOrderExtDO.setStatus(OrderStatusEnum.TRADE_FAIL.name());
        payOrderExtDO.setTmSmp(DateTimeUtils.getCurrentDateTimeStr());
        return payOrderExtDao.updateTradeFail(payOrderExtDO);
    }

    @Override
    public int updateRefundInfo(PayOrderDBBO updateOrderDBBO, BigDecimal refundAmount) {
        PayOrderRefundInfoUpdateDO refundInfoUpdateDO=new PayOrderRefundInfoUpdateDO();
        refundInfoUpdateDO.setRefundAmount(refundAmount);
        refundInfoUpdateDO.setOutOrderNo(updateOrderDBBO.getOutOrderNo());
        refundInfoUpdateDO.setStatus(updateOrderDBBO.getStatus());
        refundInfoUpdateDO.setTmSmp(DateTimeUtil.getCurrentDateTimeStr());
        return payOrderExtDao.updateRefundInfo(refundInfoUpdateDO);
    }

    @Override
    public int updateNotifyInfo(String outOrderNo) {
        PayOrderNotifyInfoUpdateDO notifyInfoUpdateDO=new PayOrderNotifyInfoUpdateDO();
        notifyInfoUpdateDO.setOutOrderNo(outOrderNo);
        notifyInfoUpdateDO.setSendNotifyTime(DateTimeUtil.getCurrentDateTimeStr());
        notifyInfoUpdateDO.setTmSmp(DateTimeUtil.getCurrentDateTimeStr());
        notifyInfoUpdateDO.setNotifyStatus(NotifyConstants.NOTIFY_STATUS_SUCCESS);
        return payOrderExtDao.updateNotifyInfo(notifyInfoUpdateDO);
    }


    private int calculateShardId(String orderNo, int shardTotal) {
        int shardId=Math.abs(orderNo.hashCode()) % shardTotal;
        return shardId ;
    }

}
