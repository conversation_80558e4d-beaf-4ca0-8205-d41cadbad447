/*
 * @ClassName CheckFileVerifyDO
 * @Description 
 * @version 1.0
 * @Date 2024-09-23 19:54:23
 */
package com.cmpay.dceppay.entity.check;

import com.cmpay.framework.data.BaseDO;
import com.cmpay.lemon.framework.annotation.DataObject;

@DataObject
public class CheckFileVerifyDO extends BaseDO {
    /**
     * @Fields checkFileDate 对账日期
     */
    private String checkFileDate;
    /**
     * @Fields institutionCode 机构编码
     */
    private String institutionCode;
    /**
     * @Fields handleFlag 处理标志 不做任务处理: DO_NOTHING, 忽略差异:IGNORE, 重新获取对账文件:RE_GET
     */
    private String handleFlag;
    /**
     * @Fields downloadEndTime 文件下载结束时间
     */
    private String downloadEndTime;
    /**
     * @Fields verifyMessage 差异信息
     */
    private String verifyMessage;
    /**
     * @Fields operateId 操作者id
     */
    private String operateId;
    /**
     * @Fields remark 备注
     */
    private String remark;
    /**
     * @Fields tmSmp 最后更新时间
     */
    private String tmSmp;

    public String getCheckFileDate() {
        return checkFileDate;
    }

    public void setCheckFileDate(String checkFileDate) {
        this.checkFileDate = checkFileDate;
    }

    public String getInstitutionCode() {
        return institutionCode;
    }

    public void setInstitutionCode(String institutionCode) {
        this.institutionCode = institutionCode;
    }

    public String getHandleFlag() {
        return handleFlag;
    }

    public void setHandleFlag(String handleFlag) {
        this.handleFlag = handleFlag;
    }

    public String getDownloadEndTime() {
        return downloadEndTime;
    }

    public void setDownloadEndTime(String downloadEndTime) {
        this.downloadEndTime = downloadEndTime;
    }

    public String getVerifyMessage() {
        return verifyMessage;
    }

    public void setVerifyMessage(String verifyMessage) {
        this.verifyMessage = verifyMessage;
    }

    public String getOperateId() {
        return operateId;
    }

    public void setOperateId(String operateId) {
        this.operateId = operateId;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(String tmSmp) {
        this.tmSmp = tmSmp;
    }
}