package com.cmpay.dceppay.service.pay.impl;

import com.cmpay.dceppay.bo.pay.PayAccountBO;
import com.cmpay.dceppay.bo.pay.PayAccountRecordBO;
import com.cmpay.dceppay.bo.pay.PayOrderDBBO;
import com.cmpay.dceppay.bo.pay.RefundOrderDBBO;
import com.cmpay.dceppay.service.pay.IPayAccountDetailDBService;
import com.cmpay.dceppay.service.pay.IPayAccountRecordDBService;
import com.cmpay.dceppay.service.pay.IPayAccountService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 2024/9/13 15:39
 * 账务表
 */
@Service
public class PayAccountServiceImpl implements IPayAccountService {
    @Autowired
    private IPayAccountRecordDBService accountRecordDBService;
    @Autowired
    private IPayAccountDetailDBService accountDetailDBService;



    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW,rollbackFor = Exception.class)
    public PayAccountRecordBO registerWaitAccount(PayAccountBO payAccountBO) {
        accountRecordDBService.saveWaitPayAccountRecord(payAccountBO);
        accountDetailDBService.savePayAccountDetail(payAccountBO.getPayAccountDetailBOList(),payAccountBO);
        PayAccountRecordBO payAccountRecordBO=new PayAccountRecordBO();
        payAccountRecordBO.setJrnNo(payAccountBO.getBusJrnNo());
        return payAccountRecordBO;
    }

    @Override
    public PayAccountRecordBO getByUk(PayAccountBO accountBO) {
        PayAccountRecordBO query = new PayAccountRecordBO();
        query.setOrderNo(accountBO.getOrderNo());
        query.setOrderType(accountBO.getOrderType());
        query.setBusCode(accountBO.getBusCode());
       return  accountRecordDBService.getByUk(query);
    }


}
