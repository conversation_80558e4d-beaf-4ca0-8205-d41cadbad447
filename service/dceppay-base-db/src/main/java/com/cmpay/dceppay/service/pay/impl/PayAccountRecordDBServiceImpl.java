package com.cmpay.dceppay.service.pay.impl;

import com.cmpay.dceppay.bo.pay.AccountQueryBO;
import com.cmpay.dceppay.bo.pay.PayAccountBO;
import com.cmpay.dceppay.bo.pay.PayAccountRecordBO;
import com.cmpay.dceppay.dao.pay.IPayAccountRecordExtDao;
import com.cmpay.dceppay.entity.pay.AccountQueryDO;
import com.cmpay.dceppay.entity.pay.PayAccountRecordDO;
import com.cmpay.dceppay.enums.pay.AccountProcessStatusEnum;
import com.cmpay.dceppay.service.pay.IPayAccountRecordDBService;
import com.cmpay.dceppay.util.BeanConvertUtils;
import com.cmpay.dceppay.util.DateTimeUtil;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/9/3 17:09
 */
@Service
public class PayAccountRecordDBServiceImpl implements IPayAccountRecordDBService {
    @Autowired
    private IPayAccountRecordExtDao accountRecordExtDao;


    @Override
    public int saveWaitPayAccountRecord(PayAccountBO accountBO) {
        return savePayAccountRecord(accountBO, AccountProcessStatusEnum.WAIT.name());
    }

    @Override
    public List<PayAccountRecordBO> queryWaitHandleAccount(AccountQueryBO accountQueryBO) {
        AccountQueryDO accountQueryDO = new AccountQueryDO();
        BeanUtils.copyProperties(accountQueryDO, accountQueryBO);
        List<PayAccountRecordDO> accountRecordDOList = accountRecordExtDao.queryWaitHandleAccount(accountQueryDO);
        if (JudgeUtils.isNull(accountRecordDOList)) {
            return new ArrayList<>();
        }
        accountRecordDOList = accountRecordDOList.stream()
                .filter(account -> calculateShardId(account.getJrnNo(), accountQueryBO.getShardTotal()) == accountQueryBO.getShardIndex())
                .collect(Collectors.toList());
        return BeanConvertUtils.convertList(accountRecordDOList, PayAccountRecordBO.class);
    }

    @Override
    public void updateAccountSuccess(PayAccountRecordBO accountRecordBO) {
        PayAccountRecordDO payAccountRecordDO = new PayAccountRecordDO();
        BeanUtils.copyProperties(payAccountRecordDO, accountRecordBO);
        payAccountRecordDO.setHandleStatus(AccountProcessStatusEnum.SUCCESS.name());
        payAccountRecordDO.setFinishTime(DateTimeUtil.getCurrentDateTimeStr());
        payAccountRecordDO.setTmSmp(DateTimeUtil.getCurrentDateTimeStr());
        accountRecordExtDao.updateAccountSuccess(payAccountRecordDO);
    }

    @Override
    public PayAccountRecordBO getByJrnNo(String jrnNo) {
        PayAccountRecordDO payAccountRecordDO = accountRecordExtDao.get(jrnNo);
        return JudgeUtils.isNull(payAccountRecordDO) ? null : BeanUtils.copyPropertiesReturnDest(new PayAccountRecordBO(), payAccountRecordDO);
    }

    @Override
    public int updateWaitDeal(String jrnNo) {
        PayAccountRecordDO payAccountRecordDO = new PayAccountRecordDO();
        payAccountRecordDO.setJrnNo(jrnNo);
        payAccountRecordDO.setHandleStatus(AccountProcessStatusEnum.WAIT.name());
        payAccountRecordDO.setTmSmp(DateTimeUtil.getCurrentDateTimeStr());
        return accountRecordExtDao.updateWaitDeal(payAccountRecordDO);
    }

    @Override
    public void updateAccountCancelReserve(PayAccountRecordBO accountRecordBO) {
        PayAccountRecordDO payAccountRecordDO = new PayAccountRecordDO();
        BeanUtils.copyProperties(payAccountRecordDO, accountRecordBO);
        payAccountRecordDO.setHandleStatus(AccountProcessStatusEnum.REVERSE.name());
        payAccountRecordDO.setFinishTime(DateTimeUtil.getCurrentDateTimeStr());
        payAccountRecordDO.setTmSmp(DateTimeUtil.getCurrentDateTimeStr());
        accountRecordExtDao.updateAccountCancelReserve(payAccountRecordDO);
    }

    @Override
    public void updateNoRecordAccount(PayAccountRecordBO accountRecordBO) {
        PayAccountRecordDO payAccountRecordDO = new PayAccountRecordDO();
        BeanUtils.copyProperties(payAccountRecordDO, accountRecordBO);
        payAccountRecordDO.setHandleStatus(AccountProcessStatusEnum.NORECORD.name());
        payAccountRecordDO.setFinishTime(DateTimeUtil.getCurrentDateTimeStr());
        payAccountRecordDO.setTmSmp(DateTimeUtil.getCurrentDateTimeStr());
        accountRecordExtDao.updateNoRecordAccount(payAccountRecordDO);
    }

    @Override
    public PayAccountRecordBO getByUk(PayAccountRecordBO accountRecordBO) {
        PayAccountRecordDO payAccountRecordUk = new PayAccountRecordDO();
        payAccountRecordUk.setOrderType(accountRecordBO.getOrderType());
        payAccountRecordUk.setBusCode(accountRecordBO.getBusCode());
        payAccountRecordUk.setOrderNo(accountRecordBO.getOrderNo());
        PayAccountRecordDO payAccountRecordDO = accountRecordExtDao.getByUk(payAccountRecordUk);
        if (JudgeUtils.isNull(payAccountRecordDO)) {
            return null;
        }
        return BeanUtils.copyPropertiesReturnDest(new PayAccountRecordBO(), payAccountRecordDO);
    }


    private int calculateShardId(String orderNo, int shardTotal) {
        return Math.abs(orderNo.hashCode()) % shardTotal;
    }

    private int savePayAccountRecord(PayAccountBO accountBO, String handleStatus) {
        PayAccountRecordDO payAccountRecordDO = createPayAccountRecordDO(accountBO, handleStatus);
        return accountRecordExtDao.insert(payAccountRecordDO);
    }

    private PayAccountRecordDO createPayAccountRecordDO(PayAccountBO accountBO, String handleStatus) {
        PayAccountRecordDO payAccountRecordDO = new PayAccountRecordDO();
        payAccountRecordDO.setAccountDate(accountBO.getAccountDate());
        payAccountRecordDO.setOrderDate(accountBO.getOrderDate());
        payAccountRecordDO.setJrnNo(accountBO.getBusJrnNo());
        payAccountRecordDO.setOrderNo(accountBO.getOrderNo());
        payAccountRecordDO.setAccountFrom(accountBO.getAccountFrom());
        payAccountRecordDO.setBusChannel(accountBO.getBusChannel());
        payAccountRecordDO.setBusCode(accountBO.getBusCode());
        payAccountRecordDO.setBusType(accountBO.getBusType());
        payAccountRecordDO.setHandleStatus(handleStatus);
        payAccountRecordDO.setOrderType(accountBO.getOrderType());
        payAccountRecordDO.setTradeType(accountBO.getTradeType());
        payAccountRecordDO.setAmount(accountBO.getOrderAmount());
        String currentTime = DateTimeUtil.getCurrentDateTimeStr();
        payAccountRecordDO.setCreateTime(currentTime);
        payAccountRecordDO.setTmSmp(currentTime);
        if (JudgeUtils.equals(handleStatus, AccountProcessStatusEnum.SUCCESS.name())) {
            payAccountRecordDO.setFinishTime(currentTime);
        }
        return payAccountRecordDO;
    }

}
