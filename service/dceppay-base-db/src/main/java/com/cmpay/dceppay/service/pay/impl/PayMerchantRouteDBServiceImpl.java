package com.cmpay.dceppay.service.pay.impl;

import com.cmpay.dceppay.bo.pay.MerchantRouteBO;
import com.cmpay.dceppay.dao.pay.IPayMerchantRouteExtDao;
import com.cmpay.dceppay.entity.pay.PayMerchantRouteDO;
import com.cmpay.dceppay.service.pay.IPayMerchantRouteDBService;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/9/3 17:48
 */
@Service
public class PayMerchantRouteDBServiceImpl implements IPayMerchantRouteDBService {
    @Autowired
    private IPayMerchantRouteExtDao payMerchantRouteExtDao;

    @Override
    public MerchantRouteBO getByBusCode(String busCode) {
        PayMerchantRouteDO merchantRouteDO = payMerchantRouteExtDao.getByBusCode(busCode);
        MerchantRouteBO merchantRouteBO = new MerchantRouteBO();
        if (JudgeUtils.isNotNull(merchantRouteDO)) {
            BeanUtils.copyProperties(merchantRouteBO, merchantRouteDO);
            return merchantRouteBO;
        }
        return null;
    }
}
