package com.cmpay.dceppay.service.check.impl;

import com.cmpay.dceppay.bo.check.CheckFileProcessBO;
import com.cmpay.dceppay.dao.check.ICheckFileProcessExtDao;
import com.cmpay.dceppay.entity.check.CheckFileProcessDO;
import com.cmpay.dceppay.entity.check.CheckFileProcessDOKey;
import com.cmpay.dceppay.enums.check.CheckFileProcessEnums;
import com.cmpay.dceppay.service.check.ICheckFileProcessDBService;
import com.cmpay.dceppay.util.DateTimeUtil;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/9/23 10:13
 */
@Service
public class ICheckFileProcessDBServiceImpl implements ICheckFileProcessDBService {
    @Autowired
    private ICheckFileProcessExtDao checkFileProcessExtDao;

    @Override
    public CheckFileProcessBO getProcessBO(String checkDate, String institutionCode) {
        CheckFileProcessDOKey checkFileProcessDOKey = getCheckFileProcessDOKey(checkDate, institutionCode);
        CheckFileProcessDO checkFileProcessDO = checkFileProcessExtDao.get(checkFileProcessDOKey);
        if (JudgeUtils.isNull(checkFileProcessDO)) {
            return null;
        } else {
            return BeanUtils.copyPropertiesReturnDest(new CheckFileProcessBO(), checkFileProcessDO);
        }
    }

    @Override
    public void initCheckFileProcess(String checkFileDate, String institutionCode) {
        CheckFileProcessDOKey checkFileProcessDOKey = getCheckFileProcessDOKey(checkFileDate, institutionCode);
        if (JudgeUtils.isNull(checkFileProcessExtDao.get(checkFileProcessDOKey))) {
            initFileWaitDownload(checkFileProcessDOKey);
        } else {
            updateFileWaitDownload(checkFileProcessDOKey);
        }
    }

    private static CheckFileProcessDOKey getCheckFileProcessDOKey(String checkFileDate, String institutionCode) {
        CheckFileProcessDOKey checkFileProcessDOKey = new CheckFileProcessDOKey();
        checkFileProcessDOKey.setCheckFileDate(checkFileDate);
        checkFileProcessDOKey.setInstitutionCode(institutionCode);
        return checkFileProcessDOKey;
    }

    @Override
    public void updateCheckFileDownloadBegin(String checkFileDate, String institutionCode) {
        CheckFileProcessDO checkFileProcessDO = getCheckFileProcessDO(checkFileDate, institutionCode);
        checkFileProcessDO.setCheckStatus(CheckFileProcessEnums.DOWNLOADING.name());
        checkFileProcessDO.setDownloadBeginTime(DateTimeUtil.getCurrentDateTimeStr());
        checkFileProcessDO.setTmSmp(DateTimeUtil.getCurrentDateTimeStr());
        checkFileProcessExtDao.updateFileWaitDownload(checkFileProcessDO);
    }


    @Override
    public void updateCheckFileDownloadEnd(String checkFileDate, String institutionCode) {
        CheckFileProcessDO checkFileProcessDO = getCheckFileProcessDO(checkFileDate, institutionCode);
        checkFileProcessDO.setCheckStatus(CheckFileProcessEnums.DOWNLOADED.name());
        checkFileProcessDO.setDownloadEndTime(DateTimeUtil.getCurrentDateTimeStr());
        checkFileProcessDO.setTmSmp(DateTimeUtil.getCurrentDateTimeStr());
        checkFileProcessExtDao.updateCheckFileDownloadEnd(checkFileProcessDO);
    }

    @Override
    public void updateImportBegin(String checkFileDate, String institutionCode) {
        CheckFileProcessDO checkFileProcessDO = getCheckFileProcessDO(checkFileDate, institutionCode);
        checkFileProcessDO.setCheckStatus(CheckFileProcessEnums.IMPORTING.name());
        checkFileProcessDO.setImportBeginTime(DateTimeUtil.getCurrentDateTimeStr());
        checkFileProcessDO.setTmSmp(DateTimeUtil.getCurrentDateTimeStr());
        checkFileProcessExtDao.updateImportBegin(checkFileProcessDO);
    }

    @Override
    public void updateImportEnd(String checkFileDate, String institutionCode) {
        CheckFileProcessDO checkFileProcessDO = getCheckFileProcessDO(checkFileDate, institutionCode);
        checkFileProcessDO.setCheckStatus(CheckFileProcessEnums.IMPORTED.name());
        checkFileProcessDO.setImportEndTime(DateTimeUtil.getCurrentDateTimeStr());
        checkFileProcessDO.setTmSmp(DateTimeUtil.getCurrentDateTimeStr());
        checkFileProcessExtDao.updateImportEnd(checkFileProcessDO);
    }

    @Override
    public void updateCheckBegin(String checkFileDate, String institutionCode) {
        CheckFileProcessDO checkFileProcessDO = getCheckFileProcessDO(checkFileDate, institutionCode);
        checkFileProcessDO.setCheckStatus(CheckFileProcessEnums.CHECKING.name());
        checkFileProcessDO.setCheckBeginTime(DateTimeUtil.getCurrentDateTimeStr());
        checkFileProcessDO.setTmSmp(DateTimeUtil.getCurrentDateTimeStr());
        checkFileProcessExtDao.updateCheckBegin(checkFileProcessDO);
    }

    @Override
    public void updateCheckEnd(String checkFileDate, String institutionCode) {
        CheckFileProcessDO checkFileProcessDO = getCheckFileProcessDO(checkFileDate, institutionCode);
        checkFileProcessDO.setCheckStatus(CheckFileProcessEnums.CHECK_FINISH.name());
        checkFileProcessDO.setCheckEndTime(DateTimeUtil.getCurrentDateTimeStr());
        checkFileProcessDO.setTmSmp(DateTimeUtil.getCurrentDateTimeStr());
        checkFileProcessExtDao.updateCheckEnd(checkFileProcessDO);
    }

    @Override
    public void updateWaitConfirmInfo(String checkFileDate, String institutionCode) {
        CheckFileProcessDO checkFileProcessDO = getCheckFileProcessDO(checkFileDate, institutionCode);
        checkFileProcessDO.setCheckStatus(CheckFileProcessEnums.WAIT_VERIFY.name());
        checkFileProcessDO.setTmSmp(DateTimeUtil.getCurrentDateTimeStr());
        checkFileProcessExtDao.updateWaitConfirmInfo(checkFileProcessDO);
    }


    private void updateFileWaitDownload(CheckFileProcessDOKey checkFileProcessDOKey) {
        CheckFileProcessDO checkFileProcessDO = new CheckFileProcessDO();
        BeanUtils.copyProperties(checkFileProcessDO, checkFileProcessDOKey);
        checkFileProcessDO.setCheckStatus(CheckFileProcessEnums.WAIT_DOWNLOAD.name());
        checkFileProcessDO.setTmSmp(DateTimeUtil.getCurrentDateTimeStr());
        checkFileProcessExtDao.updateFileWaitDownload(checkFileProcessDO);
    }

    private void initFileWaitDownload(CheckFileProcessDOKey checkFileProcessDOKey) {
        CheckFileProcessDO checkFileProcessDO = new CheckFileProcessDO();
        BeanUtils.copyProperties(checkFileProcessDO, checkFileProcessDOKey);
        checkFileProcessDO.setCheckStatus(CheckFileProcessEnums.WAIT_DOWNLOAD.name());
        checkFileProcessDO.setTmSmp(DateTimeUtil.getCurrentDateTimeStr());
        checkFileProcessExtDao.insert(checkFileProcessDO);
    }

    private static CheckFileProcessDO getCheckFileProcessDO(String checkFileDate, String institutionCode) {
        CheckFileProcessDOKey checkFileProcessDOKey = getCheckFileProcessDOKey(checkFileDate, institutionCode);
        CheckFileProcessDO checkFileProcessDO = new CheckFileProcessDO();
        BeanUtils.copyProperties(checkFileProcessDO, checkFileProcessDOKey);
        return checkFileProcessDO;
    }
}
