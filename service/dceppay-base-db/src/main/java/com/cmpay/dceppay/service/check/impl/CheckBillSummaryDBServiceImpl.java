package com.cmpay.dceppay.service.check.impl;

import com.cmpay.dceppay.bo.check.CheckFileSummaryBO;
import com.cmpay.dceppay.dao.check.ICheckBillSummaryExtDao;
import com.cmpay.dceppay.dao.check.ICheckFileDetailExtDao;
import com.cmpay.dceppay.entity.check.CheckBillSummaryDO;
import com.cmpay.dceppay.entity.check.CheckBillSummaryDOKey;
import com.cmpay.dceppay.entity.check.CheckFileDetailDO;
import com.cmpay.dceppay.entity.check.CheckSummaryExtDO;
import com.cmpay.dceppay.service.check.ICheckBillSummaryDBService;
import com.cmpay.dceppay.util.DateTimeUtil;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/9/23 10:15
 */
@Service
public class CheckBillSummaryDBServiceImpl implements ICheckBillSummaryDBService {
    @Autowired
    private ICheckBillSummaryExtDao checkBillSummaryExtDao;
    @Autowired
    private ICheckFileDetailExtDao checkFileDetailExtDao;

    @Override
    public int initCheckBillInfo(CheckFileSummaryBO checkFileSummaryBO) {
        CheckBillSummaryDOKey billSummaryDOKey = new CheckBillSummaryDOKey();
        billSummaryDOKey.setCheckFileDate(checkFileSummaryBO.getCheckFileDate());
        billSummaryDOKey.setInstitutionCode(checkFileSummaryBO.getInstitutionCode());
        if (JudgeUtils.isNull(checkBillSummaryExtDao.get(billSummaryDOKey))) {
            CheckBillSummaryDO checkBillSummaryDO = new CheckBillSummaryDO();
            BeanUtils.copyProperties(checkBillSummaryDO, checkFileSummaryBO);
            return addCheckBillInfo(checkBillSummaryDO);
        } else {
            return 1;
        }
    }

    @Override
    public CheckFileSummaryBO getByKey(String accountDate, String institutionCode) {
        CheckBillSummaryDOKey billSummaryDOKey = new CheckBillSummaryDOKey();
        billSummaryDOKey.setCheckFileDate(accountDate);
        billSummaryDOKey.setInstitutionCode(institutionCode);
        CheckBillSummaryDO checkBillSummaryDO = checkBillSummaryExtDao.get(billSummaryDOKey);
        if (JudgeUtils.isNotNull(checkBillSummaryDO)) {
            return BeanUtils.copyPropertiesReturnDest(new CheckFileSummaryBO(), checkBillSummaryDO);
        }
        return null;
    }

    private int addCheckBillInfo(CheckBillSummaryDO checkBillSummaryDO) {
        checkBillSummaryDO.setTmSmp(DateTimeUtil.getCurrentDateTimeStr());
        return checkBillSummaryExtDao.insert(checkBillSummaryDO);
    }

    @Override
    public void updateSummaryInfo(String checkFileDate, String institutionCode) {
        CheckFileDetailDO checkFileDetailDO = new CheckFileDetailDO();
        checkFileDetailDO.setCheckFileDate(checkFileDate);
        checkFileDetailDO.setAcceptInstitutionCode(institutionCode);
        CheckSummaryExtDO checkSummary = checkFileDetailExtDao.summaryInfo(checkFileDetailDO);
        CheckBillSummaryDOKey billSummaryDOKey = new CheckBillSummaryDOKey();
        billSummaryDOKey.setCheckFileDate(checkFileDate);
        billSummaryDOKey.setInstitutionCode(institutionCode);
        CheckBillSummaryDO checkBillSummaryDBDO = checkBillSummaryExtDao.get(billSummaryDOKey);
        CheckBillSummaryDO checkBillSummaryDO = new CheckBillSummaryDO();
        checkBillSummaryDO.setCheckFileDate(checkFileDate);
        checkBillSummaryDO.setInstitutionCode(institutionCode);
        checkBillSummaryDO.setPaymentCount(checkSummary.getPaymentCount());
        checkBillSummaryDO.setPaymentAmount(checkSummary.getPaymentAmount());
        checkBillSummaryDO.setRefundCount(checkSummary.getRefundCount());
        checkBillSummaryDO.setRefundAmount(checkSummary.getRefundAmount());

        checkBillSummaryDO.setTotalSuccessCount(checkSummary.getTotalSuccessCount());
        checkBillSummaryDO.setTotalSuccessAmount(checkSummary.getTotalSuccessAmount());
        checkBillSummaryDO.setPaymentSuccessCount(checkSummary.getPaymentSuccessCount());
        checkBillSummaryDO.setPaymentSuccessAmount(checkSummary.getPaymentSuccessAmount());
        checkBillSummaryDO.setRefundSuccessCount(checkSummary.getRefundSuccessCount());
        checkBillSummaryDO.setRefundSuccessAmount(checkSummary.getRefundSuccessAmount());

        checkBillSummaryDO.setTmSmp(DateTimeUtil.getCurrentDateTimeStr());
        if (JudgeUtils.isNull(checkBillSummaryDBDO)) {
            checkBillSummaryExtDao.insert(checkBillSummaryDO);
        } else {
            checkBillSummaryExtDao.updateSummaryInfo(checkBillSummaryDO);
        }
    }
}
