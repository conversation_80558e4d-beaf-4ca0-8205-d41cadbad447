/*
 * @ClassName DcepLineManagementDO
 * @Description 专线管理实体类
 * @version 1.0
 * @Date 2024-12-23 10:00:00
 */
package com.cmpay.dceppay.entity.mng;

import com.cmpay.framework.data.BaseDO;
import com.cmpay.lemon.framework.annotation.DataObject;

import java.util.Date;

@DataObject
public class DcepLineManagementDO extends BaseDO {
    /**
     * @Fields lineMappingAddress 专线映射地址，格式：IP:端口
     */
    private String lineMappingAddress;
    
    /**
     * @Fields lineName 专线名称
     */
    private String lineName;
    
    /**
     * @Fields institutionLineAddress 机构专线地址，仅展示信息
     */
    private String institutionLineAddress;
    
    /**
     * @Fields pbocLineAddress 央行专线地址
     */
    private String pbocLineAddress;
    
    /**
     * @Fields pbocIdcCode 央行IDC标识，如bj-1
     */
    private String pbocIdcCode;
    
    /**
     * @Fields pbocRealAddress 央行真实地址，用于关联专线地址进一步请求央行，主要适用于SFTP业务
     */
    private String pbocRealAddress;
    
    /**
     * @Fields isDefault 默认标识，Y/N，默认专线仅适用于API业务，有且仅有一条
     */
    private String isDefault;
    
    /**
     * @Fields businessType 业务类型，API/SFTP
     */
    private String businessType;
    
    /**
     * @Fields healthCheckEnabled 探活开关，Y-启用/N-禁用当前专线的探活
     */
    private String healthCheckEnabled;
    
    /**
     * @Fields networkStatus 网络状态，NORMAL-正常/WARNING-告警/FAULT-故障
     */
    private String networkStatus;
    
    /**
     * @Fields serviceStatus 服务状态，NORMAL-正常/ABNORMAL-异常
     */
    private String serviceStatus;
    
    /**
     * @Fields probeResult 探测结果，最近一次接口探测返回的机房信息
     */
    private String probeResult;
    
    /**
     * @Fields lastHealthCheckTime 最近一次探活时间
     */
    private Date lastHealthCheckTime;
    
    /**
     * @Fields healthCheckFailStartTime 探活异常开始时间，当探活失败时登记，探活成功后清空
     */
    private Date healthCheckFailStartTime;
    
    /**
     * @Fields consecutiveFailCount 连续失败次数
     */
    private Integer consecutiveFailCount;
    
    /**
     * @Fields responseTimeAvg 平均响应时间（毫秒）
     */
    private Long responseTimeAvg;
    
    /**
     * @Fields status 记录状态，A-有效/I-无效
     */
    private String status;
    
    /**
     * @Fields remark 备注
     */
    private String remark;
    
    /**
     * @Fields createUserNo 创建人账号
     */
    private String createUserNo;
    
    /**
     * @Fields createTime 创建时间
     */
    private Date createTime;
    
    /**
     * @Fields updateUserNo 更新人账号
     */
    private String updateUserNo;
    
    /**
     * @Fields updateTime 更新时间
     */
    private Date updateTime;

    public String getLineMappingAddress() {
        return lineMappingAddress;
    }

    public void setLineMappingAddress(String lineMappingAddress) {
        this.lineMappingAddress = lineMappingAddress;
    }

    public String getLineName() {
        return lineName;
    }

    public void setLineName(String lineName) {
        this.lineName = lineName;
    }

    public String getInstitutionLineAddress() {
        return institutionLineAddress;
    }

    public void setInstitutionLineAddress(String institutionLineAddress) {
        this.institutionLineAddress = institutionLineAddress;
    }

    public String getPbocLineAddress() {
        return pbocLineAddress;
    }

    public void setPbocLineAddress(String pbocLineAddress) {
        this.pbocLineAddress = pbocLineAddress;
    }

    public String getPbocIdcCode() {
        return pbocIdcCode;
    }

    public void setPbocIdcCode(String pbocIdcCode) {
        this.pbocIdcCode = pbocIdcCode;
    }

    public String getPbocRealAddress() {
        return pbocRealAddress;
    }

    public void setPbocRealAddress(String pbocRealAddress) {
        this.pbocRealAddress = pbocRealAddress;
    }

    public String getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(String isDefault) {
        this.isDefault = isDefault;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public String getHealthCheckEnabled() {
        return healthCheckEnabled;
    }

    public void setHealthCheckEnabled(String healthCheckEnabled) {
        this.healthCheckEnabled = healthCheckEnabled;
    }

    public String getNetworkStatus() {
        return networkStatus;
    }

    public void setNetworkStatus(String networkStatus) {
        this.networkStatus = networkStatus;
    }

    public String getServiceStatus() {
        return serviceStatus;
    }

    public void setServiceStatus(String serviceStatus) {
        this.serviceStatus = serviceStatus;
    }

    public String getProbeResult() {
        return probeResult;
    }

    public void setProbeResult(String probeResult) {
        this.probeResult = probeResult;
    }

    public Date getLastHealthCheckTime() {
        return lastHealthCheckTime;
    }

    public void setLastHealthCheckTime(Date lastHealthCheckTime) {
        this.lastHealthCheckTime = lastHealthCheckTime;
    }

    public Date getHealthCheckFailStartTime() {
        return healthCheckFailStartTime;
    }

    public void setHealthCheckFailStartTime(Date healthCheckFailStartTime) {
        this.healthCheckFailStartTime = healthCheckFailStartTime;
    }

    public Integer getConsecutiveFailCount() {
        return consecutiveFailCount;
    }

    public void setConsecutiveFailCount(Integer consecutiveFailCount) {
        this.consecutiveFailCount = consecutiveFailCount;
    }

    public Long getResponseTimeAvg() {
        return responseTimeAvg;
    }

    public void setResponseTimeAvg(Long responseTimeAvg) {
        this.responseTimeAvg = responseTimeAvg;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCreateUserNo() {
        return createUserNo;
    }

    public void setCreateUserNo(String createUserNo) {
        this.createUserNo = createUserNo;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getUpdateUserNo() {
        return updateUserNo;
    }

    public void setUpdateUserNo(String updateUserNo) {
        this.updateUserNo = updateUserNo;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
