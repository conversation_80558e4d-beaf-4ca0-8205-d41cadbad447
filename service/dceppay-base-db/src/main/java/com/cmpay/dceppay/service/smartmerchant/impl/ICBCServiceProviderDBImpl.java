package com.cmpay.dceppay.service.smartmerchant.impl;

import com.cmpay.dceppay.bo.smartmerchant.ICBCServiceProviderBO;
import com.cmpay.dceppay.bo.smartmerchant.ICBCServiceProviderUpdateStatusBO;
import com.cmpay.dceppay.dao.smartmerchant.IICBCServiceProviderExtDao;
import com.cmpay.dceppay.entity.smartmerchant.ICBCServiceProviderDO;
import com.cmpay.dceppay.enums.common.StatusEnum;
import com.cmpay.dceppay.enums.icbc.MerchantOnboardStatusEnum;
import com.cmpay.dceppay.service.smartmerchant.IICBCServiceProviderDBService;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/8/28 16:49
 */
@Service
public class ICBCServiceProviderDBImpl implements IICBCServiceProviderDBService {
    @Autowired
    private IICBCServiceProviderExtDao serviceProviderExtDao;

    @Override
    public ICBCServiceProviderBO getServiceProviderInfoByServiceID(String serviceId) {
        ICBCServiceProviderDO serviceProviderDO = serviceProviderExtDao.getServiceProviderInfoByServiceID(serviceId);
        ICBCServiceProviderBO icbcServiceProviderBO = new ICBCServiceProviderBO();
        if (JudgeUtils.isNotNull(serviceProviderDO)) {
            BeanUtils.copyProperties(icbcServiceProviderBO, serviceProviderDO);
            return icbcServiceProviderBO;
        }
        return null;
    }

    @Override
    public int updateOnboarding(ICBCServiceProviderUpdateStatusBO updateBO) {
        ICBCServiceProviderDO serviceProviderDO = new ICBCServiceProviderDO();
        serviceProviderDO.setServiceId(updateBO.getServiceId());
        serviceProviderDO.setOnboardStatus(MerchantOnboardStatusEnum.ONBOARDING.getStatus());
        serviceProviderDO.setUpdateId(updateBO.getUpdateId());
        serviceProviderDO.setUpdateTime(DateTimeUtils.getCurrentDateTimeStr());
        return serviceProviderExtDao.updateOnboarding(serviceProviderDO);
    }

    @Override
    public int updateOnboardFail(ICBCServiceProviderUpdateStatusBO updateBO) {
        ICBCServiceProviderDO serviceProviderDO = new ICBCServiceProviderDO();
        serviceProviderDO.setServiceId(updateBO.getServiceId());
        serviceProviderDO.setOnboardStatus(MerchantOnboardStatusEnum.ONBOARD_FAIL.getStatus());
        serviceProviderDO.setMsgCode(updateBO.getMsgCode());
        serviceProviderDO.setMsgInfo(updateBO.getMsgInfo());
        serviceProviderDO.setUpdateId(updateBO.getUpdateId());
        serviceProviderDO.setOnboardId(updateBO.getOnboardId());
        serviceProviderDO.setUpdateTime(DateTimeUtils.getCurrentDateTimeStr());
        return serviceProviderExtDao.updateOnboardFail(serviceProviderDO);
    }

    @Override
    public int updateOnboardSuccess(ICBCServiceProviderUpdateStatusBO updateBO) {
        ICBCServiceProviderDO serviceProviderDO = new ICBCServiceProviderDO();
        BeanUtils.copyProperties(serviceProviderDO,updateBO);
        serviceProviderDO.setOnboardStatus(MerchantOnboardStatusEnum.ONBOARD_SUCCESS.getStatus());
        serviceProviderDO.setOnboardTime(DateTimeUtils.getCurrentDateTimeStr());
        serviceProviderDO.setUpdateTime(DateTimeUtils.getCurrentDateTimeStr());
        return serviceProviderExtDao.updateOnboardSuccess(serviceProviderDO);
    }

    @Override
    public int updateOnboardCancel(ICBCServiceProviderUpdateStatusBO updateBO) {
        ICBCServiceProviderDO serviceProviderDO = new ICBCServiceProviderDO();
        serviceProviderDO.setServiceId(updateBO.getServiceId());
        serviceProviderDO.setOnboardStatus(MerchantOnboardStatusEnum.ONBOARD_CANCELED.getStatus());
        serviceProviderDO.setServiceStatus(StatusEnum.DISABLED.getStatus());
        serviceProviderDO.setUpdateId(updateBO.getUpdateId());
        serviceProviderDO.setUpdateTime(DateTimeUtils.getCurrentDateTimeStr());
        return serviceProviderExtDao.updateOnboardCancel(serviceProviderDO);
    }
}
