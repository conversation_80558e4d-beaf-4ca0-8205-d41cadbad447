package com.cmpay.dceppay.service.check;

import com.cmpay.dceppay.bo.check.CheckErrorBO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/24 17:34
 */
public interface ICheckErrorService {
    void addError(CheckErrorBO checkErrorBO);

    CheckErrorBO getByKey(String outTradeNo,String outRefundNo);

    List<CheckErrorBO> findDoubleList(String lastCheckDate);

    /**
     * 更新存疑对平
     * @param checkErrorBO
     */
    void updateDoubleComplete(CheckErrorBO checkErrorBO);

    void updatePaymentShort(CheckErrorBO checkErrorBO);

    void updateAmountError(CheckErrorBO checkErrorBO);

    void updateRefundLong(CheckErrorBO checkErrorBO);

    int cancelError(CheckErrorBO checkErrorBO);

    int updateAmount(CheckErrorBO checkErrorBO);

    int cancelRefund(CheckErrorBO checkErrorBO);
}
