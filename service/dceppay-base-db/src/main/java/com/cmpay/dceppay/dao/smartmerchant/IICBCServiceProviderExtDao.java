package com.cmpay.dceppay.dao.smartmerchant;

import com.cmpay.dceppay.entity.smartmerchant.ICBCServiceProviderDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR>
 * @date 2024/8/28 16:44
 * 工行服务商信息表扩展dao
 */
@Mapper
public interface IICBCServiceProviderExtDao extends  IICBCServiceProviderDao{
    /**
     * 根据商户号查询商户数据
     *
     * @param serviceId 商户号
     * @return 商户信息
     */
    ICBCServiceProviderDO getServiceProviderInfoByServiceID(String serviceId);


    /**
     * 商户进件状态--进件中
     *
     * @param updateDO
     * @return 更新记录数
     */
    int updateOnboarding(ICBCServiceProviderDO updateDO);

    /**
     * 商户进件状态--进件失败
     *
     * @param updateDO
     * @return 更新记录数
     */
    int updateOnboardFail(ICBCServiceProviderDO updateDO);

    /**
     * 商户进件状态--进件成功
     *
     * @param updateDO
     * @return 更新记录数
     */
    int updateOnboardSuccess(ICBCServiceProviderDO updateDO);


    /**
     * 商户进件状态--进件作废
     *
     * @param updateDO
     * @return 更新记录数
     */
    int updateOnboardCancel(ICBCServiceProviderDO updateDO);
}
