package com.cmpay.dceppay.service.check.impl;

import com.cmpay.dceppay.bo.check.CheckFileDetailBO;
import com.cmpay.dceppay.bo.pay.PaySettlementBO;
import com.cmpay.dceppay.constant.dcep.DcepResponseStatusConstant;
import com.cmpay.dceppay.constant.pay.PaymentConstants;
import com.cmpay.dceppay.dao.check.ICheckFileDetailDao;
import com.cmpay.dceppay.dao.check.ICheckFileDetailExtDao;
import com.cmpay.dceppay.entity.check.CheckFileDetailDO;
import com.cmpay.dceppay.entity.pay.PaySettlementDO;
import com.cmpay.dceppay.enums.MsgCodeEnum;
import com.cmpay.dceppay.enums.check.CheckStatusEnum;
import com.cmpay.dceppay.enums.check.ErrorTypeEnum;
import com.cmpay.dceppay.enums.pay.OrderTypeEnum;
import com.cmpay.dceppay.jdbc.CustomBatchPreparedStatementSetter;
import com.cmpay.dceppay.service.check.ICheckFileDetailService;
import com.cmpay.dceppay.util.BeanConvertUtils;
import com.cmpay.dceppay.util.DateTimeUtil;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/23 18:10
 */
@Service
@Slf4j
public class CheckFileDetailServiceImpl implements ICheckFileDetailService {
    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private ICheckFileDetailExtDao checkFileDetailExtDao;

    @Override
    public void addCheckFileCount(List<CheckFileDetailBO> list) {

        String sql = "insert into check_file_detail (out_order_no, refund_order_no, check_file_date, order_complete_time, message_number, " +
                "message_identifier, accept_institution_code, pay_institution_code, receive_institution_code, " +
                "currency_code, order_amount, check_type, bank_order_no, bank_refund_no, order_status, " +
                "trade_desc, check_status, tm_smp) " +
                "values (?, ?, ?, ?, ?, " +
                "?, ?, ?, ?, " +
                "?, ?, ?, ?, ?, ?," +
                "?, ?, ?)";
        List<Object[]> params = new ArrayList<>();
        for (CheckFileDetailBO checkFileDetailBO : list) {
            try {
                String orderStatus = checkFileDetailBO.getOrderStatus();
                if (JudgeUtils.notEquals(orderStatus, DcepResponseStatusConstant.CHECK_FILE_TRADE_SUCCESS)) {
                    continue;
                }
                String outOrderNo = checkFileDetailBO.getOutOrderNo();
                String refundOrderNo = checkFileDetailBO.getRefundOrderNo();
                String checkFileDate = checkFileDetailBO.getCheckFileDate();
                String orderCompleteTime = checkFileDetailBO.getOrderCompleteTime();
                String messageNumber = checkFileDetailBO.getMessageNumber();
                String messageIdentifier = checkFileDetailBO.getMessageIdentifier();
                String acceptInstitutionCode = checkFileDetailBO.getAcceptInstitutionCode();
                String payInstitutionCode = checkFileDetailBO.getPayInstitutionCode();
                String receiveInstitutionCode = checkFileDetailBO.getReceiveInstitutionCode();
                String currencyCode = checkFileDetailBO.getCurrencyCode();
                BigDecimal paymentAmount = JudgeUtils.isNotNull(checkFileDetailBO.getOrderAmount()) ? checkFileDetailBO.getOrderAmount() : BigDecimal.ZERO;
                String checkType;
                if (JudgeUtils.isBlankAny(checkFileDetailBO.getRefundOrderNo(), checkFileDetailBO.getBankRefundNo())) {
                    checkType = OrderTypeEnum.PAYMENT.name();
                    refundOrderNo = "0";
                    checkFileDetailBO.setBankRefundNo("");
                } else {
                    checkType = OrderTypeEnum.REFUND.name();
                    outOrderNo = "0";
                    checkFileDetailBO.setBankOrderNo("");
                }
                String bankOrderNO = checkFileDetailBO.getBankOrderNo();
                String bankRefundNo = checkFileDetailBO.getBankRefundNo();
                String tradeDesc = checkFileDetailBO.getTradeDesc();
                String checkStatus = CheckStatusEnum.WAIT.name();
                String tmSmp = DateTimeUtils.getCurrentDateTimeStr();
                params.add(new Object[]{outOrderNo, refundOrderNo, checkFileDate, orderCompleteTime, messageNumber,
                        messageIdentifier, acceptInstitutionCode, payInstitutionCode, receiveInstitutionCode, currencyCode,
                        paymentAmount, checkType, bankOrderNO, bankRefundNo, orderStatus, tradeDesc, checkStatus, tmSmp});
            } catch (Exception e) {
                log.error("解析对账文件明细数据失败,错误信息:{}", e.getCause());
                BusinessException.throwBusinessException(MsgCodeEnum.PARSE_CHECK_DETAIL_FILE_ERROR);
            }
        }
        try {
            jdbcTemplate.batchUpdate(sql, new CustomBatchPreparedStatementSetter(params));
        } catch (Exception e) {
            log.error("添加对账文件明细数据失败,错误信息:{}", e.getCause());
            BusinessException.throwBusinessException(MsgCodeEnum.INSERT_CHECK_DETAIL_FILE_ERROR, e.getCause());
        }
    }

    @Override
    public List<CheckFileDetailBO> listWaitDetailBO(String checkDate) {
        List<CheckFileDetailDO> list = checkFileDetailExtDao.listWaitDetail(checkDate);
        if (JudgeUtils.isNull(list) || list.size() == 0) {
            return null;
        }
        return BeanConvertUtils.convertList(list, CheckFileDetailBO.class);
    }

    @Override
    public void updatePaymentLongNoOrder(CheckFileDetailBO checkFileDetailBO, String checkDate) {
        CheckFileDetailDO checkFileDetailDO = getCheckFileDetailDO(checkFileDetailBO, ErrorTypeEnum.PAYMENT_LONG_NOT_ORD.name());
        checkFileDetailExtDao.updatePaymentLongNoOrder(checkFileDetailDO);
    }


    @Override
    public void updatePaymentLongStsError(CheckFileDetailBO checkFileDetailBO, String checkDate) {
        CheckFileDetailDO checkFileDetailDO = getCheckFileDetailDO(checkFileDetailBO, ErrorTypeEnum.PAYMENT_LONG_STS_DIF.name());
        checkFileDetailExtDao.updatePaymentLongStsError(checkFileDetailDO);
    }

    @Override
    public void updateCheckComplete(CheckFileDetailBO checkFileDetailBO, String checkDate) {
        CheckFileDetailDO checkFileDetailDO = getCheckFileDetailDO(checkFileDetailBO, CheckStatusEnum.COMPLETE.name());
        checkFileDetailExtDao.updateCheckComplete(checkFileDetailDO);
    }

    @Override
    public void updateAmountError(CheckFileDetailBO checkFileDetailBO, String checkDate) {
        CheckFileDetailDO checkFileDetailDO = getCheckFileDetailDO(checkFileDetailBO, ErrorTypeEnum.AMOUNT_ERROR.name());
        checkFileDetailExtDao.updateAmountError(checkFileDetailDO);
    }

    @Override
    public void updateRefundShortNoOrder(CheckFileDetailBO checkFileDetailBO, String checkDate) {
        CheckFileDetailDO checkFileDetailDO = getCheckFileDetailDO(checkFileDetailBO, ErrorTypeEnum.REFUND_SHORT_NOT_ORD.name());
        checkFileDetailExtDao.updateRefundShortNoOrder(checkFileDetailDO);
    }

    @Override
    public void updateRefundShortStsError(CheckFileDetailBO checkFileDetailBO, String checkDate) {
        CheckFileDetailDO checkFileDetailDO = getCheckFileDetailDO(checkFileDetailBO, ErrorTypeEnum.REFUND_SHORT_STS_DIF.name());
        checkFileDetailExtDao.updateRefundShortStsError(checkFileDetailDO);
    }

    @Override
    public CheckFileDetailBO getPaymentShortDoubt(CheckFileDetailBO checkFileDetailBO) {
        CheckFileDetailDO queryDO = new CheckFileDetailDO();
        BeanUtils.copyProperties(queryDO, checkFileDetailBO);
        CheckFileDetailDO checkFileDetailDO = checkFileDetailExtDao.getPaymentShortDoubt(queryDO);
        if (JudgeUtils.isNull(checkFileDetailDO)) {
            return null;
        }
        return BeanUtils.copyPropertiesReturnDest(new CheckFileDetailBO(), checkFileDetailDO);
    }

    @Override
    public CheckFileDetailBO getRefundLogDoubt(CheckFileDetailBO checkFileDetailBO) {
        CheckFileDetailDO queryDO = new CheckFileDetailDO();
        BeanUtils.copyProperties(queryDO, checkFileDetailBO);
        CheckFileDetailDO checkFileDetailDO = checkFileDetailExtDao.getRefundLogDoubt(queryDO);
        if (JudgeUtils.isNull(checkFileDetailDO)) {
            return null;
        }
        return BeanUtils.copyPropertiesReturnDest(new CheckFileDetailBO(), checkFileDetailDO);
    }

    @Override
    public void updateCheckErrorComplete(String outOrderNo, String refundOrderNo) {
        CheckFileDetailDO checkFileDetailDO = new CheckFileDetailDO();
        checkFileDetailDO.setOutOrderNo(outOrderNo);
        checkFileDetailDO.setRefundOrderNo(refundOrderNo);
        checkFileDetailDO.setCheckStatus(CheckStatusEnum.COMPLETE.name());
        checkFileDetailDO.setCheckCompleteTime(DateTimeUtil.getCurrentDateTimeStr());
        checkFileDetailDO.setTmSmp(DateTimeUtil.getCurrentDateTimeStr());
        checkFileDetailExtDao.updateCheckErrorComplete(checkFileDetailDO);
    }

    @Override
    public void updateCheckErrorAmount(String outOrderNo, String refundOrderNo, BigDecimal amount) {
        CheckFileDetailDO checkFileDetailDO = new CheckFileDetailDO();
        if (JudgeUtils.equals(refundOrderNo, PaymentConstants.DEFAULT_REFUND_NO)) {
            checkFileDetailDO.setOutOrderNo(outOrderNo);
        } else {
            checkFileDetailDO.setOutOrderNo(PaymentConstants.DEFAULT_REFUND_NO);
        }
        checkFileDetailDO.setRefundOrderNo(refundOrderNo);
        checkFileDetailDO.setCheckStatus(CheckStatusEnum.COMPLETE.name());
        checkFileDetailDO.setCheckCompleteTime(DateTimeUtil.getCurrentDateTimeStr());
        checkFileDetailDO.setTmSmp(DateTimeUtil.getCurrentDateTimeStr());
        checkFileDetailDO.setOrderAmount(amount);
        checkFileDetailExtDao.updateCheckErrorAmount(checkFileDetailDO);
    }

    private static CheckFileDetailDO getCheckFileDetailDO(CheckFileDetailBO checkFileDetailBO, String checkStatus) {
        CheckFileDetailDO checkFileDetailDO = new CheckFileDetailDO();
        checkFileDetailDO.setOutOrderNo(checkFileDetailBO.getOutOrderNo());
        checkFileDetailDO.setRefundOrderNo(checkFileDetailBO.getRefundOrderNo());
        checkFileDetailDO.setCheckStatus(checkStatus);
        checkFileDetailDO.setCheckFileDate(checkFileDetailBO.getCheckFileDate());
        checkFileDetailDO.setCheckCompleteTime(DateTimeUtil.getCurrentDateTimeStr());
        checkFileDetailDO.setTmSmp(DateTimeUtil.getCurrentDateTimeStr());
        return checkFileDetailDO;
    }
}