package com.cmpay.dceppay.bo.check;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/9/23 17:54
 */
@Data
public class CheckFileDetailBO {
    /**
     * @Fields outOrderNo 交易订单号
     */
    private String outOrderNo;
    /**
     * @Fields refundOrderNo 退款订单号
     */
    private String refundOrderNo;
    /**
     * @Fields checkFileDate 对账日期
     */
    private String checkFileDate;
    /**
     * @Fields orderCompleteTime 订单完成时间
     */
    private String orderCompleteTime;
    /**
     * @Fields messageNumber 报文编号
     */
    private String messageNumber;
    /**
     * @Fields messageIdentifier 报文标识号
     */
    private String messageIdentifier;
    /**
     * @Fields acceptInstitutionCode 受理服务机构编码
     */
    private String acceptInstitutionCode;
    /**
     * @Fields payInstitutionCode 付款运营机构编码
     */
    private String payInstitutionCode;
    /**
     * @Fields receiveInstitutionCode 收款运营机构编码
     */
    private String receiveInstitutionCode;
    /**
     * @Fields currencyCode 货币代码
     */
    private String currencyCode;
    /**
     * @Fields orderAmount 订单金额
     */
    private BigDecimal orderAmount;
    /**
     * @Fields checkType 对账类型
     */
    private String checkType;
    /**
     * @Fields bankOrderNo 机构交易订单号
     */
    private String bankOrderNo;
    /**
     * @Fields bankRefundNo 机构退款订单号
     */
    private String bankRefundNo;
    /**
     * @Fields orderStatus 业务状态
     */
    private String orderStatus;
    /**
     * @Fields tradeDesc 交易描述信息
     */
    private String tradeDesc;
    /**
     * @Fields checkStatus 对账状态
     */
    private String checkStatus;
    /**
     * @Fields checkCompleteTime 对账完成时间
     */
    private String checkCompleteTime;
    /**
     * @Fields tmSmp 最后更新时间
     */
    private String tmSmp;
}
