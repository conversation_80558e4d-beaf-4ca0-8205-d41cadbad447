package com.cmpay.dceppay.service.mng.impl;

import com.cmpay.dceppay.bo.mng.BusParamBO;
import com.cmpay.dceppay.constant.mng.BusParamTypeConstants;
import com.cmpay.dceppay.dao.mng.IBusParamExtDao;
import com.cmpay.dceppay.entity.mng.BusParamDO;
import com.cmpay.dceppay.service.mng.IIDCParamService;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.datasource.TargetDataSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/12/20 11:22
 */
@Service
public class IIDCParamServiceImpl implements IIDCParamService {
    @Autowired
    private IBusParamExtDao paramExtDao;

    @Override
    @TargetDataSource("dcepmng")
    public BusParamBO getIdcInfoByKey(String paramType, String paramLabel) {
        BusParamDO queryDO = new BusParamDO();
        queryDO.setParamType(paramType);
        queryDO.setParamLabel(paramLabel);
        BusParamDO busParamDO = paramExtDao.getByTypeAndLabel(queryDO);
        if (JudgeUtils.isNotNull(busParamDO)) {
            return BeanUtils.copyPropertiesReturnDest(new BusParamBO(), busParamDO);
        }
        return null;
    }

    @Override
    @TargetDataSource("dcepmng")
    public String getCityZoneDefaultIdcInfo() {
        BusParamDO busParamDO = paramExtDao.getByParamType(BusParamTypeConstants.IDC_CZONE_DEFAULT);
        if (JudgeUtils.isNotNull(busParamDO)) {
            return busParamDO.getParamCode();
        }
        return null;
    }

    @Override
    @TargetDataSource("dcepmng")
    public String getGlobalZoneDefaultIdcInfo() {
        BusParamDO busParamDO = paramExtDao.getByParamType(BusParamTypeConstants.IDC_GZONE_DEFAULT);
        if (JudgeUtils.isNotNull(busParamDO)) {
            return busParamDO.getParamCode();
        }
        return null;
    }

}
