package com.cmpay.dceppay.service.pay.impl;

import com.cmpay.dceppay.bo.pay.PayOrderQueryBO;
import com.cmpay.dceppay.bo.pay.RefundOrderDBBO;
import com.cmpay.dceppay.dao.pay.IPayRefundExtDao;
import com.cmpay.dceppay.entity.pay.PayOrderAccountInfoUpdateDO;
import com.cmpay.dceppay.entity.pay.PayRefundDO;
import com.cmpay.dceppay.entity.pay.RefundQueryDO;
import com.cmpay.dceppay.enums.pay.OrderStatusEnum;
import com.cmpay.dceppay.service.pay.IPayRefundDBService;
import com.cmpay.dceppay.util.BeanConvertUtils;
import com.cmpay.dceppay.util.DateTimeUtil;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/9/3 17:06
 */
@Service
public class PayRefundDBServiceImpl implements IPayRefundDBService {
    @Autowired
    private IPayRefundExtDao payRefundExtDao;

    @Override
    public RefundOrderDBBO getByOutRefundNo(String outRefundNo) {
        PayRefundDO refundDO = payRefundExtDao.getByOutRefundNo(outRefundNo);
        RefundOrderDBBO refundOrderDBBO = new RefundOrderDBBO();
        if (JudgeUtils.isNotNull(refundDO)) {
            BeanUtils.copyProperties(refundOrderDBBO, refundDO);
            return refundOrderDBBO;
        }
        return null;
    }

    @Override
    public BigDecimal sumRefundAmount(String outOrderNo) {
        return payRefundExtDao.sumRefundAmount(outOrderNo);
    }

    @Override
    public int addRefundOrder(RefundOrderDBBO refundOrderDBBO) {
        PayRefundDO refundDO = new PayRefundDO();
        BeanUtils.copyProperties(refundDO, refundOrderDBBO);
        refundDO.setTmSmp(DateTimeUtils.getCurrentDateTimeStr());
        return payRefundExtDao.insert(refundDO);
    }

    @Override
    public List<RefundOrderDBBO> findWaitRefundList(PayOrderQueryBO payOrderQueryBO) {
        RefundQueryDO refundQueryDO = new RefundQueryDO();
        BeanUtils.copyProperties(refundQueryDO, payOrderQueryBO);
        List<PayRefundDO> refundDOList = payRefundExtDao.findWaitRefundList(refundQueryDO);
        if (JudgeUtils.isNull(refundDOList)) {
            return new ArrayList<>();
        }
        refundDOList = refundDOList.stream()
                .filter(order -> calculateShardId(order.getRefundOrderNo(), payOrderQueryBO.getShardTotal()) == payOrderQueryBO.getShardIndex())
                .collect(Collectors.toList());
        return BeanConvertUtils.convertList(refundDOList, RefundOrderDBBO.class);
    }

    @Override
    public int updateRefundSuccess(RefundOrderDBBO updateRefundOrderDBBO) {
        PayRefundDO refundDO = new PayRefundDO();
        BeanUtils.copyProperties(refundDO, updateRefundOrderDBBO);
        refundDO.setTmSmp(DateTimeUtils.getCurrentDateTimeStr());
        refundDO.setStatus(OrderStatusEnum.REFUND_SUCCESS.name());
        return payRefundExtDao.updateRefundSuccess(refundDO);
    }

    @Override
    public int updateRefundFail(RefundOrderDBBO updateRefundOrderDBBO) {
        PayRefundDO refundDO = new PayRefundDO();
        BeanUtils.copyProperties(refundDO, updateRefundOrderDBBO);
        refundDO.setStatus(OrderStatusEnum.REFUND_FAIL.name());
        refundDO.setTmSmp(DateTimeUtils.getCurrentDateTimeStr());
        return payRefundExtDao.updateRefundFail(refundDO);
    }

    @Override
    public BigDecimal sumRefundSuccessAmount(String outOrderNo) {
        return payRefundExtDao.sumRefundSuccessAmount(outOrderNo);
    }

    @Override
    public void updateAccountDate(RefundOrderDBBO refundOrderDBBO) {
        PayRefundDO refundDO=new PayRefundDO();
        refundDO.setRefundOrderNo(refundOrderDBBO.getRefundOrderNo());
        refundDO.setTmSmp(DateTimeUtil.getCurrentDateTimeStr());
        refundDO.setAccountDate(refundOrderDBBO.getAccountDate());
        payRefundExtDao.updateAccountDate(refundDO);
    }

    @Override
    public void updateRefundWait(RefundOrderDBBO refundOrderBO) {
        PayRefundDO refundDO=new PayRefundDO();
        refundDO.setRefundOrderNo(refundOrderBO.getRefundOrderNo());
        refundDO.setMessageIdentification(refundOrderBO.getMessageIdentification());
        refundDO.setStatus(OrderStatusEnum.REFUND_WAIT.name());
        refundDO.setBankOrderNo(refundOrderBO.getBankOrderNo());
        refundDO.setBankRefundNo(refundOrderBO.getBankRefundNo());
        refundDO.setTmSmp(DateTimeUtil.getCurrentDateTimeStr());
        payRefundExtDao.updateRefundWait(refundDO);
    }

    @Override
    public List<RefundOrderDBBO> findWaitPendList(PayOrderQueryBO payOrderQueryBO) {
        RefundQueryDO refundQueryDO = new RefundQueryDO();
        BeanUtils.copyProperties(refundQueryDO, payOrderQueryBO);
        List<PayRefundDO> refundDOList = payRefundExtDao.findWaitPendList(refundQueryDO);
        if (JudgeUtils.isNull(refundDOList)) {
            return new ArrayList<>();
        }
        refundDOList = refundDOList.stream()
                .filter(order -> calculateShardId(order.getRefundOrderNo(), payOrderQueryBO.getShardTotal()) == payOrderQueryBO.getShardIndex())
                .collect(Collectors.toList());
        return BeanConvertUtils.convertList(refundDOList, RefundOrderDBBO.class);
    }

    private int calculateShardId(String orderNo, int shardTotal) {
        return Math.abs(orderNo.hashCode()) % shardTotal;
    }
}
