package com.cmpay.dceppay.service.pay;

import com.cmpay.dceppay.bo.pay.PayOrderQueryBO;
import com.cmpay.dceppay.bo.pay.RefundOrderDBBO;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/3 17:02
 */
public interface IPayRefundDBService {
    /**
     * 跟据退款订单号查询退款订单信息
     * @param outRefundNo
     * @return
     */
    RefundOrderDBBO getByOutRefundNo(String outRefundNo);

    /**
     * 统计已退款总金额（）
     * @param outOrderNo
     * @return
     */

    BigDecimal sumRefundAmount(String outOrderNo);

    /**
     * 登记退款订单
     * @param refundOrderDBBO
     * @return
     */

    int addRefundOrder(RefundOrderDBBO refundOrderDBBO);

    List<RefundOrderDBBO> findWaitRefundList(PayOrderQueryBO refundOrderQueryBO);

    int updateRefundSuccess(RefundOrderDBBO updateRefundOrderDBBO);

    int updateRefundFail(RefundOrderDBBO updateRefundOrderDBBO);

    /**
     * 统计已退款成功金额
     * @param outOrderNo
     * @return
     */
    BigDecimal sumRefundSuccessAmount(String outOrderNo);

    void updateAccountDate(RefundOrderDBBO refundOrderDBBO);

    /**
     * 退款受理成功，等待退款
     * @param refundOrderBO 退款对象
     */
    void updateRefundWait(RefundOrderDBBO refundOrderBO);

    /**
     * 查询等待发起订单
     * @param payOrderQueryBO
     * @return
     */
    List<RefundOrderDBBO> findWaitPendList(PayOrderQueryBO payOrderQueryBO);
}
