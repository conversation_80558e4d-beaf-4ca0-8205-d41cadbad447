/*
 * @ClassName DcepLineInfoDO
 * @Description 专线管理信息实体类
 * @version 1.0
 * @Date 2024-12-23 10:00:00
 */
package com.cmpay.dceppay.entity.mng;

import com.cmpay.framework.data.BaseDO;
import com.cmpay.lemon.framework.annotation.DataObject;

@DataObject
public class DcepLineInfoDO extends BaseDO {
    /**
     * @Fields lineId 专线编号
     */
    private String lineId;
    
    /**
     * @Fields lineMappingAddress 专线映射地址
     */
    private String lineMappingAddress;
    
    /**
     * @Fields lineName 专线名称
     */
    private String lineName;
    
    /**
     * @Fields institutionLineAddress 机构专线地址
     */
    private String institutionLineAddress;
    
    /**
     * @Fields pbocLineAddress 央行专线地址
     */
    private String pbocLineAddress;
    
    /**
     * @Fields pbocIdcCode 央行IDC标识
     */
    private String pbocIdcCode;
    
    /**
     * @Fields pbocRealAddress 央行真实地址
     */
    private String pbocRealAddress;
    
    /**
     * @Fields businessType 业务类型
     */
    private String businessType;
    
    /**
     * @Fields isDefault 默认标识
     */
    private String isDefault;
    
    /**
     * @Fields healthCheckEnabled 探活开关
     */
    private String healthCheckEnabled;
    
    /**
     * @Fields networkStatus 网络状态
     */
    private String networkStatus;
    
    /**
     * @Fields serviceStatus 服务状态
     */
    private String serviceStatus;
    
    /**
     * @Fields probeResult 探测结果
     */
    private String probeResult;
    
    /**
     * @Fields lastCheckTime 最近一次探测时间
     */
    private String lastCheckTime;
    
    /**
     * @Fields failStartTime 探测异常开始时间
     */
    private String failStartTime;
    
    /**
     * @Fields failCount 连续失败次数
     */
    private Integer failCount;
    
    /**
     * @Fields rspTimeAvg 平均响应时长
     */
    private Long rspTimeAvg;
    
    /**
     * @Fields delFlag 删除标识
     */
    private String delFlag;
    
    /**
     * @Fields remark 备注
     */
    private String remark;
    
    /**
     * @Fields createUserNo 创建者
     */
    private String createUserNo;
    
    /**
     * @Fields createTime 创建时间
     */
    private String createTime;
    
    /**
     * @Fields updateUserNo 更新者
     */
    private String updateUserNo;
    
    /**
     * @Fields updateTime 更新时间
     */
    private String updateTime;
    
    /**
     * @Fields tmSmp 时间戳
     */
    private String tmSmp;

    // Getter and Setter methods
    public String getLineId() {
        return lineId;
    }

    public void setLineId(String lineId) {
        this.lineId = lineId;
    }

    public String getLineMappingAddress() {
        return lineMappingAddress;
    }

    public void setLineMappingAddress(String lineMappingAddress) {
        this.lineMappingAddress = lineMappingAddress;
    }

    public String getLineName() {
        return lineName;
    }

    public void setLineName(String lineName) {
        this.lineName = lineName;
    }

    public String getInstitutionLineAddress() {
        return institutionLineAddress;
    }

    public void setInstitutionLineAddress(String institutionLineAddress) {
        this.institutionLineAddress = institutionLineAddress;
    }

    public String getPbocLineAddress() {
        return pbocLineAddress;
    }

    public void setPbocLineAddress(String pbocLineAddress) {
        this.pbocLineAddress = pbocLineAddress;
    }

    public String getPbocIdcCode() {
        return pbocIdcCode;
    }

    public void setPbocIdcCode(String pbocIdcCode) {
        this.pbocIdcCode = pbocIdcCode;
    }

    public String getPbocRealAddress() {
        return pbocRealAddress;
    }

    public void setPbocRealAddress(String pbocRealAddress) {
        this.pbocRealAddress = pbocRealAddress;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public String getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(String isDefault) {
        this.isDefault = isDefault;
    }

    public String getHealthCheckEnabled() {
        return healthCheckEnabled;
    }

    public void setHealthCheckEnabled(String healthCheckEnabled) {
        this.healthCheckEnabled = healthCheckEnabled;
    }

    public String getNetworkStatus() {
        return networkStatus;
    }

    public void setNetworkStatus(String networkStatus) {
        this.networkStatus = networkStatus;
    }

    public String getServiceStatus() {
        return serviceStatus;
    }

    public void setServiceStatus(String serviceStatus) {
        this.serviceStatus = serviceStatus;
    }

    public String getProbeResult() {
        return probeResult;
    }

    public void setProbeResult(String probeResult) {
        this.probeResult = probeResult;
    }

    public String getLastCheckTime() {
        return lastCheckTime;
    }

    public void setLastCheckTime(String lastCheckTime) {
        this.lastCheckTime = lastCheckTime;
    }

    public String getFailStartTime() {
        return failStartTime;
    }

    public void setFailStartTime(String failStartTime) {
        this.failStartTime = failStartTime;
    }

    public Integer getFailCount() {
        return failCount;
    }

    public void setFailCount(Integer failCount) {
        this.failCount = failCount;
    }

    public Long getRspTimeAvg() {
        return rspTimeAvg;
    }

    public void setRspTimeAvg(Long rspTimeAvg) {
        this.rspTimeAvg = rspTimeAvg;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCreateUserNo() {
        return createUserNo;
    }

    public void setCreateUserNo(String createUserNo) {
        this.createUserNo = createUserNo;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateUserNo() {
        return updateUserNo;
    }

    public void setUpdateUserNo(String updateUserNo) {
        this.updateUserNo = updateUserNo;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public String getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(String tmSmp) {
        this.tmSmp = tmSmp;
    }
}
