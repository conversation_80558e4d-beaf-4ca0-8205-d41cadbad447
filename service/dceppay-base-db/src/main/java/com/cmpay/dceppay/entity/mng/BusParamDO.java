/*
 * @ClassName BusParamDO
 * @Description 
 * @version 1.0
 * @Date 2024-10-08 14:05:20
 */
package com.cmpay.dceppay.entity.mng;

import com.cmpay.framework.data.BaseDO;
import com.cmpay.lemon.framework.annotation.DataObject;

@DataObject
public class BusParamDO extends BaseDO {
    /**
     * @Fields paramNo 参数编号
     */
    private String paramNo;
    /**
     * @Fields paramName 参数名称
     */
    private String paramName;
    /**
     * @Fields paramType 参数类型
     */
    private String paramType;
    /**
     * @Fields paramCode 参数键值
     */
    private String paramCode;
    /**
     * @Fields paramLabel 标签
     */
    private String paramLabel;
    /**
     * @Fields orderNum 排序
     */
    private Byte orderNum;
    /**
     * @Fields remark 备注
     */
    private String remark;
    /**
     * @Fields createUserNo 创建人账号
     */
    private String createUserNo;
    /**
     * @Fields createTime 创建时间
     */
    private String createTime;
    /**
     * @Fields updateUserNo 更新人账号
     */
    private String updateUserNo;
    /**
     * @Fields updateTime 更新时间
     */
    private String updateTime;

    public String getParamNo() {
        return paramNo;
    }

    public void setParamNo(String paramNo) {
        this.paramNo = paramNo;
    }

    public String getParamName() {
        return paramName;
    }

    public void setParamName(String paramName) {
        this.paramName = paramName;
    }

    public String getParamType() {
        return paramType;
    }

    public void setParamType(String paramType) {
        this.paramType = paramType;
    }

    public String getParamCode() {
        return paramCode;
    }

    public void setParamCode(String paramCode) {
        this.paramCode = paramCode;
    }

    public String getParamLabel() {
        return paramLabel;
    }

    public void setParamLabel(String paramLabel) {
        this.paramLabel = paramLabel;
    }

    public Byte getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(Byte orderNum) {
        this.orderNum = orderNum;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCreateUserNo() {
        return createUserNo;
    }

    public void setCreateUserNo(String createUserNo) {
        this.createUserNo = createUserNo;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateUserNo() {
        return updateUserNo;
    }

    public void setUpdateUserNo(String updateUserNo) {
        this.updateUserNo = updateUserNo;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }
}