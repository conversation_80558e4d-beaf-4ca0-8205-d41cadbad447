/*
 * @ClassName CheckFileProcessDO
 * @Description 
 * @version 1.0
 * @Date 2024-09-03 16:03:09
 */
package com.cmpay.dceppay.entity.check;

import com.cmpay.framework.data.BaseDO;
import com.cmpay.lemon.framework.annotation.DataObject;

@DataObject
public class CheckFileProcessDO extends BaseDO {
    /**
     * @Fields checkFileDate 对账日期
     */
    private String checkFileDate;
    /**
     * @Fields institutionCode 机构编码
     */
    private String institutionCode;
    /**
     * @Fields downloadBeginTime 文件下载开始时间
     */
    private String downloadBeginTime;
    /**
     * @Fields downloadEndTime 文件下载结束时间
     */
    private String downloadEndTime;
    /**
     * @Fields importBeginTime 文件入库开始时间
     */
    private String importBeginTime;
    /**
     * @Fields importEndTime 文件入库结束时间
     */
    private String importEndTime;
    /**
     * @Fields checkBeginTime 对账开始时间
     */
    private String checkBeginTime;
    /**
     * @Fields checkEndTime 对账结束时间
     */
    private String checkEndTime;
    /**
     * @Fields checkStatus 对账状态
     */
    private String checkStatus;
    /**
     * @Fields tmSmp 最后更新时间
     */
    private String tmSmp;

    public String getCheckFileDate() {
        return checkFileDate;
    }

    public void setCheckFileDate(String checkFileDate) {
        this.checkFileDate = checkFileDate;
    }

    public String getInstitutionCode() {
        return institutionCode;
    }

    public void setInstitutionCode(String institutionCode) {
        this.institutionCode = institutionCode;
    }

    public String getDownloadBeginTime() {
        return downloadBeginTime;
    }

    public void setDownloadBeginTime(String downloadBeginTime) {
        this.downloadBeginTime = downloadBeginTime;
    }

    public String getDownloadEndTime() {
        return downloadEndTime;
    }

    public void setDownloadEndTime(String downloadEndTime) {
        this.downloadEndTime = downloadEndTime;
    }

    public String getImportBeginTime() {
        return importBeginTime;
    }

    public void setImportBeginTime(String importBeginTime) {
        this.importBeginTime = importBeginTime;
    }

    public String getImportEndTime() {
        return importEndTime;
    }

    public void setImportEndTime(String importEndTime) {
        this.importEndTime = importEndTime;
    }

    public String getCheckBeginTime() {
        return checkBeginTime;
    }

    public void setCheckBeginTime(String checkBeginTime) {
        this.checkBeginTime = checkBeginTime;
    }

    public String getCheckEndTime() {
        return checkEndTime;
    }

    public void setCheckEndTime(String checkEndTime) {
        this.checkEndTime = checkEndTime;
    }

    public String getCheckStatus() {
        return checkStatus;
    }

    public void setCheckStatus(String checkStatus) {
        this.checkStatus = checkStatus;
    }

    public String getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(String tmSmp) {
        this.tmSmp = tmSmp;
    }
}