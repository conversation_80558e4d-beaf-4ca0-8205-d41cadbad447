package com.cmpay.dceppay.dao.check;

import com.cmpay.dceppay.entity.check.CheckErrorDetailDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/3 13:46
 */
@Mapper
public interface ICheckErrorDetailExtDao extends ICheckErrorDetailDao{
    List<CheckErrorDetailDO> findDoubleList(String checkFileDate);

    void updateDoubleComplete(CheckErrorDetailDO errorDetailDO);

    void updatePaymentShort(CheckErrorDetailDO errorDetailDO);

    void updateAmountError(CheckErrorDetailDO errorDetailDO);

    void updateRefundLong(CheckErrorDetailDO errorDetailDO);

    int cancelError(CheckErrorDetailDO errorDetailDO);

    int updateAmount(CheckErrorDetailDO errorDetailDO);

    int cancelRefund(CheckErrorDetailDO errorDetailDO);
}
