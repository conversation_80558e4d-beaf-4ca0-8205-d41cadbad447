/*
 * @ClassName TaskStatusDO
 * @Description 
 * @version 1.0
 * @Date 2024-09-26 13:27:56
 */
package com.cmpay.dceppay.entity.mng;

import com.cmpay.framework.data.BaseDO;
import com.cmpay.lemon.framework.annotation.DataObject;

@DataObject
public class TaskStatusDO extends BaseDO {
    /**
     * @Fields taskDate 执行日期
     */
    private String taskDate;
    /**
     * @Fields taskKey 任务key
     */
    private String taskKey;
    /**
     * @Fields taskDesc 任务说明
     */
    private String taskDesc;
    /**
     * @Fields status 任务状态PENDING,RUNNING,SUCCESS,FAILED,CANCELLED
     */
    private String status;
    /**
     * @Fields result 执行结果，可以存储错误信息或者成功信息
     */
    private String result;
    /**
     * @Fields createdTime 创建时间
     */
    private String createdTime;
    /**
     * @Fields updatedTime 更新时间
     */
    private String updatedTime;

    public String getTaskDate() {
        return taskDate;
    }

    public void setTaskDate(String taskDate) {
        this.taskDate = taskDate;
    }

    public String getTaskKey() {
        return taskKey;
    }

    public void setTaskKey(String taskKey) {
        this.taskKey = taskKey;
    }

    public String getTaskDesc() {
        return taskDesc;
    }

    public void setTaskDesc(String taskDesc) {
        this.taskDesc = taskDesc;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public String getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(String createdTime) {
        this.createdTime = createdTime;
    }

    public String getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(String updatedTime) {
        this.updatedTime = updatedTime;
    }
}