/*
 * @ClassName PayMerchantRouteDO
 * @Description 
 * @version 1.0
 * @Date 2024-09-03 16:50:36
 */
package com.cmpay.dceppay.entity.pay;

import com.cmpay.framework.data.BaseDO;
import com.cmpay.lemon.framework.annotation.DataObject;

@DataObject
public class PayMerchantRouteDO extends BaseDO {
    /**
     * @Fields routeId 路由编号
     */
    private String routeId;
    /**
     * @Fields bussCode 业务类型标识
     */
    private String bussCode;
    /**
     * @Fields bussType 业务类型名称
     */
    private String bussType;
    /**
     * @Fields merchantNo 商户号
     */
    private String merchantNo;
    /**
     * @Fields routeStatus 路由状态 disable enable
     */
    private String routeStatus;
    /**
     * @Fields remark 备注信息
     */
    private String remark;
    /**
     * @Fields createId 创建者
     */
    private String createId;
    /**
     * @Fields createTime 创建时间
     */
    private String createTime;
    /**
     * @Fields updateId 更新者
     */
    private String updateId;
    /**
     * @Fields updateTime 更新时间
     */
    private String updateTime;

    public String getRouteId() {
        return routeId;
    }

    public void setRouteId(String routeId) {
        this.routeId = routeId;
    }

    public String getBussCode() {
        return bussCode;
    }

    public void setBussCode(String bussCode) {
        this.bussCode = bussCode;
    }

    public String getBussType() {
        return bussType;
    }

    public void setBussType(String bussType) {
        this.bussType = bussType;
    }

    public String getMerchantNo() {
        return merchantNo;
    }

    public void setMerchantNo(String merchantNo) {
        this.merchantNo = merchantNo;
    }

    public String getRouteStatus() {
        return routeStatus;
    }

    public void setRouteStatus(String routeStatus) {
        this.routeStatus = routeStatus;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCreateId() {
        return createId;
    }

    public void setCreateId(String createId) {
        this.createId = createId;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateId() {
        return updateId;
    }

    public void setUpdateId(String updateId) {
        this.updateId = updateId;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }
}