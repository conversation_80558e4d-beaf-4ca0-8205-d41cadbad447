package com.cmpay.dceppay.service.pay.impl;

import com.cmpay.dceppay.bo.pay.PayOrderDBBO;
import com.cmpay.dceppay.bo.pay.PaySettlementBO;
import com.cmpay.dceppay.bo.pay.RefundOrderDBBO;
import com.cmpay.dceppay.constant.pay.SettlementConstants;
import com.cmpay.dceppay.dao.pay.IPaySettlementExtDao;
import com.cmpay.dceppay.entity.pay.PaySettlementDO;
import com.cmpay.dceppay.entity.pay.PaySettlementDOKey;
import com.cmpay.dceppay.enums.check.CheckStatusEnum;
import com.cmpay.dceppay.enums.check.ErrorTypeEnum;
import com.cmpay.dceppay.enums.common.IdGenKeyEnum;
import com.cmpay.dceppay.enums.pay.OrderTypeEnum;
import com.cmpay.dceppay.service.pay.IPaySettlementDBService;
import com.cmpay.dceppay.util.BeanConvertUtils;
import com.cmpay.dceppay.util.DateTimeUtil;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.utils.IdGenUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/3 17:06
 */
@Service
public class PaySettlementDBServiceImpl implements IPaySettlementDBService {
    @Autowired
    private IPaySettlementExtDao paySettlementExtDao;


    @Override
    public PaySettlementBO getByKey(String outOrderNo, String refundOrderNo) {
        PaySettlementDOKey settlementDOKey = new PaySettlementDOKey();
        settlementDOKey.setOutOrderNo(outOrderNo);
        settlementDOKey.setRefundOrderNo(refundOrderNo);
        PaySettlementDO paySettlementDO = paySettlementExtDao.get(settlementDOKey);
        if (JudgeUtils.isNull(paySettlementDO)) {
            return null;
        }
        return BeanUtils.copyPropertiesReturnDest(new PaySettlementBO(), paySettlementDO);
    }

    @Override
    public int registerPaySettlement(PayOrderDBBO payOrderDBBO) {
        try {
            PaySettlementDO paySettlementDO = getPaymentSettlement(payOrderDBBO);
            paySettlementDO.setSettlementDate(DateTimeUtil.getCurrentDateStr());
            paySettlementDO.setJrnNo(IdGenUtils.generateIdWithDateTime(IdGenKeyEnum.SETTLEMENT_JRN_NO.name(), IdGenKeyEnum.SETTLEMENT_JRN_NO_LENGTH));
            paySettlementDO.setTmSmp(DateTimeUtil.getCurrentDateTimeStr());
            return paySettlementExtDao.insert(paySettlementDO);
        } catch (Exception e) {
            PaySettlementBO settlementBO = getByKey(payOrderDBBO.getOutOrderNo(), SettlementConstants.DEFAULT_REFUND_NO);
            if (JudgeUtils.isNull(settlementBO)) {
                settlementBO.setAccountDate(payOrderDBBO.getAccountDate());
                return updateAccountDate(settlementBO);
            }
        }
        return 0;
    }

    @Override
    public int registerRefundSettlement(RefundOrderDBBO refundOrderDBBO) {
        try {
            PaySettlementDO paySettlementDO = getRefundSettlement(refundOrderDBBO);
            paySettlementDO.setSettlementDate(DateTimeUtil.getCurrentDateStr());
            paySettlementDO.setJrnNo(IdGenUtils.generateIdWithDateTime(IdGenKeyEnum.SETTLEMENT_JRN_NO.name(), IdGenKeyEnum.SETTLEMENT_JRN_NO_LENGTH));
            paySettlementDO.setTmSmp(DateTimeUtil.getCurrentDateTimeStr());
            return paySettlementExtDao.insert(paySettlementDO);
        } catch (Exception e) {
            PaySettlementBO settlementBO = getByKey(refundOrderDBBO.getOutOrderNo(), refundOrderDBBO.getRefundOrderNo());
            if (JudgeUtils.isNull(settlementBO)) {
                settlementBO.setAccountDate(refundOrderDBBO.getAccountDate());
                return updateAccountDate(settlementBO);
            }
        }
        return 0;
    }


    @Override
    public PaySettlementBO getPaymentSettlement(PaySettlementBO queryPaySettlementBO) {
        PaySettlementDO queryDO = new PaySettlementDO();
        BeanUtils.copyProperties(queryDO, queryPaySettlementBO);
        PaySettlementDO paySettlementDO = paySettlementExtDao.getPaymentSettlement(queryDO);
        if (JudgeUtils.isNull(paySettlementDO)) {
            return null;
        }
        return BeanUtils.copyPropertiesReturnDest(new PaySettlementBO(), paySettlementDO);
    }

    @Override
    public PaySettlementBO getRefundSettlement(PaySettlementBO queryPaySettlementBO) {
        PaySettlementDO queryDO = new PaySettlementDO();
        BeanUtils.copyProperties(queryDO, queryPaySettlementBO);
        PaySettlementDO paySettlementDO = paySettlementExtDao.getRefundSettlement(queryDO);
        if (JudgeUtils.isNull(paySettlementDO)) {
            return null;
        }
        return BeanUtils.copyPropertiesReturnDest(new PaySettlementBO(), paySettlementDO);
    }


    @Override
    public void updateCheckComplete(PaySettlementBO settlementBO, String checkDate) {
        PaySettlementDO paySettlementDO = new PaySettlementDO();
        paySettlementDO.setOutOrderNo(settlementBO.getOutOrderNo());
        paySettlementDO.setRefundOrderNo(settlementBO.getRefundOrderNo());
        paySettlementDO.setSettlementDate(settlementBO.getSettlementDate());
        paySettlementDO.setCheckStatus(CheckStatusEnum.COMPLETE.name());
        paySettlementDO.setCheckDate(checkDate);
        paySettlementDO.setCheckCompleteTime(DateTimeUtil.getCurrentDateTimeStr());
        paySettlementDO.setTmSmp(DateTimeUtil.getCurrentDateTimeStr());
        paySettlementExtDao.updateCheckComplete(paySettlementDO);
    }

    @Override
    public void updateAmountError(PaySettlementBO settlementBO, String checkDate) {
        PaySettlementDO paySettlementDO = new PaySettlementDO();
        paySettlementDO.setOutOrderNo(settlementBO.getOutOrderNo());
        paySettlementDO.setRefundOrderNo(settlementBO.getRefundOrderNo());
        paySettlementDO.setSettlementDate(settlementBO.getSettlementDate());
        paySettlementDO.setCheckStatus(ErrorTypeEnum.AMOUNT_ERROR.name());
        paySettlementDO.setCheckDate(checkDate);
        paySettlementDO.setCheckCompleteTime(DateTimeUtil.getCurrentDateTimeStr());
        paySettlementDO.setTmSmp(DateTimeUtil.getCurrentDateTimeStr());
        paySettlementExtDao.updateAmountError(paySettlementDO);
    }

    @Override
    public List<PaySettlementBO> findWaitOrderList(String checkDate) {
        List<PaySettlementDO> settlementDOList = paySettlementExtDao.findWaitOrderList(checkDate);
        if (JudgeUtils.isNull(settlementDOList)) {
            return new ArrayList<>();
        }
        return BeanConvertUtils.convertList(settlementDOList, PaySettlementBO.class);
    }

    @Override
    public PaySettlementBO getPaymentShortDoubt(PaySettlementBO settlementBO) {
        PaySettlementDO queryDO = new PaySettlementDO();
        BeanUtils.copyProperties(queryDO, settlementBO);
        PaySettlementDO paySettlementDO = paySettlementExtDao.getPaymentShortDoubt(queryDO);
        if (JudgeUtils.isNull(paySettlementDO)) {
            return null;
        }
        return BeanUtils.copyPropertiesReturnDest(new PaySettlementBO(), paySettlementDO);
    }

    @Override
    public PaySettlementBO getRefundLongDoubt(PaySettlementBO settlementBO) {
        PaySettlementDO queryDO = new PaySettlementDO();
        BeanUtils.copyProperties(queryDO, settlementBO);
        PaySettlementDO paySettlementDO = paySettlementExtDao.getRefundLongDoubt(queryDO);
        if (JudgeUtils.isNull(paySettlementDO)) {
            return null;
        }
        return BeanUtils.copyPropertiesReturnDest(new PaySettlementBO(), paySettlementDO);
    }

    @Override
    public void updateCheckErrorComplete(String outOrderNo, String refundOrderNo) {
        PaySettlementDO paySettlementDO = new PaySettlementDO();
        paySettlementDO.setOutOrderNo(outOrderNo);
        paySettlementDO.setRefundOrderNo(refundOrderNo);
        paySettlementDO.setCheckStatus(CheckStatusEnum.COMPLETE.name());
        paySettlementDO.setCheckCompleteTime(DateTimeUtil.getCurrentDateTimeStr());
        paySettlementDO.setTmSmp(DateTimeUtil.getCurrentDateTimeStr());
        paySettlementExtDao.updateCheckErrorComplete(paySettlementDO);
    }

    @Override
    public void updateCheckErrorAmount(String outOrderNo, String refundOrderNo, BigDecimal amount) {
        PaySettlementDO paySettlementDO = new PaySettlementDO();
        paySettlementDO.setOutOrderNo(outOrderNo);
        paySettlementDO.setRefundOrderNo(refundOrderNo);
        paySettlementDO.setCheckStatus(CheckStatusEnum.COMPLETE.name());
        paySettlementDO.setCheckCompleteTime(DateTimeUtil.getCurrentDateTimeStr());
        paySettlementDO.setOrderAmount(amount);
        paySettlementDO.setTmSmp(DateTimeUtil.getCurrentDateTimeStr());
        paySettlementExtDao.updateCheckErrorAmount(paySettlementDO);
    }

    @Override
    public int updateAccountDate(PaySettlementBO settlementBO) {
        PaySettlementDO paySettlementDO = new PaySettlementDO();
        paySettlementDO.setOutOrderNo(settlementBO.getOutOrderNo());
        paySettlementDO.setRefundOrderNo(settlementBO.getRefundOrderNo());
        paySettlementDO.setAccountDate(settlementBO.getAccountDate());
        paySettlementDO.setTmSmp(DateTimeUtil.getCurrentDateTimeStr());
        return paySettlementExtDao.updateAccountDate(paySettlementDO);
    }

    @Override
    public void updatePaymentShort(PaySettlementBO paySettlementBO, String checkDate) {
        PaySettlementDO paySettlementDO = new PaySettlementDO();
        paySettlementDO.setOutOrderNo(paySettlementBO.getOutOrderNo());
        paySettlementDO.setRefundOrderNo(paySettlementBO.getRefundOrderNo());
        paySettlementDO.setSettlementDate(paySettlementBO.getSettlementDate());
        paySettlementDO.setCheckStatus(ErrorTypeEnum.PAYMENT_SHORT.name());
        paySettlementDO.setCheckDate(checkDate);
        paySettlementDO.setCheckCompleteTime(DateTimeUtil.getCurrentDateTimeStr());
        paySettlementDO.setTmSmp(DateTimeUtil.getCurrentDateTimeStr());
        paySettlementExtDao.updatePaymentShort(paySettlementDO);
    }

    @Override
    public void updateRefundLog(PaySettlementBO paySettlementBO, String checkDate) {
        PaySettlementDO paySettlementDO = new PaySettlementDO();
        paySettlementDO.setOutOrderNo(paySettlementBO.getOutOrderNo());
        paySettlementDO.setRefundOrderNo(paySettlementBO.getRefundOrderNo());
        paySettlementDO.setSettlementDate(paySettlementBO.getSettlementDate());
        paySettlementDO.setCheckStatus(ErrorTypeEnum.REFUND_LONG.name());
        paySettlementDO.setCheckDate(checkDate);
        paySettlementDO.setCheckCompleteTime(DateTimeUtil.getCurrentDateTimeStr());
        paySettlementDO.setTmSmp(DateTimeUtil.getCurrentDateTimeStr());
        paySettlementExtDao.updateRefundLog(paySettlementDO);
    }

    private PaySettlementDO getPaymentSettlement(PayOrderDBBO payOrderDBBO) {
        PaySettlementDO paySettlementDO = new PaySettlementDO();
        paySettlementDO.setOutOrderNo(payOrderDBBO.getOutOrderNo());
        paySettlementDO.setTradeJrnNo(payOrderDBBO.getTradeJrnNo());
        paySettlementDO.setRefundOrderNo(SettlementConstants.DEFAULT_REFUND_NO);
        paySettlementDO.setSettlementDate(DateTimeUtil.getCurrentDateStr());
        paySettlementDO.setOrderDate(payOrderDBBO.getOrderDate());
        paySettlementDO.setOrderTime(payOrderDBBO.getOrderTime());
        paySettlementDO.setChannelCode(payOrderDBBO.getChannelCode());
        paySettlementDO.setPayWay(payOrderDBBO.getPayWay());
        paySettlementDO.setScene(payOrderDBBO.getScene());
        paySettlementDO.setBusType(payOrderDBBO.getBusType());
        paySettlementDO.setMerchantNo(payOrderDBBO.getMerchantNo());
        paySettlementDO.setOrgMerchantNo(payOrderDBBO.getOrgMerchantNo());
        paySettlementDO.setWalletId(payOrderDBBO.getWalletId());
        paySettlementDO.setAccountDate(payOrderDBBO.getAccountDate());
        paySettlementDO.setOrderAmount(payOrderDBBO.getOrderAmount());
        paySettlementDO.setBankOrderNo(payOrderDBBO.getBankOrderNo());
        paySettlementDO.setSettlementType(OrderTypeEnum.PAYMENT.name());
        paySettlementDO.setCheckStatus(CheckStatusEnum.WAIT.name());
        paySettlementDO.setOrderCompleteTime(payOrderDBBO.getOrderCompleteTime());
        paySettlementDO.setRemark(payOrderDBBO.getExtra());
        return paySettlementDO;
    }


    private PaySettlementDO getRefundSettlement(RefundOrderDBBO refundOrderDBBO) {
        PaySettlementDO settlementDO = new PaySettlementDO();
        settlementDO.setOutOrderNo(refundOrderDBBO.getOutOrderNo());
        settlementDO.setTradeJrnNo(refundOrderDBBO.getRefundOrderNo());
        settlementDO.setRefundOrderNo(refundOrderDBBO.getRefundOrderNo());
        settlementDO.setSettlementDate(DateTimeUtil.getCurrentDateStr());
        settlementDO.setOrderDate(refundOrderDBBO.getRefundDate());
        settlementDO.setOrderTime(refundOrderDBBO.getRefundTime());
        settlementDO.setChannelCode(refundOrderDBBO.getChannelCode());
        settlementDO.setPayWay(refundOrderDBBO.getPayWay());
        settlementDO.setScene(refundOrderDBBO.getScene());
        settlementDO.setBusType(refundOrderDBBO.getBusType());
        settlementDO.setMerchantNo(refundOrderDBBO.getMerchantNo());
        settlementDO.setOrgMerchantNo(refundOrderDBBO.getOrgMerchantNo());
        settlementDO.setWalletId(refundOrderDBBO.getWalletId());
        settlementDO.setAccountDate(refundOrderDBBO.getAccountDate());
        settlementDO.setOrderAmount(refundOrderDBBO.getRefundAmount());
        settlementDO.setBankOrderNo(refundOrderDBBO.getBankRefundNo());
        settlementDO.setSettlementType(OrderTypeEnum.REFUND.name());
        settlementDO.setCheckStatus(CheckStatusEnum.WAIT.name());
        return settlementDO;
    }


}
