package com.cmpay.dceppay.service.check.impl;

import com.cmpay.dceppay.bo.check.CheckErrorBO;
import com.cmpay.dceppay.dao.check.ICheckErrorDetailExtDao;
import com.cmpay.dceppay.entity.check.CheckErrorDetailDO;
import com.cmpay.dceppay.entity.check.CheckErrorDetailDOKey;
import com.cmpay.dceppay.service.check.ICheckErrorService;
import com.cmpay.dceppay.util.BeanConvertUtils;
import com.cmpay.dceppay.util.DateTimeUtil;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/24 19:01
 */
@Service
public class CheckErrorServiceImpl implements ICheckErrorService {
    @Autowired
    private ICheckErrorDetailExtDao checkErrorDetailExtDao;

    @Override
    public void addError(CheckErrorBO checkErrorBO) {
        CheckErrorDetailDO errorDetailDO = new CheckErrorDetailDO();
        BeanUtils.copyProperties(errorDetailDO, checkErrorBO);
        errorDetailDO.setTmSmp(DateTimeUtil.getCurrentDateTimeStr());
        checkErrorDetailExtDao.insert(errorDetailDO);

    }

    @Override
    public CheckErrorBO getByKey(String outTradeNo, String outRefundNo) {
        CheckErrorDetailDOKey errorDetailDOKey = new CheckErrorDetailDOKey();
        errorDetailDOKey.setOutOrderNo(outTradeNo);
        errorDetailDOKey.setRefundOrderNo(outRefundNo);
        CheckErrorDetailDO errorDetailDO = checkErrorDetailExtDao.get(errorDetailDOKey);

        if (JudgeUtils.isNull(errorDetailDO)) {
            return null;
        }
        return BeanUtils.copyPropertiesReturnDest(new CheckErrorBO(), errorDetailDO);
    }


    @Override
    public List<CheckErrorBO> findDoubleList(String lastCheckDate) {
        List<CheckErrorDetailDO> list = checkErrorDetailExtDao.findDoubleList(lastCheckDate);
        if (JudgeUtils.isNull(list)) {
            return new ArrayList<>();
        }
        return BeanConvertUtils.convertList(list, CheckErrorBO.class);
    }

    @Override
    public void updateDoubleComplete(CheckErrorBO checkErrorBO) {
        CheckErrorDetailDO errorDetailDO = new CheckErrorDetailDO();
        BeanUtils.copyProperties(errorDetailDO, checkErrorBO);
        errorDetailDO.setErrorProcessTime(DateTimeUtil.getCurrentDateTimeStr());
        errorDetailDO.setTmSmp(DateTimeUtil.getCurrentDateTimeStr());
        checkErrorDetailExtDao.updateDoubleComplete(errorDetailDO);
    }

    @Override
    public void updatePaymentShort(CheckErrorBO checkErrorBO) {
        CheckErrorDetailDO errorDetailDO = new CheckErrorDetailDO();
        BeanUtils.copyProperties(errorDetailDO, checkErrorBO);
        errorDetailDO.setTmSmp(DateTimeUtil.getCurrentDateTimeStr());
        checkErrorDetailExtDao.updatePaymentShort(errorDetailDO);
    }

    @Override
    public void updateAmountError(CheckErrorBO checkErrorBO) {
        CheckErrorDetailDO errorDetailDO = new CheckErrorDetailDO();
        BeanUtils.copyProperties(errorDetailDO, checkErrorBO);
        errorDetailDO.setTmSmp(DateTimeUtil.getCurrentDateTimeStr());
        checkErrorDetailExtDao.updateAmountError(errorDetailDO);
    }

    @Override
    public void updateRefundLong(CheckErrorBO checkErrorBO) {
        CheckErrorDetailDO errorDetailDO = new CheckErrorDetailDO();
        BeanUtils.copyProperties(errorDetailDO, checkErrorBO);
        errorDetailDO.setTmSmp(DateTimeUtil.getCurrentDateTimeStr());
        checkErrorDetailExtDao.updateRefundLong(errorDetailDO);
    }

    @Override
    public int cancelError(CheckErrorBO checkErrorBO) {
        CheckErrorDetailDO errorDetailDO = new CheckErrorDetailDO();
        BeanUtils.copyProperties(errorDetailDO, checkErrorBO);
        errorDetailDO.setTmSmp(DateTimeUtil.getCurrentDateTimeStr());
        return checkErrorDetailExtDao.cancelError(errorDetailDO);
    }

    @Override
    public int updateAmount(CheckErrorBO checkErrorBO) {
        CheckErrorDetailDO errorDetailDO = new CheckErrorDetailDO();
        BeanUtils.copyProperties(errorDetailDO, checkErrorBO);
        errorDetailDO.setTmSmp(DateTimeUtil.getCurrentDateTimeStr());
        return checkErrorDetailExtDao.updateAmount(errorDetailDO);
    }

    @Override
    public int cancelRefund(CheckErrorBO checkErrorBO) {
        CheckErrorDetailDO errorDetailDO = new CheckErrorDetailDO();
        BeanUtils.copyProperties(errorDetailDO, checkErrorBO);
        errorDetailDO.setTmSmp(DateTimeUtil.getCurrentDateTimeStr());
        return checkErrorDetailExtDao.cancelRefund(errorDetailDO);
    }

}
