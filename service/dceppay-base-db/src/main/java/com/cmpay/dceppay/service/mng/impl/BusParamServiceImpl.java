package com.cmpay.dceppay.service.mng.impl;

import com.cmpay.dceppay.bo.mng.BusParamBO;
import com.cmpay.dceppay.constant.mng.BusParamTypeConstants;
import com.cmpay.dceppay.constant.pay.PaymentConstants;
import com.cmpay.dceppay.dao.mng.IBusParamExtDao;
import com.cmpay.dceppay.entity.mng.BusParamDO;
import com.cmpay.dceppay.service.mng.IBusParamService;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.cache.redis.RedisCacheable;
import com.cmpay.lemon.framework.datasource.TargetDataSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/10/8 14:11
 */
@Service
public class BusParamServiceImpl implements IBusParamService {
    @Autowired
    private IBusParamExtDao paramExtDao;


    @Override
    @TargetDataSource("dcepmng")
    @RedisCacheable(cacheNames = " BusParam", key = "'REFUND_LIMIT_DAY:'")
    public int getRefundLimitDay() {
        try {
            BusParamDO busParamDO = paramExtDao.getByParamType(BusParamTypeConstants.REFUND_LIMIT_DAY);
            if (JudgeUtils.isNotNull(busParamDO)) {
                return Integer.parseInt(busParamDO.getParamCode());
            }
        } catch (NumberFormatException e) {
            return PaymentConstants.REFUND_LIMIT_DAYS;
        }
        return PaymentConstants.REFUND_LIMIT_DAYS;
    }

    @Override
    @TargetDataSource("dcepmng")
    @RedisCacheable(cacheNames = " BusParam", key = "'NOTIFY_MAX_TIME:'")
    public int getMaxNotifyCount() {
        try {
            BusParamDO busParamDO = paramExtDao.getByParamType(BusParamTypeConstants.NOTIFY_MAX_TIME);
            if (JudgeUtils.isNotNull(busParamDO)) {
                return Integer.parseInt(busParamDO.getParamCode());
            }
        } catch (NumberFormatException e) {
            return PaymentConstants.NOTIFY_MAX_TIME;
        }
        return PaymentConstants.NOTIFY_MAX_TIME;
    }


}
