/*
 * @ClassName CheckGenFileStatusDO
 * @Description 
 * @version 1.0
 * @Date 2024-09-03 16:03:09
 */
package com.cmpay.dceppay.entity.check;

import com.cmpay.framework.data.BaseDO;
import com.cmpay.lemon.framework.annotation.DataObject;
import java.math.BigDecimal;

@DataObject
public class CheckGenFileStatusDO extends BaseDO {
    /**
     * @Fields checkFileDate 对账日期
     */
    private String checkFileDate;
    /**
     * @Fields channelCode 渠道号
     */
    private String channelCode;
    /**
     * @Fields totalCount 总笔数
     */
    private Integer totalCount;
    /**
     * @Fields totalAmount 总金额
     */
    private BigDecimal totalAmount;
    /**
     * @Fields paymentCount 支付总笔数
     */
    private Integer paymentCount;
    /**
     * @Fields paymentAmount 支付总金额
     */
    private BigDecimal paymentAmount;
    /**
     * @Fields refundCount 退款总笔数
     */
    private Integer refundCount;
    /**
     * @Fields refundAmount 退款总金额
     */
    private BigDecimal refundAmount;
    /**
     * @Fields fileStartTime 文件生成开始时间
     */
    private String fileStartTime;
    /**
     * @Fields fileEndTime 文件生成结束时间
     */
    private String fileEndTime;
    /**
     * @Fields fileSendTime 文件推送时间
     */
    private String fileSendTime;
    /**
     * @Fields fileStatus 文件状态 文件生成中：file_process, 对账文件生成完成：file_success, 对账文件生成失败：file_fail, 文件已发送：file_send_success, 文件发送失败：file_send_fail
     */
    private String fileStatus;
    /**
     * @Fields tmSmp 最后更新时间
     */
    private String tmSmp;

    public String getCheckFileDate() {
        return checkFileDate;
    }

    public void setCheckFileDate(String checkFileDate) {
        this.checkFileDate = checkFileDate;
    }

    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }

    public Integer getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public Integer getPaymentCount() {
        return paymentCount;
    }

    public void setPaymentCount(Integer paymentCount) {
        this.paymentCount = paymentCount;
    }

    public BigDecimal getPaymentAmount() {
        return paymentAmount;
    }

    public void setPaymentAmount(BigDecimal paymentAmount) {
        this.paymentAmount = paymentAmount;
    }

    public Integer getRefundCount() {
        return refundCount;
    }

    public void setRefundCount(Integer refundCount) {
        this.refundCount = refundCount;
    }

    public BigDecimal getRefundAmount() {
        return refundAmount;
    }

    public void setRefundAmount(BigDecimal refundAmount) {
        this.refundAmount = refundAmount;
    }

    public String getFileStartTime() {
        return fileStartTime;
    }

    public void setFileStartTime(String fileStartTime) {
        this.fileStartTime = fileStartTime;
    }

    public String getFileEndTime() {
        return fileEndTime;
    }

    public void setFileEndTime(String fileEndTime) {
        this.fileEndTime = fileEndTime;
    }

    public String getFileSendTime() {
        return fileSendTime;
    }

    public void setFileSendTime(String fileSendTime) {
        this.fileSendTime = fileSendTime;
    }

    public String getFileStatus() {
        return fileStatus;
    }

    public void setFileStatus(String fileStatus) {
        this.fileStatus = fileStatus;
    }

    public String getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(String tmSmp) {
        this.tmSmp = tmSmp;
    }
}