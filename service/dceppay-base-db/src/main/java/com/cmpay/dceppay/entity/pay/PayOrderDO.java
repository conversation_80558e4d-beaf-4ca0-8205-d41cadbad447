/*
 * @ClassName PayOrderDO
 * @Description 
 * @version 1.0
 * @Date 2024-09-26 17:10:18
 */
package com.cmpay.dceppay.entity.pay;

import com.cmpay.framework.data.BaseDO;
import com.cmpay.lemon.framework.annotation.DataObject;
import java.math.BigDecimal;

@DataObject
public class PayOrderDO extends BaseDO {
    /**
     * @Fields outOrderNo 交易订单号
     */
    private String outOrderNo;
    /**
     * @Fields tradeJrnNo 
     */
    private String tradeJrnNo;
    /**
     * @Fields orderDate 订单提交日期
     */
    private String orderDate;
    /**
     * @Fields orderTime 订单提交时间
     */
    private String orderTime;
    /**
     * @Fields orderTimeExpire 订单失效时间
     */
    private String orderTimeExpire;
    /**
     * @Fields channelCode 支付渠道号
     */
    private String channelCode;
    /**
     * @Fields payWay 支付方式
     */
    private String payWay;
    /**
     * @Fields scene 支付场景
     */
    private String scene;
    /**
     * @Fields orderType 下单类型
     */
    private String orderType;
    /**
     * @Fields accountDate 会计日期
     */
    private String accountDate;
    /**
     * @Fields productName 商品名称
     */
    private String productName;
    /**
     * @Fields productDesc 商品描述
     */
    private String productDesc;
    /**
     * @Fields orderAmount 订单总金额
     */
    private BigDecimal orderAmount;
    /**
     * @Fields busType 商户业务类型
     */
    private String busType;
    /**
     * @Fields merchantNo 商户编号
     */
    private String merchantNo;
    /**
     * @Fields orgMerchantNo 运营机构商户号
     */
    private String orgMerchantNo;
    /**
     * @Fields walletId 商户钱包id
     */
    private String walletId;
    /**
     * @Fields bizType 业务类型
     */
    private String bizType;
    /**
     * @Fields bizCategory 业务种类
     */
    private String bizCategory;
    /**
     * @Fields terminalNo 终端编号
     */
    private String terminalNo;
    /**
     * @Fields terminalIp 终端ip
     */
    private String terminalIp;
    /**
     * @Fields terminalLocation 终端地理位置
     */
    private String terminalLocation;
    /**
     * @Fields notifyUrl 通知url
     */
    private String notifyUrl;
    /**
     * @Fields pageNotifyUrl 页面通知url
     */
    private String pageNotifyUrl;
    /**
     * @Fields status 订单状态
     */
    private String status;
    /**
     * @Fields bankOrderNo 支付机构订单号
     */
    private String bankOrderNo;
    /**
     * @Fields errMsgCd 错误码
     */
    private String errMsgCd;
    /**
     * @Fields errMsgInfo 错误码信息
     */
    private String errMsgInfo;
    /**
     * @Fields orderCompleteTime 订单完成时间
     */
    private String orderCompleteTime;
    /**
     * @Fields receiveNotifyTime 接收通知时间
     */
    private String receiveNotifyTime;
    /**
     * @Fields refundTimes 退款次数
     */
    private Integer refundTimes;
    /**
     * @Fields successRefundAmount 成功退款金额
     */
    private BigDecimal successRefundAmount;
    /**
     * @Fields notifyStatus 通知状态
     */
    private String notifyStatus;
    /**
     * @Fields sendNotifyTime 下发通知时间
     */
    private String sendNotifyTime;
    /**
     * @Fields extra 保留字段
     */
    private String extra;
    /**
     * @Fields messageIdentification 报文标识号
     */
    private String messageIdentification;
    /**
     * @Fields tmSmp 最后更新时间
     */
    private String tmSmp;

    public String getOutOrderNo() {
        return outOrderNo;
    }

    public void setOutOrderNo(String outOrderNo) {
        this.outOrderNo = outOrderNo;
    }

    public String getTradeJrnNo() {
        return tradeJrnNo;
    }

    public void setTradeJrnNo(String tradeJrnNo) {
        this.tradeJrnNo = tradeJrnNo;
    }

    public String getOrderDate() {
        return orderDate;
    }

    public void setOrderDate(String orderDate) {
        this.orderDate = orderDate;
    }

    public String getOrderTime() {
        return orderTime;
    }

    public void setOrderTime(String orderTime) {
        this.orderTime = orderTime;
    }

    public String getOrderTimeExpire() {
        return orderTimeExpire;
    }

    public void setOrderTimeExpire(String orderTimeExpire) {
        this.orderTimeExpire = orderTimeExpire;
    }

    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }

    public String getPayWay() {
        return payWay;
    }

    public void setPayWay(String payWay) {
        this.payWay = payWay;
    }

    public String getScene() {
        return scene;
    }

    public void setScene(String scene) {
        this.scene = scene;
    }

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public String getAccountDate() {
        return accountDate;
    }

    public void setAccountDate(String accountDate) {
        this.accountDate = accountDate;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getProductDesc() {
        return productDesc;
    }

    public void setProductDesc(String productDesc) {
        this.productDesc = productDesc;
    }

    public BigDecimal getOrderAmount() {
        return orderAmount;
    }

    public void setOrderAmount(BigDecimal orderAmount) {
        this.orderAmount = orderAmount;
    }

    public String getBusType() {
        return busType;
    }

    public void setBusType(String busType) {
        this.busType = busType;
    }

    public String getMerchantNo() {
        return merchantNo;
    }

    public void setMerchantNo(String merchantNo) {
        this.merchantNo = merchantNo;
    }

    public String getOrgMerchantNo() {
        return orgMerchantNo;
    }

    public void setOrgMerchantNo(String orgMerchantNo) {
        this.orgMerchantNo = orgMerchantNo;
    }

    public String getWalletId() {
        return walletId;
    }

    public void setWalletId(String walletId) {
        this.walletId = walletId;
    }

    public String getBizType() {
        return bizType;
    }

    public void setBizType(String bizType) {
        this.bizType = bizType;
    }

    public String getBizCategory() {
        return bizCategory;
    }

    public void setBizCategory(String bizCategory) {
        this.bizCategory = bizCategory;
    }

    public String getTerminalNo() {
        return terminalNo;
    }

    public void setTerminalNo(String terminalNo) {
        this.terminalNo = terminalNo;
    }

    public String getTerminalIp() {
        return terminalIp;
    }

    public void setTerminalIp(String terminalIp) {
        this.terminalIp = terminalIp;
    }

    public String getTerminalLocation() {
        return terminalLocation;
    }

    public void setTerminalLocation(String terminalLocation) {
        this.terminalLocation = terminalLocation;
    }

    public String getNotifyUrl() {
        return notifyUrl;
    }

    public void setNotifyUrl(String notifyUrl) {
        this.notifyUrl = notifyUrl;
    }

    public String getPageNotifyUrl() {
        return pageNotifyUrl;
    }

    public void setPageNotifyUrl(String pageNotifyUrl) {
        this.pageNotifyUrl = pageNotifyUrl;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getBankOrderNo() {
        return bankOrderNo;
    }

    public void setBankOrderNo(String bankOrderNo) {
        this.bankOrderNo = bankOrderNo;
    }

    public String getErrMsgCd() {
        return errMsgCd;
    }

    public void setErrMsgCd(String errMsgCd) {
        this.errMsgCd = errMsgCd;
    }

    public String getErrMsgInfo() {
        return errMsgInfo;
    }

    public void setErrMsgInfo(String errMsgInfo) {
        this.errMsgInfo = errMsgInfo;
    }

    public String getOrderCompleteTime() {
        return orderCompleteTime;
    }

    public void setOrderCompleteTime(String orderCompleteTime) {
        this.orderCompleteTime = orderCompleteTime;
    }

    public String getReceiveNotifyTime() {
        return receiveNotifyTime;
    }

    public void setReceiveNotifyTime(String receiveNotifyTime) {
        this.receiveNotifyTime = receiveNotifyTime;
    }

    public Integer getRefundTimes() {
        return refundTimes;
    }

    public void setRefundTimes(Integer refundTimes) {
        this.refundTimes = refundTimes;
    }

    public BigDecimal getSuccessRefundAmount() {
        return successRefundAmount;
    }

    public void setSuccessRefundAmount(BigDecimal successRefundAmount) {
        this.successRefundAmount = successRefundAmount;
    }

    public String getNotifyStatus() {
        return notifyStatus;
    }

    public void setNotifyStatus(String notifyStatus) {
        this.notifyStatus = notifyStatus;
    }

    public String getSendNotifyTime() {
        return sendNotifyTime;
    }

    public void setSendNotifyTime(String sendNotifyTime) {
        this.sendNotifyTime = sendNotifyTime;
    }

    public String getExtra() {
        return extra;
    }

    public void setExtra(String extra) {
        this.extra = extra;
    }

    public String getMessageIdentification() {
        return messageIdentification;
    }

    public void setMessageIdentification(String messageIdentification) {
        this.messageIdentification = messageIdentification;
    }

    public String getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(String tmSmp) {
        this.tmSmp = tmSmp;
    }
}