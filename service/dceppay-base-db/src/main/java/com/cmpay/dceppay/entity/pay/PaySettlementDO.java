/*
 * @ClassName PaySettlementDO
 * @Description 
 * @version 1.0
 * @Date 2024-09-27 13:40:54
 */
package com.cmpay.dceppay.entity.pay;

import com.cmpay.framework.data.BaseDO;
import com.cmpay.lemon.framework.annotation.DataObject;
import java.math.BigDecimal;

@DataObject
public class PaySettlementDO extends BaseDO {
    /**
     * @Fields outOrderNo 交易订单号
     */
    private String outOrderNo;
    /**
     * @Fields refundOrderNo 退款订单号
     */
    private String refundOrderNo;
    /**
     * @Fields jrnNo 结算流水号
     */
    private String jrnNo;
    /**
     * @Fields settlementDate 结算日期
     */
    private String settlementDate;
    /**
     * @Fields tradeJrnNo 交易流水号
     */
    private String tradeJrnNo;
    /**
     * @Fields orderDate 订单提交日期
     */
    private String orderDate;
    /**
     * @Fields orderTime 订单提交时间
     */
    private String orderTime;
    /**
     * @Fields channelCode 支付渠道号
     */
    private String channelCode;
    /**
     * @Fields payWay 支付方式
     */
    private String payWay;
    /**
     * @Fields scene 支付场景
     */
    private String scene;
    /**
     * @Fields busType 业务类型
     */
    private String busType;
    /**
     * @Fields merchantNo 商户编号
     */
    private String merchantNo;
    /**
     * @Fields orgMerchantNo 运营机构商户号
     */
    private String orgMerchantNo;
    /**
     * @Fields walletId 商户钱包id
     */
    private String walletId;
    /**
     * @Fields accountDate 会计日期
     */
    private String accountDate;
    /**
     * @Fields orderAmount 交易金额
     */
    private BigDecimal orderAmount;
    /**
     * @Fields bankOrderNo 支付机构订单号
     */
    private String bankOrderNo;
    /**
     * @Fields orderCompleteTime 交易完成时间
     */
    private String orderCompleteTime;
    /**
     * @Fields settlementType 订单结算类型
     */
    private String settlementType;
    /**
     * @Fields checkStatus 对账状态
     */
    private String checkStatus;
    /**
     * @Fields checkDate 账期
     */
    private String checkDate;
    /**
     * @Fields checkCompleteTime 对账完成时间
     */
    private String checkCompleteTime;
    /**
     * @Fields remark 备注
     */
    private String remark;
    /**
     * @Fields tmSmp 最后更新时间
     */
    private String tmSmp;

    public String getOutOrderNo() {
        return outOrderNo;
    }

    public void setOutOrderNo(String outOrderNo) {
        this.outOrderNo = outOrderNo;
    }

    public String getRefundOrderNo() {
        return refundOrderNo;
    }

    public void setRefundOrderNo(String refundOrderNo) {
        this.refundOrderNo = refundOrderNo;
    }

    public String getJrnNo() {
        return jrnNo;
    }

    public void setJrnNo(String jrnNo) {
        this.jrnNo = jrnNo;
    }

    public String getSettlementDate() {
        return settlementDate;
    }

    public void setSettlementDate(String settlementDate) {
        this.settlementDate = settlementDate;
    }

    public String getTradeJrnNo() {
        return tradeJrnNo;
    }

    public void setTradeJrnNo(String tradeJrnNo) {
        this.tradeJrnNo = tradeJrnNo;
    }

    public String getOrderDate() {
        return orderDate;
    }

    public void setOrderDate(String orderDate) {
        this.orderDate = orderDate;
    }

    public String getOrderTime() {
        return orderTime;
    }

    public void setOrderTime(String orderTime) {
        this.orderTime = orderTime;
    }

    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }

    public String getPayWay() {
        return payWay;
    }

    public void setPayWay(String payWay) {
        this.payWay = payWay;
    }

    public String getScene() {
        return scene;
    }

    public void setScene(String scene) {
        this.scene = scene;
    }

    public String getBusType() {
        return busType;
    }

    public void setBusType(String busType) {
        this.busType = busType;
    }

    public String getMerchantNo() {
        return merchantNo;
    }

    public void setMerchantNo(String merchantNo) {
        this.merchantNo = merchantNo;
    }

    public String getOrgMerchantNo() {
        return orgMerchantNo;
    }

    public void setOrgMerchantNo(String orgMerchantNo) {
        this.orgMerchantNo = orgMerchantNo;
    }

    public String getWalletId() {
        return walletId;
    }

    public void setWalletId(String walletId) {
        this.walletId = walletId;
    }

    public String getAccountDate() {
        return accountDate;
    }

    public void setAccountDate(String accountDate) {
        this.accountDate = accountDate;
    }

    public BigDecimal getOrderAmount() {
        return orderAmount;
    }

    public void setOrderAmount(BigDecimal orderAmount) {
        this.orderAmount = orderAmount;
    }

    public String getBankOrderNo() {
        return bankOrderNo;
    }

    public void setBankOrderNo(String bankOrderNo) {
        this.bankOrderNo = bankOrderNo;
    }

    public String getOrderCompleteTime() {
        return orderCompleteTime;
    }

    public void setOrderCompleteTime(String orderCompleteTime) {
        this.orderCompleteTime = orderCompleteTime;
    }

    public String getSettlementType() {
        return settlementType;
    }

    public void setSettlementType(String settlementType) {
        this.settlementType = settlementType;
    }

    public String getCheckStatus() {
        return checkStatus;
    }

    public void setCheckStatus(String checkStatus) {
        this.checkStatus = checkStatus;
    }

    public String getCheckDate() {
        return checkDate;
    }

    public void setCheckDate(String checkDate) {
        this.checkDate = checkDate;
    }

    public String getCheckCompleteTime() {
        return checkCompleteTime;
    }

    public void setCheckCompleteTime(String checkCompleteTime) {
        this.checkCompleteTime = checkCompleteTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(String tmSmp) {
        this.tmSmp = tmSmp;
    }
}