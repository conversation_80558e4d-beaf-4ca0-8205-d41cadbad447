package com.cmpay.dceppay.bo.smartmerchant;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/8/29 9:10
 */
@Data
public class ICBCMerchantUpdateStatusBO {
    /**
     * @Fields merchantId 商户编号
     */
    private String merchantId;
    /**
     * @Fields operatorMerchantId 运营机构商户号
     */
    private String operatorMerchantId;
    /**
     * @Fields onboardStatus 进件状态
     */
    private String onboardStatus;
    /**
     * @Fields serviceStatus 商户状态
     */
    private String serviceStatus;
    /**
     * @Fields onboardId 进件id
     */
    private String onboardId;
    /**
     * @Fields onboardTime 进件时间
     */
    private String onboardTime;
    /**
     * @Fields msgCode 错误码
     */
    private String msgCode;
    /**
     * @Fields msgInfo 错误信息
     */
    private String msgInfo;
    /**
     *  更新人
     */
    private String updateId;
    /**
     * @Fields updateTime 更新时间
     */
    private String updateTime;
    /**
     * @Fields walletId 结算钱包id
     */
    private String walletId;
    /**
     * @Fields walletName 结算钱包名称
     */
    private String walletName;
    /**
     * @Fields merchantWalletName 商户伞底钱包名称
     */
    private String merchantWalletName;
    /**
     * @Fields merchantWalletId 商户伞底钱包ID
     */
    private String merchantWalletId;
    /**
     * @Fields protocolWalletName 协议伞顶钱包名称
     */
    private String protocolWalletName;
    /**
     * @Fields protocolWalletId 协议伞顶钱包ID
     */
    private String protocolWalletId;
}
