package com.cmpay.dceppay.service.smartmerchant.impl;

import com.cmpay.dceppay.bo.smartmerchant.ICBCMerchantBO;
import com.cmpay.dceppay.bo.smartmerchant.ICBCMerchantUpdateStatusBO;
import com.cmpay.dceppay.dao.smartmerchant.IICBCMerchantExtDao;
import com.cmpay.dceppay.entity.smartmerchant.ICBCMerchantDO;
import com.cmpay.dceppay.enums.common.StatusEnum;
import com.cmpay.dceppay.enums.icbc.MerchantOnboardStatusEnum;
import com.cmpay.dceppay.service.smartmerchant.IICBCMerchantDBService;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/8/28 16:48
 */
@Service
public class ICBCMerchantServiceImpl implements IICBCMerchantDBService {
    @Autowired
    private IICBCMerchantExtDao merchantExtDao;

    @Override
    public ICBCMerchantBO getMerchantByMerchantID(String merchantId) {
        ICBCMerchantDO merchantDO = merchantExtDao.getMerchantByMerchantID(merchantId);
        ICBCMerchantBO merchantBO=new ICBCMerchantBO();
        if (JudgeUtils.isNotNull(merchantDO)) {
            BeanUtils.copyProperties(merchantBO, merchantDO);
            return merchantBO;
        }
        return null;
    }

    @Override
    public int updateOnboarding(ICBCMerchantUpdateStatusBO updateBO) {
        ICBCMerchantDO merchantDO = new ICBCMerchantDO();
        merchantDO.setMerchantId(updateBO.getMerchantId());
        merchantDO.setOnboardStatus(MerchantOnboardStatusEnum.ONBOARDING.getStatus());
        merchantDO.setUpdateId(updateBO.getUpdateId());
        merchantDO.setUpdateTime(DateTimeUtils.getCurrentDateTimeStr());
        return merchantExtDao.updateOnboarding(merchantDO);
    }

    @Override
    public int updateOnboardFail(ICBCMerchantUpdateStatusBO updateBO) {
        ICBCMerchantDO merchantDO = new ICBCMerchantDO();
        merchantDO.setMerchantId(updateBO.getMerchantId());
        merchantDO.setOnboardStatus(MerchantOnboardStatusEnum.ONBOARD_FAIL.getStatus());
        merchantDO.setUpdateId(updateBO.getUpdateId());
        merchantDO.setUpdateTime(DateTimeUtils.getCurrentDateTimeStr());
        merchantDO.setOnboardTime(DateTimeUtils.getCurrentDateTimeStr());
        merchantDO.setMsgCode(updateBO.getMsgCode());
        merchantDO.setMsgInfo(updateBO.getMsgInfo());
        merchantDO.setOnboardId(updateBO.getOnboardId());
        return merchantExtDao.updateOnboardFail(merchantDO);
    }

    @Override
    public int updateOnboardSuccess(ICBCMerchantUpdateStatusBO updateBO) {
        ICBCMerchantDO merchantDO = new ICBCMerchantDO();
        BeanUtils.copyProperties(merchantDO,updateBO);
        merchantDO.setOnboardStatus(MerchantOnboardStatusEnum.ONBOARD_SUCCESS.getStatus());
        merchantDO.setOnboardTime(DateTimeUtils.getCurrentDateTimeStr());
        merchantDO.setUpdateTime(DateTimeUtils.getCurrentDateTimeStr());
        return merchantExtDao.updateOnboardSuccess(merchantDO);
    }
    @Override
    public int updateOnboardCanceling(ICBCMerchantUpdateStatusBO updateBO) {
        ICBCMerchantDO merchantDO = new ICBCMerchantDO();
        merchantDO.setMerchantId(updateBO.getMerchantId());
        merchantDO.setOnboardStatus(MerchantOnboardStatusEnum.ONBOARD_CANCELING.getStatus());
        merchantDO.setUpdateTime(DateTimeUtils.getCurrentDateTimeStr());
        return merchantExtDao.updateOnboardCanceling(merchantDO);
    }
    @Override
    public int updateOnboardCancelFail(ICBCMerchantUpdateStatusBO updateBO) {
        ICBCMerchantDO merchantDO = new ICBCMerchantDO();
        merchantDO.setMerchantId(updateBO.getMerchantId());
        merchantDO.setOnboardStatus(MerchantOnboardStatusEnum.CANCEL_FAIL.getStatus());
        merchantDO.setUpdateId(updateBO.getUpdateId());
        merchantDO.setUpdateTime(DateTimeUtils.getCurrentDateTimeStr());
        return merchantExtDao.updateOnboardCancelFail(merchantDO);
    }
    @Override
    public int updateOnboardCancel(ICBCMerchantUpdateStatusBO updateBO) {
        ICBCMerchantDO merchantDO = new ICBCMerchantDO();
        merchantDO.setMerchantId(updateBO.getMerchantId());
        merchantDO.setOnboardStatus(MerchantOnboardStatusEnum.ONBOARD_CANCELED.getStatus());
        merchantDO.setServiceStatus(StatusEnum.DISABLED.getStatus());
        merchantDO.setUpdateId(updateBO.getUpdateId());
        merchantDO.setUpdateTime(DateTimeUtils.getCurrentDateTimeStr());
        return merchantExtDao.updateOnboardCancel(merchantDO);
    }

}
