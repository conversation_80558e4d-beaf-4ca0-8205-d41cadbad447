/*
 * @ClassName CheckErrorDetailDO
 * @Description 
 * @version 1.0
 * @Date 2024-09-29 11:34:39
 */
package com.cmpay.dceppay.entity.check;

import com.cmpay.framework.data.BaseDO;
import com.cmpay.lemon.framework.annotation.DataObject;
import java.math.BigDecimal;

@DataObject
public class CheckErrorDetailDO extends BaseDO {
    /**
     * @Fields outOrderNo 交易订单号
     */
    private String outOrderNo;
    /**
     * @Fields refundOrderNo 退款订单号
     */
    private String refundOrderNo;
    /**
     * @Fields checkFileDate 对账日期
     */
    private String checkFileDate;
    /**
     * @Fields errorType 差错类型
     */
    private String errorType;
    /**
     * @Fields errorTime 差错时间
     */
    private String errorTime;
    /**
     * @Fields errorHandleType 差错处理方式
     */
    private String errorHandleType;
    /**
     * @Fields errorProcessTime 差错处理时间
     */
    private String errorProcessTime;
    /**
     * @Fields tradeAmount 交易金额
     */
    private BigDecimal tradeAmount;
    /**
     * @Fields checkAmount 对账金额
     */
    private BigDecimal checkAmount;
    /**
     * @Fields errorStatus 差错状态
     */
    private String errorStatus;
    /**
     * @Fields tradeType 交易类型
     */
    private String tradeType;
    /**
     * @Fields merchantNo 商户编号
     */
    private String merchantNo;
    /**
     * @Fields orgMerchantNo 运营机构商户号
     */
    private String orgMerchantNo;
    /**
     * @Fields doubtFlag 存疑表示
     */
    private String doubtFlag;
    /**
     * @Fields remark 备注
     */
    private String remark;
    /**
     * @Fields updateId 操作者
     */
    private String updateId;
    /**
     * @Fields tmSmp 时间戳
     */
    private String tmSmp;

    public String getOutOrderNo() {
        return outOrderNo;
    }

    public void setOutOrderNo(String outOrderNo) {
        this.outOrderNo = outOrderNo;
    }

    public String getRefundOrderNo() {
        return refundOrderNo;
    }

    public void setRefundOrderNo(String refundOrderNo) {
        this.refundOrderNo = refundOrderNo;
    }

    public String getCheckFileDate() {
        return checkFileDate;
    }

    public void setCheckFileDate(String checkFileDate) {
        this.checkFileDate = checkFileDate;
    }

    public String getErrorType() {
        return errorType;
    }

    public void setErrorType(String errorType) {
        this.errorType = errorType;
    }

    public String getErrorTime() {
        return errorTime;
    }

    public void setErrorTime(String errorTime) {
        this.errorTime = errorTime;
    }

    public String getErrorHandleType() {
        return errorHandleType;
    }

    public void setErrorHandleType(String errorHandleType) {
        this.errorHandleType = errorHandleType;
    }

    public String getErrorProcessTime() {
        return errorProcessTime;
    }

    public void setErrorProcessTime(String errorProcessTime) {
        this.errorProcessTime = errorProcessTime;
    }

    public BigDecimal getTradeAmount() {
        return tradeAmount;
    }

    public void setTradeAmount(BigDecimal tradeAmount) {
        this.tradeAmount = tradeAmount;
    }

    public BigDecimal getCheckAmount() {
        return checkAmount;
    }

    public void setCheckAmount(BigDecimal checkAmount) {
        this.checkAmount = checkAmount;
    }

    public String getErrorStatus() {
        return errorStatus;
    }

    public void setErrorStatus(String errorStatus) {
        this.errorStatus = errorStatus;
    }

    public String getTradeType() {
        return tradeType;
    }

    public void setTradeType(String tradeType) {
        this.tradeType = tradeType;
    }

    public String getMerchantNo() {
        return merchantNo;
    }

    public void setMerchantNo(String merchantNo) {
        this.merchantNo = merchantNo;
    }

    public String getOrgMerchantNo() {
        return orgMerchantNo;
    }

    public void setOrgMerchantNo(String orgMerchantNo) {
        this.orgMerchantNo = orgMerchantNo;
    }

    public String getDoubtFlag() {
        return doubtFlag;
    }

    public void setDoubtFlag(String doubtFlag) {
        this.doubtFlag = doubtFlag;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getUpdateId() {
        return updateId;
    }

    public void setUpdateId(String updateId) {
        this.updateId = updateId;
    }

    public String getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(String tmSmp) {
        this.tmSmp = tmSmp;
    }
}