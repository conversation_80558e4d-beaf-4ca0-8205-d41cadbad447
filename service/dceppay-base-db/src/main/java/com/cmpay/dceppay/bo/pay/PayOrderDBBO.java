package com.cmpay.dceppay.bo.pay;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/9/3 17:26
 */
@Data
public class PayOrderDBBO {
    /**
     * @Fields tradeJrnNo
     */
    private String tradeJrnNo;
    /**
     * @Fields outOrderNo 交易订单号
     */
    private String outOrderNo;
    /**
     * @Fields orderDate 订单提交日期
     */
    private String orderDate;
    /**
     * @Fields orderTime 订单提交时间
     */
    private String orderTime;
    /**
     * @Fields orderTimeExpire 订单失效时间
     */
    private String orderTimeExpire;
    /**
     * @Fields busType 商户业务类型
     */
    private String busType;
    /**
     * @Fields merchantNo 商户编号
     */
    private String merchantNo;
    /**
     * @Fields orgMerchantNo 运营机构商户号
     */
    private String orgMerchantNo;
    /**
     * @Fields walletId 商户钱包id
     */
    private String walletId;
    /**
     * @Fields bizType 业务类型
     */
    private String bizType;
    /**
     * @Fields bizCategory 业务种类
     */
    private String bizCategory;
    /**
     * @Fields channelCode 支付渠道号
     */
    private String channelCode;
    /**
     * @Fields payWay 支付方式
     */
    private String payWay;
    /**
     * @Fields scene 支付场景
     */
    private String scene;
    /**
     * @Fields orderType 下单类型
     */
    private String orderType;
    /**
     * @Fields accountDate 会计日期
     */
    private String accountDate;
    /**
     * @Fields productName 商品名称
     */
    private String productName;
    /**
     * @Fields productDesc 商品描述
     */
    private String productDesc;
    /**
     * @Fields orderAmount 订单总金额
     */
    private BigDecimal orderAmount;
    /**
     * @Fields status 订单状态
     */
    private String status;
    /**
     * @Fields bankOrderNo 支付机构订单号
     */
    private String bankOrderNo;
    /**
     * @Fields errMsgCd 错误码
     */
    private String errMsgCd;
    /**
     * @Fields errMsgInfo 错误码信息
     */
    private String errMsgInfo;
    /**
     * @Fields orderCompleteTime 订单完成时间
     */
    private String orderCompleteTime;
    /**
     * @Fields extra 保留字段
     */
    private String extra;
    /**
     * @Fields messageIdentification 报文标识号
     */
    private String messageIdentification;
    /**
     * @Fields terminalNo 终端编号
     */
    private String terminalNo;
    /**
     * @Fields terminalIp 终端ip
     */
    private String terminalIp;
    /**
     * @Fields terminalLocation 终端地理位置
     */
    private String terminalLocation;
    /**
     * @Fields notifyUrl 通知url
     */
    private String notifyUrl;
    /**
     * @Fields pageNotifyUrl 页面通知url
     */
    private String pageNotifyUrl;
    private String receiveNotifyTime;
}
