package com.cmpay.dceppay.bo.pay;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/9/13 15:57
 */
@Data
public class PayAccountDetailBO {
    /**
     * @Fields dcFlag 借贷方向，D 表示借方，C 表示贷方
     */
    private String dcFlag;
    /**
     * @Fields accountNo 账号
     */
    private String accountNo;
    /**
     * @Fields orderDate 交易日期
     */
    private String orderDate;
    /**
     * @Fields accountName 账户名称
     */
    private String accountName;
    /**
     * @Fields amount 金额
     */
    private BigDecimal amount;
}
