/*
 * @ClassName PayNotifyDO
 * @Description 
 * @version 1.0
 * @Date 2024-10-23 16:46:25
 */
package com.cmpay.dceppay.entity.pay;

import com.cmpay.framework.data.BaseDO;
import com.cmpay.lemon.framework.annotation.DataObject;
import java.math.BigDecimal;

@DataObject
public class PayNotifyDO extends BaseDO {
    /**
     * @Fields outOrderNo 交易订单号
     */
    private String outOrderNo;
    /**
     * @Fields refundOrderNo 退款订单号
     */
    private String refundOrderNo;
    /**
     * @Fields notifyDate 登记通知日期
     */
    private String notifyDate;
    /**
     * @Fields notifyTime 登记通知时间
     */
    private String notifyTime;
    /**
     * @Fields tradeJrnNo 交易流水号
     */
    private String tradeJrnNo;
    /**
     * @Fields orderDate 订单提交日期
     */
    private String orderDate;
    /**
     * @Fields orderTime 订单提交时间
     */
    private String orderTime;
    /**
     * @Fields channelCode 支付渠道号
     */
    private String channelCode;
    /**
     * @Fields payWay 支付方式
     */
    private String payWay;
    /**
     * @Fields scene 支付场景
     */
    private String scene;
    /**
     * @Fields busType 业务类型
     */
    private String busType;
    /**
     * @Fields accountDate 会计日期
     */
    private String accountDate;
    /**
     * @Fields orderAmount 订单总金额
     */
    private BigDecimal orderAmount;
    /**
     * @Fields notifyUrl 通知url
     */
    private String notifyUrl;
    /**
     * @Fields bankOrderNo 支付机构订单号
     */
    private String bankOrderNo;
    /**
     * @Fields orderCompleteTime 订单完成时间
     */
    private String orderCompleteTime;
    /**
     * @Fields notifyType 通知类型
     */
    private String notifyType;
    /**
     * @Fields notifyStatus 通知状态
     */
    private String notifyStatus;
    /**
     * @Fields errMsgCd 错误码
     */
    private String errMsgCd;
    /**
     * @Fields errMsgInfo 错误码信息
     */
    private String errMsgInfo;
    /**
     * @Fields notifyCompleteTime 订单通知完成时间
     */
    private String notifyCompleteTime;
    /**
     * @Fields notifyCount 通知次数
     */
    private Integer notifyCount;
    /**
     * @Fields remark 保留字段
     */
    private String remark;

    public String getOutOrderNo() {
        return outOrderNo;
    }

    public void setOutOrderNo(String outOrderNo) {
        this.outOrderNo = outOrderNo;
    }

    public String getRefundOrderNo() {
        return refundOrderNo;
    }

    public void setRefundOrderNo(String refundOrderNo) {
        this.refundOrderNo = refundOrderNo;
    }

    public String getNotifyDate() {
        return notifyDate;
    }

    public void setNotifyDate(String notifyDate) {
        this.notifyDate = notifyDate;
    }

    public String getNotifyTime() {
        return notifyTime;
    }

    public void setNotifyTime(String notifyTime) {
        this.notifyTime = notifyTime;
    }

    public String getTradeJrnNo() {
        return tradeJrnNo;
    }

    public void setTradeJrnNo(String tradeJrnNo) {
        this.tradeJrnNo = tradeJrnNo;
    }

    public String getOrderDate() {
        return orderDate;
    }

    public void setOrderDate(String orderDate) {
        this.orderDate = orderDate;
    }

    public String getOrderTime() {
        return orderTime;
    }

    public void setOrderTime(String orderTime) {
        this.orderTime = orderTime;
    }

    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }

    public String getPayWay() {
        return payWay;
    }

    public void setPayWay(String payWay) {
        this.payWay = payWay;
    }

    public String getScene() {
        return scene;
    }

    public void setScene(String scene) {
        this.scene = scene;
    }

    public String getBusType() {
        return busType;
    }

    public void setBusType(String busType) {
        this.busType = busType;
    }

    public String getAccountDate() {
        return accountDate;
    }

    public void setAccountDate(String accountDate) {
        this.accountDate = accountDate;
    }

    public BigDecimal getOrderAmount() {
        return orderAmount;
    }

    public void setOrderAmount(BigDecimal orderAmount) {
        this.orderAmount = orderAmount;
    }

    public String getNotifyUrl() {
        return notifyUrl;
    }

    public void setNotifyUrl(String notifyUrl) {
        this.notifyUrl = notifyUrl;
    }

    public String getBankOrderNo() {
        return bankOrderNo;
    }

    public void setBankOrderNo(String bankOrderNo) {
        this.bankOrderNo = bankOrderNo;
    }

    public String getOrderCompleteTime() {
        return orderCompleteTime;
    }

    public void setOrderCompleteTime(String orderCompleteTime) {
        this.orderCompleteTime = orderCompleteTime;
    }

    public String getNotifyType() {
        return notifyType;
    }

    public void setNotifyType(String notifyType) {
        this.notifyType = notifyType;
    }

    public String getNotifyStatus() {
        return notifyStatus;
    }

    public void setNotifyStatus(String notifyStatus) {
        this.notifyStatus = notifyStatus;
    }

    public String getErrMsgCd() {
        return errMsgCd;
    }

    public void setErrMsgCd(String errMsgCd) {
        this.errMsgCd = errMsgCd;
    }

    public String getErrMsgInfo() {
        return errMsgInfo;
    }

    public void setErrMsgInfo(String errMsgInfo) {
        this.errMsgInfo = errMsgInfo;
    }

    public String getNotifyCompleteTime() {
        return notifyCompleteTime;
    }

    public void setNotifyCompleteTime(String notifyCompleteTime) {
        this.notifyCompleteTime = notifyCompleteTime;
    }

    public Integer getNotifyCount() {
        return notifyCount;
    }

    public void setNotifyCount(Integer notifyCount) {
        this.notifyCount = notifyCount;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}