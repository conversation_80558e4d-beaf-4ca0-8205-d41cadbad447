package com.cmpay.dceppay.service.pay;

import com.cmpay.dceppay.bo.pay.PayAccountRecordBO;
import com.cmpay.dceppay.bo.pay.PayOrderCloseUpdateBO;
import com.cmpay.dceppay.bo.pay.PayOrderDBBO;
import com.cmpay.dceppay.bo.pay.PayOrderQueryBO;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/3 17:01
 */
public interface IPayOrderDBService {

    /**
     * 订单号查询
     * @param orderNo
     * @return
     */
    PayOrderDBBO getByOutOrderNo(String orderNo);

    /**
     * 订单登记
     * @param order
     * @return
     */
    int insertOrder(PayOrderDBBO order);

    /**
     * 更新订单状态为订单关闭
     * @param closeUpdateBO 订单更新对象
     * @return更新记录数
     */

    int updateTradeClose(PayOrderCloseUpdateBO closeUpdateBO);


    /**
     * 分片查询等待支付的订单集
     * @param payOrderQueryBO
     * @return
     */
    List<PayOrderDBBO> findWaitPayList(PayOrderQueryBO payOrderQueryBO);

    /**
     * 更新订单状态为支付成功
     * @param payOrderDBBO
     * @return
     */
    int updateTradeSuccess(PayOrderDBBO payOrderDBBO);
    /**
     * 更新订单状态为支付失败
     * @param payOrderDBBO
     * @return
     */
    int updateTradeFail(PayOrderDBBO payOrderDBBO);

    int updateRefundInfo(PayOrderDBBO updateOrderDBBO, BigDecimal refundAmount);

    int updateNotifyInfo(String outOrderN);

}
