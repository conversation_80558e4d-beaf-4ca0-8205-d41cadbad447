package com.cmpay.dceppay.service.pay;

import com.cmpay.dceppay.bo.pay.PayOrderDBBO;
import com.cmpay.dceppay.bo.pay.PaySettlementBO;
import com.cmpay.dceppay.bo.pay.RefundOrderDBBO;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/3 17:02
 */
public interface IPaySettlementDBService {

    PaySettlementBO getByKey(String outOrderNo,String refundOrderNo );


    int registerPaySettlement(PayOrderDBBO settlementBO);


    int registerRefundSettlement(RefundOrderDBBO settlementBO);

    /**
     * 查询结算表结算明细
     *
     * @param queryPaySettlementBO
     * @return
     */
    PaySettlementBO getPaymentSettlement(PaySettlementBO queryPaySettlementBO);

    /**
     * 结算表退款订单明细
     *
     * @param queryPaySettlementBO
     * @return
     */
    PaySettlementBO getRefundSettlement(PaySettlementBO queryPaySettlementBO);


    void updateCheckComplete(PaySettlementBO settlementBO, String checkDate);

    void updateAmountError(PaySettlementBO settlementBO, String checkDate);

    /**
     * 查询等待对账的数据
     * @param checkDate
     * @return
     */

    List<PaySettlementBO> findWaitOrderList(String checkDate);

    /**
     * 查询支付短款存疑对平的结算数据
     * @param settlementBO
     * @return
     */
    PaySettlementBO getPaymentShortDoubt(PaySettlementBO settlementBO);

    /**
     * 查询退款长款存疑对平结算表数据
     * @param settlementBO
     * @return
     */
    PaySettlementBO getRefundLongDoubt(PaySettlementBO settlementBO);

    /**
     * 差错取消对平
     * @param outOrderNo
     * @param refundOrderNo
     */

    void updateCheckErrorComplete(String outOrderNo, String refundOrderNo);

    void updateCheckErrorAmount(String outOrderNo, String refundOrderNo, BigDecimal amount);

    int updateAccountDate(PaySettlementBO settlementBO);

    void updatePaymentShort(PaySettlementBO paySettlementBO, String checkDate);

    void updateRefundLog(PaySettlementBO paySettlementBO, String checkDate);
}
