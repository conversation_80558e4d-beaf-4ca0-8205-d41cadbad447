package com.cmpay.dceppay.dao.pay;

import com.cmpay.dceppay.entity.pay.*;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/2 15:27
 */
@Mapper
public interface IPayOrderExtDao extends IPayOrderDao {
    PayOrderDO getByOutOrderNo(String outOrderNo);

    int updateTradeClose(PayOrderDO payOrderDO);

    /**
     * 订单状态查询
     *
     * @param payOrderExtDO
     * @return
     */
    List<PayOrderDO> findWaitPayList(PayOrderExtDO payOrderExtDO);

    /**
     * 更新订单成功
     *
     * @param payOrderExtDO
     * @return
     */
    int updateTradeSuccess(PayOrderExtDO payOrderExtDO);

    /**
     * 更新订单失败
     *
     * @param payOrderExtDO
     * @return
     */
    int updateTradeFail(PayOrderExtDO payOrderExtDO);

    /**
     * 退款成功更新原支付订饭状态
     * @param refundInfoUpdateDO
     * @return
     */
    int updateRefundInfo(PayOrderRefundInfoUpdateDO refundInfoUpdateDO);

    /**
     * 更新通知下发信息
     * @param notifyInfoUpdateDO
     * @return
     */
    int updateNotifyInfo(PayOrderNotifyInfoUpdateDO notifyInfoUpdateDO);

    void updateAccountDate(PayOrderAccountInfoUpdateDO accountInfoUpdateDO);
}
