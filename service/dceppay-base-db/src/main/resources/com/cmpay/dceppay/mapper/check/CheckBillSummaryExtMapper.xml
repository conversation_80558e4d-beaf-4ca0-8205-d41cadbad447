<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.dceppay.dao.check.ICheckBillSummaryExtDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.dceppay.entity.check.CheckBillSummaryDO" >
        <id column="check_file_date" property="checkFileDate" jdbcType="VARCHAR" />
        <id column="institution_code" property="institutionCode" jdbcType="VARCHAR" />
        <result column="total_count" property="totalCount" jdbcType="INTEGER" />
        <result column="total_amount" property="totalAmount" jdbcType="DECIMAL" />
        <result column="payment_count" property="paymentCount" jdbcType="INTEGER" />
        <result column="payment_amount" property="paymentAmount" jdbcType="DECIMAL" />
        <result column="refund_count" property="refundCount" jdbcType="INTEGER" />
        <result column="refund_amount" property="refundAmount" jdbcType="DECIMAL" />
        <result column="total_success_count" property="totalSuccessCount" jdbcType="INTEGER" />
        <result column="total_success_amount" property="totalSuccessAmount" jdbcType="DECIMAL" />
        <result column="payment_success_count" property="paymentSuccessCount" jdbcType="INTEGER" />
        <result column="payment_success_amount" property="paymentSuccessAmount" jdbcType="DECIMAL" />
        <result column="refund_success_count" property="refundSuccessCount" jdbcType="INTEGER" />
        <result column="refund_success_amount" property="refundSuccessAmount" jdbcType="DECIMAL" />
        <result column="file_count" property="fileCount" jdbcType="INTEGER" />
        <result column="file_path" property="filePath" jdbcType="VARCHAR" />
        <result column="file_name_list" property="fileNameList" jdbcType="VARCHAR" />
        <result column="tm_smp" property="tmSmp" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        check_file_date, institution_code, total_count, total_amount, payment_count, payment_amount,
        refund_count, refund_amount, total_success_count, total_success_amount, payment_success_count,
        payment_success_amount, refund_success_count, refund_success_amount, file_count,
        file_path, file_name_list, tm_smp
    </sql>

    <update id="updateSummaryInfo" parameterType="com.cmpay.dceppay.entity.check.CheckBillSummaryDO" >
        update check_bill_summary
        <set >
            <if test="totalCount != null" >
                total_count = #{totalCount,jdbcType=INTEGER},
            </if>
            <if test="totalAmount != null" >
                total_amount = #{totalAmount,jdbcType=DECIMAL},
            </if>
            <if test="paymentCount != null" >
                payment_count = #{paymentCount,jdbcType=INTEGER},
            </if>
            <if test="paymentAmount != null" >
                payment_amount = #{paymentAmount,jdbcType=DECIMAL},
            </if>
            <if test="refundCount != null" >
                refund_count = #{refundCount,jdbcType=INTEGER},
            </if>
            <if test="refundAmount != null" >
                refund_amount = #{refundAmount,jdbcType=DECIMAL},
            </if>
            <if test="totalSuccessCount != null" >
                total_success_count = #{totalSuccessCount,jdbcType=INTEGER},
            </if>
            <if test="totalSuccessAmount != null" >
                total_success_amount = #{totalSuccessAmount,jdbcType=DECIMAL},
            </if>
            <if test="paymentSuccessCount != null" >
                payment_success_count = #{paymentSuccessCount,jdbcType=INTEGER},
            </if>
            <if test="paymentSuccessAmount != null" >
                payment_success_amount = #{paymentSuccessAmount,jdbcType=DECIMAL},
            </if>
            <if test="refundSuccessCount != null" >
                refund_success_count = #{refundSuccessCount,jdbcType=INTEGER},
            </if>
            <if test="refundSuccessAmount != null" >
                refund_success_amount = #{refundSuccessAmount,jdbcType=DECIMAL},
            </if>
            <if test="fileCount != null" >
                file_count = #{fileCount,jdbcType=INTEGER},
            </if>
            <if test="tmSmp != null" >
                tm_smp = #{tmSmp,jdbcType=VARCHAR},
            </if>
        </set>
        where check_file_date = #{checkFileDate,jdbcType=VARCHAR}
        and institution_code = #{institutionCode,jdbcType=VARCHAR}
    </update>

</mapper>