<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.dceppay.dao.pay.IPayNotifyDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.dceppay.entity.pay.PayNotifyDO" >
        <id column="out_order_no" property="outOrderNo" jdbcType="VARCHAR" />
        <id column="refund_order_no" property="refundOrderNo" jdbcType="VARCHAR" />
        <result column="notify_date" property="notifyDate" jdbcType="VARCHAR" />
        <result column="notify_time" property="notifyTime" jdbcType="VARCHAR" />
        <result column="trade_jrn_no" property="tradeJrnNo" jdbcType="VARCHAR" />
        <result column="order_date" property="orderDate" jdbcType="VARCHAR" />
        <result column="order_time" property="orderTime" jdbcType="VARCHAR" />
        <result column="channel_code" property="channelCode" jdbcType="VARCHAR" />
        <result column="pay_way" property="payWay" jdbcType="VARCHAR" />
        <result column="scene" property="scene" jdbcType="VARCHAR" />
        <result column="bus_type" property="busType" jdbcType="VARCHAR" />
        <result column="account_date" property="accountDate" jdbcType="VARCHAR" />
        <result column="order_amount" property="orderAmount" jdbcType="DECIMAL" />
        <result column="notify_url" property="notifyUrl" jdbcType="VARCHAR" />
        <result column="bank_order_no" property="bankOrderNo" jdbcType="VARCHAR" />
        <result column="order_complete_time" property="orderCompleteTime" jdbcType="VARCHAR" />
        <result column="notify_type" property="notifyType" jdbcType="VARCHAR" />
        <result column="notify_status" property="notifyStatus" jdbcType="VARCHAR" />
        <result column="err_msg_cd" property="errMsgCd" jdbcType="VARCHAR" />
        <result column="err_msg_info" property="errMsgInfo" jdbcType="VARCHAR" />
        <result column="notify_complete_time" property="notifyCompleteTime" jdbcType="VARCHAR" />
        <result column="notify_count" property="notifyCount" jdbcType="INTEGER" />
        <result column="remark" property="remark" jdbcType="VARCHAR" />
        <result column="tm_smp" property="tmSmp" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        out_order_no, refund_order_no, notify_date, notify_time, trade_jrn_no, order_date, 
        order_time, channel_code, pay_way, scene, bus_type, account_date, order_amount, notify_url, 
        bank_order_no, order_complete_time, notify_type, notify_status, err_msg_cd, err_msg_info, 
        notify_complete_time, notify_count, remark, tm_smp
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="com.cmpay.dceppay.entity.pay.PayNotifyDOKey" >
        select 
        <include refid="Base_Column_List" />
        from pay_notify
        where out_order_no = #{outOrderNo,jdbcType=VARCHAR}
          and refund_order_no = #{refundOrderNo,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="com.cmpay.dceppay.entity.pay.PayNotifyDOKey" >
        delete from pay_notify
        where out_order_no = #{outOrderNo,jdbcType=VARCHAR}
          and refund_order_no = #{refundOrderNo,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.cmpay.dceppay.entity.pay.PayNotifyDO" >
        insert into pay_notify
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="outOrderNo != null" >
                out_order_no,
            </if>
            <if test="refundOrderNo != null" >
                refund_order_no,
            </if>
            <if test="notifyDate != null" >
                notify_date,
            </if>
            <if test="notifyTime != null" >
                notify_time,
            </if>
            <if test="tradeJrnNo != null" >
                trade_jrn_no,
            </if>
            <if test="orderDate != null" >
                order_date,
            </if>
            <if test="orderTime != null" >
                order_time,
            </if>
            <if test="channelCode != null" >
                channel_code,
            </if>
            <if test="payWay != null" >
                pay_way,
            </if>
            <if test="scene != null" >
                scene,
            </if>
            <if test="busType != null" >
                bus_type,
            </if>
            <if test="accountDate != null" >
                account_date,
            </if>
            <if test="orderAmount != null" >
                order_amount,
            </if>
            <if test="notifyUrl != null" >
                notify_url,
            </if>
            <if test="bankOrderNo != null" >
                bank_order_no,
            </if>
            <if test="orderCompleteTime != null" >
                order_complete_time,
            </if>
            <if test="notifyType != null" >
                notify_type,
            </if>
            <if test="notifyStatus != null" >
                notify_status,
            </if>
            <if test="errMsgCd != null" >
                err_msg_cd,
            </if>
            <if test="errMsgInfo != null" >
                err_msg_info,
            </if>
            <if test="notifyCompleteTime != null" >
                notify_complete_time,
            </if>
            <if test="notifyCount != null" >
                notify_count,
            </if>
            <if test="remark != null" >
                remark,
            </if>
            <if test="tmSmp != null" >
                tm_smp,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="outOrderNo != null" >
                #{outOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="refundOrderNo != null" >
                #{refundOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="notifyDate != null" >
                #{notifyDate,jdbcType=VARCHAR},
            </if>
            <if test="notifyTime != null" >
                #{notifyTime,jdbcType=VARCHAR},
            </if>
            <if test="tradeJrnNo != null" >
                #{tradeJrnNo,jdbcType=VARCHAR},
            </if>
            <if test="orderDate != null" >
                #{orderDate,jdbcType=VARCHAR},
            </if>
            <if test="orderTime != null" >
                #{orderTime,jdbcType=VARCHAR},
            </if>
            <if test="channelCode != null" >
                #{channelCode,jdbcType=VARCHAR},
            </if>
            <if test="payWay != null" >
                #{payWay,jdbcType=VARCHAR},
            </if>
            <if test="scene != null" >
                #{scene,jdbcType=VARCHAR},
            </if>
            <if test="busType != null" >
                #{busType,jdbcType=VARCHAR},
            </if>
            <if test="accountDate != null" >
                #{accountDate,jdbcType=VARCHAR},
            </if>
            <if test="orderAmount != null" >
                #{orderAmount,jdbcType=DECIMAL},
            </if>
            <if test="notifyUrl != null" >
                #{notifyUrl,jdbcType=VARCHAR},
            </if>
            <if test="bankOrderNo != null" >
                #{bankOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="orderCompleteTime != null" >
                #{orderCompleteTime,jdbcType=VARCHAR},
            </if>
            <if test="notifyType != null" >
                #{notifyType,jdbcType=VARCHAR},
            </if>
            <if test="notifyStatus != null" >
                #{notifyStatus,jdbcType=VARCHAR},
            </if>
            <if test="errMsgCd != null" >
                #{errMsgCd,jdbcType=VARCHAR},
            </if>
            <if test="errMsgInfo != null" >
                #{errMsgInfo,jdbcType=VARCHAR},
            </if>
            <if test="notifyCompleteTime != null" >
                #{notifyCompleteTime,jdbcType=VARCHAR},
            </if>
            <if test="notifyCount != null" >
                #{notifyCount,jdbcType=INTEGER},
            </if>
            <if test="remark != null" >
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.cmpay.dceppay.entity.pay.PayNotifyDO" >
        update pay_notify
        <set >
            <if test="notifyDate != null" >
                notify_date = #{notifyDate,jdbcType=VARCHAR},
            </if>
            <if test="notifyTime != null" >
                notify_time = #{notifyTime,jdbcType=VARCHAR},
            </if>
            <if test="tradeJrnNo != null" >
                trade_jrn_no = #{tradeJrnNo,jdbcType=VARCHAR},
            </if>
            <if test="orderDate != null" >
                order_date = #{orderDate,jdbcType=VARCHAR},
            </if>
            <if test="orderTime != null" >
                order_time = #{orderTime,jdbcType=VARCHAR},
            </if>
            <if test="channelCode != null" >
                channel_code = #{channelCode,jdbcType=VARCHAR},
            </if>
            <if test="payWay != null" >
                pay_way = #{payWay,jdbcType=VARCHAR},
            </if>
            <if test="scene != null" >
                scene = #{scene,jdbcType=VARCHAR},
            </if>
            <if test="busType != null" >
                bus_type = #{busType,jdbcType=VARCHAR},
            </if>
            <if test="accountDate != null" >
                account_date = #{accountDate,jdbcType=VARCHAR},
            </if>
            <if test="orderAmount != null" >
                order_amount = #{orderAmount,jdbcType=DECIMAL},
            </if>
            <if test="notifyUrl != null" >
                notify_url = #{notifyUrl,jdbcType=VARCHAR},
            </if>
            <if test="bankOrderNo != null" >
                bank_order_no = #{bankOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="orderCompleteTime != null" >
                order_complete_time = #{orderCompleteTime,jdbcType=VARCHAR},
            </if>
            <if test="notifyType != null" >
                notify_type = #{notifyType,jdbcType=VARCHAR},
            </if>
            <if test="notifyStatus != null" >
                notify_status = #{notifyStatus,jdbcType=VARCHAR},
            </if>
            <if test="errMsgCd != null" >
                err_msg_cd = #{errMsgCd,jdbcType=VARCHAR},
            </if>
            <if test="errMsgInfo != null" >
                err_msg_info = #{errMsgInfo,jdbcType=VARCHAR},
            </if>
            <if test="notifyCompleteTime != null" >
                notify_complete_time = #{notifyCompleteTime,jdbcType=VARCHAR},
            </if>
            <if test="notifyCount != null" >
                notify_count = #{notifyCount,jdbcType=INTEGER},
            </if>
            <if test="remark != null" >
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="tmSmp != null" >
                tm_smp = #{tmSmp,jdbcType=VARCHAR},
            </if>
        </set>
        where out_order_no = #{outOrderNo,jdbcType=VARCHAR}
          and refund_order_no = #{refundOrderNo,jdbcType=VARCHAR}
    </update>

    <select id="find" resultMap="BaseResultMap" parameterType="com.cmpay.dceppay.entity.pay.PayNotifyDO" >
        select 
        <include refid="Base_Column_List" />
        from pay_notify
        <where >
            <if test="outOrderNo != null" >
                and out_order_no = #{outOrderNo,jdbcType=VARCHAR}
            </if>
            <if test="refundOrderNo != null" >
                and refund_order_no = #{refundOrderNo,jdbcType=VARCHAR}
            </if>
            <if test="notifyDate != null" >
                and notify_date = #{notifyDate,jdbcType=VARCHAR}
            </if>
            <if test="notifyTime != null" >
                and notify_time = #{notifyTime,jdbcType=VARCHAR}
            </if>
            <if test="tradeJrnNo != null" >
                and trade_jrn_no = #{tradeJrnNo,jdbcType=VARCHAR}
            </if>
            <if test="orderDate != null" >
                and order_date = #{orderDate,jdbcType=VARCHAR}
            </if>
            <if test="orderTime != null" >
                and order_time = #{orderTime,jdbcType=VARCHAR}
            </if>
            <if test="channelCode != null" >
                and channel_code = #{channelCode,jdbcType=VARCHAR}
            </if>
            <if test="payWay != null" >
                and pay_way = #{payWay,jdbcType=VARCHAR}
            </if>
            <if test="scene != null" >
                and scene = #{scene,jdbcType=VARCHAR}
            </if>
            <if test="busType != null" >
                and bus_type = #{busType,jdbcType=VARCHAR}
            </if>
            <if test="accountDate != null" >
                and account_date = #{accountDate,jdbcType=VARCHAR}
            </if>
            <if test="orderAmount != null" >
                and order_amount = #{orderAmount,jdbcType=DECIMAL}
            </if>
            <if test="notifyUrl != null" >
                and notify_url = #{notifyUrl,jdbcType=VARCHAR}
            </if>
            <if test="bankOrderNo != null" >
                and bank_order_no = #{bankOrderNo,jdbcType=VARCHAR}
            </if>
            <if test="orderCompleteTime != null" >
                and order_complete_time = #{orderCompleteTime,jdbcType=VARCHAR}
            </if>
            <if test="notifyType != null" >
                and notify_type = #{notifyType,jdbcType=VARCHAR}
            </if>
            <if test="notifyStatus != null" >
                and notify_status = #{notifyStatus,jdbcType=VARCHAR}
            </if>
            <if test="errMsgCd != null" >
                and err_msg_cd = #{errMsgCd,jdbcType=VARCHAR}
            </if>
            <if test="errMsgInfo != null" >
                and err_msg_info = #{errMsgInfo,jdbcType=VARCHAR}
            </if>
            <if test="notifyCompleteTime != null" >
                and notify_complete_time = #{notifyCompleteTime,jdbcType=VARCHAR}
            </if>
            <if test="notifyCount != null" >
                and notify_count = #{notifyCount,jdbcType=INTEGER}
            </if>
            <if test="remark != null" >
                and remark = #{remark,jdbcType=VARCHAR}
            </if>
            <if test="tmSmp != null" >
                and tm_smp = #{tmSmp,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
</mapper>