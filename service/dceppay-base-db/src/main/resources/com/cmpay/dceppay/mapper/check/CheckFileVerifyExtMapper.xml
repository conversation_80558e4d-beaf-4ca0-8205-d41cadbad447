<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.dceppay.dao.check.ICheckFileVerifyExtDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.dceppay.entity.check.CheckFileVerifyDO" >
        <id column="check_file_date" property="checkFileDate" jdbcType="VARCHAR" />
        <id column="institution_code" property="institutionCode" jdbcType="VARCHAR" />
        <result column="handle_flag" property="handleFlag" jdbcType="VARCHAR" />
        <result column="download_end_time" property="downloadEndTime" jdbcType="VARCHAR" />
        <result column="verify_message" property="verifyMessage" jdbcType="VARCHAR" />
        <result column="operate_id" property="operateId" jdbcType="VARCHAR" />
        <result column="remark" property="remark" jdbcType="VARCHAR" />
        <result column="tm_smp" property="tmSmp" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        check_file_date, institution_code, handle_flag, download_end_time, verify_message, 
        operate_id, remark, tm_smp
    </sql>

    <update id="resetHandleFlag" parameterType="com.cmpay.dceppay.entity.check.CheckFileVerifyDO" >
        update check_file_verify
        <set>
            handle_flag = #{handleFlag,jdbcType=VARCHAR},
            tm_smp = #{tmSmp,jdbcType=VARCHAR}
        </set>
        where check_file_date = #{checkFileDate,jdbcType=VARCHAR}
        and institution_code = #{institutionCode,jdbcType=VARCHAR}
    </update>


</mapper>