<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.dceppay.dao.pay.IPayAccountRecordExtDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.dceppay.entity.pay.PayAccountRecordDO" >
        <id column="jrn_no" property="jrnNo" jdbcType="VARCHAR" />
        <result column="order_date" property="orderDate" jdbcType="VARCHAR" />
        <result column="order_no" property="orderNo" jdbcType="VARCHAR" />
        <result column="order_type" property="orderType" jdbcType="VARCHAR" />
        <result column="bus_type" property="busType" jdbcType="VARCHAR" />
        <result column="bus_channel" property="busChannel" jdbcType="VARCHAR" />
        <result column="trade_type" property="tradeType" jdbcType="VARCHAR" />
        <result column="bus_code" property="busCode" jdbcType="VARCHAR" />
        <result column="account_from" property="accountFrom" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="VARCHAR" />
        <result column="finish_time" property="finishTime" jdbcType="VARCHAR" />
        <result column="handle_status" property="handleStatus" jdbcType="VARCHAR" />
        <result column="account_date" property="accountDate" jdbcType="VARCHAR" />
        <result column="remark" property="remark" jdbcType="VARCHAR" />
        <result column="amount" property="amount" jdbcType="DECIMAL" />
        <result column="tm_smp" property="tmSmp" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        jrn_no, order_date, order_no, order_type, bus_type, bus_channel, trade_type, bus_code,
        account_from, create_time, finish_time, handle_status, account_date, remark, amount,
        tm_smp
    </sql>


    <select id="getByUk" resultMap="BaseResultMap" parameterType="com.cmpay.dceppay.entity.pay.PayAccountRecordDO" >
        select
        <include refid="Base_Column_List" />
        from pay_account_record
        where order_no = #{orderNo,jdbcType=VARCHAR}
        and order_type = #{orderType,jdbcType=VARCHAR}
        and bus_code = #{busCode,jdbcType=VARCHAR}
    </select>

    <select id="queryWaitHandleAccount" resultMap="BaseResultMap"
            parameterType="com.cmpay.dceppay.entity.pay.AccountQueryDO">
        select
        <include refid="Base_Column_List"/>
        from pay_account_record
        WHERE
        order_date  >= #{requestDate,jdbcType=VARCHAR}
        AND create_time >= #{timeBegin,jdbcType=VARCHAR}
        AND create_time &lt;= #{timeEnd,jdbcType=VARCHAR}
        AND handle_status ='WAIT'
        LIMIT #{total,jdbcType=INTEGER}
    </select>


    <update id="updateAccountSuccess" parameterType="com.cmpay.dceppay.entity.pay.PayAccountRecordDO" >
        update pay_account_record
        <set>
            <if test="accountDate != null and accountDate != ''" >
                 account_date = #{accountDate,jdbcType=VARCHAR},
            </if>
            finish_time = #{finishTime,jdbcType=VARCHAR},
            handle_status = #{handleStatus,jdbcType=VARCHAR},
            tm_smp = #{tmSmp,jdbcType=VARCHAR}
        </set>
        where jrn_no = #{jrnNo,jdbcType=VARCHAR}
    </update>

    <update id="updateWaitDeal" parameterType="com.cmpay.dceppay.entity.pay.PayAccountRecordDO" >
        update pay_account_record
        <set>
            <if test="accountDate != null and accountDate != ''" >
                 account_date = #{accountDate,jdbcType=VARCHAR},
            </if>
            handle_status = #{handleStatus,jdbcType=VARCHAR},
            tm_smp = #{tmSmp,jdbcType=VARCHAR}
        </set>
        where jrn_no = #{jrnNo,jdbcType=VARCHAR}
    </update>


    <update id="updateAccountCancelReserve" parameterType="com.cmpay.dceppay.entity.pay.PayAccountRecordDO" >
        update pay_account_record
        <set>
            <if test="accountDate != null and accountDate != ''" >
                 account_date = #{accountDate,jdbcType=VARCHAR},
            </if>
            finish_time = #{finishTime,jdbcType=VARCHAR},
            handle_status = #{handleStatus,jdbcType=VARCHAR},
            tm_smp = #{tmSmp,jdbcType=VARCHAR}
        </set>
        where jrn_no = #{jrnNo,jdbcType=VARCHAR}
    </update>

    <update id="updateNoRecordAccount" parameterType="com.cmpay.dceppay.entity.pay.PayAccountRecordDO" >
        update pay_account_record
        <set>
            <if test="accountDate != null and accountDate != ''">
                account_date = #{accountDate,jdbcType=VARCHAR},
            </if>
            finish_time = #{finishTime,jdbcType=VARCHAR},
            handle_status = #{handleStatus,jdbcType=VARCHAR},
            tm_smp = #{tmSmp,jdbcType=VARCHAR}
        </set>
        where jrn_no = #{jrnNo,jdbcType=VARCHAR}
    </update>

</mapper>