<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.dceppay.dao.pay.IPayRefundDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.dceppay.entity.pay.PayRefundDO" >
        <id column="refund_order_no" property="refundOrderNo" jdbcType="VARCHAR" />
        <result column="out_order_no" property="outOrderNo" jdbcType="VARCHAR" />
        <result column="refund_date" property="refundDate" jdbcType="VARCHAR" />
        <result column="refund_time" property="refundTime" jdbcType="VARCHAR" />
        <result column="channel_code" property="channelCode" jdbcType="VARCHAR" />
        <result column="pay_way" property="payWay" jdbcType="VARCHAR" />
        <result column="scene" property="scene" jdbcType="VARCHAR" />
        <result column="bus_type" property="busType" jdbcType="VARCHAR" />
        <result column="merchant_no" property="merchantNo" jdbcType="VARCHAR" />
        <result column="org_merchant_no" property="orgMerchantNo" jdbcType="VARCHAR" />
        <result column="wallet_id" property="walletId" jdbcType="VARCHAR" />
        <result column="account_date" property="accountDate" jdbcType="VARCHAR" />
        <result column="order_amount" property="orderAmount" jdbcType="DECIMAL" />
        <result column="refund_amount" property="refundAmount" jdbcType="DECIMAL" />
        <result column="notify_url" property="notifyUrl" jdbcType="VARCHAR" />
        <result column="cancel_flag" property="cancelFlag" jdbcType="VARCHAR" />
        <result column="status" property="status" jdbcType="VARCHAR" />
        <result column="bank_order_no" property="bankOrderNo" jdbcType="VARCHAR" />
        <result column="err_msg_cd" property="errMsgCd" jdbcType="VARCHAR" />
        <result column="err_msg_info" property="errMsgInfo" jdbcType="VARCHAR" />
        <result column="order_complete_time" property="orderCompleteTime" jdbcType="VARCHAR" />
        <result column="receive_notify_time" property="receiveNotifyTime" jdbcType="VARCHAR" />
        <result column="refund_reason" property="refundReason" jdbcType="VARCHAR" />
        <result column="biz_type" property="bizType" jdbcType="VARCHAR" />
        <result column="biz_category" property="bizCategory" jdbcType="VARCHAR" />
        <result column="bank_refund_no" property="bankRefundNo" jdbcType="VARCHAR" />
        <result column="notify_status" property="notifyStatus" jdbcType="VARCHAR" />
        <result column="send_notify_time" property="sendNotifyTime" jdbcType="VARCHAR" />
        <result column="extra" property="extra" jdbcType="VARCHAR" />
        <result column="message_identification" property="messageIdentification" jdbcType="VARCHAR" />
        <result column="tm_smp" property="tmSmp" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        refund_order_no, out_order_no, refund_date, refund_time, channel_code, pay_way, scene, 
        bus_type, merchant_no, org_merchant_no, wallet_id, account_date, order_amount, refund_amount, 
        notify_url, cancel_flag, status, bank_order_no, err_msg_cd, err_msg_info, order_complete_time, 
        receive_notify_time, refund_reason, biz_type, biz_category, bank_refund_no, notify_status, 
        send_notify_time, extra, message_identification, tm_smp
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from pay_refund
        where refund_order_no = #{refundOrderNo,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from pay_refund
        where refund_order_no = #{refundOrderNo,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.cmpay.dceppay.entity.pay.PayRefundDO" >
        insert into pay_refund
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="refundOrderNo != null" >
                refund_order_no,
            </if>
            <if test="outOrderNo != null" >
                out_order_no,
            </if>
            <if test="refundDate != null" >
                refund_date,
            </if>
            <if test="refundTime != null" >
                refund_time,
            </if>
            <if test="channelCode != null" >
                channel_code,
            </if>
            <if test="payWay != null" >
                pay_way,
            </if>
            <if test="scene != null" >
                scene,
            </if>
            <if test="busType != null" >
                bus_type,
            </if>
            <if test="merchantNo != null" >
                merchant_no,
            </if>
            <if test="orgMerchantNo != null" >
                org_merchant_no,
            </if>
            <if test="walletId != null" >
                wallet_id,
            </if>
            <if test="accountDate != null" >
                account_date,
            </if>
            <if test="orderAmount != null" >
                order_amount,
            </if>
            <if test="refundAmount != null" >
                refund_amount,
            </if>
            <if test="notifyUrl != null" >
                notify_url,
            </if>
            <if test="cancelFlag != null" >
                cancel_flag,
            </if>
            <if test="status != null" >
                status,
            </if>
            <if test="bankOrderNo != null" >
                bank_order_no,
            </if>
            <if test="errMsgCd != null" >
                err_msg_cd,
            </if>
            <if test="errMsgInfo != null" >
                err_msg_info,
            </if>
            <if test="orderCompleteTime != null" >
                order_complete_time,
            </if>
            <if test="receiveNotifyTime != null" >
                receive_notify_time,
            </if>
            <if test="refundReason != null" >
                refund_reason,
            </if>
            <if test="bizType != null" >
                biz_type,
            </if>
            <if test="bizCategory != null" >
                biz_category,
            </if>
            <if test="bankRefundNo != null" >
                bank_refund_no,
            </if>
            <if test="notifyStatus != null" >
                notify_status,
            </if>
            <if test="sendNotifyTime != null" >
                send_notify_time,
            </if>
            <if test="extra != null" >
                extra,
            </if>
            <if test="messageIdentification != null" >
                message_identification,
            </if>
            <if test="tmSmp != null" >
                tm_smp,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="refundOrderNo != null" >
                #{refundOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="outOrderNo != null" >
                #{outOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="refundDate != null" >
                #{refundDate,jdbcType=VARCHAR},
            </if>
            <if test="refundTime != null" >
                #{refundTime,jdbcType=VARCHAR},
            </if>
            <if test="channelCode != null" >
                #{channelCode,jdbcType=VARCHAR},
            </if>
            <if test="payWay != null" >
                #{payWay,jdbcType=VARCHAR},
            </if>
            <if test="scene != null" >
                #{scene,jdbcType=VARCHAR},
            </if>
            <if test="busType != null" >
                #{busType,jdbcType=VARCHAR},
            </if>
            <if test="merchantNo != null" >
                #{merchantNo,jdbcType=VARCHAR},
            </if>
            <if test="orgMerchantNo != null" >
                #{orgMerchantNo,jdbcType=VARCHAR},
            </if>
            <if test="walletId != null" >
                #{walletId,jdbcType=VARCHAR},
            </if>
            <if test="accountDate != null" >
                #{accountDate,jdbcType=VARCHAR},
            </if>
            <if test="orderAmount != null" >
                #{orderAmount,jdbcType=DECIMAL},
            </if>
            <if test="refundAmount != null" >
                #{refundAmount,jdbcType=DECIMAL},
            </if>
            <if test="notifyUrl != null" >
                #{notifyUrl,jdbcType=VARCHAR},
            </if>
            <if test="cancelFlag != null" >
                #{cancelFlag,jdbcType=VARCHAR},
            </if>
            <if test="status != null" >
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="bankOrderNo != null" >
                #{bankOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="errMsgCd != null" >
                #{errMsgCd,jdbcType=VARCHAR},
            </if>
            <if test="errMsgInfo != null" >
                #{errMsgInfo,jdbcType=VARCHAR},
            </if>
            <if test="orderCompleteTime != null" >
                #{orderCompleteTime,jdbcType=VARCHAR},
            </if>
            <if test="receiveNotifyTime != null" >
                #{receiveNotifyTime,jdbcType=VARCHAR},
            </if>
            <if test="refundReason != null" >
                #{refundReason,jdbcType=VARCHAR},
            </if>
            <if test="bizType != null" >
                #{bizType,jdbcType=VARCHAR},
            </if>
            <if test="bizCategory != null" >
                #{bizCategory,jdbcType=VARCHAR},
            </if>
            <if test="bankRefundNo != null" >
                #{bankRefundNo,jdbcType=VARCHAR},
            </if>
            <if test="notifyStatus != null" >
                #{notifyStatus,jdbcType=VARCHAR},
            </if>
            <if test="sendNotifyTime != null" >
                #{sendNotifyTime,jdbcType=VARCHAR},
            </if>
            <if test="extra != null" >
                #{extra,jdbcType=VARCHAR},
            </if>
            <if test="messageIdentification != null" >
                #{messageIdentification,jdbcType=VARCHAR},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.cmpay.dceppay.entity.pay.PayRefundDO" >
        update pay_refund
        <set >
            <if test="outOrderNo != null" >
                out_order_no = #{outOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="refundDate != null" >
                refund_date = #{refundDate,jdbcType=VARCHAR},
            </if>
            <if test="refundTime != null" >
                refund_time = #{refundTime,jdbcType=VARCHAR},
            </if>
            <if test="channelCode != null" >
                channel_code = #{channelCode,jdbcType=VARCHAR},
            </if>
            <if test="payWay != null" >
                pay_way = #{payWay,jdbcType=VARCHAR},
            </if>
            <if test="scene != null" >
                scene = #{scene,jdbcType=VARCHAR},
            </if>
            <if test="busType != null" >
                bus_type = #{busType,jdbcType=VARCHAR},
            </if>
            <if test="merchantNo != null" >
                merchant_no = #{merchantNo,jdbcType=VARCHAR},
            </if>
            <if test="orgMerchantNo != null" >
                org_merchant_no = #{orgMerchantNo,jdbcType=VARCHAR},
            </if>
            <if test="walletId != null" >
                wallet_id = #{walletId,jdbcType=VARCHAR},
            </if>
            <if test="accountDate != null" >
                account_date = #{accountDate,jdbcType=VARCHAR},
            </if>
            <if test="orderAmount != null" >
                order_amount = #{orderAmount,jdbcType=DECIMAL},
            </if>
            <if test="refundAmount != null" >
                refund_amount = #{refundAmount,jdbcType=DECIMAL},
            </if>
            <if test="notifyUrl != null" >
                notify_url = #{notifyUrl,jdbcType=VARCHAR},
            </if>
            <if test="cancelFlag != null" >
                cancel_flag = #{cancelFlag,jdbcType=VARCHAR},
            </if>
            <if test="status != null" >
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="bankOrderNo != null" >
                bank_order_no = #{bankOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="errMsgCd != null" >
                err_msg_cd = #{errMsgCd,jdbcType=VARCHAR},
            </if>
            <if test="errMsgInfo != null" >
                err_msg_info = #{errMsgInfo,jdbcType=VARCHAR},
            </if>
            <if test="orderCompleteTime != null" >
                order_complete_time = #{orderCompleteTime,jdbcType=VARCHAR},
            </if>
            <if test="receiveNotifyTime != null" >
                receive_notify_time = #{receiveNotifyTime,jdbcType=VARCHAR},
            </if>
            <if test="refundReason != null" >
                refund_reason = #{refundReason,jdbcType=VARCHAR},
            </if>
            <if test="bizType != null" >
                biz_type = #{bizType,jdbcType=VARCHAR},
            </if>
            <if test="bizCategory != null" >
                biz_category = #{bizCategory,jdbcType=VARCHAR},
            </if>
            <if test="bankRefundNo != null" >
                bank_refund_no = #{bankRefundNo,jdbcType=VARCHAR},
            </if>
            <if test="notifyStatus != null" >
                notify_status = #{notifyStatus,jdbcType=VARCHAR},
            </if>
            <if test="sendNotifyTime != null" >
                send_notify_time = #{sendNotifyTime,jdbcType=VARCHAR},
            </if>
            <if test="extra != null" >
                extra = #{extra,jdbcType=VARCHAR},
            </if>
            <if test="messageIdentification != null" >
                message_identification = #{messageIdentification,jdbcType=VARCHAR},
            </if>
            <if test="tmSmp != null" >
                tm_smp = #{tmSmp,jdbcType=VARCHAR},
            </if>
        </set>
        where refund_order_no = #{refundOrderNo,jdbcType=VARCHAR}
    </update>

    <select id="find" resultMap="BaseResultMap" parameterType="com.cmpay.dceppay.entity.pay.PayRefundDO" >
        select 
        <include refid="Base_Column_List" />
        from pay_refund
        <where >
            <if test="refundOrderNo != null" >
                and refund_order_no = #{refundOrderNo,jdbcType=VARCHAR}
            </if>
            <if test="outOrderNo != null" >
                and out_order_no = #{outOrderNo,jdbcType=VARCHAR}
            </if>
            <if test="refundDate != null" >
                and refund_date = #{refundDate,jdbcType=VARCHAR}
            </if>
            <if test="refundTime != null" >
                and refund_time = #{refundTime,jdbcType=VARCHAR}
            </if>
            <if test="channelCode != null" >
                and channel_code = #{channelCode,jdbcType=VARCHAR}
            </if>
            <if test="payWay != null" >
                and pay_way = #{payWay,jdbcType=VARCHAR}
            </if>
            <if test="scene != null" >
                and scene = #{scene,jdbcType=VARCHAR}
            </if>
            <if test="busType != null" >
                and bus_type = #{busType,jdbcType=VARCHAR}
            </if>
            <if test="merchantNo != null" >
                and merchant_no = #{merchantNo,jdbcType=VARCHAR}
            </if>
            <if test="orgMerchantNo != null" >
                and org_merchant_no = #{orgMerchantNo,jdbcType=VARCHAR}
            </if>
            <if test="walletId != null" >
                and wallet_id = #{walletId,jdbcType=VARCHAR}
            </if>
            <if test="accountDate != null" >
                and account_date = #{accountDate,jdbcType=VARCHAR}
            </if>
            <if test="orderAmount != null" >
                and order_amount = #{orderAmount,jdbcType=DECIMAL}
            </if>
            <if test="refundAmount != null" >
                and refund_amount = #{refundAmount,jdbcType=DECIMAL}
            </if>
            <if test="notifyUrl != null" >
                and notify_url = #{notifyUrl,jdbcType=VARCHAR}
            </if>
            <if test="cancelFlag != null" >
                and cancel_flag = #{cancelFlag,jdbcType=VARCHAR}
            </if>
            <if test="status != null" >
                and status = #{status,jdbcType=VARCHAR}
            </if>
            <if test="bankOrderNo != null" >
                and bank_order_no = #{bankOrderNo,jdbcType=VARCHAR}
            </if>
            <if test="errMsgCd != null" >
                and err_msg_cd = #{errMsgCd,jdbcType=VARCHAR}
            </if>
            <if test="errMsgInfo != null" >
                and err_msg_info = #{errMsgInfo,jdbcType=VARCHAR}
            </if>
            <if test="orderCompleteTime != null" >
                and order_complete_time = #{orderCompleteTime,jdbcType=VARCHAR}
            </if>
            <if test="receiveNotifyTime != null" >
                and receive_notify_time = #{receiveNotifyTime,jdbcType=VARCHAR}
            </if>
            <if test="refundReason != null" >
                and refund_reason = #{refundReason,jdbcType=VARCHAR}
            </if>
            <if test="bizType != null" >
                and biz_type = #{bizType,jdbcType=VARCHAR}
            </if>
            <if test="bizCategory != null" >
                and biz_category = #{bizCategory,jdbcType=VARCHAR}
            </if>
            <if test="bankRefundNo != null" >
                and bank_refund_no = #{bankRefundNo,jdbcType=VARCHAR}
            </if>
            <if test="notifyStatus != null" >
                and notify_status = #{notifyStatus,jdbcType=VARCHAR}
            </if>
            <if test="sendNotifyTime != null" >
                and send_notify_time = #{sendNotifyTime,jdbcType=VARCHAR}
            </if>
            <if test="extra != null" >
                and extra = #{extra,jdbcType=VARCHAR}
            </if>
            <if test="messageIdentification != null" >
                and message_identification = #{messageIdentification,jdbcType=VARCHAR}
            </if>
            <if test="tmSmp != null" >
                and tm_smp = #{tmSmp,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
</mapper>