<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.dceppay.dao.check.ICheckFileProcessExtDao">

    <resultMap id="BaseResultMap" type="com.cmpay.dceppay.entity.check.CheckFileProcessDO">
        <id column="check_file_date" property="checkFileDate" jdbcType="VARCHAR"/>
        <id column="institution_code" property="institutionCode" jdbcType="VARCHAR"/>
        <result column="download_begin_time" property="downloadBeginTime" jdbcType="VARCHAR"/>
        <result column="download_end_time" property="downloadEndTime" jdbcType="VARCHAR"/>
        <result column="import_begin_time" property="importBeginTime" jdbcType="VARCHAR"/>
        <result column="import_end_time" property="importEndTime" jdbcType="VARCHAR"/>
        <result column="check_begin_time" property="checkBeginTime" jdbcType="VARCHAR"/>
        <result column="check_end_time" property="checkEndTime" jdbcType="VARCHAR"/>
        <result column="check_status" property="checkStatus" jdbcType="VARCHAR"/>
        <result column="tm_smp" property="tmSmp" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        check_file_date
        , institution_code, download_begin_time, download_end_time, import_begin_time,
        import_end_time, check_begin_time, check_end_time, check_status, tm_smp
    </sql>

    <update id="updateFileWaitDownload" parameterType="com.cmpay.dceppay.entity.check.CheckFileProcessDO">
        update check_file_process
        <set>
            check_status = #{checkStatus,jdbcType=VARCHAR},
            download_begin_time= #{downloadBeginTime,jdbcType=VARCHAR},
            tm_smp = #{tmSmp,jdbcType=VARCHAR}
        </set>
        where check_file_date = #{checkFileDate,jdbcType=VARCHAR}
        and institution_code = #{institutionCode,jdbcType=VARCHAR}
    </update>
    <update id="updateCheckFileDownloadEnd" parameterType="com.cmpay.dceppay.entity.check.CheckFileProcessDO">
        update check_file_process
        <set>
            check_status = #{checkStatus,jdbcType=VARCHAR},
            download_end_time= #{downloadEndTime,jdbcType=VARCHAR},
            tm_smp = #{tmSmp,jdbcType=VARCHAR}
        </set>
        where check_file_date = #{checkFileDate,jdbcType=VARCHAR}
        and institution_code = #{institutionCode,jdbcType=VARCHAR}
    </update>
    <update id="updateImportBegin" parameterType="com.cmpay.dceppay.entity.check.CheckFileProcessDO">
        update check_file_process
        <set>
            check_status = #{checkStatus,jdbcType=VARCHAR},
            import_begin_time= #{importBeginTime,jdbcType=VARCHAR},
            tm_smp = #{tmSmp,jdbcType=VARCHAR}
        </set>
        where check_file_date = #{checkFileDate,jdbcType=VARCHAR}
        and institution_code = #{institutionCode,jdbcType=VARCHAR}
    </update>
    <update id="updateImportEnd" parameterType="com.cmpay.dceppay.entity.check.CheckFileProcessDO">
        update check_file_process
        <set>
            check_status = #{checkStatus,jdbcType=VARCHAR},
            import_end_time= #{importEndTime,jdbcType=VARCHAR},
            tm_smp = #{tmSmp,jdbcType=VARCHAR}
        </set>
        where check_file_date = #{checkFileDate,jdbcType=VARCHAR}
        and institution_code = #{institutionCode,jdbcType=VARCHAR}
    </update>
    <update id="updateCheckBegin" parameterType="com.cmpay.dceppay.entity.check.CheckFileProcessDO">
        update check_file_process
        <set>
            check_status = #{checkStatus,jdbcType=VARCHAR},
            check_begin_time= #{checkBeginTime,jdbcType=VARCHAR},
            tm_smp = #{tmSmp,jdbcType=VARCHAR}
        </set>
        where check_file_date = #{checkFileDate,jdbcType=VARCHAR}
        and institution_code = #{institutionCode,jdbcType=VARCHAR}
    </update>
    <update id="updateCheckEnd" parameterType="com.cmpay.dceppay.entity.check.CheckFileProcessDO">
        update check_file_process
        <set>
            check_status = #{checkStatus,jdbcType=VARCHAR},
            check_end_time= #{checkEndTime,jdbcType=VARCHAR},
            tm_smp = #{tmSmp,jdbcType=VARCHAR}
        </set>
        where check_file_date = #{checkFileDate,jdbcType=VARCHAR}
        and institution_code = #{institutionCode,jdbcType=VARCHAR}
    </update>
    <update id="updateWaitConfirmInfo" parameterType="com.cmpay.dceppay.entity.check.CheckFileProcessDO">
        update check_file_process
        <set>
            check_status = #{checkStatus,jdbcType=VARCHAR},
            tm_smp = #{tmSmp,jdbcType=VARCHAR}
        </set>
        where check_file_date = #{checkFileDate,jdbcType=VARCHAR}
        and institution_code = #{institutionCode,jdbcType=VARCHAR}
    </update>
</mapper>