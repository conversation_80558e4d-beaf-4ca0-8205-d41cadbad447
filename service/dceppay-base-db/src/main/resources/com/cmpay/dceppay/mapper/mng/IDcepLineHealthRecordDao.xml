<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmpay.dceppay.dao.mng.IDcepLineHealthRecordDao">
    
    <resultMap id="BaseResultMap" type="com.cmpay.dceppay.entity.mng.DcepLineHealthRecordDO">
        <id column="id" jdbcType="VARCHAR" property="id" />
        <result column="line_id" jdbcType="VARCHAR" property="lineId" />
        <result column="check_time" jdbcType="TIMESTAMP" property="checkTime" />
        <result column="check_type" jdbcType="VARCHAR" property="checkType" />
        <result column="response_time" jdbcType="BIGINT" property="responseTime" />
        <result column="check_result" jdbcType="VARCHAR" property="checkResult" />
        <result column="error_message" jdbcType="VARCHAR" property="errorMessage" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    </resultMap>

    <sql id="Base_Column_List">
        id, line_id, check_time, check_type, response_time, check_result, error_message, create_time
    </sql>

    <select id="selectRecentRecords" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM dcep_line_health_record
        WHERE line_id = #{lineId}
        ORDER BY check_time DESC
        LIMIT #{limit}
    </select>

    <select id="calculateAvgResponseTime" resultType="java.lang.Long">
        SELECT AVG(response_time)
        FROM dcep_line_health_record
        WHERE line_id = #{lineId} AND check_result = 'SUCCESS' AND response_time IS NOT NULL
        ORDER BY check_time DESC
        LIMIT #{limit}
    </select>

    <delete id="deleteRecordsBefore">
        DELETE FROM dcep_line_health_record
        WHERE create_time &lt; #{beforeTime}
    </delete>

</mapper>
