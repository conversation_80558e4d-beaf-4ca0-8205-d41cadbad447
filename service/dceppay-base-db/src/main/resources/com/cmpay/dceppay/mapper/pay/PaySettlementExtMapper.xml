<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.dceppay.dao.pay.IPaySettlementExtDao">

    <resultMap id="BaseResultMap" type="com.cmpay.dceppay.entity.pay.PaySettlementDO">
        <id column="out_order_no" property="outOrderNo" jdbcType="VARCHAR"/>
        <id column="refund_order_no" property="refundOrderNo" jdbcType="VARCHAR"/>
        <result column="jrn_no" property="jrnNo" jdbcType="VARCHAR"/>
        <result column="settlement_date" property="settlementDate" jdbcType="VARCHAR"/>
        <result column="order_date" property="orderDate" jdbcType="VARCHAR"/>
        <result column="order_time" property="orderTime" jdbcType="VARCHAR"/>
        <result column="channel_code" property="channelCode" jdbcType="VARCHAR"/>
        <result column="pay_way" property="payWay" jdbcType="VARCHAR"/>
        <result column="scene" property="scene" jdbcType="VARCHAR"/>
        <result column="bus_type" property="busType" jdbcType="VARCHAR"/>
        <result column="merchant_no" property="merchantNo" jdbcType="VARCHAR"/>
        <result column="org_merchant_no" property="orgMerchantNo" jdbcType="VARCHAR"/>
        <result column="wallet_id" property="walletId" jdbcType="VARCHAR"/>
        <result column="account_date" property="accountDate" jdbcType="VARCHAR"/>
        <result column="order_amount" property="orderAmount" jdbcType="DECIMAL"/>
        <result column="bank_order_no" property="bankOrderNo" jdbcType="VARCHAR"/>
        <result column="settlement_type" property="settlementType" jdbcType="VARCHAR"/>
        <result column="check_status" property="checkStatus" jdbcType="VARCHAR"/>
        <result column="check_date" property="checkDate" jdbcType="VARCHAR"/>
        <result column="check_complete_time" property="checkCompleteTime" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="tm_smp" property="tmSmp" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        out_order_no
        , refund_order_no, jrn_no, settlement_date, order_date, order_time, channel_code,
        pay_way, scene, bus_type, merchant_no, org_merchant_no, wallet_id, account_date,
        order_amount, bank_order_no, settlement_type, check_status, check_date, check_complete_time,
        remark, tm_smp
    </sql>

    <select id="getPaymentSettlement" resultMap="BaseResultMap"
            parameterType="com.cmpay.dceppay.entity.pay.PaySettlementDO">
        select
        <include refid="Base_Column_List"/>
        from pay_settlement
        where out_order_no = #{outOrderNo,jdbcType=VARCHAR}
        and settlement_date >= #{settlementDate,jdbcType=VARCHAR}
        and settlement_type = #{settlementType,jdbcType=VARCHAR}
    </select>

    <select id="getRefundSettlement" resultMap="BaseResultMap"  parameterType="com.cmpay.dceppay.entity.pay.PaySettlementDO">
        select
        <include refid="Base_Column_List"/>
        from pay_settlement
        where refund_order_no = #{refundOrderNo,jdbcType=VARCHAR}
        and settlement_date >= #{settlementDate,jdbcType=VARCHAR}
        and settlement_type = #{settlementType,jdbcType=VARCHAR}
    </select>

    <update id="updateCheckComplete" parameterType="com.cmpay.dceppay.entity.pay.PaySettlementDO" >
        update pay_settlement
        <set>
            check_status = #{checkStatus,jdbcType=VARCHAR},
            check_date = #{checkDate,jdbcType=VARCHAR},
            check_complete_time = #{checkCompleteTime,jdbcType=VARCHAR},
            tm_smp = #{tmSmp,jdbcType=VARCHAR}
        </set>
        where out_order_no = #{outOrderNo,jdbcType=VARCHAR}
        and refund_order_no = #{refundOrderNo,jdbcType=VARCHAR}
        and settlement_date = #{settlementDate,jdbcType=VARCHAR}
    </update>

    <update id="updateAmountError" parameterType="com.cmpay.dceppay.entity.pay.PaySettlementDO" >
        update pay_settlement
        <set>
            check_status = #{checkStatus,jdbcType=VARCHAR},
            check_date = #{checkDate,jdbcType=VARCHAR},
            check_complete_time = #{checkCompleteTime,jdbcType=VARCHAR},
            tm_smp = #{tmSmp,jdbcType=VARCHAR}
        </set>
        where out_order_no = #{outOrderNo,jdbcType=VARCHAR}
        and refund_order_no = #{refundOrderNo,jdbcType=VARCHAR}
        and settlement_date = #{settlementDate,jdbcType=VARCHAR}
    </update>
    <update id="updatePaymentShort" parameterType="com.cmpay.dceppay.entity.pay.PaySettlementDO" >
        update pay_settlement
        <set>
            check_status = #{checkStatus,jdbcType=VARCHAR},
            check_date = #{checkDate,jdbcType=VARCHAR},
            check_complete_time = #{checkCompleteTime,jdbcType=VARCHAR},
            tm_smp = #{tmSmp,jdbcType=VARCHAR}
        </set>
        where out_order_no = #{outOrderNo,jdbcType=VARCHAR}
        and refund_order_no = #{refundOrderNo,jdbcType=VARCHAR}
        and settlement_date = #{settlementDate,jdbcType=VARCHAR}
    </update>

    <update id="updateRefundLog" parameterType="com.cmpay.dceppay.entity.pay.PaySettlementDO" >
        update pay_settlement
        <set>
            check_status = #{checkStatus,jdbcType=VARCHAR},
            check_date = #{checkDate,jdbcType=VARCHAR},
            check_complete_time = #{checkCompleteTime,jdbcType=VARCHAR},
            tm_smp = #{tmSmp,jdbcType=VARCHAR}
        </set>
        where out_order_no = #{outOrderNo,jdbcType=VARCHAR}
        and refund_order_no = #{refundOrderNo,jdbcType=VARCHAR}
        and settlement_date = #{settlementDate,jdbcType=VARCHAR}
    </update>

    <select id="findWaitOrderList" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List" />
        from pay_settlement a
        where check_status ='WAIT'
        and settlement_date = #{settlementDate,jdbcType=VARCHAR}
    </select>

    <select id="getPaymentShortDoubt" resultMap="BaseResultMap"
            parameterType="com.cmpay.dceppay.entity.pay.PaySettlementDO">
        select
        <include refid="Base_Column_List"/>
        from pay_settlement
        where out_order_no = #{outOrderNo,jdbcType=VARCHAR}
        and refund_order_no = #{refundOrderNo,jdbcType=VARCHAR}
        and settlement_date = #{settlementDate,jdbcType=VARCHAR}
        and settlement_type = #{settlementType,jdbcType=VARCHAR}
    </select>

    <select id="getRefundLongDoubt" resultMap="BaseResultMap"
            parameterType="com.cmpay.dceppay.entity.pay.PaySettlementDO">
        select
        <include refid="Base_Column_List"/>
        from pay_settlement
        where out_order_no = #{outOrderNo,jdbcType=VARCHAR}
        and refund_order_no = #{refundOrderNo,jdbcType=VARCHAR}
        and settlement_date = #{settlementDate,jdbcType=VARCHAR}
        and settlement_type = #{settlementType,jdbcType=VARCHAR}
    </select>

    <update id="updateCheckErrorComplete" parameterType="com.cmpay.dceppay.entity.pay.PaySettlementDO" >
        update pay_settlement
        <set>
            check_status = #{checkStatus,jdbcType=VARCHAR},
            check_complete_time = #{checkCompleteTime,jdbcType=VARCHAR},
            tm_smp = #{tmSmp,jdbcType=VARCHAR}
        </set>
        where out_order_no = #{outOrderNo,jdbcType=VARCHAR}
        and refund_order_no = #{refundOrderNo,jdbcType=VARCHAR}
    </update>
    <update id="updateCheckErrorAmount" parameterType="com.cmpay.dceppay.entity.pay.PaySettlementDO" >
        update pay_settlement
        <set>
            check_status = #{checkStatus,jdbcType=VARCHAR},
            check_complete_time = #{checkCompleteTime,jdbcType=VARCHAR},
            order_amount = #{orderAmount,jdbcType=DECIMAL},
            tm_smp = #{tmSmp,jdbcType=VARCHAR}
        </set>
        where out_order_no = #{outOrderNo,jdbcType=VARCHAR}
        and refund_order_no = #{refundOrderNo,jdbcType=VARCHAR}
    </update>

    <update id="updateAccountDate" parameterType="com.cmpay.dceppay.entity.pay.PaySettlementDO" >
        update pay_settlement
        <set>
            <if test="accountDate != null" >
                account_date = #{accountDate,jdbcType=VARCHAR},
            </if>
            tm_smp = #{tmSmp,jdbcType=VARCHAR}
        </set>
        where out_order_no = #{outOrderNo,jdbcType=VARCHAR}
        and refund_order_no = #{refundOrderNo,jdbcType=VARCHAR}
    </update>


</mapper>