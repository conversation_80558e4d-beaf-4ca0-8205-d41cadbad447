<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.dceppay.dao.pay.IPayOrderExtDao">

    <resultMap id="BaseResultMap" type="com.cmpay.dceppay.entity.pay.PayOrderDO" >
        <id column="out_order_no" property="outOrderNo" jdbcType="VARCHAR" />
        <result column="trade_jrn_no" property="tradeJrnNo" jdbcType="VARCHAR" />
        <result column="order_date" property="orderDate" jdbcType="VARCHAR" />
        <result column="order_time" property="orderTime" jdbcType="VARCHAR" />
        <result column="order_time_expire" property="orderTimeExpire" jdbcType="VARCHAR" />
        <result column="channel_code" property="channelCode" jdbcType="VARCHAR" />
        <result column="pay_way" property="payWay" jdbcType="VARCHAR" />
        <result column="scene" property="scene" jdbcType="VARCHAR" />
        <result column="order_type" property="orderType" jdbcType="VARCHAR" />
        <result column="account_date" property="accountDate" jdbcType="VARCHAR" />
        <result column="product_name" property="productName" jdbcType="VARCHAR" />
        <result column="product_desc" property="productDesc" jdbcType="VARCHAR" />
        <result column="order_amount" property="orderAmount" jdbcType="DECIMAL" />
        <result column="bus_type" property="busType" jdbcType="VARCHAR" />
        <result column="merchant_no" property="merchantNo" jdbcType="VARCHAR" />
        <result column="org_merchant_no" property="orgMerchantNo" jdbcType="VARCHAR" />
        <result column="wallet_id" property="walletId" jdbcType="VARCHAR" />
        <result column="biz_type" property="bizType" jdbcType="VARCHAR" />
        <result column="biz_category" property="bizCategory" jdbcType="VARCHAR" />
        <result column="terminal_no" property="terminalNo" jdbcType="VARCHAR" />
        <result column="terminal_ip" property="terminalIp" jdbcType="VARCHAR" />
        <result column="terminal_location" property="terminalLocation" jdbcType="VARCHAR" />
        <result column="notify_url" property="notifyUrl" jdbcType="VARCHAR" />
        <result column="page_notify_url" property="pageNotifyUrl" jdbcType="VARCHAR" />
        <result column="status" property="status" jdbcType="VARCHAR" />
        <result column="bank_order_no" property="bankOrderNo" jdbcType="VARCHAR" />
        <result column="err_msg_cd" property="errMsgCd" jdbcType="VARCHAR" />
        <result column="err_msg_info" property="errMsgInfo" jdbcType="VARCHAR" />
        <result column="order_complete_time" property="orderCompleteTime" jdbcType="VARCHAR" />
        <result column="receive_notify_time" property="receiveNotifyTime" jdbcType="VARCHAR" />
        <result column="refund_times" property="refundTimes" jdbcType="INTEGER" />
        <result column="success_refund_amount" property="successRefundAmount" jdbcType="DECIMAL" />
        <result column="notify_status" property="notifyStatus" jdbcType="VARCHAR" />
        <result column="send_notify_time" property="sendNotifyTime" jdbcType="VARCHAR" />
        <result column="extra" property="extra" jdbcType="VARCHAR" />
        <result column="message_identification" property="messageIdentification" jdbcType="VARCHAR" />
        <result column="tm_smp" property="tmSmp" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        out_order_no, trade_jrn_no, order_date, order_time, order_time_expire, channel_code,
        pay_way, scene, order_type, account_date, product_name, product_desc, order_amount,
        bus_type, merchant_no, org_merchant_no, wallet_id, biz_type, biz_category, terminal_no,
        terminal_ip, terminal_location, notify_url, page_notify_url, status, bank_order_no,
        err_msg_cd, err_msg_info, order_complete_time, receive_notify_time, refund_times,
        success_refund_amount, notify_status, send_notify_time, extra, message_identification,
        tm_smp
    </sql>


    <select id="getByOutOrderNo" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from pay_order
        where out_order_no = #{outOrderNo,jdbcType=VARCHAR}
    </select>

    <update id="updateTradeClose" parameterType="com.cmpay.dceppay.entity.pay.PayOrderDO">
        update pay_order
        set status = #{status,jdbcType=VARCHAR},
            tm_smp = #{tmSmp,jdbcType=VARCHAR}
        where out_order_no = #{outOrderNo,jdbcType=VARCHAR}
          and status = 'WAIT_PAY'
    </update>


    <select id="findWaitPayList" resultMap="BaseResultMap" parameterType="com.cmpay.dceppay.entity.pay.PayOrderExtDO">
        select
        <include refid="Base_Column_List"/>
        from pay_order
        WHERE order_date >= #{requestDate,jdbcType=VARCHAR}
        AND CONCAT(order_date, order_time) >= #{orderTimeBegin,jdbcType=VARCHAR}
        AND CONCAT(order_date, order_time) &lt;= #{orderTimeEnd,jdbcType=VARCHAR}
        AND status ='WAIT_PAY'
        LIMIT #{total,jdbcType=INTEGER}
    </select>

    <update id="updateTradeSuccess" parameterType="com.cmpay.dceppay.entity.pay.PayOrderDO">
        update pay_order
        set status = #{status,jdbcType=VARCHAR},
        receive_notify_time = #{receiveNotifyTime,jdbcType=VARCHAR},
        order_complete_time = #{orderCompleteTime,jdbcType=VARCHAR},
        <if test="bankOrderNo != null and bankOrderNo!=''">
            bank_order_no = #{bankOrderNo,jdbcType=VARCHAR},
        </if>
        <if test="accountDate != null and accountDate!=''">
            account_date = #{accountDate,jdbcType=VARCHAR},
        </if>
        tm_smp = #{tmSmp,jdbcType=VARCHAR}
        where out_order_no = #{outOrderNo,jdbcType=VARCHAR}
    </update>

    <update id="updateTradeFail" parameterType="com.cmpay.dceppay.entity.pay.PayOrderDO">
        update pay_order
        set status = #{status,jdbcType=VARCHAR},
        receive_notify_time = #{receiveNotifyTime,jdbcType=VARCHAR},
        order_complete_time = #{orderCompleteTime,jdbcType=VARCHAR},
        <if test="errMsgCd != null">
            err_msg_cd = #{errMsgCd,jdbcType=VARCHAR},
        </if>
        <if test="errMsgInfo != null">
            err_msg_info = #{errMsgInfo,jdbcType=VARCHAR},
        </if>
        tm_smp = #{tmSmp,jdbcType=VARCHAR}
        where out_order_no = #{outOrderNo,jdbcType=VARCHAR}
    </update>

    <update id="updateRefundInfo" parameterType="com.cmpay.dceppay.entity.pay.PayOrderRefundInfoUpdateDO">
        update pay_order
        set status               = #{status,jdbcType=VARCHAR},
            refund_times=refund_times + 1,
            success_refund_amount= success_refund_amount + #{refundAmount,jdbcType=DECIMAL},
            tm_smp               = #{tmSmp,jdbcType=VARCHAR}
        where out_order_no = #{outOrderNo,jdbcType=VARCHAR}
    </update>


    <update id="updateNotifyInfo" parameterType="com.cmpay.dceppay.entity.pay.PayOrderNotifyInfoUpdateDO">
        update pay_order
        set notify_status    = #{notifyStatus,jdbcType=VARCHAR},
            send_notify_time = #{sendNotifyTime,jdbcType=VARCHAR},
            tm_smp           = #{tmSmp,jdbcType=VARCHAR}
        where out_order_no = #{outOrderNo,jdbcType=VARCHAR}
    </update>

    <update id="updateAccountDate" parameterType="com.cmpay.dceppay.entity.pay.PayOrderAccountInfoUpdateDO">
        update pay_order
        set  account_date = #{accountDate,jdbcType=VARCHAR},
            tm_smp           = #{tmSmp,jdbcType=VARCHAR}
        where out_order_no = #{outOrderNo,jdbcType=VARCHAR}
    </update>
</mapper>