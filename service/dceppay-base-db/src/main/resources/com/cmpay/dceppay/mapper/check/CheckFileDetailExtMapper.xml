<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.dceppay.dao.check.ICheckFileDetailExtDao">

    <resultMap id="BaseResultMap" type="com.cmpay.dceppay.entity.check.CheckFileDetailDO">
        <id column="out_order_no" property="outOrderNo" jdbcType="VARCHAR"/>
        <id column="refund_order_no" property="refundOrderNo" jdbcType="VARCHAR"/>
        <result column="check_file_date" property="checkFileDate" jdbcType="VARCHAR"/>
        <result column="order_complete_time" property="orderCompleteTime" jdbcType="VARCHAR"/>
        <result column="message_number" property="messageNumber" jdbcType="VARCHAR"/>
        <result column="message_identifier" property="messageIdentifier" jdbcType="VARCHAR"/>
        <result column="accept_institution_code" property="acceptInstitutionCode" jdbcType="VARCHAR"/>
        <result column="pay_institution_code" property="payInstitutionCode" jdbcType="VARCHAR"/>
        <result column="receive_institution_code" property="receiveInstitutionCode" jdbcType="VARCHAR"/>
        <result column="currency_code" property="currencyCode" jdbcType="VARCHAR"/>
        <result column="order_amount" property="orderAmount" jdbcType="DECIMAL"/>
        <result column="check_type" property="checkType" jdbcType="VARCHAR"/>
        <result column="bank_order_no" property="bankOrderNo" jdbcType="VARCHAR"/>
        <result column="bank_refund_no" property="bankRefundNo" jdbcType="VARCHAR"/>
        <result column="order_status" property="orderStatus" jdbcType="VARCHAR"/>
        <result column="trade_desc" property="tradeDesc" jdbcType="VARCHAR"/>
        <result column="check_status" property="checkStatus" jdbcType="VARCHAR"/>
        <result column="check_complete_time" property="checkCompleteTime" jdbcType="VARCHAR"/>
        <result column="tm_smp" property="tmSmp" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap id="summaryResultMap" type="com.cmpay.dceppay.entity.check.CheckSummaryExtDO" >
        <result column="total_count" property="totalCount" jdbcType="INTEGER" />
        <result column="total_amount" property="totalAmount" jdbcType="DECIMAL" />
        <result column="payment_count" property="paymentCount" jdbcType="INTEGER" />
        <result column="payment_amount" property="paymentAmount" jdbcType="DECIMAL" />
        <result column="refund_count" property="refundCount" jdbcType="INTEGER" />
        <result column="refund_amount" property="refundAmount" jdbcType="DECIMAL" />
        <result column="total_success_count" property="totalSuccessCount" jdbcType="INTEGER" />
        <result column="total_success_amount" property="totalSuccessAmount" jdbcType="DECIMAL" />
        <result column="payment_success_count" property="paymentSuccessCount" jdbcType="INTEGER" />
        <result column="payment_success_amount" property="paymentSuccessAmount" jdbcType="DECIMAL" />
        <result column="refund_success_count" property="refundSuccessCount" jdbcType="INTEGER" />
        <result column="refund_success_amount" property="refundSuccessAmount" jdbcType="DECIMAL" />
    </resultMap>

    <sql id="Base_Column_List">
        out_order_no
        , refund_order_no, check_file_date, order_complete_time, message_number,
        message_identifier, accept_institution_code, pay_institution_code, receive_institution_code, 
        currency_code, order_amount, check_type, bank_order_no, bank_refund_no, order_status, 
        trade_desc, check_status, check_complete_time, tm_smp
    </sql>

    <select id="listWaitDetail" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from check_file_detail
        where check_file_date = #{checkFileDate,jdbcType=VARCHAR}
        and check_status='wait'
    </select>

    <update id="updatePaymentLongNoOrder" parameterType="com.cmpay.dceppay.entity.check.CheckFileDetailDO">
        update check_file_detail
        <set>
            check_status = #{checkStatus,jdbcType=VARCHAR},
            check_complete_time = #{checkCompleteTime,jdbcType=VARCHAR},
            tm_smp = #{tmSmp,jdbcType=VARCHAR}
        </set>
        where out_order_no = #{outOrderNo,jdbcType=VARCHAR}
        and refund_order_no = #{refundOrderNo,jdbcType=VARCHAR}
        and check_file_date = #{checkFileDate,jdbcType=VARCHAR}
    </update>
    <update id="updatePaymentLongStsError" parameterType="com.cmpay.dceppay.entity.check.CheckFileDetailDO">
        update check_file_detail
        <set>
            check_status = #{checkStatus,jdbcType=VARCHAR},
            check_complete_time = #{checkCompleteTime,jdbcType=VARCHAR},
            tm_smp = #{tmSmp,jdbcType=VARCHAR}
        </set>
        where out_order_no = #{outOrderNo,jdbcType=VARCHAR}
        and refund_order_no = #{refundOrderNo,jdbcType=VARCHAR}
        and check_file_date = #{checkFileDate,jdbcType=VARCHAR}
    </update>

    <update id="updateCheckComplete" parameterType="com.cmpay.dceppay.entity.check.CheckFileDetailDO">
        update check_file_detail
        <set>
            check_status = #{checkStatus,jdbcType=VARCHAR},
            check_complete_time = #{checkCompleteTime,jdbcType=VARCHAR},
            tm_smp = #{tmSmp,jdbcType=VARCHAR}
        </set>
        where out_order_no = #{outOrderNo,jdbcType=VARCHAR}
        and refund_order_no = #{refundOrderNo,jdbcType=VARCHAR}
        and check_file_date = #{checkFileDate,jdbcType=VARCHAR}
    </update>

    <update id="updateAmountError" parameterType="com.cmpay.dceppay.entity.check.CheckFileDetailDO">
        update check_file_detail
        <set>
            check_status = #{checkStatus,jdbcType=VARCHAR},
            check_complete_time = #{checkCompleteTime,jdbcType=VARCHAR},
            tm_smp = #{tmSmp,jdbcType=VARCHAR}
        </set>
        where out_order_no = #{outOrderNo,jdbcType=VARCHAR}
        and refund_order_no = #{refundOrderNo,jdbcType=VARCHAR}
        and check_file_date = #{checkFileDate,jdbcType=VARCHAR}
    </update>

    <update id="updateRefundShortNoOrder" parameterType="com.cmpay.dceppay.entity.check.CheckFileDetailDO">
        update check_file_detail
        <set>
            check_status = #{checkStatus,jdbcType=VARCHAR},
            check_complete_time = #{checkCompleteTime,jdbcType=VARCHAR},
            tm_smp = #{tmSmp,jdbcType=VARCHAR}
        </set>
        where out_order_no = #{outOrderNo,jdbcType=VARCHAR}
        and refund_order_no = #{refundOrderNo,jdbcType=VARCHAR}
        and check_file_date = #{checkFileDate,jdbcType=VARCHAR}
    </update>

    <update id="updateRefundShortStsError" parameterType="com.cmpay.dceppay.entity.check.CheckFileDetailDO">
        update check_file_detail
        <set>
            check_status = #{checkStatus,jdbcType=VARCHAR},
            check_complete_time = #{checkCompleteTime,jdbcType=VARCHAR},
            tm_smp = #{tmSmp,jdbcType=VARCHAR}
        </set>
        where out_order_no = #{outOrderNo,jdbcType=VARCHAR}
        and refund_order_no = #{refundOrderNo,jdbcType=VARCHAR}
        and check_file_date = #{checkFileDate,jdbcType=VARCHAR}
    </update>

    <select id="getPaymentShortDoubt" resultMap="BaseResultMap"  parameterType="com.cmpay.dceppay.entity.check.CheckFileDetailDO">
        select
        <include refid="Base_Column_List"/>
        from check_file_detail
        where out_order_no = #{outOrderNo,jdbcType=VARCHAR}
        and refund_order_no = #{refundOrderNo,jdbcType=VARCHAR}
        and check_file_date = #{checkFileDate,jdbcType=VARCHAR}
        and check_type = #{checkType,jdbcType=VARCHAR}
    </select>

    <select id="getRefundLogDoubt" resultMap="BaseResultMap" parameterType="com.cmpay.dceppay.entity.check.CheckFileDetailDO">
        select
        <include refid="Base_Column_List"/>
        from check_file_detail
        where out_order_no = #{outOrderNo,jdbcType=VARCHAR}
        and refund_order_no = #{refundOrderNo,jdbcType=VARCHAR}
        and check_file_date = #{checkFileDate,jdbcType=VARCHAR}
        and check_type = #{checkType,jdbcType=VARCHAR}
    </select>

    <select id="summaryInfo" resultMap="summaryResultMap">
        SELECT COALESCE(COUNT(*), 0)          AS total_count,
               COALESCE(SUM(order_amount), 0) AS total_amount,
               COALESCE(SUM(CASE WHEN check_type = 'PAYMENT' THEN 1 ELSE 0 END),
                        0)                    AS payment_count,
               COALESCE(SUM(CASE WHEN check_type = 'PAYMENT' THEN order_amount ELSE 0 END),
                        0)                    AS payment_amount,
               COALESCE(SUM(CASE WHEN check_type = 'REFUND' THEN 1 ELSE 0 END),
                        0)                    AS refund_count,
               COALESCE(SUM(CASE WHEN check_type = 'REFUND' THEN order_amount ELSE 0 END),
                        0)                    AS refund_amount,
               COALESCE(SUM(CASE WHEN order_status = 'PR00' THEN 1 ELSE 0 END),
                        0)                    AS total_success_count,
               COALESCE(SUM(CASE WHEN order_status = 'PR00' THEN order_amount ELSE 0 END),
                        0)                    AS total_success_amount,
               COALESCE(SUM(CASE
                                WHEN check_type = 'PAYMENT' AND order_status = 'PR00' THEN 1
                                ELSE 0 END),
                        0)                    AS payment_success_count,
               COALESCE(SUM(CASE WHEN check_type = 'PAYMENT' AND order_status = 'PR00' THEN order_amount ELSE 0 END),
                        0)                    AS payment_success_amount,
               COALESCE(
                       SUM(CASE
                               WHEN check_type = 'REFUND' AND order_status = 'PR00' THEN 1
                               ELSE 0 END),
                       0)                     AS refund_success_count,
               COALESCE(SUM(CASE WHEN check_type = 'REFUND' AND order_status = 'PR00' THEN order_amount ELSE 0 END),
                        0)                    AS refund_success_amount
        FROM check_file_detail
        WHERE check_file_date = #{checkFileDate,jdbcType=VARCHAR}
    </select>


    <update id="updateCheckErrorComplete" parameterType="com.cmpay.dceppay.entity.check.CheckFileDetailDO">
        update check_file_detail
        <set>
            check_status = #{checkStatus,jdbcType=VARCHAR},
            check_complete_time = #{checkCompleteTime,jdbcType=VARCHAR},
            tm_smp = #{tmSmp,jdbcType=VARCHAR}
        </set>
        where out_order_no = #{outOrderNo,jdbcType=VARCHAR}
        and refund_order_no = #{refundOrderNo,jdbcType=VARCHAR}
    </update>


    <update id="updateCheckErrorAmount" parameterType="com.cmpay.dceppay.entity.check.CheckFileDetailDO">
        update check_file_detail
        <set>
            check_status = #{checkStatus,jdbcType=VARCHAR},
            check_complete_time = #{checkCompleteTime,jdbcType=VARCHAR},
            order_amount = #{orderAmount,jdbcType=DECIMAL},
            tm_smp = #{tmSmp,jdbcType=VARCHAR}
        </set>
        where out_order_no = #{outOrderNo,jdbcType=VARCHAR}
        and refund_order_no = #{refundOrderNo,jdbcType=VARCHAR}
    </update>

</mapper>