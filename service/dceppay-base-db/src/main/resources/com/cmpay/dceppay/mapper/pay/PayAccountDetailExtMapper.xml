<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.dceppay.dao.pay.IPayAccountDetailExtDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.dceppay.entity.pay.PayAccountDetailDO" >
        <id column="jrn_no" property="jrnNo" jdbcType="VARCHAR" />
        <id column="dc_flag" property="dcFlag" jdbcType="VARCHAR" />
        <id column="account_no" property="accountNo" jdbcType="VARCHAR" />
        <result column="order_date" property="orderDate" jdbcType="VARCHAR" />
        <result column="account_name" property="accountName" jdbcType="VARCHAR" />
        <result column="amount" property="amount" jdbcType="DECIMAL" />
    </resultMap>

    <sql id="Base_Column_List" >
        jrn_no, dc_flag, account_no, order_date, account_name, amount
    </sql>


</mapper>