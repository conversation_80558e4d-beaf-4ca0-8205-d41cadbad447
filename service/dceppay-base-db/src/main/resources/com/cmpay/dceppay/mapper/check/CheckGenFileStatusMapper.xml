<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.dceppay.dao.check.ICheckGenFileStatusDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.dceppay.entity.check.CheckGenFileStatusDO" >
        <id column="check_file_date" property="checkFileDate" jdbcType="VARCHAR" />
        <id column="channel_code" property="channelCode" jdbcType="VARCHAR" />
        <result column="total_count" property="totalCount" jdbcType="INTEGER" />
        <result column="total_amount" property="totalAmount" jdbcType="DECIMAL" />
        <result column="payment_count" property="paymentCount" jdbcType="INTEGER" />
        <result column="payment_amount" property="paymentAmount" jdbcType="DECIMAL" />
        <result column="refund_count" property="refundCount" jdbcType="INTEGER" />
        <result column="refund_amount" property="refundAmount" jdbcType="DECIMAL" />
        <result column="file_start_time" property="fileStartTime" jdbcType="VARCHAR" />
        <result column="file_end_time" property="fileEndTime" jdbcType="VARCHAR" />
        <result column="file_send_time" property="fileSendTime" jdbcType="VARCHAR" />
        <result column="file_status" property="fileStatus" jdbcType="VARCHAR" />
        <result column="tm_smp" property="tmSmp" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        check_file_date, channel_code, total_count, total_amount, payment_count, payment_amount, 
        refund_count, refund_amount, file_start_time, file_end_time, file_send_time, file_status, 
        tm_smp
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="com.cmpay.dceppay.entity.check.CheckGenFileStatusDOKey" >
        select 
        <include refid="Base_Column_List" />
        from check_gen_file_status
        where check_file_date = #{checkFileDate,jdbcType=VARCHAR}
          and channel_code = #{channelCode,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="com.cmpay.dceppay.entity.check.CheckGenFileStatusDOKey" >
        delete from check_gen_file_status
        where check_file_date = #{checkFileDate,jdbcType=VARCHAR}
          and channel_code = #{channelCode,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.cmpay.dceppay.entity.check.CheckGenFileStatusDO" >
        insert into check_gen_file_status
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="checkFileDate != null" >
                check_file_date,
            </if>
            <if test="channelCode != null" >
                channel_code,
            </if>
            <if test="totalCount != null" >
                total_count,
            </if>
            <if test="totalAmount != null" >
                total_amount,
            </if>
            <if test="paymentCount != null" >
                payment_count,
            </if>
            <if test="paymentAmount != null" >
                payment_amount,
            </if>
            <if test="refundCount != null" >
                refund_count,
            </if>
            <if test="refundAmount != null" >
                refund_amount,
            </if>
            <if test="fileStartTime != null" >
                file_start_time,
            </if>
            <if test="fileEndTime != null" >
                file_end_time,
            </if>
            <if test="fileSendTime != null" >
                file_send_time,
            </if>
            <if test="fileStatus != null" >
                file_status,
            </if>
            <if test="tmSmp != null" >
                tm_smp,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="checkFileDate != null" >
                #{checkFileDate,jdbcType=VARCHAR},
            </if>
            <if test="channelCode != null" >
                #{channelCode,jdbcType=VARCHAR},
            </if>
            <if test="totalCount != null" >
                #{totalCount,jdbcType=INTEGER},
            </if>
            <if test="totalAmount != null" >
                #{totalAmount,jdbcType=DECIMAL},
            </if>
            <if test="paymentCount != null" >
                #{paymentCount,jdbcType=INTEGER},
            </if>
            <if test="paymentAmount != null" >
                #{paymentAmount,jdbcType=DECIMAL},
            </if>
            <if test="refundCount != null" >
                #{refundCount,jdbcType=INTEGER},
            </if>
            <if test="refundAmount != null" >
                #{refundAmount,jdbcType=DECIMAL},
            </if>
            <if test="fileStartTime != null" >
                #{fileStartTime,jdbcType=VARCHAR},
            </if>
            <if test="fileEndTime != null" >
                #{fileEndTime,jdbcType=VARCHAR},
            </if>
            <if test="fileSendTime != null" >
                #{fileSendTime,jdbcType=VARCHAR},
            </if>
            <if test="fileStatus != null" >
                #{fileStatus,jdbcType=VARCHAR},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.cmpay.dceppay.entity.check.CheckGenFileStatusDO" >
        update check_gen_file_status
        <set >
            <if test="totalCount != null" >
                total_count = #{totalCount,jdbcType=INTEGER},
            </if>
            <if test="totalAmount != null" >
                total_amount = #{totalAmount,jdbcType=DECIMAL},
            </if>
            <if test="paymentCount != null" >
                payment_count = #{paymentCount,jdbcType=INTEGER},
            </if>
            <if test="paymentAmount != null" >
                payment_amount = #{paymentAmount,jdbcType=DECIMAL},
            </if>
            <if test="refundCount != null" >
                refund_count = #{refundCount,jdbcType=INTEGER},
            </if>
            <if test="refundAmount != null" >
                refund_amount = #{refundAmount,jdbcType=DECIMAL},
            </if>
            <if test="fileStartTime != null" >
                file_start_time = #{fileStartTime,jdbcType=VARCHAR},
            </if>
            <if test="fileEndTime != null" >
                file_end_time = #{fileEndTime,jdbcType=VARCHAR},
            </if>
            <if test="fileSendTime != null" >
                file_send_time = #{fileSendTime,jdbcType=VARCHAR},
            </if>
            <if test="fileStatus != null" >
                file_status = #{fileStatus,jdbcType=VARCHAR},
            </if>
            <if test="tmSmp != null" >
                tm_smp = #{tmSmp,jdbcType=VARCHAR},
            </if>
        </set>
        where check_file_date = #{checkFileDate,jdbcType=VARCHAR}
          and channel_code = #{channelCode,jdbcType=VARCHAR}
    </update>

    <select id="find" resultMap="BaseResultMap" parameterType="com.cmpay.dceppay.entity.check.CheckGenFileStatusDO" >
        select 
        <include refid="Base_Column_List" />
        from check_gen_file_status
        <where >
            <if test="checkFileDate != null" >
                and check_file_date = #{checkFileDate,jdbcType=VARCHAR}
            </if>
            <if test="channelCode != null" >
                and channel_code = #{channelCode,jdbcType=VARCHAR}
            </if>
            <if test="totalCount != null" >
                and total_count = #{totalCount,jdbcType=INTEGER}
            </if>
            <if test="totalAmount != null" >
                and total_amount = #{totalAmount,jdbcType=DECIMAL}
            </if>
            <if test="paymentCount != null" >
                and payment_count = #{paymentCount,jdbcType=INTEGER}
            </if>
            <if test="paymentAmount != null" >
                and payment_amount = #{paymentAmount,jdbcType=DECIMAL}
            </if>
            <if test="refundCount != null" >
                and refund_count = #{refundCount,jdbcType=INTEGER}
            </if>
            <if test="refundAmount != null" >
                and refund_amount = #{refundAmount,jdbcType=DECIMAL}
            </if>
            <if test="fileStartTime != null" >
                and file_start_time = #{fileStartTime,jdbcType=VARCHAR}
            </if>
            <if test="fileEndTime != null" >
                and file_end_time = #{fileEndTime,jdbcType=VARCHAR}
            </if>
            <if test="fileSendTime != null" >
                and file_send_time = #{fileSendTime,jdbcType=VARCHAR}
            </if>
            <if test="fileStatus != null" >
                and file_status = #{fileStatus,jdbcType=VARCHAR}
            </if>
            <if test="tmSmp != null" >
                and tm_smp = #{tmSmp,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
</mapper>