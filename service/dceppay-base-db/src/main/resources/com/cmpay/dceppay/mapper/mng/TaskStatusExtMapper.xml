<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.dceppay.dao.mng.ITaskStatusExtDao">

    <resultMap id="BaseResultMap" type="com.cmpay.dceppay.entity.mng.TaskStatusDO">
        <id column="task_date" property="taskDate" jdbcType="VARCHAR"/>
        <id column="task_key" property="taskKey" jdbcType="VARCHAR"/>
        <result column="task_desc" property="taskDesc" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="result" property="result" jdbcType="VARCHAR"/>
        <result column="created_time" property="createdTime" jdbcType="VARCHAR"/>
        <result column="updated_time" property="updatedTime" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        task_date
        , task_key, task_desc, status, result, created_time, updated_time
    </sql>
</mapper>