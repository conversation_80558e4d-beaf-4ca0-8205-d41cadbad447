<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.dceppay.dao.pay.IPayAccountDetailDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.dceppay.entity.pay.PayAccountDetailDO" >
        <id column="jrn_no" property="jrnNo" jdbcType="VARCHAR" />
        <id column="dc_flag" property="dcFlag" jdbcType="VARCHAR" />
        <id column="account_no" property="accountNo" jdbcType="VARCHAR" />
        <result column="order_date" property="orderDate" jdbcType="VARCHAR" />
        <result column="account_name" property="accountName" jdbcType="VARCHAR" />
        <result column="amount" property="amount" jdbcType="DECIMAL" />
    </resultMap>

    <sql id="Base_Column_List" >
        jrn_no, dc_flag, account_no, order_date, account_name, amount
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="com.cmpay.dceppay.entity.pay.PayAccountDetailDOKey" >
        select 
        <include refid="Base_Column_List" />
        from pay_account_detail
        where jrn_no = #{jrnNo,jdbcType=VARCHAR}
          and dc_flag = #{dcFlag,jdbcType=VARCHAR}
          and account_no = #{accountNo,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="com.cmpay.dceppay.entity.pay.PayAccountDetailDOKey" >
        delete from pay_account_detail
        where jrn_no = #{jrnNo,jdbcType=VARCHAR}
          and dc_flag = #{dcFlag,jdbcType=VARCHAR}
          and account_no = #{accountNo,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.cmpay.dceppay.entity.pay.PayAccountDetailDO" >
        insert into pay_account_detail
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="jrnNo != null" >
                jrn_no,
            </if>
            <if test="dcFlag != null" >
                dc_flag,
            </if>
            <if test="accountNo != null" >
                account_no,
            </if>
            <if test="orderDate != null" >
                order_date,
            </if>
            <if test="accountName != null" >
                account_name,
            </if>
            <if test="amount != null" >
                amount,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="jrnNo != null" >
                #{jrnNo,jdbcType=VARCHAR},
            </if>
            <if test="dcFlag != null" >
                #{dcFlag,jdbcType=VARCHAR},
            </if>
            <if test="accountNo != null" >
                #{accountNo,jdbcType=VARCHAR},
            </if>
            <if test="orderDate != null" >
                #{orderDate,jdbcType=VARCHAR},
            </if>
            <if test="accountName != null" >
                #{accountName,jdbcType=VARCHAR},
            </if>
            <if test="amount != null" >
                #{amount,jdbcType=DECIMAL},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.cmpay.dceppay.entity.pay.PayAccountDetailDO" >
        update pay_account_detail
        <set >
            <if test="orderDate != null" >
                order_date = #{orderDate,jdbcType=VARCHAR},
            </if>
            <if test="accountName != null" >
                account_name = #{accountName,jdbcType=VARCHAR},
            </if>
            <if test="amount != null" >
                amount = #{amount,jdbcType=DECIMAL},
            </if>
        </set>
        where jrn_no = #{jrnNo,jdbcType=VARCHAR}
          and dc_flag = #{dcFlag,jdbcType=VARCHAR}
          and account_no = #{accountNo,jdbcType=VARCHAR}
    </update>

    <select id="find" resultMap="BaseResultMap" parameterType="com.cmpay.dceppay.entity.pay.PayAccountDetailDO" >
        select 
        <include refid="Base_Column_List" />
        from pay_account_detail
        <where >
            <if test="jrnNo != null" >
                and jrn_no = #{jrnNo,jdbcType=VARCHAR}
            </if>
            <if test="dcFlag != null" >
                and dc_flag = #{dcFlag,jdbcType=VARCHAR}
            </if>
            <if test="accountNo != null" >
                and account_no = #{accountNo,jdbcType=VARCHAR}
            </if>
            <if test="orderDate != null" >
                and order_date = #{orderDate,jdbcType=VARCHAR}
            </if>
            <if test="accountName != null" >
                and account_name = #{accountName,jdbcType=VARCHAR}
            </if>
            <if test="amount != null" >
                and amount = #{amount,jdbcType=DECIMAL}
            </if>
        </where>
    </select>
</mapper>