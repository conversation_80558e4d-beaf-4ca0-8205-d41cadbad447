<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.dceppay.dao.smartmerchant.IICBCMerchantExtDao">

    <resultMap id="BaseResultMap" type="com.cmpay.dceppay.entity.smartmerchant.ICBCMerchantDO" >
        <id column="merchant_id" property="merchantId" jdbcType="VARCHAR" />
        <result column="operator_merchant_id" property="operatorMerchantId" jdbcType="VARCHAR" />
        <result column="service_merchant_flag" property="serviceMerchantFlag" jdbcType="VARCHAR" />
        <result column="service_id" property="serviceId" jdbcType="VARCHAR" />
        <result column="merchant_name" property="merchantName" jdbcType="VARCHAR" />
        <result column="merchant_show_name" property="merchantShowName" jdbcType="VARCHAR" />
        <result column="merchant_short_name" property="merchantShortName" jdbcType="VARCHAR" />
        <result column="merchant_category" property="merchantCategory" jdbcType="VARCHAR" />
        <result column="buss_code" property="bussCode" jdbcType="VARCHAR" />
        <result column="buss_type" property="bussType" jdbcType="VARCHAR" />
        <result column="merchant_type" property="merchantType" jdbcType="VARCHAR" />
        <result column="merchant_license_type" property="merchantLicenseType" jdbcType="VARCHAR" />
        <result column="merchant_license" property="merchantLicense" jdbcType="VARCHAR" />
        <result column="contact_name" property="contactName" jdbcType="VARCHAR" />
        <result column="service_phone" property="servicePhone" jdbcType="VARCHAR" />
        <result column="contact_phone" property="contactPhone" jdbcType="VARCHAR" />
        <result column="contact_email" property="contactEmail" jdbcType="VARCHAR" />
        <result column="unify_entry_flag" property="unifyEntryFlag" jdbcType="VARCHAR" />
        <result column="settle_cycle" property="settleCycle" jdbcType="VARCHAR" />
        <result column="acc_type" property="accType" jdbcType="VARCHAR" />
        <result column="acc_name" property="accName" jdbcType="VARCHAR" />
        <result column="acc_no" property="accNo" jdbcType="VARCHAR" />
        <result column="acc_bank_name" property="accBankName" jdbcType="VARCHAR" />
        <result column="acc_bank_code" property="accBankCode" jdbcType="VARCHAR" />
        <result column="wallet_id" property="walletId" jdbcType="VARCHAR" />
        <result column="wallet_name" property="walletName" jdbcType="VARCHAR" />
        <result column="merchant_wallet_name" property="merchantWalletName" jdbcType="VARCHAR" />
        <result column="merchant_wallet_id" property="merchantWalletId" jdbcType="VARCHAR" />
        <result column="protocol_wallet_name" property="protocolWalletName" jdbcType="VARCHAR" />
        <result column="protocol_wallet_id" property="protocolWalletId" jdbcType="VARCHAR" />
        <result column="remark" property="remark" jdbcType="VARCHAR" />
        <result column="onboard_status" property="onboardStatus" jdbcType="VARCHAR" />
        <result column="service_status" property="serviceStatus" jdbcType="VARCHAR" />
        <result column="onboard_time" property="onboardTime" jdbcType="VARCHAR" />
        <result column="create_id" property="createId" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="VARCHAR" />
        <result column="update_id" property="updateId" jdbcType="VARCHAR" />
        <result column="update_time" property="updateTime" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        merchant_id, operator_merchant_id, service_merchant_flag, service_id, merchant_name,
        merchant_show_name, merchant_short_name, merchant_category, buss_code, buss_type,
        merchant_type, merchant_license_type, merchant_license, contact_name, service_phone,
        contact_phone, contact_email, unify_entry_flag, settle_cycle, acc_type, acc_name,
        acc_no, acc_bank_name, acc_bank_code, wallet_id, wallet_name, merchant_wallet_name,
        merchant_wallet_id, protocol_wallet_name, protocol_wallet_id, remark, onboard_status,
        service_status, onboard_time, create_id, create_time, update_id, update_time
    </sql>



    <select id="getMerchantByMerchantID" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from icbc_merchant
        where merchant_id = #{merchantId,jdbcType=VARCHAR}
    </select>


    <update id="updateOnboarding" parameterType="com.cmpay.dceppay.entity.smartmerchant.ICBCMerchantDO" >
        update icbc_merchant
        <set>
            onboard_status = #{onboardStatus,jdbcType=VARCHAR},
            update_id = #{updateId,jdbcType=VARCHAR},
            update_time = #{updateTime,jdbcType=VARCHAR}
        </set>
        where merchant_id = #{merchantId,jdbcType=VARCHAR}
    </update>


    <update id="updateOnboardFail" parameterType="com.cmpay.dceppay.entity.smartmerchant.ICBCMerchantDO" >
        update icbc_merchant
        <set>
            onboard_status = #{onboardStatus,jdbcType=VARCHAR},
            onboard_time = #{onboardTime,jdbcType=VARCHAR},
            update_id = #{updateId,jdbcType=VARCHAR},
            update_time = #{updateTime,jdbcType=VARCHAR},
            onboard_id = #{onboardId,jdbcType=VARCHAR},
            msg_code = #{msgCode,jdbcType=VARCHAR},
            msg_info = #{msgInfo,jdbcType=VARCHAR}
        </set>
        where merchant_id = #{merchantId,jdbcType=VARCHAR}
    </update>

    <update id="updateOnboardSuccess" parameterType="com.cmpay.dceppay.entity.smartmerchant.ICBCMerchantDO" >
        update icbc_merchant
        <set>
            onboard_status = #{onboardStatus,jdbcType=VARCHAR},
            onboard_time = #{onboardTime,jdbcType=VARCHAR},
            operator_merchant_id = #{operatorMerchantId,jdbcType=VARCHAR},
            update_id = #{updateId,jdbcType=VARCHAR},
            update_time = #{updateTime,jdbcType=VARCHAR},
            onboard_id = #{onboardId,jdbcType=VARCHAR},
            msg_code = #{msgCode,jdbcType=VARCHAR},
            msg_info = #{msgInfo,jdbcType=VARCHAR},
            merchant_wallet_name = #{merchantWalletName,jdbcType=VARCHAR},
            merchant_wallet_id = #{merchantWalletId,jdbcType=VARCHAR},
            protocol_wallet_name = #{protocolWalletName,jdbcType=VARCHAR},
            protocol_wallet_id = #{protocolWalletId,jdbcType=VARCHAR}

        </set>
        where merchant_id = #{merchantId,jdbcType=VARCHAR}
    </update>

    <update id="updateOnboardCancel" parameterType="com.cmpay.dceppay.entity.smartmerchant.ICBCMerchantDO" >
        update icbc_merchant
        <set>
            onboard_status = #{onboardStatus,jdbcType=VARCHAR},
            service_status = #{serviceStatus,jdbcType=VARCHAR},
            update_id = #{updateId,jdbcType=VARCHAR},
            update_time = #{updateTime,jdbcType=VARCHAR}
        </set>
        where merchant_id = #{merchantId,jdbcType=VARCHAR}
    </update>
    <update id="updateOnboardCanceling" parameterType="com.cmpay.dceppay.entity.smartmerchant.ICBCMerchantDO" >
        update icbc_merchant
        <set>
            onboard_status = #{onboardStatus,jdbcType=VARCHAR},
            update_id = #{updateId,jdbcType=VARCHAR},
            update_time = #{updateTime,jdbcType=VARCHAR}
        </set>
        where merchant_id = #{merchantId,jdbcType=VARCHAR}
    </update>
    <update id="updateOnboardCancelFail" parameterType="com.cmpay.dceppay.entity.smartmerchant.ICBCMerchantDO" >
        update icbc_merchant
        <set>
            onboard_status = #{onboardStatus,jdbcType=VARCHAR},
            update_id = #{updateId,jdbcType=VARCHAR},
            update_time = #{updateTime,jdbcType=VARCHAR}
        </set>
        where merchant_id = #{merchantId,jdbcType=VARCHAR}
    </update>


</mapper>