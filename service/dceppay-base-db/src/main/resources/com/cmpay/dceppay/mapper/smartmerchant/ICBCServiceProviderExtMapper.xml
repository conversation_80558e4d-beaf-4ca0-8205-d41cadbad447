<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.dceppay.dao.smartmerchant.IICBCServiceProviderExtDao">

    <resultMap id="BaseResultMap" type="com.cmpay.dceppay.entity.smartmerchant.ICBCServiceProviderDO">
        <id column="service_id" property="serviceId" jdbcType="VARCHAR"/>
        <result column="service_provider_id" property="serviceProviderId" jdbcType="VARCHAR"/>
        <result column="service_name" property="serviceName" jdbcType="VARCHAR"/>
        <result column="license_type" property="licenseType" jdbcType="VARCHAR"/>
        <result column="license_no" property="licenseNo" jdbcType="VARCHAR"/>
        <result column="scene_desc" property="sceneDesc" jdbcType="VARCHAR"/>
        <result column="scene_id" property="sceneId" jdbcType="VARCHAR"/>
        <result column="onboard_status" property="onboardStatus" jdbcType="VARCHAR"/>
        <result column="service_status" property="serviceStatus" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="onboard_time" property="onboardTime" jdbcType="VARCHAR"/>
        <result column="create_id" property="createId" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="VARCHAR"/>
        <result column="update_id" property="updateId" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        service_id
        , service_provider_id, service_name, license_type, license_no, scene_desc,
        scene_id, onboard_status, service_status, remark, onboard_time, create_id, create_time, 
        update_id, update_time
    </sql>
    <select id="getServiceProviderInfoByServiceID" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from icbc_service_provider
        where service_id = #{serviceId,jdbcType=VARCHAR}
    </select>


    <update id="updateOnboarding" parameterType="com.cmpay.dceppay.entity.smartmerchant.ICBCServiceProviderDO">
        update icbc_service_provider
        <set>
            onboard_status = #{onboardStatus,jdbcType=VARCHAR},
            update_id = #{updateId,jdbcType=VARCHAR},
            update_time = #{updateTime,jdbcType=VARCHAR}
        </set>
        where service_id = #{serviceId,jdbcType=VARCHAR}

    </update>


    <update id="updateOnboardFail" parameterType="com.cmpay.dceppay.entity.smartmerchant.ICBCServiceProviderDO">
        update icbc_service_provider
        <set>
            onboard_status = #{onboardStatus,jdbcType=VARCHAR},
            update_id = #{updateId,jdbcType=VARCHAR},
            update_time = #{updateTime,jdbcType=VARCHAR},
            onboard_id = #{onboardId,jdbcType=VARCHAR},
            msg_code = #{msgCode,jdbcType=VARCHAR},
            msg_info = #{msgInfo,jdbcType=VARCHAR}
        </set>
        where service_id = #{serviceId,jdbcType=VARCHAR}
    </update>

    <update id="updateOnboardSuccess" parameterType="com.cmpay.dceppay.entity.smartmerchant.ICBCServiceProviderDO">
        update icbc_service_provider
        <set>
            service_provider_id = #{serviceProviderId,jdbcType=VARCHAR},
            onboard_status = #{onboardStatus,jdbcType=VARCHAR},
            onboard_time = #{onboardTime,jdbcType=VARCHAR},
            update_id = #{updateId,jdbcType=VARCHAR},
            update_time = #{updateTime,jdbcType=VARCHAR},
            onboard_id = #{onboardId,jdbcType=VARCHAR},
            msg_code = #{msgCode,jdbcType=VARCHAR},
            msg_info = #{msgInfo,jdbcType=VARCHAR}
        </set>
        where service_id = #{serviceId,jdbcType=VARCHAR}
    </update>

    <update id="updateOnboardCancel" parameterType="com.cmpay.dceppay.entity.smartmerchant.ICBCServiceProviderDO">
        update icbc_service_provider
        <set>
            onboard_status = #{onboardStatus,jdbcType=VARCHAR},
            service_status = #{serviceStatus,jdbcType=VARCHAR},
            update_id = #{updateId,jdbcType=VARCHAR},
            update_time = #{updateTime,jdbcType=VARCHAR}
        </set>
        where merchant_id = #{serviceId,jdbcType=VARCHAR}
    </update>

</mapper>