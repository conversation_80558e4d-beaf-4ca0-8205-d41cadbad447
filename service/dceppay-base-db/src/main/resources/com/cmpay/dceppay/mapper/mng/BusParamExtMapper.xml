<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.dceppay.dao.mng.IBusParamExtDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.dceppay.entity.mng.BusParamDO" >
        <id column="param_no" property="paramNo" jdbcType="VARCHAR" />
        <result column="param_name" property="paramName" jdbcType="VARCHAR" />
        <result column="param_type" property="paramType" jdbcType="VARCHAR" />
        <result column="param_code" property="paramCode" jdbcType="VARCHAR" />
        <result column="param_label" property="paramLabel" jdbcType="VARCHAR" />
        <result column="order_num" property="orderNum" jdbcType="TINYINT" />
        <result column="remark" property="remark" jdbcType="VARCHAR" />
        <result column="create_user_no" property="createUserNo" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="VARCHAR" />
        <result column="update_user_no" property="updateUserNo" jdbcType="VARCHAR" />
        <result column="update_time" property="updateTime" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        param_no, param_name, param_type, param_code, param_label, order_num, remark, create_user_no, 
        create_time, update_user_no, update_time
    </sql>

    <select id="getByTypeAndLabel" resultMap="BaseResultMap" parameterType="com.cmpay.dceppay.entity.mng.BusParamDO" >
        select 
        <include refid="Base_Column_List" />
        from bus_param
        where param_type = #{paramType,jdbcType=VARCHAR}
        and  param_label = #{paramLabel,jdbcType=VARCHAR}
    </select>

    <select id="getByParamType" resultMap="BaseResultMap" parameterType="com.cmpay.dceppay.entity.mng.BusParamDO" >
        select
        <include refid="Base_Column_List" />
        from bus_param
        where param_type = #{paramType,jdbcType=VARCHAR}
    </select>


    <update id="updateFinancialStatus" parameterType="com.cmpay.dceppay.entity.mng.BusParamDO" >
        update bus_param
        <set>
            param_code = #{paramCode,jdbcType=VARCHAR},
            param_label = #{paramLabel,jdbcType=VARCHAR},
            update_time = #{updateTime,jdbcType=VARCHAR},
            <if test="updateUserNo != null">
                update_user_no = #{updateUserNo,jdbcType=VARCHAR},
            </if>
        </set>
        where param_no = #{paramNo,jdbcType=VARCHAR}
    </update>

</mapper>