<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.dceppay.dao.bus.IBusMessageDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.dceppay.entity.bus.BusMessageDO" >
        <id column="message_id" property="messageId" jdbcType="VARCHAR" />
        <result column="message_type" property="messageType" jdbcType="VARCHAR" />
        <result column="message_date" property="messageDate" jdbcType="VARCHAR" />
        <result column="message_time" property="messageTime" jdbcType="VARCHAR" />
        <result column="sender" property="sender" jdbcType="VARCHAR" />
        <result column="receiver" property="receiver" jdbcType="VARCHAR" />
        <result column="message_content" property="messageContent" jdbcType="VARCHAR" />
        <result column="remark" property="remark" jdbcType="VARCHAR" />
        <result column="operate_id" property="operateId" jdbcType="VARCHAR" />
        <result column="tm_smp" property="tmSmp" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        message_id, message_type, message_date, message_time, sender, receiver, message_content, 
        remark, operate_id, tm_smp
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from bus_message
        where message_id = #{messageId,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from bus_message
        where message_id = #{messageId,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.cmpay.dceppay.entity.bus.BusMessageDO" >
        insert into bus_message
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="messageId != null" >
                message_id,
            </if>
            <if test="messageType != null" >
                message_type,
            </if>
            <if test="messageDate != null" >
                message_date,
            </if>
            <if test="messageTime != null" >
                message_time,
            </if>
            <if test="sender != null" >
                sender,
            </if>
            <if test="receiver != null" >
                receiver,
            </if>
            <if test="messageContent != null" >
                message_content,
            </if>
            <if test="remark != null" >
                remark,
            </if>
            <if test="operateId != null" >
                operate_id,
            </if>
            <if test="tmSmp != null" >
                tm_smp,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="messageId != null" >
                #{messageId,jdbcType=VARCHAR},
            </if>
            <if test="messageType != null" >
                #{messageType,jdbcType=VARCHAR},
            </if>
            <if test="messageDate != null" >
                #{messageDate,jdbcType=VARCHAR},
            </if>
            <if test="messageTime != null" >
                #{messageTime,jdbcType=VARCHAR},
            </if>
            <if test="sender != null" >
                #{sender,jdbcType=VARCHAR},
            </if>
            <if test="receiver != null" >
                #{receiver,jdbcType=VARCHAR},
            </if>
            <if test="messageContent != null" >
                #{messageContent,jdbcType=VARCHAR},
            </if>
            <if test="remark != null" >
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="operateId != null" >
                #{operateId,jdbcType=VARCHAR},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.cmpay.dceppay.entity.bus.BusMessageDO" >
        update bus_message
        <set >
            <if test="messageType != null" >
                message_type = #{messageType,jdbcType=VARCHAR},
            </if>
            <if test="messageDate != null" >
                message_date = #{messageDate,jdbcType=VARCHAR},
            </if>
            <if test="messageTime != null" >
                message_time = #{messageTime,jdbcType=VARCHAR},
            </if>
            <if test="sender != null" >
                sender = #{sender,jdbcType=VARCHAR},
            </if>
            <if test="receiver != null" >
                receiver = #{receiver,jdbcType=VARCHAR},
            </if>
            <if test="messageContent != null" >
                message_content = #{messageContent,jdbcType=VARCHAR},
            </if>
            <if test="remark != null" >
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="operateId != null" >
                operate_id = #{operateId,jdbcType=VARCHAR},
            </if>
            <if test="tmSmp != null" >
                tm_smp = #{tmSmp,jdbcType=VARCHAR},
            </if>
        </set>
        where message_id = #{messageId,jdbcType=VARCHAR}
    </update>

    <select id="find" resultMap="BaseResultMap" parameterType="com.cmpay.dceppay.entity.bus.BusMessageDO" >
        select 
        <include refid="Base_Column_List" />
        from bus_message
        <where >
            <if test="messageId != null" >
                and message_id = #{messageId,jdbcType=VARCHAR}
            </if>
            <if test="messageType != null" >
                and message_type = #{messageType,jdbcType=VARCHAR}
            </if>
            <if test="messageDate != null" >
                and message_date = #{messageDate,jdbcType=VARCHAR}
            </if>
            <if test="messageTime != null" >
                and message_time = #{messageTime,jdbcType=VARCHAR}
            </if>
            <if test="sender != null" >
                and sender = #{sender,jdbcType=VARCHAR}
            </if>
            <if test="receiver != null" >
                and receiver = #{receiver,jdbcType=VARCHAR}
            </if>
            <if test="messageContent != null" >
                and message_content = #{messageContent,jdbcType=VARCHAR}
            </if>
            <if test="remark != null" >
                and remark = #{remark,jdbcType=VARCHAR}
            </if>
            <if test="operateId != null" >
                and operate_id = #{operateId,jdbcType=VARCHAR}
            </if>
            <if test="tmSmp != null" >
                and tm_smp = #{tmSmp,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
</mapper>