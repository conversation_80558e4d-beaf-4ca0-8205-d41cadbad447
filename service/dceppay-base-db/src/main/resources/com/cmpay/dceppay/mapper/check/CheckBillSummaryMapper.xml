<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.dceppay.dao.check.ICheckBillSummaryDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.dceppay.entity.check.CheckBillSummaryDO" >
        <id column="check_file_date" property="checkFileDate" jdbcType="VARCHAR" />
        <id column="institution_code" property="institutionCode" jdbcType="VARCHAR" />
        <result column="total_count" property="totalCount" jdbcType="INTEGER" />
        <result column="total_amount" property="totalAmount" jdbcType="DECIMAL" />
        <result column="payment_count" property="paymentCount" jdbcType="INTEGER" />
        <result column="payment_amount" property="paymentAmount" jdbcType="DECIMAL" />
        <result column="refund_count" property="refundCount" jdbcType="INTEGER" />
        <result column="refund_amount" property="refundAmount" jdbcType="DECIMAL" />
        <result column="total_success_count" property="totalSuccessCount" jdbcType="INTEGER" />
        <result column="total_success_amount" property="totalSuccessAmount" jdbcType="DECIMAL" />
        <result column="payment_success_count" property="paymentSuccessCount" jdbcType="INTEGER" />
        <result column="payment_success_amount" property="paymentSuccessAmount" jdbcType="DECIMAL" />
        <result column="refund_success_count" property="refundSuccessCount" jdbcType="INTEGER" />
        <result column="refund_success_amount" property="refundSuccessAmount" jdbcType="DECIMAL" />
        <result column="file_count" property="fileCount" jdbcType="INTEGER" />
        <result column="file_path" property="filePath" jdbcType="VARCHAR" />
        <result column="file_name_list" property="fileNameList" jdbcType="VARCHAR" />
        <result column="digital_env" property="digitalEnv" jdbcType="VARCHAR" />
        <result column="tm_smp" property="tmSmp" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        check_file_date, institution_code, total_count, total_amount, payment_count, payment_amount, 
        refund_count, refund_amount, total_success_count, total_success_amount, payment_success_count, 
        payment_success_amount, refund_success_count, refund_success_amount, file_count, 
        file_path, file_name_list, digital_env, tm_smp
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="com.cmpay.dceppay.entity.check.CheckBillSummaryDOKey" >
        select 
        <include refid="Base_Column_List" />
        from check_bill_summary
        where check_file_date = #{checkFileDate,jdbcType=VARCHAR}
          and institution_code = #{institutionCode,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="com.cmpay.dceppay.entity.check.CheckBillSummaryDOKey" >
        delete from check_bill_summary
        where check_file_date = #{checkFileDate,jdbcType=VARCHAR}
          and institution_code = #{institutionCode,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.cmpay.dceppay.entity.check.CheckBillSummaryDO" >
        insert into check_bill_summary
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="checkFileDate != null" >
                check_file_date,
            </if>
            <if test="institutionCode != null" >
                institution_code,
            </if>
            <if test="totalCount != null" >
                total_count,
            </if>
            <if test="totalAmount != null" >
                total_amount,
            </if>
            <if test="paymentCount != null" >
                payment_count,
            </if>
            <if test="paymentAmount != null" >
                payment_amount,
            </if>
            <if test="refundCount != null" >
                refund_count,
            </if>
            <if test="refundAmount != null" >
                refund_amount,
            </if>
            <if test="totalSuccessCount != null" >
                total_success_count,
            </if>
            <if test="totalSuccessAmount != null" >
                total_success_amount,
            </if>
            <if test="paymentSuccessCount != null" >
                payment_success_count,
            </if>
            <if test="paymentSuccessAmount != null" >
                payment_success_amount,
            </if>
            <if test="refundSuccessCount != null" >
                refund_success_count,
            </if>
            <if test="refundSuccessAmount != null" >
                refund_success_amount,
            </if>
            <if test="fileCount != null" >
                file_count,
            </if>
            <if test="filePath != null" >
                file_path,
            </if>
            <if test="fileNameList != null" >
                file_name_list,
            </if>
            <if test="digitalEnv != null" >
                digital_env,
            </if>
            <if test="tmSmp != null" >
                tm_smp,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="checkFileDate != null" >
                #{checkFileDate,jdbcType=VARCHAR},
            </if>
            <if test="institutionCode != null" >
                #{institutionCode,jdbcType=VARCHAR},
            </if>
            <if test="totalCount != null" >
                #{totalCount,jdbcType=INTEGER},
            </if>
            <if test="totalAmount != null" >
                #{totalAmount,jdbcType=DECIMAL},
            </if>
            <if test="paymentCount != null" >
                #{paymentCount,jdbcType=INTEGER},
            </if>
            <if test="paymentAmount != null" >
                #{paymentAmount,jdbcType=DECIMAL},
            </if>
            <if test="refundCount != null" >
                #{refundCount,jdbcType=INTEGER},
            </if>
            <if test="refundAmount != null" >
                #{refundAmount,jdbcType=DECIMAL},
            </if>
            <if test="totalSuccessCount != null" >
                #{totalSuccessCount,jdbcType=INTEGER},
            </if>
            <if test="totalSuccessAmount != null" >
                #{totalSuccessAmount,jdbcType=DECIMAL},
            </if>
            <if test="paymentSuccessCount != null" >
                #{paymentSuccessCount,jdbcType=INTEGER},
            </if>
            <if test="paymentSuccessAmount != null" >
                #{paymentSuccessAmount,jdbcType=DECIMAL},
            </if>
            <if test="refundSuccessCount != null" >
                #{refundSuccessCount,jdbcType=INTEGER},
            </if>
            <if test="refundSuccessAmount != null" >
                #{refundSuccessAmount,jdbcType=DECIMAL},
            </if>
            <if test="fileCount != null" >
                #{fileCount,jdbcType=INTEGER},
            </if>
            <if test="filePath != null" >
                #{filePath,jdbcType=VARCHAR},
            </if>
            <if test="fileNameList != null" >
                #{fileNameList,jdbcType=VARCHAR},
            </if>
            <if test="digitalEnv != null" >
                #{digitalEnv,jdbcType=VARCHAR},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.cmpay.dceppay.entity.check.CheckBillSummaryDO" >
        update check_bill_summary
        <set >
            <if test="totalCount != null" >
                total_count = #{totalCount,jdbcType=INTEGER},
            </if>
            <if test="totalAmount != null" >
                total_amount = #{totalAmount,jdbcType=DECIMAL},
            </if>
            <if test="paymentCount != null" >
                payment_count = #{paymentCount,jdbcType=INTEGER},
            </if>
            <if test="paymentAmount != null" >
                payment_amount = #{paymentAmount,jdbcType=DECIMAL},
            </if>
            <if test="refundCount != null" >
                refund_count = #{refundCount,jdbcType=INTEGER},
            </if>
            <if test="refundAmount != null" >
                refund_amount = #{refundAmount,jdbcType=DECIMAL},
            </if>
            <if test="totalSuccessCount != null" >
                total_success_count = #{totalSuccessCount,jdbcType=INTEGER},
            </if>
            <if test="totalSuccessAmount != null" >
                total_success_amount = #{totalSuccessAmount,jdbcType=DECIMAL},
            </if>
            <if test="paymentSuccessCount != null" >
                payment_success_count = #{paymentSuccessCount,jdbcType=INTEGER},
            </if>
            <if test="paymentSuccessAmount != null" >
                payment_success_amount = #{paymentSuccessAmount,jdbcType=DECIMAL},
            </if>
            <if test="refundSuccessCount != null" >
                refund_success_count = #{refundSuccessCount,jdbcType=INTEGER},
            </if>
            <if test="refundSuccessAmount != null" >
                refund_success_amount = #{refundSuccessAmount,jdbcType=DECIMAL},
            </if>
            <if test="fileCount != null" >
                file_count = #{fileCount,jdbcType=INTEGER},
            </if>
            <if test="filePath != null" >
                file_path = #{filePath,jdbcType=VARCHAR},
            </if>
            <if test="fileNameList != null" >
                file_name_list = #{fileNameList,jdbcType=VARCHAR},
            </if>
            <if test="digitalEnv != null" >
                digital_env = #{digitalEnv,jdbcType=VARCHAR},
            </if>
            <if test="tmSmp != null" >
                tm_smp = #{tmSmp,jdbcType=VARCHAR},
            </if>
        </set>
        where check_file_date = #{checkFileDate,jdbcType=VARCHAR}
          and institution_code = #{institutionCode,jdbcType=VARCHAR}
    </update>

    <select id="find" resultMap="BaseResultMap" parameterType="com.cmpay.dceppay.entity.check.CheckBillSummaryDO" >
        select 
        <include refid="Base_Column_List" />
        from check_bill_summary
        <where >
            <if test="checkFileDate != null" >
                and check_file_date = #{checkFileDate,jdbcType=VARCHAR}
            </if>
            <if test="institutionCode != null" >
                and institution_code = #{institutionCode,jdbcType=VARCHAR}
            </if>
            <if test="totalCount != null" >
                and total_count = #{totalCount,jdbcType=INTEGER}
            </if>
            <if test="totalAmount != null" >
                and total_amount = #{totalAmount,jdbcType=DECIMAL}
            </if>
            <if test="paymentCount != null" >
                and payment_count = #{paymentCount,jdbcType=INTEGER}
            </if>
            <if test="paymentAmount != null" >
                and payment_amount = #{paymentAmount,jdbcType=DECIMAL}
            </if>
            <if test="refundCount != null" >
                and refund_count = #{refundCount,jdbcType=INTEGER}
            </if>
            <if test="refundAmount != null" >
                and refund_amount = #{refundAmount,jdbcType=DECIMAL}
            </if>
            <if test="totalSuccessCount != null" >
                and total_success_count = #{totalSuccessCount,jdbcType=INTEGER}
            </if>
            <if test="totalSuccessAmount != null" >
                and total_success_amount = #{totalSuccessAmount,jdbcType=DECIMAL}
            </if>
            <if test="paymentSuccessCount != null" >
                and payment_success_count = #{paymentSuccessCount,jdbcType=INTEGER}
            </if>
            <if test="paymentSuccessAmount != null" >
                and payment_success_amount = #{paymentSuccessAmount,jdbcType=DECIMAL}
            </if>
            <if test="refundSuccessCount != null" >
                and refund_success_count = #{refundSuccessCount,jdbcType=INTEGER}
            </if>
            <if test="refundSuccessAmount != null" >
                and refund_success_amount = #{refundSuccessAmount,jdbcType=DECIMAL}
            </if>
            <if test="fileCount != null" >
                and file_count = #{fileCount,jdbcType=INTEGER}
            </if>
            <if test="filePath != null" >
                and file_path = #{filePath,jdbcType=VARCHAR}
            </if>
            <if test="fileNameList != null" >
                and file_name_list = #{fileNameList,jdbcType=VARCHAR}
            </if>
            <if test="digitalEnv != null" >
                and digital_env = #{digitalEnv,jdbcType=VARCHAR}
            </if>
            <if test="tmSmp != null" >
                and tm_smp = #{tmSmp,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
</mapper>