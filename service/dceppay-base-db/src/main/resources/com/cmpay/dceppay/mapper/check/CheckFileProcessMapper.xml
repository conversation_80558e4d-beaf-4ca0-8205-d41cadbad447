<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.dceppay.dao.check.ICheckFileProcessDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.dceppay.entity.check.CheckFileProcessDO" >
        <id column="check_file_date" property="checkFileDate" jdbcType="VARCHAR" />
        <id column="institution_code" property="institutionCode" jdbcType="VARCHAR" />
        <result column="download_begin_time" property="downloadBeginTime" jdbcType="VARCHAR" />
        <result column="download_end_time" property="downloadEndTime" jdbcType="VARCHAR" />
        <result column="import_begin_time" property="importBeginTime" jdbcType="VARCHAR" />
        <result column="import_end_time" property="importEndTime" jdbcType="VARCHAR" />
        <result column="check_begin_time" property="checkBeginTime" jdbcType="VARCHAR" />
        <result column="check_end_time" property="checkEndTime" jdbcType="VARCHAR" />
        <result column="check_status" property="checkStatus" jdbcType="VARCHAR" />
        <result column="tm_smp" property="tmSmp" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        check_file_date, institution_code, download_begin_time, download_end_time, import_begin_time, 
        import_end_time, check_begin_time, check_end_time, check_status, tm_smp
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="com.cmpay.dceppay.entity.check.CheckFileProcessDOKey" >
        select 
        <include refid="Base_Column_List" />
        from check_file_process
        where check_file_date = #{checkFileDate,jdbcType=VARCHAR}
          and institution_code = #{institutionCode,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="com.cmpay.dceppay.entity.check.CheckFileProcessDOKey" >
        delete from check_file_process
        where check_file_date = #{checkFileDate,jdbcType=VARCHAR}
          and institution_code = #{institutionCode,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.cmpay.dceppay.entity.check.CheckFileProcessDO" >
        insert into check_file_process
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="checkFileDate != null" >
                check_file_date,
            </if>
            <if test="institutionCode != null" >
                institution_code,
            </if>
            <if test="downloadBeginTime != null" >
                download_begin_time,
            </if>
            <if test="downloadEndTime != null" >
                download_end_time,
            </if>
            <if test="importBeginTime != null" >
                import_begin_time,
            </if>
            <if test="importEndTime != null" >
                import_end_time,
            </if>
            <if test="checkBeginTime != null" >
                check_begin_time,
            </if>
            <if test="checkEndTime != null" >
                check_end_time,
            </if>
            <if test="checkStatus != null" >
                check_status,
            </if>
            <if test="tmSmp != null" >
                tm_smp,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="checkFileDate != null" >
                #{checkFileDate,jdbcType=VARCHAR},
            </if>
            <if test="institutionCode != null" >
                #{institutionCode,jdbcType=VARCHAR},
            </if>
            <if test="downloadBeginTime != null" >
                #{downloadBeginTime,jdbcType=VARCHAR},
            </if>
            <if test="downloadEndTime != null" >
                #{downloadEndTime,jdbcType=VARCHAR},
            </if>
            <if test="importBeginTime != null" >
                #{importBeginTime,jdbcType=VARCHAR},
            </if>
            <if test="importEndTime != null" >
                #{importEndTime,jdbcType=VARCHAR},
            </if>
            <if test="checkBeginTime != null" >
                #{checkBeginTime,jdbcType=VARCHAR},
            </if>
            <if test="checkEndTime != null" >
                #{checkEndTime,jdbcType=VARCHAR},
            </if>
            <if test="checkStatus != null" >
                #{checkStatus,jdbcType=VARCHAR},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.cmpay.dceppay.entity.check.CheckFileProcessDO" >
        update check_file_process
        <set >
            <if test="downloadBeginTime != null" >
                download_begin_time = #{downloadBeginTime,jdbcType=VARCHAR},
            </if>
            <if test="downloadEndTime != null" >
                download_end_time = #{downloadEndTime,jdbcType=VARCHAR},
            </if>
            <if test="importBeginTime != null" >
                import_begin_time = #{importBeginTime,jdbcType=VARCHAR},
            </if>
            <if test="importEndTime != null" >
                import_end_time = #{importEndTime,jdbcType=VARCHAR},
            </if>
            <if test="checkBeginTime != null" >
                check_begin_time = #{checkBeginTime,jdbcType=VARCHAR},
            </if>
            <if test="checkEndTime != null" >
                check_end_time = #{checkEndTime,jdbcType=VARCHAR},
            </if>
            <if test="checkStatus != null" >
                check_status = #{checkStatus,jdbcType=VARCHAR},
            </if>
            <if test="tmSmp != null" >
                tm_smp = #{tmSmp,jdbcType=VARCHAR},
            </if>
        </set>
        where check_file_date = #{checkFileDate,jdbcType=VARCHAR}
          and institution_code = #{institutionCode,jdbcType=VARCHAR}
    </update>

    <select id="find" resultMap="BaseResultMap" parameterType="com.cmpay.dceppay.entity.check.CheckFileProcessDO" >
        select 
        <include refid="Base_Column_List" />
        from check_file_process
        <where >
            <if test="checkFileDate != null" >
                and check_file_date = #{checkFileDate,jdbcType=VARCHAR}
            </if>
            <if test="institutionCode != null" >
                and institution_code = #{institutionCode,jdbcType=VARCHAR}
            </if>
            <if test="downloadBeginTime != null" >
                and download_begin_time = #{downloadBeginTime,jdbcType=VARCHAR}
            </if>
            <if test="downloadEndTime != null" >
                and download_end_time = #{downloadEndTime,jdbcType=VARCHAR}
            </if>
            <if test="importBeginTime != null" >
                and import_begin_time = #{importBeginTime,jdbcType=VARCHAR}
            </if>
            <if test="importEndTime != null" >
                and import_end_time = #{importEndTime,jdbcType=VARCHAR}
            </if>
            <if test="checkBeginTime != null" >
                and check_begin_time = #{checkBeginTime,jdbcType=VARCHAR}
            </if>
            <if test="checkEndTime != null" >
                and check_end_time = #{checkEndTime,jdbcType=VARCHAR}
            </if>
            <if test="checkStatus != null" >
                and check_status = #{checkStatus,jdbcType=VARCHAR}
            </if>
            <if test="tmSmp != null" >
                and tm_smp = #{tmSmp,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
</mapper>