<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.dceppay.dao.pay.IPayAccountRecordDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.dceppay.entity.pay.PayAccountRecordDO" >
        <id column="jrn_no" property="jrnNo" jdbcType="VARCHAR" />
        <result column="order_date" property="orderDate" jdbcType="VARCHAR" />
        <result column="order_no" property="orderNo" jdbcType="VARCHAR" />
        <result column="order_type" property="orderType" jdbcType="VARCHAR" />
        <result column="bus_type" property="busType" jdbcType="VARCHAR" />
        <result column="bus_channel" property="busChannel" jdbcType="VARCHAR" />
        <result column="trade_type" property="tradeType" jdbcType="VARCHAR" />
        <result column="bus_code" property="busCode" jdbcType="VARCHAR" />
        <result column="account_from" property="accountFrom" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="VARCHAR" />
        <result column="finish_time" property="finishTime" jdbcType="VARCHAR" />
        <result column="handle_status" property="handleStatus" jdbcType="VARCHAR" />
        <result column="account_date" property="accountDate" jdbcType="VARCHAR" />
        <result column="remark" property="remark" jdbcType="VARCHAR" />
        <result column="amount" property="amount" jdbcType="DECIMAL" />
        <result column="tm_smp" property="tmSmp" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        jrn_no, order_date, order_no, order_type, bus_type, bus_channel, trade_type, bus_code, 
        account_from, create_time, finish_time, handle_status, account_date, remark, amount, 
        tm_smp
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from pay_account_record
        where jrn_no = #{jrnNo,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from pay_account_record
        where jrn_no = #{jrnNo,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.cmpay.dceppay.entity.pay.PayAccountRecordDO" >
        insert into pay_account_record
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="jrnNo != null" >
                jrn_no,
            </if>
            <if test="orderDate != null" >
                order_date,
            </if>
            <if test="orderNo != null" >
                order_no,
            </if>
            <if test="orderType != null" >
                order_type,
            </if>
            <if test="busType != null" >
                bus_type,
            </if>
            <if test="busChannel != null" >
                bus_channel,
            </if>
            <if test="tradeType != null" >
                trade_type,
            </if>
            <if test="busCode != null" >
                bus_code,
            </if>
            <if test="accountFrom != null" >
                account_from,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="finishTime != null" >
                finish_time,
            </if>
            <if test="handleStatus != null" >
                handle_status,
            </if>
            <if test="accountDate != null" >
                account_date,
            </if>
            <if test="remark != null" >
                remark,
            </if>
            <if test="amount != null" >
                amount,
            </if>
            <if test="tmSmp != null" >
                tm_smp,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="jrnNo != null" >
                #{jrnNo,jdbcType=VARCHAR},
            </if>
            <if test="orderDate != null" >
                #{orderDate,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null" >
                #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="orderType != null" >
                #{orderType,jdbcType=VARCHAR},
            </if>
            <if test="busType != null" >
                #{busType,jdbcType=VARCHAR},
            </if>
            <if test="busChannel != null" >
                #{busChannel,jdbcType=VARCHAR},
            </if>
            <if test="tradeType != null" >
                #{tradeType,jdbcType=VARCHAR},
            </if>
            <if test="busCode != null" >
                #{busCode,jdbcType=VARCHAR},
            </if>
            <if test="accountFrom != null" >
                #{accountFrom,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=VARCHAR},
            </if>
            <if test="finishTime != null" >
                #{finishTime,jdbcType=VARCHAR},
            </if>
            <if test="handleStatus != null" >
                #{handleStatus,jdbcType=VARCHAR},
            </if>
            <if test="accountDate != null" >
                #{accountDate,jdbcType=VARCHAR},
            </if>
            <if test="remark != null" >
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="amount != null" >
                #{amount,jdbcType=DECIMAL},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.cmpay.dceppay.entity.pay.PayAccountRecordDO" >
        update pay_account_record
        <set >
            <if test="orderDate != null" >
                order_date = #{orderDate,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null" >
                order_no = #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="orderType != null" >
                order_type = #{orderType,jdbcType=VARCHAR},
            </if>
            <if test="busType != null" >
                bus_type = #{busType,jdbcType=VARCHAR},
            </if>
            <if test="busChannel != null" >
                bus_channel = #{busChannel,jdbcType=VARCHAR},
            </if>
            <if test="tradeType != null" >
                trade_type = #{tradeType,jdbcType=VARCHAR},
            </if>
            <if test="busCode != null" >
                bus_code = #{busCode,jdbcType=VARCHAR},
            </if>
            <if test="accountFrom != null" >
                account_from = #{accountFrom,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=VARCHAR},
            </if>
            <if test="finishTime != null" >
                finish_time = #{finishTime,jdbcType=VARCHAR},
            </if>
            <if test="handleStatus != null" >
                handle_status = #{handleStatus,jdbcType=VARCHAR},
            </if>
            <if test="accountDate != null" >
                account_date = #{accountDate,jdbcType=VARCHAR},
            </if>
            <if test="remark != null" >
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="amount != null" >
                amount = #{amount,jdbcType=DECIMAL},
            </if>
            <if test="tmSmp != null" >
                tm_smp = #{tmSmp,jdbcType=VARCHAR},
            </if>
        </set>
        where jrn_no = #{jrnNo,jdbcType=VARCHAR}
    </update>

    <select id="find" resultMap="BaseResultMap" parameterType="com.cmpay.dceppay.entity.pay.PayAccountRecordDO" >
        select 
        <include refid="Base_Column_List" />
        from pay_account_record
        <where >
            <if test="jrnNo != null" >
                and jrn_no = #{jrnNo,jdbcType=VARCHAR}
            </if>
            <if test="orderDate != null" >
                and order_date = #{orderDate,jdbcType=VARCHAR}
            </if>
            <if test="orderNo != null" >
                and order_no = #{orderNo,jdbcType=VARCHAR}
            </if>
            <if test="orderType != null" >
                and order_type = #{orderType,jdbcType=VARCHAR}
            </if>
            <if test="busType != null" >
                and bus_type = #{busType,jdbcType=VARCHAR}
            </if>
            <if test="busChannel != null" >
                and bus_channel = #{busChannel,jdbcType=VARCHAR}
            </if>
            <if test="tradeType != null" >
                and trade_type = #{tradeType,jdbcType=VARCHAR}
            </if>
            <if test="busCode != null" >
                and bus_code = #{busCode,jdbcType=VARCHAR}
            </if>
            <if test="accountFrom != null" >
                and account_from = #{accountFrom,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null" >
                and create_time = #{createTime,jdbcType=VARCHAR}
            </if>
            <if test="finishTime != null" >
                and finish_time = #{finishTime,jdbcType=VARCHAR}
            </if>
            <if test="handleStatus != null" >
                and handle_status = #{handleStatus,jdbcType=VARCHAR}
            </if>
            <if test="accountDate != null" >
                and account_date = #{accountDate,jdbcType=VARCHAR}
            </if>
            <if test="remark != null" >
                and remark = #{remark,jdbcType=VARCHAR}
            </if>
            <if test="amount != null" >
                and amount = #{amount,jdbcType=DECIMAL}
            </if>
            <if test="tmSmp != null" >
                and tm_smp = #{tmSmp,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
</mapper>