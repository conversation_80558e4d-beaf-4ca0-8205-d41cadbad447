<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.dceppay.dao.check.ICheckErrorDetailDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.dceppay.entity.check.CheckErrorDetailDO" >
        <id column="out_order_no" property="outOrderNo" jdbcType="VARCHAR" />
        <id column="refund_order_no" property="refundOrderNo" jdbcType="VARCHAR" />
        <result column="check_file_date" property="checkFileDate" jdbcType="VARCHAR" />
        <result column="error_type" property="errorType" jdbcType="VARCHAR" />
        <result column="error_time" property="errorTime" jdbcType="VARCHAR" />
        <result column="error_handle_type" property="errorHandleType" jdbcType="VARCHAR" />
        <result column="error_process_time" property="errorProcessTime" jdbcType="VARCHAR" />
        <result column="trade_amount" property="tradeAmount" jdbcType="DECIMAL" />
        <result column="check_amount" property="checkAmount" jdbcType="DECIMAL" />
        <result column="error_status" property="errorStatus" jdbcType="VARCHAR" />
        <result column="trade_type" property="tradeType" jdbcType="VARCHAR" />
        <result column="merchant_no" property="merchantNo" jdbcType="VARCHAR" />
        <result column="org_merchant_no" property="orgMerchantNo" jdbcType="VARCHAR" />
        <result column="doubt_flag" property="doubtFlag" jdbcType="VARCHAR" />
        <result column="remark" property="remark" jdbcType="VARCHAR" />
        <result column="update_id" property="updateId" jdbcType="VARCHAR" />
        <result column="tm_smp" property="tmSmp" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        out_order_no, refund_order_no, check_file_date, error_type, error_time, error_handle_type, 
        error_process_time, trade_amount, check_amount, error_status, trade_type, merchant_no, 
        org_merchant_no, doubt_flag, remark, update_id, tm_smp
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="com.cmpay.dceppay.entity.check.CheckErrorDetailDOKey" >
        select 
        <include refid="Base_Column_List" />
        from check_error
        where out_order_no = #{outOrderNo,jdbcType=VARCHAR}
          and refund_order_no = #{refundOrderNo,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="com.cmpay.dceppay.entity.check.CheckErrorDetailDOKey" >
        delete from check_error
        where out_order_no = #{outOrderNo,jdbcType=VARCHAR}
          and refund_order_no = #{refundOrderNo,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.cmpay.dceppay.entity.check.CheckErrorDetailDO" >
        insert into check_error
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="outOrderNo != null" >
                out_order_no,
            </if>
            <if test="refundOrderNo != null" >
                refund_order_no,
            </if>
            <if test="checkFileDate != null" >
                check_file_date,
            </if>
            <if test="errorType != null" >
                error_type,
            </if>
            <if test="errorTime != null" >
                error_time,
            </if>
            <if test="errorHandleType != null" >
                error_handle_type,
            </if>
            <if test="errorProcessTime != null" >
                error_process_time,
            </if>
            <if test="tradeAmount != null" >
                trade_amount,
            </if>
            <if test="checkAmount != null" >
                check_amount,
            </if>
            <if test="errorStatus != null" >
                error_status,
            </if>
            <if test="tradeType != null" >
                trade_type,
            </if>
            <if test="merchantNo != null" >
                merchant_no,
            </if>
            <if test="orgMerchantNo != null" >
                org_merchant_no,
            </if>
            <if test="doubtFlag != null" >
                doubt_flag,
            </if>
            <if test="remark != null" >
                remark,
            </if>
            <if test="updateId != null" >
                update_id,
            </if>
            <if test="tmSmp != null" >
                tm_smp,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="outOrderNo != null" >
                #{outOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="refundOrderNo != null" >
                #{refundOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="checkFileDate != null" >
                #{checkFileDate,jdbcType=VARCHAR},
            </if>
            <if test="errorType != null" >
                #{errorType,jdbcType=VARCHAR},
            </if>
            <if test="errorTime != null" >
                #{errorTime,jdbcType=VARCHAR},
            </if>
            <if test="errorHandleType != null" >
                #{errorHandleType,jdbcType=VARCHAR},
            </if>
            <if test="errorProcessTime != null" >
                #{errorProcessTime,jdbcType=VARCHAR},
            </if>
            <if test="tradeAmount != null" >
                #{tradeAmount,jdbcType=DECIMAL},
            </if>
            <if test="checkAmount != null" >
                #{checkAmount,jdbcType=DECIMAL},
            </if>
            <if test="errorStatus != null" >
                #{errorStatus,jdbcType=VARCHAR},
            </if>
            <if test="tradeType != null" >
                #{tradeType,jdbcType=VARCHAR},
            </if>
            <if test="merchantNo != null" >
                #{merchantNo,jdbcType=VARCHAR},
            </if>
            <if test="orgMerchantNo != null" >
                #{orgMerchantNo,jdbcType=VARCHAR},
            </if>
            <if test="doubtFlag != null" >
                #{doubtFlag,jdbcType=VARCHAR},
            </if>
            <if test="remark != null" >
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="updateId != null" >
                #{updateId,jdbcType=VARCHAR},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.cmpay.dceppay.entity.check.CheckErrorDetailDO" >
        update check_error
        <set >
            <if test="checkFileDate != null" >
                check_file_date = #{checkFileDate,jdbcType=VARCHAR},
            </if>
            <if test="errorType != null" >
                error_type = #{errorType,jdbcType=VARCHAR},
            </if>
            <if test="errorTime != null" >
                error_time = #{errorTime,jdbcType=VARCHAR},
            </if>
            <if test="errorHandleType != null" >
                error_handle_type = #{errorHandleType,jdbcType=VARCHAR},
            </if>
            <if test="errorProcessTime != null" >
                error_process_time = #{errorProcessTime,jdbcType=VARCHAR},
            </if>
            <if test="tradeAmount != null" >
                trade_amount = #{tradeAmount,jdbcType=DECIMAL},
            </if>
            <if test="checkAmount != null" >
                check_amount = #{checkAmount,jdbcType=DECIMAL},
            </if>
            <if test="errorStatus != null" >
                error_status = #{errorStatus,jdbcType=VARCHAR},
            </if>
            <if test="tradeType != null" >
                trade_type = #{tradeType,jdbcType=VARCHAR},
            </if>
            <if test="merchantNo != null" >
                merchant_no = #{merchantNo,jdbcType=VARCHAR},
            </if>
            <if test="orgMerchantNo != null" >
                org_merchant_no = #{orgMerchantNo,jdbcType=VARCHAR},
            </if>
            <if test="doubtFlag != null" >
                doubt_flag = #{doubtFlag,jdbcType=VARCHAR},
            </if>
            <if test="remark != null" >
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="updateId != null" >
                update_id = #{updateId,jdbcType=VARCHAR},
            </if>
            <if test="tmSmp != null" >
                tm_smp = #{tmSmp,jdbcType=VARCHAR},
            </if>
        </set>
        where out_order_no = #{outOrderNo,jdbcType=VARCHAR}
          and refund_order_no = #{refundOrderNo,jdbcType=VARCHAR}
    </update>

    <select id="find" resultMap="BaseResultMap" parameterType="com.cmpay.dceppay.entity.check.CheckErrorDetailDO" >
        select 
        <include refid="Base_Column_List" />
        from check_error
        <where >
            <if test="outOrderNo != null" >
                and out_order_no = #{outOrderNo,jdbcType=VARCHAR}
            </if>
            <if test="refundOrderNo != null" >
                and refund_order_no = #{refundOrderNo,jdbcType=VARCHAR}
            </if>
            <if test="checkFileDate != null" >
                and check_file_date = #{checkFileDate,jdbcType=VARCHAR}
            </if>
            <if test="errorType != null" >
                and error_type = #{errorType,jdbcType=VARCHAR}
            </if>
            <if test="errorTime != null" >
                and error_time = #{errorTime,jdbcType=VARCHAR}
            </if>
            <if test="errorHandleType != null" >
                and error_handle_type = #{errorHandleType,jdbcType=VARCHAR}
            </if>
            <if test="errorProcessTime != null" >
                and error_process_time = #{errorProcessTime,jdbcType=VARCHAR}
            </if>
            <if test="tradeAmount != null" >
                and trade_amount = #{tradeAmount,jdbcType=DECIMAL}
            </if>
            <if test="checkAmount != null" >
                and check_amount = #{checkAmount,jdbcType=DECIMAL}
            </if>
            <if test="errorStatus != null" >
                and error_status = #{errorStatus,jdbcType=VARCHAR}
            </if>
            <if test="tradeType != null" >
                and trade_type = #{tradeType,jdbcType=VARCHAR}
            </if>
            <if test="merchantNo != null" >
                and merchant_no = #{merchantNo,jdbcType=VARCHAR}
            </if>
            <if test="orgMerchantNo != null" >
                and org_merchant_no = #{orgMerchantNo,jdbcType=VARCHAR}
            </if>
            <if test="doubtFlag != null" >
                and doubt_flag = #{doubtFlag,jdbcType=VARCHAR}
            </if>
            <if test="remark != null" >
                and remark = #{remark,jdbcType=VARCHAR}
            </if>
            <if test="updateId != null" >
                and update_id = #{updateId,jdbcType=VARCHAR}
            </if>
            <if test="tmSmp != null" >
                and tm_smp = #{tmSmp,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
</mapper>