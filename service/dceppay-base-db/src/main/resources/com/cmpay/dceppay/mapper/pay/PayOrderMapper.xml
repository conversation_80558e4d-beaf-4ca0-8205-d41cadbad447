<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.dceppay.dao.pay.IPayOrderDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.dceppay.entity.pay.PayOrderDO" >
        <id column="out_order_no" property="outOrderNo" jdbcType="VARCHAR" />
        <result column="trade_jrn_no" property="tradeJrnNo" jdbcType="VARCHAR" />
        <result column="order_date" property="orderDate" jdbcType="VARCHAR" />
        <result column="order_time" property="orderTime" jdbcType="VARCHAR" />
        <result column="order_time_expire" property="orderTimeExpire" jdbcType="VARCHAR" />
        <result column="channel_code" property="channelCode" jdbcType="VARCHAR" />
        <result column="pay_way" property="payWay" jdbcType="VARCHAR" />
        <result column="scene" property="scene" jdbcType="VARCHAR" />
        <result column="order_type" property="orderType" jdbcType="VARCHAR" />
        <result column="account_date" property="accountDate" jdbcType="VARCHAR" />
        <result column="product_name" property="productName" jdbcType="VARCHAR" />
        <result column="product_desc" property="productDesc" jdbcType="VARCHAR" />
        <result column="order_amount" property="orderAmount" jdbcType="DECIMAL" />
        <result column="bus_type" property="busType" jdbcType="VARCHAR" />
        <result column="merchant_no" property="merchantNo" jdbcType="VARCHAR" />
        <result column="org_merchant_no" property="orgMerchantNo" jdbcType="VARCHAR" />
        <result column="wallet_id" property="walletId" jdbcType="VARCHAR" />
        <result column="biz_type" property="bizType" jdbcType="VARCHAR" />
        <result column="biz_category" property="bizCategory" jdbcType="VARCHAR" />
        <result column="terminal_no" property="terminalNo" jdbcType="VARCHAR" />
        <result column="terminal_ip" property="terminalIp" jdbcType="VARCHAR" />
        <result column="terminal_location" property="terminalLocation" jdbcType="VARCHAR" />
        <result column="notify_url" property="notifyUrl" jdbcType="VARCHAR" />
        <result column="page_notify_url" property="pageNotifyUrl" jdbcType="VARCHAR" />
        <result column="status" property="status" jdbcType="VARCHAR" />
        <result column="bank_order_no" property="bankOrderNo" jdbcType="VARCHAR" />
        <result column="err_msg_cd" property="errMsgCd" jdbcType="VARCHAR" />
        <result column="err_msg_info" property="errMsgInfo" jdbcType="VARCHAR" />
        <result column="order_complete_time" property="orderCompleteTime" jdbcType="VARCHAR" />
        <result column="receive_notify_time" property="receiveNotifyTime" jdbcType="VARCHAR" />
        <result column="refund_times" property="refundTimes" jdbcType="INTEGER" />
        <result column="success_refund_amount" property="successRefundAmount" jdbcType="DECIMAL" />
        <result column="notify_status" property="notifyStatus" jdbcType="VARCHAR" />
        <result column="send_notify_time" property="sendNotifyTime" jdbcType="VARCHAR" />
        <result column="extra" property="extra" jdbcType="VARCHAR" />
        <result column="message_identification" property="messageIdentification" jdbcType="VARCHAR" />
        <result column="tm_smp" property="tmSmp" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        out_order_no, trade_jrn_no, order_date, order_time, order_time_expire, channel_code, 
        pay_way, scene, order_type, account_date, product_name, product_desc, order_amount, 
        bus_type, merchant_no, org_merchant_no, wallet_id, biz_type, biz_category, terminal_no, 
        terminal_ip, terminal_location, notify_url, page_notify_url, status, bank_order_no, 
        err_msg_cd, err_msg_info, order_complete_time, receive_notify_time, refund_times, 
        success_refund_amount, notify_status, send_notify_time, extra, message_identification, 
        tm_smp
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from pay_order
        where out_order_no = #{outOrderNo,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from pay_order
        where out_order_no = #{outOrderNo,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.cmpay.dceppay.entity.pay.PayOrderDO" >
        insert into pay_order
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="outOrderNo != null" >
                out_order_no,
            </if>
            <if test="tradeJrnNo != null" >
                trade_jrn_no,
            </if>
            <if test="orderDate != null" >
                order_date,
            </if>
            <if test="orderTime != null" >
                order_time,
            </if>
            <if test="orderTimeExpire != null" >
                order_time_expire,
            </if>
            <if test="channelCode != null" >
                channel_code,
            </if>
            <if test="payWay != null" >
                pay_way,
            </if>
            <if test="scene != null" >
                scene,
            </if>
            <if test="orderType != null" >
                order_type,
            </if>
            <if test="accountDate != null" >
                account_date,
            </if>
            <if test="productName != null" >
                product_name,
            </if>
            <if test="productDesc != null" >
                product_desc,
            </if>
            <if test="orderAmount != null" >
                order_amount,
            </if>
            <if test="busType != null" >
                bus_type,
            </if>
            <if test="merchantNo != null" >
                merchant_no,
            </if>
            <if test="orgMerchantNo != null" >
                org_merchant_no,
            </if>
            <if test="walletId != null" >
                wallet_id,
            </if>
            <if test="bizType != null" >
                biz_type,
            </if>
            <if test="bizCategory != null" >
                biz_category,
            </if>
            <if test="terminalNo != null" >
                terminal_no,
            </if>
            <if test="terminalIp != null" >
                terminal_ip,
            </if>
            <if test="terminalLocation != null" >
                terminal_location,
            </if>
            <if test="notifyUrl != null" >
                notify_url,
            </if>
            <if test="pageNotifyUrl != null" >
                page_notify_url,
            </if>
            <if test="status != null" >
                status,
            </if>
            <if test="bankOrderNo != null" >
                bank_order_no,
            </if>
            <if test="errMsgCd != null" >
                err_msg_cd,
            </if>
            <if test="errMsgInfo != null" >
                err_msg_info,
            </if>
            <if test="orderCompleteTime != null" >
                order_complete_time,
            </if>
            <if test="receiveNotifyTime != null" >
                receive_notify_time,
            </if>
            <if test="refundTimes != null" >
                refund_times,
            </if>
            <if test="successRefundAmount != null" >
                success_refund_amount,
            </if>
            <if test="notifyStatus != null" >
                notify_status,
            </if>
            <if test="sendNotifyTime != null" >
                send_notify_time,
            </if>
            <if test="extra != null" >
                extra,
            </if>
            <if test="messageIdentification != null" >
                message_identification,
            </if>
            <if test="tmSmp != null" >
                tm_smp,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="outOrderNo != null" >
                #{outOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="tradeJrnNo != null" >
                #{tradeJrnNo,jdbcType=VARCHAR},
            </if>
            <if test="orderDate != null" >
                #{orderDate,jdbcType=VARCHAR},
            </if>
            <if test="orderTime != null" >
                #{orderTime,jdbcType=VARCHAR},
            </if>
            <if test="orderTimeExpire != null" >
                #{orderTimeExpire,jdbcType=VARCHAR},
            </if>
            <if test="channelCode != null" >
                #{channelCode,jdbcType=VARCHAR},
            </if>
            <if test="payWay != null" >
                #{payWay,jdbcType=VARCHAR},
            </if>
            <if test="scene != null" >
                #{scene,jdbcType=VARCHAR},
            </if>
            <if test="orderType != null" >
                #{orderType,jdbcType=VARCHAR},
            </if>
            <if test="accountDate != null" >
                #{accountDate,jdbcType=VARCHAR},
            </if>
            <if test="productName != null" >
                #{productName,jdbcType=VARCHAR},
            </if>
            <if test="productDesc != null" >
                #{productDesc,jdbcType=VARCHAR},
            </if>
            <if test="orderAmount != null" >
                #{orderAmount,jdbcType=DECIMAL},
            </if>
            <if test="busType != null" >
                #{busType,jdbcType=VARCHAR},
            </if>
            <if test="merchantNo != null" >
                #{merchantNo,jdbcType=VARCHAR},
            </if>
            <if test="orgMerchantNo != null" >
                #{orgMerchantNo,jdbcType=VARCHAR},
            </if>
            <if test="walletId != null" >
                #{walletId,jdbcType=VARCHAR},
            </if>
            <if test="bizType != null" >
                #{bizType,jdbcType=VARCHAR},
            </if>
            <if test="bizCategory != null" >
                #{bizCategory,jdbcType=VARCHAR},
            </if>
            <if test="terminalNo != null" >
                #{terminalNo,jdbcType=VARCHAR},
            </if>
            <if test="terminalIp != null" >
                #{terminalIp,jdbcType=VARCHAR},
            </if>
            <if test="terminalLocation != null" >
                #{terminalLocation,jdbcType=VARCHAR},
            </if>
            <if test="notifyUrl != null" >
                #{notifyUrl,jdbcType=VARCHAR},
            </if>
            <if test="pageNotifyUrl != null" >
                #{pageNotifyUrl,jdbcType=VARCHAR},
            </if>
            <if test="status != null" >
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="bankOrderNo != null" >
                #{bankOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="errMsgCd != null" >
                #{errMsgCd,jdbcType=VARCHAR},
            </if>
            <if test="errMsgInfo != null" >
                #{errMsgInfo,jdbcType=VARCHAR},
            </if>
            <if test="orderCompleteTime != null" >
                #{orderCompleteTime,jdbcType=VARCHAR},
            </if>
            <if test="receiveNotifyTime != null" >
                #{receiveNotifyTime,jdbcType=VARCHAR},
            </if>
            <if test="refundTimes != null" >
                #{refundTimes,jdbcType=INTEGER},
            </if>
            <if test="successRefundAmount != null" >
                #{successRefundAmount,jdbcType=DECIMAL},
            </if>
            <if test="notifyStatus != null" >
                #{notifyStatus,jdbcType=VARCHAR},
            </if>
            <if test="sendNotifyTime != null" >
                #{sendNotifyTime,jdbcType=VARCHAR},
            </if>
            <if test="extra != null" >
                #{extra,jdbcType=VARCHAR},
            </if>
            <if test="messageIdentification != null" >
                #{messageIdentification,jdbcType=VARCHAR},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.cmpay.dceppay.entity.pay.PayOrderDO" >
        update pay_order
        <set >
            <if test="tradeJrnNo != null" >
                trade_jrn_no = #{tradeJrnNo,jdbcType=VARCHAR},
            </if>
            <if test="orderDate != null" >
                order_date = #{orderDate,jdbcType=VARCHAR},
            </if>
            <if test="orderTime != null" >
                order_time = #{orderTime,jdbcType=VARCHAR},
            </if>
            <if test="orderTimeExpire != null" >
                order_time_expire = #{orderTimeExpire,jdbcType=VARCHAR},
            </if>
            <if test="channelCode != null" >
                channel_code = #{channelCode,jdbcType=VARCHAR},
            </if>
            <if test="payWay != null" >
                pay_way = #{payWay,jdbcType=VARCHAR},
            </if>
            <if test="scene != null" >
                scene = #{scene,jdbcType=VARCHAR},
            </if>
            <if test="orderType != null" >
                order_type = #{orderType,jdbcType=VARCHAR},
            </if>
            <if test="accountDate != null" >
                account_date = #{accountDate,jdbcType=VARCHAR},
            </if>
            <if test="productName != null" >
                product_name = #{productName,jdbcType=VARCHAR},
            </if>
            <if test="productDesc != null" >
                product_desc = #{productDesc,jdbcType=VARCHAR},
            </if>
            <if test="orderAmount != null" >
                order_amount = #{orderAmount,jdbcType=DECIMAL},
            </if>
            <if test="busType != null" >
                bus_type = #{busType,jdbcType=VARCHAR},
            </if>
            <if test="merchantNo != null" >
                merchant_no = #{merchantNo,jdbcType=VARCHAR},
            </if>
            <if test="orgMerchantNo != null" >
                org_merchant_no = #{orgMerchantNo,jdbcType=VARCHAR},
            </if>
            <if test="walletId != null" >
                wallet_id = #{walletId,jdbcType=VARCHAR},
            </if>
            <if test="bizType != null" >
                biz_type = #{bizType,jdbcType=VARCHAR},
            </if>
            <if test="bizCategory != null" >
                biz_category = #{bizCategory,jdbcType=VARCHAR},
            </if>
            <if test="terminalNo != null" >
                terminal_no = #{terminalNo,jdbcType=VARCHAR},
            </if>
            <if test="terminalIp != null" >
                terminal_ip = #{terminalIp,jdbcType=VARCHAR},
            </if>
            <if test="terminalLocation != null" >
                terminal_location = #{terminalLocation,jdbcType=VARCHAR},
            </if>
            <if test="notifyUrl != null" >
                notify_url = #{notifyUrl,jdbcType=VARCHAR},
            </if>
            <if test="pageNotifyUrl != null" >
                page_notify_url = #{pageNotifyUrl,jdbcType=VARCHAR},
            </if>
            <if test="status != null" >
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="bankOrderNo != null" >
                bank_order_no = #{bankOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="errMsgCd != null" >
                err_msg_cd = #{errMsgCd,jdbcType=VARCHAR},
            </if>
            <if test="errMsgInfo != null" >
                err_msg_info = #{errMsgInfo,jdbcType=VARCHAR},
            </if>
            <if test="orderCompleteTime != null" >
                order_complete_time = #{orderCompleteTime,jdbcType=VARCHAR},
            </if>
            <if test="receiveNotifyTime != null" >
                receive_notify_time = #{receiveNotifyTime,jdbcType=VARCHAR},
            </if>
            <if test="refundTimes != null" >
                refund_times = #{refundTimes,jdbcType=INTEGER},
            </if>
            <if test="successRefundAmount != null" >
                success_refund_amount = #{successRefundAmount,jdbcType=DECIMAL},
            </if>
            <if test="notifyStatus != null" >
                notify_status = #{notifyStatus,jdbcType=VARCHAR},
            </if>
            <if test="sendNotifyTime != null" >
                send_notify_time = #{sendNotifyTime,jdbcType=VARCHAR},
            </if>
            <if test="extra != null" >
                extra = #{extra,jdbcType=VARCHAR},
            </if>
            <if test="messageIdentification != null" >
                message_identification = #{messageIdentification,jdbcType=VARCHAR},
            </if>
            <if test="tmSmp != null" >
                tm_smp = #{tmSmp,jdbcType=VARCHAR},
            </if>
        </set>
        where out_order_no = #{outOrderNo,jdbcType=VARCHAR}
    </update>

    <select id="find" resultMap="BaseResultMap" parameterType="com.cmpay.dceppay.entity.pay.PayOrderDO" >
        select 
        <include refid="Base_Column_List" />
        from pay_order
        <where >
            <if test="outOrderNo != null" >
                and out_order_no = #{outOrderNo,jdbcType=VARCHAR}
            </if>
            <if test="tradeJrnNo != null" >
                and trade_jrn_no = #{tradeJrnNo,jdbcType=VARCHAR}
            </if>
            <if test="orderDate != null" >
                and order_date = #{orderDate,jdbcType=VARCHAR}
            </if>
            <if test="orderTime != null" >
                and order_time = #{orderTime,jdbcType=VARCHAR}
            </if>
            <if test="orderTimeExpire != null" >
                and order_time_expire = #{orderTimeExpire,jdbcType=VARCHAR}
            </if>
            <if test="channelCode != null" >
                and channel_code = #{channelCode,jdbcType=VARCHAR}
            </if>
            <if test="payWay != null" >
                and pay_way = #{payWay,jdbcType=VARCHAR}
            </if>
            <if test="scene != null" >
                and scene = #{scene,jdbcType=VARCHAR}
            </if>
            <if test="orderType != null" >
                and order_type = #{orderType,jdbcType=VARCHAR}
            </if>
            <if test="accountDate != null" >
                and account_date = #{accountDate,jdbcType=VARCHAR}
            </if>
            <if test="productName != null" >
                and product_name = #{productName,jdbcType=VARCHAR}
            </if>
            <if test="productDesc != null" >
                and product_desc = #{productDesc,jdbcType=VARCHAR}
            </if>
            <if test="orderAmount != null" >
                and order_amount = #{orderAmount,jdbcType=DECIMAL}
            </if>
            <if test="busType != null" >
                and bus_type = #{busType,jdbcType=VARCHAR}
            </if>
            <if test="merchantNo != null" >
                and merchant_no = #{merchantNo,jdbcType=VARCHAR}
            </if>
            <if test="orgMerchantNo != null" >
                and org_merchant_no = #{orgMerchantNo,jdbcType=VARCHAR}
            </if>
            <if test="walletId != null" >
                and wallet_id = #{walletId,jdbcType=VARCHAR}
            </if>
            <if test="bizType != null" >
                and biz_type = #{bizType,jdbcType=VARCHAR}
            </if>
            <if test="bizCategory != null" >
                and biz_category = #{bizCategory,jdbcType=VARCHAR}
            </if>
            <if test="terminalNo != null" >
                and terminal_no = #{terminalNo,jdbcType=VARCHAR}
            </if>
            <if test="terminalIp != null" >
                and terminal_ip = #{terminalIp,jdbcType=VARCHAR}
            </if>
            <if test="terminalLocation != null" >
                and terminal_location = #{terminalLocation,jdbcType=VARCHAR}
            </if>
            <if test="notifyUrl != null" >
                and notify_url = #{notifyUrl,jdbcType=VARCHAR}
            </if>
            <if test="pageNotifyUrl != null" >
                and page_notify_url = #{pageNotifyUrl,jdbcType=VARCHAR}
            </if>
            <if test="status != null" >
                and status = #{status,jdbcType=VARCHAR}
            </if>
            <if test="bankOrderNo != null" >
                and bank_order_no = #{bankOrderNo,jdbcType=VARCHAR}
            </if>
            <if test="errMsgCd != null" >
                and err_msg_cd = #{errMsgCd,jdbcType=VARCHAR}
            </if>
            <if test="errMsgInfo != null" >
                and err_msg_info = #{errMsgInfo,jdbcType=VARCHAR}
            </if>
            <if test="orderCompleteTime != null" >
                and order_complete_time = #{orderCompleteTime,jdbcType=VARCHAR}
            </if>
            <if test="receiveNotifyTime != null" >
                and receive_notify_time = #{receiveNotifyTime,jdbcType=VARCHAR}
            </if>
            <if test="refundTimes != null" >
                and refund_times = #{refundTimes,jdbcType=INTEGER}
            </if>
            <if test="successRefundAmount != null" >
                and success_refund_amount = #{successRefundAmount,jdbcType=DECIMAL}
            </if>
            <if test="notifyStatus != null" >
                and notify_status = #{notifyStatus,jdbcType=VARCHAR}
            </if>
            <if test="sendNotifyTime != null" >
                and send_notify_time = #{sendNotifyTime,jdbcType=VARCHAR}
            </if>
            <if test="extra != null" >
                and extra = #{extra,jdbcType=VARCHAR}
            </if>
            <if test="messageIdentification != null" >
                and message_identification = #{messageIdentification,jdbcType=VARCHAR}
            </if>
            <if test="tmSmp != null" >
                and tm_smp = #{tmSmp,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
</mapper>