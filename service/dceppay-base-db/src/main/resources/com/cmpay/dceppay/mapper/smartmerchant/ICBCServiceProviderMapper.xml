<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.dceppay.dao.smartmerchant.IICBCServiceProviderDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.dceppay.entity.smartmerchant.ICBCServiceProviderDO" >
        <id column="service_id" property="serviceId" jdbcType="VARCHAR" />
        <result column="service_provider_id" property="serviceProviderId" jdbcType="VARCHAR" />
        <result column="service_name" property="serviceName" jdbcType="VARCHAR" />
        <result column="license_type" property="licenseType" jdbcType="VARCHAR" />
        <result column="license_no" property="licenseNo" jdbcType="VARCHAR" />
        <result column="scene_desc" property="sceneDesc" jdbcType="VARCHAR" />
        <result column="scene_id" property="sceneId" jdbcType="VARCHAR" />
        <result column="onboard_status" property="onboardStatus" jdbcType="VARCHAR" />
        <result column="service_status" property="serviceStatus" jdbcType="VARCHAR" />
        <result column="onboard_id" property="onboardId" jdbcType="VARCHAR" />
        <result column="msg_code" property="msgCode" jdbcType="VARCHAR" />
        <result column="msg_info" property="msgInfo" jdbcType="VARCHAR" />
        <result column="remark" property="remark" jdbcType="VARCHAR" />
        <result column="onboard_time" property="onboardTime" jdbcType="VARCHAR" />
        <result column="create_id" property="createId" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="VARCHAR" />
        <result column="update_id" property="updateId" jdbcType="VARCHAR" />
        <result column="update_time" property="updateTime" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        service_id, service_provider_id, service_name, license_type, license_no, scene_desc, 
        scene_id, onboard_status, service_status, onboard_id, msg_code, msg_info, remark, 
        onboard_time, create_id, create_time, update_id, update_time
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from icbc_service_provider
        where service_id = #{serviceId,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from icbc_service_provider
        where service_id = #{serviceId,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.cmpay.dceppay.entity.smartmerchant.ICBCServiceProviderDO" >
        insert into icbc_service_provider
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="serviceId != null" >
                service_id,
            </if>
            <if test="serviceProviderId != null" >
                service_provider_id,
            </if>
            <if test="serviceName != null" >
                service_name,
            </if>
            <if test="licenseType != null" >
                license_type,
            </if>
            <if test="licenseNo != null" >
                license_no,
            </if>
            <if test="sceneDesc != null" >
                scene_desc,
            </if>
            <if test="sceneId != null" >
                scene_id,
            </if>
            <if test="onboardStatus != null" >
                onboard_status,
            </if>
            <if test="serviceStatus != null" >
                service_status,
            </if>
            <if test="onboardId != null" >
                onboard_id,
            </if>
            <if test="msgCode != null" >
                msg_code,
            </if>
            <if test="msgInfo != null" >
                msg_info,
            </if>
            <if test="remark != null" >
                remark,
            </if>
            <if test="onboardTime != null" >
                onboard_time,
            </if>
            <if test="createId != null" >
                create_id,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="updateId != null" >
                update_id,
            </if>
            <if test="updateTime != null" >
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="serviceId != null" >
                #{serviceId,jdbcType=VARCHAR},
            </if>
            <if test="serviceProviderId != null" >
                #{serviceProviderId,jdbcType=VARCHAR},
            </if>
            <if test="serviceName != null" >
                #{serviceName,jdbcType=VARCHAR},
            </if>
            <if test="licenseType != null" >
                #{licenseType,jdbcType=VARCHAR},
            </if>
            <if test="licenseNo != null" >
                #{licenseNo,jdbcType=VARCHAR},
            </if>
            <if test="sceneDesc != null" >
                #{sceneDesc,jdbcType=VARCHAR},
            </if>
            <if test="sceneId != null" >
                #{sceneId,jdbcType=VARCHAR},
            </if>
            <if test="onboardStatus != null" >
                #{onboardStatus,jdbcType=VARCHAR},
            </if>
            <if test="serviceStatus != null" >
                #{serviceStatus,jdbcType=VARCHAR},
            </if>
            <if test="onboardId != null" >
                #{onboardId,jdbcType=VARCHAR},
            </if>
            <if test="msgCode != null" >
                #{msgCode,jdbcType=VARCHAR},
            </if>
            <if test="msgInfo != null" >
                #{msgInfo,jdbcType=VARCHAR},
            </if>
            <if test="remark != null" >
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="onboardTime != null" >
                #{onboardTime,jdbcType=VARCHAR},
            </if>
            <if test="createId != null" >
                #{createId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=VARCHAR},
            </if>
            <if test="updateId != null" >
                #{updateId,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null" >
                #{updateTime,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.cmpay.dceppay.entity.smartmerchant.ICBCServiceProviderDO" >
        update icbc_service_provider
        <set >
            <if test="serviceProviderId != null" >
                service_provider_id = #{serviceProviderId,jdbcType=VARCHAR},
            </if>
            <if test="serviceName != null" >
                service_name = #{serviceName,jdbcType=VARCHAR},
            </if>
            <if test="licenseType != null" >
                license_type = #{licenseType,jdbcType=VARCHAR},
            </if>
            <if test="licenseNo != null" >
                license_no = #{licenseNo,jdbcType=VARCHAR},
            </if>
            <if test="sceneDesc != null" >
                scene_desc = #{sceneDesc,jdbcType=VARCHAR},
            </if>
            <if test="sceneId != null" >
                scene_id = #{sceneId,jdbcType=VARCHAR},
            </if>
            <if test="onboardStatus != null" >
                onboard_status = #{onboardStatus,jdbcType=VARCHAR},
            </if>
            <if test="serviceStatus != null" >
                service_status = #{serviceStatus,jdbcType=VARCHAR},
            </if>
            <if test="onboardId != null" >
                onboard_id = #{onboardId,jdbcType=VARCHAR},
            </if>
            <if test="msgCode != null" >
                msg_code = #{msgCode,jdbcType=VARCHAR},
            </if>
            <if test="msgInfo != null" >
                msg_info = #{msgInfo,jdbcType=VARCHAR},
            </if>
            <if test="remark != null" >
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="onboardTime != null" >
                onboard_time = #{onboardTime,jdbcType=VARCHAR},
            </if>
            <if test="createId != null" >
                create_id = #{createId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=VARCHAR},
            </if>
            <if test="updateId != null" >
                update_id = #{updateId,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null" >
                update_time = #{updateTime,jdbcType=VARCHAR},
            </if>
        </set>
        where service_id = #{serviceId,jdbcType=VARCHAR}
    </update>

    <select id="find" resultMap="BaseResultMap" parameterType="com.cmpay.dceppay.entity.smartmerchant.ICBCServiceProviderDO" >
        select 
        <include refid="Base_Column_List" />
        from icbc_service_provider
        <where >
            <if test="serviceId != null" >
                and service_id = #{serviceId,jdbcType=VARCHAR}
            </if>
            <if test="serviceProviderId != null" >
                and service_provider_id = #{serviceProviderId,jdbcType=VARCHAR}
            </if>
            <if test="serviceName != null" >
                and service_name = #{serviceName,jdbcType=VARCHAR}
            </if>
            <if test="licenseType != null" >
                and license_type = #{licenseType,jdbcType=VARCHAR}
            </if>
            <if test="licenseNo != null" >
                and license_no = #{licenseNo,jdbcType=VARCHAR}
            </if>
            <if test="sceneDesc != null" >
                and scene_desc = #{sceneDesc,jdbcType=VARCHAR}
            </if>
            <if test="sceneId != null" >
                and scene_id = #{sceneId,jdbcType=VARCHAR}
            </if>
            <if test="onboardStatus != null" >
                and onboard_status = #{onboardStatus,jdbcType=VARCHAR}
            </if>
            <if test="serviceStatus != null" >
                and service_status = #{serviceStatus,jdbcType=VARCHAR}
            </if>
            <if test="onboardId != null" >
                and onboard_id = #{onboardId,jdbcType=VARCHAR}
            </if>
            <if test="msgCode != null" >
                and msg_code = #{msgCode,jdbcType=VARCHAR}
            </if>
            <if test="msgInfo != null" >
                and msg_info = #{msgInfo,jdbcType=VARCHAR}
            </if>
            <if test="remark != null" >
                and remark = #{remark,jdbcType=VARCHAR}
            </if>
            <if test="onboardTime != null" >
                and onboard_time = #{onboardTime,jdbcType=VARCHAR}
            </if>
            <if test="createId != null" >
                and create_id = #{createId,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null" >
                and create_time = #{createTime,jdbcType=VARCHAR}
            </if>
            <if test="updateId != null" >
                and update_id = #{updateId,jdbcType=VARCHAR}
            </if>
            <if test="updateTime != null" >
                and update_time = #{updateTime,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
</mapper>