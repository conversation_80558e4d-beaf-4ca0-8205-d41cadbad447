<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.dceppay.dao.check.ICheckFileVerifyDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.dceppay.entity.check.CheckFileVerifyDO" >
        <id column="check_file_date" property="checkFileDate" jdbcType="VARCHAR" />
        <id column="institution_code" property="institutionCode" jdbcType="VARCHAR" />
        <result column="handle_flag" property="handleFlag" jdbcType="VARCHAR" />
        <result column="download_end_time" property="downloadEndTime" jdbcType="VARCHAR" />
        <result column="verify_message" property="verifyMessage" jdbcType="VARCHAR" />
        <result column="operate_id" property="operateId" jdbcType="VARCHAR" />
        <result column="remark" property="remark" jdbcType="VARCHAR" />
        <result column="tm_smp" property="tmSmp" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        check_file_date, institution_code, handle_flag, download_end_time, verify_message, 
        operate_id, remark, tm_smp
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="com.cmpay.dceppay.entity.check.CheckFileVerifyDOKey" >
        select 
        <include refid="Base_Column_List" />
        from check_file_verify
        where check_file_date = #{checkFileDate,jdbcType=VARCHAR}
          and institution_code = #{institutionCode,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="com.cmpay.dceppay.entity.check.CheckFileVerifyDOKey" >
        delete from check_file_verify
        where check_file_date = #{checkFileDate,jdbcType=VARCHAR}
          and institution_code = #{institutionCode,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.cmpay.dceppay.entity.check.CheckFileVerifyDO" >
        insert into check_file_verify
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="checkFileDate != null" >
                check_file_date,
            </if>
            <if test="institutionCode != null" >
                institution_code,
            </if>
            <if test="handleFlag != null" >
                handle_flag,
            </if>
            <if test="downloadEndTime != null" >
                download_end_time,
            </if>
            <if test="verifyMessage != null" >
                verify_message,
            </if>
            <if test="operateId != null" >
                operate_id,
            </if>
            <if test="remark != null" >
                remark,
            </if>
            <if test="tmSmp != null" >
                tm_smp,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="checkFileDate != null" >
                #{checkFileDate,jdbcType=VARCHAR},
            </if>
            <if test="institutionCode != null" >
                #{institutionCode,jdbcType=VARCHAR},
            </if>
            <if test="handleFlag != null" >
                #{handleFlag,jdbcType=VARCHAR},
            </if>
            <if test="downloadEndTime != null" >
                #{downloadEndTime,jdbcType=VARCHAR},
            </if>
            <if test="verifyMessage != null" >
                #{verifyMessage,jdbcType=VARCHAR},
            </if>
            <if test="operateId != null" >
                #{operateId,jdbcType=VARCHAR},
            </if>
            <if test="remark != null" >
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.cmpay.dceppay.entity.check.CheckFileVerifyDO" >
        update check_file_verify
        <set >
            <if test="handleFlag != null" >
                handle_flag = #{handleFlag,jdbcType=VARCHAR},
            </if>
            <if test="downloadEndTime != null" >
                download_end_time = #{downloadEndTime,jdbcType=VARCHAR},
            </if>
            <if test="verifyMessage != null" >
                verify_message = #{verifyMessage,jdbcType=VARCHAR},
            </if>
            <if test="operateId != null" >
                operate_id = #{operateId,jdbcType=VARCHAR},
            </if>
            <if test="remark != null" >
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="tmSmp != null" >
                tm_smp = #{tmSmp,jdbcType=VARCHAR},
            </if>
        </set>
        where check_file_date = #{checkFileDate,jdbcType=VARCHAR}
          and institution_code = #{institutionCode,jdbcType=VARCHAR}
    </update>

    <select id="find" resultMap="BaseResultMap" parameterType="com.cmpay.dceppay.entity.check.CheckFileVerifyDO" >
        select 
        <include refid="Base_Column_List" />
        from check_file_verify
        <where >
            <if test="checkFileDate != null" >
                and check_file_date = #{checkFileDate,jdbcType=VARCHAR}
            </if>
            <if test="institutionCode != null" >
                and institution_code = #{institutionCode,jdbcType=VARCHAR}
            </if>
            <if test="handleFlag != null" >
                and handle_flag = #{handleFlag,jdbcType=VARCHAR}
            </if>
            <if test="downloadEndTime != null" >
                and download_end_time = #{downloadEndTime,jdbcType=VARCHAR}
            </if>
            <if test="verifyMessage != null" >
                and verify_message = #{verifyMessage,jdbcType=VARCHAR}
            </if>
            <if test="operateId != null" >
                and operate_id = #{operateId,jdbcType=VARCHAR}
            </if>
            <if test="remark != null" >
                and remark = #{remark,jdbcType=VARCHAR}
            </if>
            <if test="tmSmp != null" >
                and tm_smp = #{tmSmp,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
</mapper>