<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.dceppay.dao.pay.IPayRefundExtDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.dceppay.entity.pay.PayRefundDO" >
        <id column="refund_order_no" property="refundOrderNo" jdbcType="VARCHAR" />
        <result column="out_order_no" property="outOrderNo" jdbcType="VARCHAR" />
        <result column="refund_date" property="refundDate" jdbcType="VARCHAR" />
        <result column="refund_time" property="refundTime" jdbcType="VARCHAR" />
        <result column="channel_code" property="channelCode" jdbcType="VARCHAR" />
        <result column="pay_way" property="payWay" jdbcType="VARCHAR" />
        <result column="scene" property="scene" jdbcType="VARCHAR" />
        <result column="bus_type" property="busType" jdbcType="VARCHAR" />
        <result column="merchant_no" property="merchantNo" jdbcType="VARCHAR" />
        <result column="org_merchant_no" property="orgMerchantNo" jdbcType="VARCHAR" />
        <result column="wallet_id" property="walletId" jdbcType="VARCHAR" />
        <result column="account_date" property="accountDate" jdbcType="VARCHAR" />
        <result column="order_amount" property="orderAmount" jdbcType="DECIMAL" />
        <result column="refund_amount" property="refundAmount" jdbcType="DECIMAL" />
        <result column="notify_url" property="notifyUrl" jdbcType="VARCHAR" />
        <result column="cancel_flag" property="cancelFlag" jdbcType="VARCHAR" />
        <result column="status" property="status" jdbcType="VARCHAR" />
        <result column="bank_order_no" property="bankOrderNo" jdbcType="VARCHAR" />
        <result column="err_msg_cd" property="errMsgCd" jdbcType="VARCHAR" />
        <result column="err_msg_info" property="errMsgInfo" jdbcType="VARCHAR" />
        <result column="order_complete_time" property="orderCompleteTime" jdbcType="VARCHAR" />
        <result column="receive_notify_time" property="receiveNotifyTime" jdbcType="VARCHAR" />
        <result column="refund_reason" property="refundReason" jdbcType="VARCHAR" />
        <result column="biz_type" property="bizType" jdbcType="VARCHAR" />
        <result column="biz_category" property="bizCategory" jdbcType="VARCHAR" />
        <result column="bank_refund_no" property="bankRefundNo" jdbcType="VARCHAR" />
        <result column="notify_status" property="notifyStatus" jdbcType="VARCHAR" />
        <result column="send_notify_time" property="sendNotifyTime" jdbcType="VARCHAR" />
        <result column="extra" property="extra" jdbcType="VARCHAR" />
        <result column="message_identification" property="messageIdentification" jdbcType="VARCHAR" />
        <result column="tm_smp" property="tmSmp" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        refund_order_no, out_order_no, refund_date, refund_time, channel_code, pay_way, scene,
        bus_type, merchant_no, org_merchant_no, wallet_id, account_date, order_amount, refund_amount,
        notify_url, cancel_flag, status, bank_order_no, err_msg_cd, err_msg_info, order_complete_time,
        receive_notify_time, refund_reason, biz_type, biz_category, bank_refund_no, notify_status,
        send_notify_time, extra, message_identification, tm_smp
    </sql>

    <select id="getByOutRefundNo" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select
        <include refid="Base_Column_List" />
        from pay_refund
        where refund_order_no = #{refundOrderNo,jdbcType=VARCHAR}
    </select>

    <update id="updateRefundWait" parameterType="com.cmpay.dceppay.entity.pay.PayRefundDO">
        update pay_refund
        set status = #{status,jdbcType=VARCHAR},
        <if test="bankRefundNo != null and bankRefundNo != ''">
            bank_refund_no = #{bankRefundNo,jdbcType=VARCHAR},
        </if>
        <if test="messageIdentification != null and messageIdentification != ''" >
            message_identification = #{messageIdentification,jdbcType=VARCHAR},
        </if>
        tm_smp = #{tmSmp,jdbcType=VARCHAR}
        where refund_order_no = #{refundOrderNo,jdbcType=VARCHAR}
    </update>

    <select id="sumRefundAmount"  resultType="java.math.BigDecimal" parameterType="java.lang.String" >
        select
            IFNULL(SUM(refund_amount),0.00)
        from pay_refund
        where out_order_no = #{outOrderNo,jdbcType=VARCHAR}
          and status in ('REFUND_PEND','REFUND_WAIT','REFUND_SUCCESS')
    </select>

    <update id="updateRefundSuccess" parameterType="com.cmpay.dceppay.entity.pay.PayRefundDO">
        update pay_refund
        set status = #{status,jdbcType=VARCHAR},
        receive_notify_time = #{receiveNotifyTime,jdbcType=VARCHAR},
        order_complete_time = #{orderCompleteTime,jdbcType=VARCHAR},
        <if test="bankRefundNo != null">
            bank_refund_no = #{bankRefundNo,jdbcType=VARCHAR},
        </if>
        tm_smp = #{tmSmp,jdbcType=VARCHAR}
        where refund_order_no = #{refundOrderNo,jdbcType=VARCHAR}
    </update>


    <update id="updateRefundFail" parameterType="com.cmpay.dceppay.entity.pay.PayRefundDO">
        update pay_refund
        set status = #{status,jdbcType=VARCHAR},
        receive_notify_time = #{receiveNotifyTime,jdbcType=VARCHAR},
        order_complete_time = #{orderCompleteTime,jdbcType=VARCHAR},
        <if test="bankRefundNo != null">
            bank_refund_no = #{bankRefundNo,jdbcType=VARCHAR},
        </if>
        tm_smp = #{tmSmp,jdbcType=VARCHAR}
        where refund_order_no = #{refundOrderNo,jdbcType=VARCHAR}
    </update>


    <select id="sumRefundSuccessAmount"  resultType="java.math.BigDecimal" parameterType="java.lang.String" >
        select IFNULL(SUM(refund_amount),0)
        from pay_refund
        where out_order_no = #{outOrderNo,jdbcType=VARCHAR}
          and status in ('REFUND_SUCCESS')
    </select>

    <update id="updateAccountDate" parameterType="com.cmpay.dceppay.entity.pay.PayRefundDO">
        update pay_refund
        set account_date = #{accountDate,jdbcType=VARCHAR},
            tm_smp       = #{tmSmp,jdbcType=VARCHAR}
        where refund_order_no = #{refundOrderNo,jdbcType=VARCHAR}
    </update>

    <select id="findWaitRefundList"   resultMap="BaseResultMap" parameterType="com.cmpay.dceppay.entity.pay.RefundQueryDO">
        select
        <include refid="Base_Column_List"/>
        from pay_refund
        WHERE refund_date >= #{requestDate,jdbcType=VARCHAR}
        AND CONCAT(refund_date, refund_time) >= #{orderTimeBegin,jdbcType=VARCHAR}
        AND CONCAT(refund_date, refund_time)  &lt;= #{orderTimeEnd,jdbcType=VARCHAR}
        AND status ='REFUND_WAIT'
        LIMIT #{total,jdbcType=INTEGER}
    </select>

    <select id="findWaitPendList"   resultMap="BaseResultMap" parameterType="com.cmpay.dceppay.entity.pay.RefundQueryDO">
        select
        <include refid="Base_Column_List"/>
        from pay_refund
        WHERE refund_date >= #{requestDate,jdbcType=VARCHAR}
        AND CONCAT(refund_date, refund_time) >= #{orderTimeBegin,jdbcType=VARCHAR}
        AND CONCAT(refund_date, refund_time)  &lt;= #{orderTimeEnd,jdbcType=VARCHAR}
        AND status ='REFUND_PEND'
        LIMIT #{total,jdbcType=INTEGER}
    </select>


</mapper>