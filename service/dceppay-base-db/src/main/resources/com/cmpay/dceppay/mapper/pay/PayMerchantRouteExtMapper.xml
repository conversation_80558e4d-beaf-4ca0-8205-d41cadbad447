<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.dceppay.dao.pay.IPayMerchantRouteExtDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.dceppay.entity.pay.PayMerchantRouteDO" >
        <id column="route_id" property="routeId" jdbcType="VARCHAR" />
        <result column="buss_code" property="bussCode" jdbcType="VARCHAR" />
        <result column="buss_type" property="bussType" jdbcType="VARCHAR" />
        <result column="merchant_no" property="merchantNo" jdbcType="VARCHAR" />
        <result column="route_status" property="routeStatus" jdbcType="VARCHAR" />
        <result column="remark" property="remark" jdbcType="VARCHAR" />
        <result column="create_id" property="createId" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="VARCHAR" />
        <result column="update_id" property="updateId" jdbcType="VARCHAR" />
        <result column="update_time" property="updateTime" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        route_id, buss_code, buss_type, merchant_no, route_status, remark, create_id, create_time, 
        update_id, update_time
    </sql>

    <select id="getByBusCode" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select
        <include refid="Base_Column_List"/>
        from pay_merchant_route
        where buss_code = #{bussCode,jdbcType=VARCHAR}
    </select>


</mapper>