<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.dceppay.dao.smartmerchant.IICBCMerchantDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.dceppay.entity.smartmerchant.ICBCMerchantDO" >
        <id column="merchant_id" property="merchantId" jdbcType="VARCHAR" />
        <result column="operator_merchant_id" property="operatorMerchantId" jdbcType="VARCHAR" />
        <result column="service_merchant_flag" property="serviceMerchantFlag" jdbcType="VARCHAR" />
        <result column="service_id" property="serviceId" jdbcType="VARCHAR" />
        <result column="merchant_name" property="merchantName" jdbcType="VARCHAR" />
        <result column="merchant_show_name" property="merchantShowName" jdbcType="VARCHAR" />
        <result column="merchant_short_name" property="merchantShortName" jdbcType="VARCHAR" />
        <result column="merchant_category" property="merchantCategory" jdbcType="VARCHAR" />
        <result column="buss_code" property="bussCode" jdbcType="VARCHAR" />
        <result column="buss_type" property="bussType" jdbcType="VARCHAR" />
        <result column="merchant_type" property="merchantType" jdbcType="VARCHAR" />
        <result column="merchant_license_type" property="merchantLicenseType" jdbcType="VARCHAR" />
        <result column="merchant_license" property="merchantLicense" jdbcType="VARCHAR" />
        <result column="contact_name" property="contactName" jdbcType="VARCHAR" />
        <result column="service_phone" property="servicePhone" jdbcType="VARCHAR" />
        <result column="contact_phone" property="contactPhone" jdbcType="VARCHAR" />
        <result column="contact_email" property="contactEmail" jdbcType="VARCHAR" />
        <result column="unify_entry_flag" property="unifyEntryFlag" jdbcType="VARCHAR" />
        <result column="settle_cycle" property="settleCycle" jdbcType="VARCHAR" />
        <result column="acc_type" property="accType" jdbcType="VARCHAR" />
        <result column="acc_name" property="accName" jdbcType="VARCHAR" />
        <result column="acc_no" property="accNo" jdbcType="VARCHAR" />
        <result column="acc_bank_name" property="accBankName" jdbcType="VARCHAR" />
        <result column="acc_bank_code" property="accBankCode" jdbcType="VARCHAR" />
        <result column="wallet_id" property="walletId" jdbcType="VARCHAR" />
        <result column="wallet_name" property="walletName" jdbcType="VARCHAR" />
        <result column="merchant_wallet_name" property="merchantWalletName" jdbcType="VARCHAR" />
        <result column="merchant_wallet_id" property="merchantWalletId" jdbcType="VARCHAR" />
        <result column="protocol_wallet_name" property="protocolWalletName" jdbcType="VARCHAR" />
        <result column="protocol_wallet_id" property="protocolWalletId" jdbcType="VARCHAR" />
        <result column="remark" property="remark" jdbcType="VARCHAR" />
        <result column="onboard_status" property="onboardStatus" jdbcType="VARCHAR" />
        <result column="service_status" property="serviceStatus" jdbcType="VARCHAR" />
        <result column="onboard_id" property="onboardId" jdbcType="VARCHAR" />
        <result column="onboard_time" property="onboardTime" jdbcType="VARCHAR" />
        <result column="msg_code" property="msgCode" jdbcType="VARCHAR" />
        <result column="msg_info" property="msgInfo" jdbcType="VARCHAR" />
        <result column="create_id" property="createId" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="VARCHAR" />
        <result column="update_id" property="updateId" jdbcType="VARCHAR" />
        <result column="update_time" property="updateTime" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        merchant_id, operator_merchant_id, service_merchant_flag, service_id, merchant_name, 
        merchant_show_name, merchant_short_name, merchant_category, buss_code, buss_type, 
        merchant_type, merchant_license_type, merchant_license, contact_name, service_phone, 
        contact_phone, contact_email, unify_entry_flag, settle_cycle, acc_type, acc_name, 
        acc_no, acc_bank_name, acc_bank_code, wallet_id, wallet_name, merchant_wallet_name, 
        merchant_wallet_id, protocol_wallet_name, protocol_wallet_id, remark, onboard_status, 
        service_status, onboard_id, onboard_time, msg_code, msg_info, create_id, create_time, 
        update_id, update_time
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from icbc_merchant
        where merchant_id = #{merchantId,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from icbc_merchant
        where merchant_id = #{merchantId,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.cmpay.dceppay.entity.smartmerchant.ICBCMerchantDO" >
        insert into icbc_merchant
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="merchantId != null" >
                merchant_id,
            </if>
            <if test="operatorMerchantId != null" >
                operator_merchant_id,
            </if>
            <if test="serviceMerchantFlag != null" >
                service_merchant_flag,
            </if>
            <if test="serviceId != null" >
                service_id,
            </if>
            <if test="merchantName != null" >
                merchant_name,
            </if>
            <if test="merchantShowName != null" >
                merchant_show_name,
            </if>
            <if test="merchantShortName != null" >
                merchant_short_name,
            </if>
            <if test="merchantCategory != null" >
                merchant_category,
            </if>
            <if test="bussCode != null" >
                buss_code,
            </if>
            <if test="bussType != null" >
                buss_type,
            </if>
            <if test="merchantType != null" >
                merchant_type,
            </if>
            <if test="merchantLicenseType != null" >
                merchant_license_type,
            </if>
            <if test="merchantLicense != null" >
                merchant_license,
            </if>
            <if test="contactName != null" >
                contact_name,
            </if>
            <if test="servicePhone != null" >
                service_phone,
            </if>
            <if test="contactPhone != null" >
                contact_phone,
            </if>
            <if test="contactEmail != null" >
                contact_email,
            </if>
            <if test="unifyEntryFlag != null" >
                unify_entry_flag,
            </if>
            <if test="settleCycle != null" >
                settle_cycle,
            </if>
            <if test="accType != null" >
                acc_type,
            </if>
            <if test="accName != null" >
                acc_name,
            </if>
            <if test="accNo != null" >
                acc_no,
            </if>
            <if test="accBankName != null" >
                acc_bank_name,
            </if>
            <if test="accBankCode != null" >
                acc_bank_code,
            </if>
            <if test="walletId != null" >
                wallet_id,
            </if>
            <if test="walletName != null" >
                wallet_name,
            </if>
            <if test="merchantWalletName != null" >
                merchant_wallet_name,
            </if>
            <if test="merchantWalletId != null" >
                merchant_wallet_id,
            </if>
            <if test="protocolWalletName != null" >
                protocol_wallet_name,
            </if>
            <if test="protocolWalletId != null" >
                protocol_wallet_id,
            </if>
            <if test="remark != null" >
                remark,
            </if>
            <if test="onboardStatus != null" >
                onboard_status,
            </if>
            <if test="serviceStatus != null" >
                service_status,
            </if>
            <if test="onboardId != null" >
                onboard_id,
            </if>
            <if test="onboardTime != null" >
                onboard_time,
            </if>
            <if test="msgCode != null" >
                msg_code,
            </if>
            <if test="msgInfo != null" >
                msg_info,
            </if>
            <if test="createId != null" >
                create_id,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="updateId != null" >
                update_id,
            </if>
            <if test="updateTime != null" >
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="merchantId != null" >
                #{merchantId,jdbcType=VARCHAR},
            </if>
            <if test="operatorMerchantId != null" >
                #{operatorMerchantId,jdbcType=VARCHAR},
            </if>
            <if test="serviceMerchantFlag != null" >
                #{serviceMerchantFlag,jdbcType=VARCHAR},
            </if>
            <if test="serviceId != null" >
                #{serviceId,jdbcType=VARCHAR},
            </if>
            <if test="merchantName != null" >
                #{merchantName,jdbcType=VARCHAR},
            </if>
            <if test="merchantShowName != null" >
                #{merchantShowName,jdbcType=VARCHAR},
            </if>
            <if test="merchantShortName != null" >
                #{merchantShortName,jdbcType=VARCHAR},
            </if>
            <if test="merchantCategory != null" >
                #{merchantCategory,jdbcType=VARCHAR},
            </if>
            <if test="bussCode != null" >
                #{bussCode,jdbcType=VARCHAR},
            </if>
            <if test="bussType != null" >
                #{bussType,jdbcType=VARCHAR},
            </if>
            <if test="merchantType != null" >
                #{merchantType,jdbcType=VARCHAR},
            </if>
            <if test="merchantLicenseType != null" >
                #{merchantLicenseType,jdbcType=VARCHAR},
            </if>
            <if test="merchantLicense != null" >
                #{merchantLicense,jdbcType=VARCHAR},
            </if>
            <if test="contactName != null" >
                #{contactName,jdbcType=VARCHAR},
            </if>
            <if test="servicePhone != null" >
                #{servicePhone,jdbcType=VARCHAR},
            </if>
            <if test="contactPhone != null" >
                #{contactPhone,jdbcType=VARCHAR},
            </if>
            <if test="contactEmail != null" >
                #{contactEmail,jdbcType=VARCHAR},
            </if>
            <if test="unifyEntryFlag != null" >
                #{unifyEntryFlag,jdbcType=VARCHAR},
            </if>
            <if test="settleCycle != null" >
                #{settleCycle,jdbcType=VARCHAR},
            </if>
            <if test="accType != null" >
                #{accType,jdbcType=VARCHAR},
            </if>
            <if test="accName != null" >
                #{accName,jdbcType=VARCHAR},
            </if>
            <if test="accNo != null" >
                #{accNo,jdbcType=VARCHAR},
            </if>
            <if test="accBankName != null" >
                #{accBankName,jdbcType=VARCHAR},
            </if>
            <if test="accBankCode != null" >
                #{accBankCode,jdbcType=VARCHAR},
            </if>
            <if test="walletId != null" >
                #{walletId,jdbcType=VARCHAR},
            </if>
            <if test="walletName != null" >
                #{walletName,jdbcType=VARCHAR},
            </if>
            <if test="merchantWalletName != null" >
                #{merchantWalletName,jdbcType=VARCHAR},
            </if>
            <if test="merchantWalletId != null" >
                #{merchantWalletId,jdbcType=VARCHAR},
            </if>
            <if test="protocolWalletName != null" >
                #{protocolWalletName,jdbcType=VARCHAR},
            </if>
            <if test="protocolWalletId != null" >
                #{protocolWalletId,jdbcType=VARCHAR},
            </if>
            <if test="remark != null" >
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="onboardStatus != null" >
                #{onboardStatus,jdbcType=VARCHAR},
            </if>
            <if test="serviceStatus != null" >
                #{serviceStatus,jdbcType=VARCHAR},
            </if>
            <if test="onboardId != null" >
                #{onboardId,jdbcType=VARCHAR},
            </if>
            <if test="onboardTime != null" >
                #{onboardTime,jdbcType=VARCHAR},
            </if>
            <if test="msgCode != null" >
                #{msgCode,jdbcType=VARCHAR},
            </if>
            <if test="msgInfo != null" >
                #{msgInfo,jdbcType=VARCHAR},
            </if>
            <if test="createId != null" >
                #{createId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=VARCHAR},
            </if>
            <if test="updateId != null" >
                #{updateId,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null" >
                #{updateTime,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.cmpay.dceppay.entity.smartmerchant.ICBCMerchantDO" >
        update icbc_merchant
        <set >
            <if test="operatorMerchantId != null" >
                operator_merchant_id = #{operatorMerchantId,jdbcType=VARCHAR},
            </if>
            <if test="serviceMerchantFlag != null" >
                service_merchant_flag = #{serviceMerchantFlag,jdbcType=VARCHAR},
            </if>
            <if test="serviceId != null" >
                service_id = #{serviceId,jdbcType=VARCHAR},
            </if>
            <if test="merchantName != null" >
                merchant_name = #{merchantName,jdbcType=VARCHAR},
            </if>
            <if test="merchantShowName != null" >
                merchant_show_name = #{merchantShowName,jdbcType=VARCHAR},
            </if>
            <if test="merchantShortName != null" >
                merchant_short_name = #{merchantShortName,jdbcType=VARCHAR},
            </if>
            <if test="merchantCategory != null" >
                merchant_category = #{merchantCategory,jdbcType=VARCHAR},
            </if>
            <if test="bussCode != null" >
                buss_code = #{bussCode,jdbcType=VARCHAR},
            </if>
            <if test="bussType != null" >
                buss_type = #{bussType,jdbcType=VARCHAR},
            </if>
            <if test="merchantType != null" >
                merchant_type = #{merchantType,jdbcType=VARCHAR},
            </if>
            <if test="merchantLicenseType != null" >
                merchant_license_type = #{merchantLicenseType,jdbcType=VARCHAR},
            </if>
            <if test="merchantLicense != null" >
                merchant_license = #{merchantLicense,jdbcType=VARCHAR},
            </if>
            <if test="contactName != null" >
                contact_name = #{contactName,jdbcType=VARCHAR},
            </if>
            <if test="servicePhone != null" >
                service_phone = #{servicePhone,jdbcType=VARCHAR},
            </if>
            <if test="contactPhone != null" >
                contact_phone = #{contactPhone,jdbcType=VARCHAR},
            </if>
            <if test="contactEmail != null" >
                contact_email = #{contactEmail,jdbcType=VARCHAR},
            </if>
            <if test="unifyEntryFlag != null" >
                unify_entry_flag = #{unifyEntryFlag,jdbcType=VARCHAR},
            </if>
            <if test="settleCycle != null" >
                settle_cycle = #{settleCycle,jdbcType=VARCHAR},
            </if>
            <if test="accType != null" >
                acc_type = #{accType,jdbcType=VARCHAR},
            </if>
            <if test="accName != null" >
                acc_name = #{accName,jdbcType=VARCHAR},
            </if>
            <if test="accNo != null" >
                acc_no = #{accNo,jdbcType=VARCHAR},
            </if>
            <if test="accBankName != null" >
                acc_bank_name = #{accBankName,jdbcType=VARCHAR},
            </if>
            <if test="accBankCode != null" >
                acc_bank_code = #{accBankCode,jdbcType=VARCHAR},
            </if>
            <if test="walletId != null" >
                wallet_id = #{walletId,jdbcType=VARCHAR},
            </if>
            <if test="walletName != null" >
                wallet_name = #{walletName,jdbcType=VARCHAR},
            </if>
            <if test="merchantWalletName != null" >
                merchant_wallet_name = #{merchantWalletName,jdbcType=VARCHAR},
            </if>
            <if test="merchantWalletId != null" >
                merchant_wallet_id = #{merchantWalletId,jdbcType=VARCHAR},
            </if>
            <if test="protocolWalletName != null" >
                protocol_wallet_name = #{protocolWalletName,jdbcType=VARCHAR},
            </if>
            <if test="protocolWalletId != null" >
                protocol_wallet_id = #{protocolWalletId,jdbcType=VARCHAR},
            </if>
            <if test="remark != null" >
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="onboardStatus != null" >
                onboard_status = #{onboardStatus,jdbcType=VARCHAR},
            </if>
            <if test="serviceStatus != null" >
                service_status = #{serviceStatus,jdbcType=VARCHAR},
            </if>
            <if test="onboardId != null" >
                onboard_id = #{onboardId,jdbcType=VARCHAR},
            </if>
            <if test="onboardTime != null" >
                onboard_time = #{onboardTime,jdbcType=VARCHAR},
            </if>
            <if test="msgCode != null" >
                msg_code = #{msgCode,jdbcType=VARCHAR},
            </if>
            <if test="msgInfo != null" >
                msg_info = #{msgInfo,jdbcType=VARCHAR},
            </if>
            <if test="createId != null" >
                create_id = #{createId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=VARCHAR},
            </if>
            <if test="updateId != null" >
                update_id = #{updateId,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null" >
                update_time = #{updateTime,jdbcType=VARCHAR},
            </if>
        </set>
        where merchant_id = #{merchantId,jdbcType=VARCHAR}
    </update>

    <select id="find" resultMap="BaseResultMap" parameterType="com.cmpay.dceppay.entity.smartmerchant.ICBCMerchantDO" >
        select 
        <include refid="Base_Column_List" />
        from icbc_merchant
        <where >
            <if test="merchantId != null" >
                and merchant_id = #{merchantId,jdbcType=VARCHAR}
            </if>
            <if test="operatorMerchantId != null" >
                and operator_merchant_id = #{operatorMerchantId,jdbcType=VARCHAR}
            </if>
            <if test="serviceMerchantFlag != null" >
                and service_merchant_flag = #{serviceMerchantFlag,jdbcType=VARCHAR}
            </if>
            <if test="serviceId != null" >
                and service_id = #{serviceId,jdbcType=VARCHAR}
            </if>
            <if test="merchantName != null" >
                and merchant_name = #{merchantName,jdbcType=VARCHAR}
            </if>
            <if test="merchantShowName != null" >
                and merchant_show_name = #{merchantShowName,jdbcType=VARCHAR}
            </if>
            <if test="merchantShortName != null" >
                and merchant_short_name = #{merchantShortName,jdbcType=VARCHAR}
            </if>
            <if test="merchantCategory != null" >
                and merchant_category = #{merchantCategory,jdbcType=VARCHAR}
            </if>
            <if test="bussCode != null" >
                and buss_code = #{bussCode,jdbcType=VARCHAR}
            </if>
            <if test="bussType != null" >
                and buss_type = #{bussType,jdbcType=VARCHAR}
            </if>
            <if test="merchantType != null" >
                and merchant_type = #{merchantType,jdbcType=VARCHAR}
            </if>
            <if test="merchantLicenseType != null" >
                and merchant_license_type = #{merchantLicenseType,jdbcType=VARCHAR}
            </if>
            <if test="merchantLicense != null" >
                and merchant_license = #{merchantLicense,jdbcType=VARCHAR}
            </if>
            <if test="contactName != null" >
                and contact_name = #{contactName,jdbcType=VARCHAR}
            </if>
            <if test="servicePhone != null" >
                and service_phone = #{servicePhone,jdbcType=VARCHAR}
            </if>
            <if test="contactPhone != null" >
                and contact_phone = #{contactPhone,jdbcType=VARCHAR}
            </if>
            <if test="contactEmail != null" >
                and contact_email = #{contactEmail,jdbcType=VARCHAR}
            </if>
            <if test="unifyEntryFlag != null" >
                and unify_entry_flag = #{unifyEntryFlag,jdbcType=VARCHAR}
            </if>
            <if test="settleCycle != null" >
                and settle_cycle = #{settleCycle,jdbcType=VARCHAR}
            </if>
            <if test="accType != null" >
                and acc_type = #{accType,jdbcType=VARCHAR}
            </if>
            <if test="accName != null" >
                and acc_name = #{accName,jdbcType=VARCHAR}
            </if>
            <if test="accNo != null" >
                and acc_no = #{accNo,jdbcType=VARCHAR}
            </if>
            <if test="accBankName != null" >
                and acc_bank_name = #{accBankName,jdbcType=VARCHAR}
            </if>
            <if test="accBankCode != null" >
                and acc_bank_code = #{accBankCode,jdbcType=VARCHAR}
            </if>
            <if test="walletId != null" >
                and wallet_id = #{walletId,jdbcType=VARCHAR}
            </if>
            <if test="walletName != null" >
                and wallet_name = #{walletName,jdbcType=VARCHAR}
            </if>
            <if test="merchantWalletName != null" >
                and merchant_wallet_name = #{merchantWalletName,jdbcType=VARCHAR}
            </if>
            <if test="merchantWalletId != null" >
                and merchant_wallet_id = #{merchantWalletId,jdbcType=VARCHAR}
            </if>
            <if test="protocolWalletName != null" >
                and protocol_wallet_name = #{protocolWalletName,jdbcType=VARCHAR}
            </if>
            <if test="protocolWalletId != null" >
                and protocol_wallet_id = #{protocolWalletId,jdbcType=VARCHAR}
            </if>
            <if test="remark != null" >
                and remark = #{remark,jdbcType=VARCHAR}
            </if>
            <if test="onboardStatus != null" >
                and onboard_status = #{onboardStatus,jdbcType=VARCHAR}
            </if>
            <if test="serviceStatus != null" >
                and service_status = #{serviceStatus,jdbcType=VARCHAR}
            </if>
            <if test="onboardId != null" >
                and onboard_id = #{onboardId,jdbcType=VARCHAR}
            </if>
            <if test="onboardTime != null" >
                and onboard_time = #{onboardTime,jdbcType=VARCHAR}
            </if>
            <if test="msgCode != null" >
                and msg_code = #{msgCode,jdbcType=VARCHAR}
            </if>
            <if test="msgInfo != null" >
                and msg_info = #{msgInfo,jdbcType=VARCHAR}
            </if>
            <if test="createId != null" >
                and create_id = #{createId,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null" >
                and create_time = #{createTime,jdbcType=VARCHAR}
            </if>
            <if test="updateId != null" >
                and update_id = #{updateId,jdbcType=VARCHAR}
            </if>
            <if test="updateTime != null" >
                and update_time = #{updateTime,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
</mapper>