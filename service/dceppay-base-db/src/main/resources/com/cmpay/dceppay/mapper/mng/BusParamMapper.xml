<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.dceppay.dao.mng.IBusParamDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.dceppay.entity.mng.BusParamDO" >
        <id column="param_no" property="paramNo" jdbcType="VARCHAR" />
        <result column="param_name" property="paramName" jdbcType="VARCHAR" />
        <result column="param_type" property="paramType" jdbcType="VARCHAR" />
        <result column="param_code" property="paramCode" jdbcType="VARCHAR" />
        <result column="param_label" property="paramLabel" jdbcType="VARCHAR" />
        <result column="order_num" property="orderNum" jdbcType="TINYINT" />
        <result column="remark" property="remark" jdbcType="VARCHAR" />
        <result column="create_user_no" property="createUserNo" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="VARCHAR" />
        <result column="update_user_no" property="updateUserNo" jdbcType="VARCHAR" />
        <result column="update_time" property="updateTime" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        param_no, param_name, param_type, param_code, param_label, order_num, remark, create_user_no, 
        create_time, update_user_no, update_time
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from bus_param
        where param_no = #{paramNo,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from bus_param
        where param_no = #{paramNo,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.cmpay.dceppay.entity.mng.BusParamDO" >
        insert into bus_param
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="paramNo != null" >
                param_no,
            </if>
            <if test="paramName != null" >
                param_name,
            </if>
            <if test="paramType != null" >
                param_type,
            </if>
            <if test="paramCode != null" >
                param_code,
            </if>
            <if test="paramLabel != null" >
                param_label,
            </if>
            <if test="orderNum != null" >
                order_num,
            </if>
            <if test="remark != null" >
                remark,
            </if>
            <if test="createUserNo != null" >
                create_user_no,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="updateUserNo != null" >
                update_user_no,
            </if>
            <if test="updateTime != null" >
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="paramNo != null" >
                #{paramNo,jdbcType=VARCHAR},
            </if>
            <if test="paramName != null" >
                #{paramName,jdbcType=VARCHAR},
            </if>
            <if test="paramType != null" >
                #{paramType,jdbcType=VARCHAR},
            </if>
            <if test="paramCode != null" >
                #{paramCode,jdbcType=VARCHAR},
            </if>
            <if test="paramLabel != null" >
                #{paramLabel,jdbcType=VARCHAR},
            </if>
            <if test="orderNum != null" >
                #{orderNum,jdbcType=TINYINT},
            </if>
            <if test="remark != null" >
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createUserNo != null" >
                #{createUserNo,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=VARCHAR},
            </if>
            <if test="updateUserNo != null" >
                #{updateUserNo,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null" >
                #{updateTime,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.cmpay.dceppay.entity.mng.BusParamDO" >
        update bus_param
        <set >
            <if test="paramName != null" >
                param_name = #{paramName,jdbcType=VARCHAR},
            </if>
            <if test="paramType != null" >
                param_type = #{paramType,jdbcType=VARCHAR},
            </if>
            <if test="paramCode != null" >
                param_code = #{paramCode,jdbcType=VARCHAR},
            </if>
            <if test="paramLabel != null" >
                param_label = #{paramLabel,jdbcType=VARCHAR},
            </if>
            <if test="orderNum != null" >
                order_num = #{orderNum,jdbcType=TINYINT},
            </if>
            <if test="remark != null" >
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createUserNo != null" >
                create_user_no = #{createUserNo,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=VARCHAR},
            </if>
            <if test="updateUserNo != null" >
                update_user_no = #{updateUserNo,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null" >
                update_time = #{updateTime,jdbcType=VARCHAR},
            </if>
        </set>
        where param_no = #{paramNo,jdbcType=VARCHAR}
    </update>

    <select id="find" resultMap="BaseResultMap" parameterType="com.cmpay.dceppay.entity.mng.BusParamDO" >
        select 
        <include refid="Base_Column_List" />
        from bus_param
        <where >
            <if test="paramNo != null" >
                and param_no = #{paramNo,jdbcType=VARCHAR}
            </if>
            <if test="paramName != null" >
                and param_name = #{paramName,jdbcType=VARCHAR}
            </if>
            <if test="paramType != null" >
                and param_type = #{paramType,jdbcType=VARCHAR}
            </if>
            <if test="paramCode != null" >
                and param_code = #{paramCode,jdbcType=VARCHAR}
            </if>
            <if test="paramLabel != null" >
                and param_label = #{paramLabel,jdbcType=VARCHAR}
            </if>
            <if test="orderNum != null" >
                and order_num = #{orderNum,jdbcType=TINYINT}
            </if>
            <if test="remark != null" >
                and remark = #{remark,jdbcType=VARCHAR}
            </if>
            <if test="createUserNo != null" >
                and create_user_no = #{createUserNo,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null" >
                and create_time = #{createTime,jdbcType=VARCHAR}
            </if>
            <if test="updateUserNo != null" >
                and update_user_no = #{updateUserNo,jdbcType=VARCHAR}
            </if>
            <if test="updateTime != null" >
                and update_time = #{updateTime,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
</mapper>