<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.dceppay.dao.pay.IPaySettlementDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.dceppay.entity.pay.PaySettlementDO" >
        <id column="out_order_no" property="outOrderNo" jdbcType="VARCHAR" />
        <id column="refund_order_no" property="refundOrderNo" jdbcType="VARCHAR" />
        <result column="jrn_no" property="jrnNo" jdbcType="VARCHAR" />
        <result column="settlement_date" property="settlementDate" jdbcType="VARCHAR" />
        <result column="trade_jrn_no" property="tradeJrnNo" jdbcType="VARCHAR" />
        <result column="order_date" property="orderDate" jdbcType="VARCHAR" />
        <result column="order_time" property="orderTime" jdbcType="VARCHAR" />
        <result column="channel_code" property="channelCode" jdbcType="VARCHAR" />
        <result column="pay_way" property="payWay" jdbcType="VARCHAR" />
        <result column="scene" property="scene" jdbcType="VARCHAR" />
        <result column="bus_type" property="busType" jdbcType="VARCHAR" />
        <result column="merchant_no" property="merchantNo" jdbcType="VARCHAR" />
        <result column="org_merchant_no" property="orgMerchantNo" jdbcType="VARCHAR" />
        <result column="wallet_id" property="walletId" jdbcType="VARCHAR" />
        <result column="account_date" property="accountDate" jdbcType="VARCHAR" />
        <result column="order_amount" property="orderAmount" jdbcType="DECIMAL" />
        <result column="bank_order_no" property="bankOrderNo" jdbcType="VARCHAR" />
        <result column="order_complete_time" property="orderCompleteTime" jdbcType="VARCHAR" />
        <result column="settlement_type" property="settlementType" jdbcType="VARCHAR" />
        <result column="check_status" property="checkStatus" jdbcType="VARCHAR" />
        <result column="check_date" property="checkDate" jdbcType="VARCHAR" />
        <result column="check_complete_time" property="checkCompleteTime" jdbcType="VARCHAR" />
        <result column="remark" property="remark" jdbcType="VARCHAR" />
        <result column="tm_smp" property="tmSmp" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        out_order_no, refund_order_no, jrn_no, settlement_date, trade_jrn_no, order_date, 
        order_time, channel_code, pay_way, scene, bus_type, merchant_no, org_merchant_no, 
        wallet_id, account_date, order_amount, bank_order_no, order_complete_time, settlement_type, 
        check_status, check_date, check_complete_time, remark, tm_smp
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="com.cmpay.dceppay.entity.pay.PaySettlementDOKey" >
        select 
        <include refid="Base_Column_List" />
        from pay_settlement
        where out_order_no = #{outOrderNo,jdbcType=VARCHAR}
          and refund_order_no = #{refundOrderNo,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="com.cmpay.dceppay.entity.pay.PaySettlementDOKey" >
        delete from pay_settlement
        where out_order_no = #{outOrderNo,jdbcType=VARCHAR}
          and refund_order_no = #{refundOrderNo,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.cmpay.dceppay.entity.pay.PaySettlementDO" >
        insert into pay_settlement
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="outOrderNo != null" >
                out_order_no,
            </if>
            <if test="refundOrderNo != null" >
                refund_order_no,
            </if>
            <if test="jrnNo != null" >
                jrn_no,
            </if>
            <if test="settlementDate != null" >
                settlement_date,
            </if>
            <if test="tradeJrnNo != null" >
                trade_jrn_no,
            </if>
            <if test="orderDate != null" >
                order_date,
            </if>
            <if test="orderTime != null" >
                order_time,
            </if>
            <if test="channelCode != null" >
                channel_code,
            </if>
            <if test="payWay != null" >
                pay_way,
            </if>
            <if test="scene != null" >
                scene,
            </if>
            <if test="busType != null" >
                bus_type,
            </if>
            <if test="merchantNo != null" >
                merchant_no,
            </if>
            <if test="orgMerchantNo != null" >
                org_merchant_no,
            </if>
            <if test="walletId != null" >
                wallet_id,
            </if>
            <if test="accountDate != null" >
                account_date,
            </if>
            <if test="orderAmount != null" >
                order_amount,
            </if>
            <if test="bankOrderNo != null" >
                bank_order_no,
            </if>
            <if test="orderCompleteTime != null" >
                order_complete_time,
            </if>
            <if test="settlementType != null" >
                settlement_type,
            </if>
            <if test="checkStatus != null" >
                check_status,
            </if>
            <if test="checkDate != null" >
                check_date,
            </if>
            <if test="checkCompleteTime != null" >
                check_complete_time,
            </if>
            <if test="remark != null" >
                remark,
            </if>
            <if test="tmSmp != null" >
                tm_smp,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="outOrderNo != null" >
                #{outOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="refundOrderNo != null" >
                #{refundOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="jrnNo != null" >
                #{jrnNo,jdbcType=VARCHAR},
            </if>
            <if test="settlementDate != null" >
                #{settlementDate,jdbcType=VARCHAR},
            </if>
            <if test="tradeJrnNo != null" >
                #{tradeJrnNo,jdbcType=VARCHAR},
            </if>
            <if test="orderDate != null" >
                #{orderDate,jdbcType=VARCHAR},
            </if>
            <if test="orderTime != null" >
                #{orderTime,jdbcType=VARCHAR},
            </if>
            <if test="channelCode != null" >
                #{channelCode,jdbcType=VARCHAR},
            </if>
            <if test="payWay != null" >
                #{payWay,jdbcType=VARCHAR},
            </if>
            <if test="scene != null" >
                #{scene,jdbcType=VARCHAR},
            </if>
            <if test="busType != null" >
                #{busType,jdbcType=VARCHAR},
            </if>
            <if test="merchantNo != null" >
                #{merchantNo,jdbcType=VARCHAR},
            </if>
            <if test="orgMerchantNo != null" >
                #{orgMerchantNo,jdbcType=VARCHAR},
            </if>
            <if test="walletId != null" >
                #{walletId,jdbcType=VARCHAR},
            </if>
            <if test="accountDate != null" >
                #{accountDate,jdbcType=VARCHAR},
            </if>
            <if test="orderAmount != null" >
                #{orderAmount,jdbcType=DECIMAL},
            </if>
            <if test="bankOrderNo != null" >
                #{bankOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="orderCompleteTime != null" >
                #{orderCompleteTime,jdbcType=VARCHAR},
            </if>
            <if test="settlementType != null" >
                #{settlementType,jdbcType=VARCHAR},
            </if>
            <if test="checkStatus != null" >
                #{checkStatus,jdbcType=VARCHAR},
            </if>
            <if test="checkDate != null" >
                #{checkDate,jdbcType=VARCHAR},
            </if>
            <if test="checkCompleteTime != null" >
                #{checkCompleteTime,jdbcType=VARCHAR},
            </if>
            <if test="remark != null" >
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.cmpay.dceppay.entity.pay.PaySettlementDO" >
        update pay_settlement
        <set >
            <if test="jrnNo != null" >
                jrn_no = #{jrnNo,jdbcType=VARCHAR},
            </if>
            <if test="settlementDate != null" >
                settlement_date = #{settlementDate,jdbcType=VARCHAR},
            </if>
            <if test="tradeJrnNo != null" >
                trade_jrn_no = #{tradeJrnNo,jdbcType=VARCHAR},
            </if>
            <if test="orderDate != null" >
                order_date = #{orderDate,jdbcType=VARCHAR},
            </if>
            <if test="orderTime != null" >
                order_time = #{orderTime,jdbcType=VARCHAR},
            </if>
            <if test="channelCode != null" >
                channel_code = #{channelCode,jdbcType=VARCHAR},
            </if>
            <if test="payWay != null" >
                pay_way = #{payWay,jdbcType=VARCHAR},
            </if>
            <if test="scene != null" >
                scene = #{scene,jdbcType=VARCHAR},
            </if>
            <if test="busType != null" >
                bus_type = #{busType,jdbcType=VARCHAR},
            </if>
            <if test="merchantNo != null" >
                merchant_no = #{merchantNo,jdbcType=VARCHAR},
            </if>
            <if test="orgMerchantNo != null" >
                org_merchant_no = #{orgMerchantNo,jdbcType=VARCHAR},
            </if>
            <if test="walletId != null" >
                wallet_id = #{walletId,jdbcType=VARCHAR},
            </if>
            <if test="accountDate != null" >
                account_date = #{accountDate,jdbcType=VARCHAR},
            </if>
            <if test="orderAmount != null" >
                order_amount = #{orderAmount,jdbcType=DECIMAL},
            </if>
            <if test="bankOrderNo != null" >
                bank_order_no = #{bankOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="orderCompleteTime != null" >
                order_complete_time = #{orderCompleteTime,jdbcType=VARCHAR},
            </if>
            <if test="settlementType != null" >
                settlement_type = #{settlementType,jdbcType=VARCHAR},
            </if>
            <if test="checkStatus != null" >
                check_status = #{checkStatus,jdbcType=VARCHAR},
            </if>
            <if test="checkDate != null" >
                check_date = #{checkDate,jdbcType=VARCHAR},
            </if>
            <if test="checkCompleteTime != null" >
                check_complete_time = #{checkCompleteTime,jdbcType=VARCHAR},
            </if>
            <if test="remark != null" >
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="tmSmp != null" >
                tm_smp = #{tmSmp,jdbcType=VARCHAR},
            </if>
        </set>
        where out_order_no = #{outOrderNo,jdbcType=VARCHAR}
          and refund_order_no = #{refundOrderNo,jdbcType=VARCHAR}
    </update>

    <select id="find" resultMap="BaseResultMap" parameterType="com.cmpay.dceppay.entity.pay.PaySettlementDO" >
        select 
        <include refid="Base_Column_List" />
        from pay_settlement
        <where >
            <if test="outOrderNo != null" >
                and out_order_no = #{outOrderNo,jdbcType=VARCHAR}
            </if>
            <if test="refundOrderNo != null" >
                and refund_order_no = #{refundOrderNo,jdbcType=VARCHAR}
            </if>
            <if test="jrnNo != null" >
                and jrn_no = #{jrnNo,jdbcType=VARCHAR}
            </if>
            <if test="settlementDate != null" >
                and settlement_date = #{settlementDate,jdbcType=VARCHAR}
            </if>
            <if test="tradeJrnNo != null" >
                and trade_jrn_no = #{tradeJrnNo,jdbcType=VARCHAR}
            </if>
            <if test="orderDate != null" >
                and order_date = #{orderDate,jdbcType=VARCHAR}
            </if>
            <if test="orderTime != null" >
                and order_time = #{orderTime,jdbcType=VARCHAR}
            </if>
            <if test="channelCode != null" >
                and channel_code = #{channelCode,jdbcType=VARCHAR}
            </if>
            <if test="payWay != null" >
                and pay_way = #{payWay,jdbcType=VARCHAR}
            </if>
            <if test="scene != null" >
                and scene = #{scene,jdbcType=VARCHAR}
            </if>
            <if test="busType != null" >
                and bus_type = #{busType,jdbcType=VARCHAR}
            </if>
            <if test="merchantNo != null" >
                and merchant_no = #{merchantNo,jdbcType=VARCHAR}
            </if>
            <if test="orgMerchantNo != null" >
                and org_merchant_no = #{orgMerchantNo,jdbcType=VARCHAR}
            </if>
            <if test="walletId != null" >
                and wallet_id = #{walletId,jdbcType=VARCHAR}
            </if>
            <if test="accountDate != null" >
                and account_date = #{accountDate,jdbcType=VARCHAR}
            </if>
            <if test="orderAmount != null" >
                and order_amount = #{orderAmount,jdbcType=DECIMAL}
            </if>
            <if test="bankOrderNo != null" >
                and bank_order_no = #{bankOrderNo,jdbcType=VARCHAR}
            </if>
            <if test="orderCompleteTime != null" >
                and order_complete_time = #{orderCompleteTime,jdbcType=VARCHAR}
            </if>
            <if test="settlementType != null" >
                and settlement_type = #{settlementType,jdbcType=VARCHAR}
            </if>
            <if test="checkStatus != null" >
                and check_status = #{checkStatus,jdbcType=VARCHAR}
            </if>
            <if test="checkDate != null" >
                and check_date = #{checkDate,jdbcType=VARCHAR}
            </if>
            <if test="checkCompleteTime != null" >
                and check_complete_time = #{checkCompleteTime,jdbcType=VARCHAR}
            </if>
            <if test="remark != null" >
                and remark = #{remark,jdbcType=VARCHAR}
            </if>
            <if test="tmSmp != null" >
                and tm_smp = #{tmSmp,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
</mapper>