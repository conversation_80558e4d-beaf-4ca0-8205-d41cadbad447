<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.dceppay.dao.pay.IPayMerchantRouteDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.dceppay.entity.pay.PayMerchantRouteDO" >
        <id column="route_id" property="routeId" jdbcType="VARCHAR" />
        <result column="buss_code" property="bussCode" jdbcType="VARCHAR" />
        <result column="buss_type" property="bussType" jdbcType="VARCHAR" />
        <result column="merchant_no" property="merchantNo" jdbcType="VARCHAR" />
        <result column="route_status" property="routeStatus" jdbcType="VARCHAR" />
        <result column="remark" property="remark" jdbcType="VARCHAR" />
        <result column="create_id" property="createId" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="VARCHAR" />
        <result column="update_id" property="updateId" jdbcType="VARCHAR" />
        <result column="update_time" property="updateTime" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        route_id, buss_code, buss_type, merchant_no, route_status, remark, create_id, create_time, 
        update_id, update_time
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from pay_merchant_route
        where route_id = #{routeId,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from pay_merchant_route
        where route_id = #{routeId,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.cmpay.dceppay.entity.pay.PayMerchantRouteDO" >
        insert into pay_merchant_route
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="routeId != null" >
                route_id,
            </if>
            <if test="bussCode != null" >
                buss_code,
            </if>
            <if test="bussType != null" >
                buss_type,
            </if>
            <if test="merchantNo != null" >
                merchant_no,
            </if>
            <if test="routeStatus != null" >
                route_status,
            </if>
            <if test="remark != null" >
                remark,
            </if>
            <if test="createId != null" >
                create_id,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="updateId != null" >
                update_id,
            </if>
            <if test="updateTime != null" >
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="routeId != null" >
                #{routeId,jdbcType=VARCHAR},
            </if>
            <if test="bussCode != null" >
                #{bussCode,jdbcType=VARCHAR},
            </if>
            <if test="bussType != null" >
                #{bussType,jdbcType=VARCHAR},
            </if>
            <if test="merchantNo != null" >
                #{merchantNo,jdbcType=VARCHAR},
            </if>
            <if test="routeStatus != null" >
                #{routeStatus,jdbcType=VARCHAR},
            </if>
            <if test="remark != null" >
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createId != null" >
                #{createId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=VARCHAR},
            </if>
            <if test="updateId != null" >
                #{updateId,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null" >
                #{updateTime,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.cmpay.dceppay.entity.pay.PayMerchantRouteDO" >
        update pay_merchant_route
        <set >
            <if test="bussCode != null" >
                buss_code = #{bussCode,jdbcType=VARCHAR},
            </if>
            <if test="bussType != null" >
                buss_type = #{bussType,jdbcType=VARCHAR},
            </if>
            <if test="merchantNo != null" >
                merchant_no = #{merchantNo,jdbcType=VARCHAR},
            </if>
            <if test="routeStatus != null" >
                route_status = #{routeStatus,jdbcType=VARCHAR},
            </if>
            <if test="remark != null" >
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createId != null" >
                create_id = #{createId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=VARCHAR},
            </if>
            <if test="updateId != null" >
                update_id = #{updateId,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null" >
                update_time = #{updateTime,jdbcType=VARCHAR},
            </if>
        </set>
        where route_id = #{routeId,jdbcType=VARCHAR}
    </update>

    <select id="find" resultMap="BaseResultMap" parameterType="com.cmpay.dceppay.entity.pay.PayMerchantRouteDO" >
        select 
        <include refid="Base_Column_List" />
        from pay_merchant_route
        <where >
            <if test="routeId != null" >
                and route_id = #{routeId,jdbcType=VARCHAR}
            </if>
            <if test="bussCode != null" >
                and buss_code = #{bussCode,jdbcType=VARCHAR}
            </if>
            <if test="bussType != null" >
                and buss_type = #{bussType,jdbcType=VARCHAR}
            </if>
            <if test="merchantNo != null" >
                and merchant_no = #{merchantNo,jdbcType=VARCHAR}
            </if>
            <if test="routeStatus != null" >
                and route_status = #{routeStatus,jdbcType=VARCHAR}
            </if>
            <if test="remark != null" >
                and remark = #{remark,jdbcType=VARCHAR}
            </if>
            <if test="createId != null" >
                and create_id = #{createId,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null" >
                and create_time = #{createTime,jdbcType=VARCHAR}
            </if>
            <if test="updateId != null" >
                and update_id = #{updateId,jdbcType=VARCHAR}
            </if>
            <if test="updateTime != null" >
                and update_time = #{updateTime,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
</mapper>