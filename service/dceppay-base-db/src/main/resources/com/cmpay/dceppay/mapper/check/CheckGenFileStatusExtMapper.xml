<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.dceppay.dao.check.ICheckGenFileStatusExtDao">

    <resultMap id="BaseResultMap" type="com.cmpay.dceppay.entity.check.CheckGenFileStatusDO">
        <id column="check_file_date" property="checkFileDate" jdbcType="VARCHAR"/>
        <id column="channel_code" property="channelCode" jdbcType="VARCHAR"/>
        <result column="total_count" property="totalCount" jdbcType="INTEGER"/>
        <result column="total_amount" property="totalAmount" jdbcType="DECIMAL"/>
        <result column="payment_count" property="paymentCount" jdbcType="INTEGER"/>
        <result column="payment_amount" property="paymentAmount" jdbcType="DECIMAL"/>
        <result column="refund_count" property="refundCount" jdbcType="INTEGER"/>
        <result column="refund_amount" property="refundAmount" jdbcType="DECIMAL"/>
        <result column="file_start_time" property="fileStartTime" jdbcType="VARCHAR"/>
        <result column="file_end_time" property="fileEndTime" jdbcType="VARCHAR"/>
        <result column="file_send_time" property="fileSendTime" jdbcType="VARCHAR"/>
        <result column="file_status" property="fileStatus" jdbcType="VARCHAR"/>
        <result column="tm_smp" property="tmSmp" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        check_file_date
        , channel_code, total_count, total_amount, payment_count, payment_amount,
        refund_count, refund_amount, file_start_time, file_end_time, file_send_time, file_status, 
        tm_smp
    </sql>

    <update id="updateFileGenFinish" parameterType="com.cmpay.dceppay.entity.check.CheckGenFileStatusDO">
        update check_gen_file_status
        <set>
            total_count = #{totalCount,jdbcType=INTEGER},
            total_amount = #{totalAmount,jdbcType=DECIMAL},
            payment_count = #{paymentCount,jdbcType=INTEGER},
            payment_amount = #{paymentAmount,jdbcType=DECIMAL},
            refund_count = #{refundCount,jdbcType=INTEGER},
            refund_amount = #{refundAmount,jdbcType=DECIMAL},
            file_end_time = #{fileEndTime,jdbcType=VARCHAR},
            file_status = #{fileStatus,jdbcType=VARCHAR},
            tm_smp = #{tmSmp,jdbcType=VARCHAR}
        </set>
        where check_file_date = #{checkFileDate,jdbcType=VARCHAR}
        and channel_code = #{channelCode,jdbcType=VARCHAR}
    </update>

    <update id="updateFileSendFinish" parameterType="com.cmpay.dceppay.entity.check.CheckGenFileStatusDO">
        update check_gen_file_status
        <set>
            file_send_time = #{fileSendTime,jdbcType=VARCHAR},
            file_status = #{fileStatus,jdbcType=VARCHAR},
            tm_smp = #{tmSmp,jdbcType=VARCHAR}
        </set>
        where check_file_date = #{checkFileDate,jdbcType=VARCHAR}
        and channel_code = #{channelCode,jdbcType=VARCHAR}
    </update>

    <update id="updateFileSendFail" parameterType="com.cmpay.dceppay.entity.check.CheckGenFileStatusDO">
        update check_gen_file_status
        <set>
            file_send_time = #{fileSendTime,jdbcType=VARCHAR},
            file_status = #{fileStatus,jdbcType=VARCHAR},
            tm_smp = #{tmSmp,jdbcType=VARCHAR}
        </set>
        where check_file_date = #{checkFileDate,jdbcType=VARCHAR}
        and channel_code = #{channelCode,jdbcType=VARCHAR}
    </update>

</mapper>