<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.dceppay.dao.check.ICheckFileDetailDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.dceppay.entity.check.CheckFileDetailDO" >
        <id column="out_order_no" property="outOrderNo" jdbcType="VARCHAR" />
        <id column="refund_order_no" property="refundOrderNo" jdbcType="VARCHAR" />
        <result column="check_file_date" property="checkFileDate" jdbcType="VARCHAR" />
        <result column="order_complete_time" property="orderCompleteTime" jdbcType="VARCHAR" />
        <result column="message_number" property="messageNumber" jdbcType="VARCHAR" />
        <result column="message_identifier" property="messageIdentifier" jdbcType="VARCHAR" />
        <result column="accept_institution_code" property="acceptInstitutionCode" jdbcType="VARCHAR" />
        <result column="pay_institution_code" property="payInstitutionCode" jdbcType="VARCHAR" />
        <result column="receive_institution_code" property="receiveInstitutionCode" jdbcType="VARCHAR" />
        <result column="currency_code" property="currencyCode" jdbcType="VARCHAR" />
        <result column="order_amount" property="orderAmount" jdbcType="DECIMAL" />
        <result column="check_type" property="checkType" jdbcType="VARCHAR" />
        <result column="bank_order_no" property="bankOrderNo" jdbcType="VARCHAR" />
        <result column="bank_refund_no" property="bankRefundNo" jdbcType="VARCHAR" />
        <result column="order_status" property="orderStatus" jdbcType="VARCHAR" />
        <result column="trade_desc" property="tradeDesc" jdbcType="VARCHAR" />
        <result column="check_status" property="checkStatus" jdbcType="VARCHAR" />
        <result column="check_complete_time" property="checkCompleteTime" jdbcType="VARCHAR" />
        <result column="tm_smp" property="tmSmp" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        out_order_no, refund_order_no, check_file_date, order_complete_time, message_number, 
        message_identifier, accept_institution_code, pay_institution_code, receive_institution_code, 
        currency_code, order_amount, check_type, bank_order_no, bank_refund_no, order_status, 
        trade_desc, check_status, check_complete_time, tm_smp
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="com.cmpay.dceppay.entity.check.CheckFileDetailDOKey" >
        select 
        <include refid="Base_Column_List" />
        from check_file_detail
        where out_order_no = #{outOrderNo,jdbcType=VARCHAR}
          and refund_order_no = #{refundOrderNo,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="com.cmpay.dceppay.entity.check.CheckFileDetailDOKey" >
        delete from check_file_detail
        where out_order_no = #{outOrderNo,jdbcType=VARCHAR}
          and refund_order_no = #{refundOrderNo,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.cmpay.dceppay.entity.check.CheckFileDetailDO" >
        insert into check_file_detail
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="outOrderNo != null" >
                out_order_no,
            </if>
            <if test="refundOrderNo != null" >
                refund_order_no,
            </if>
            <if test="checkFileDate != null" >
                check_file_date,
            </if>
            <if test="orderCompleteTime != null" >
                order_complete_time,
            </if>
            <if test="messageNumber != null" >
                message_number,
            </if>
            <if test="messageIdentifier != null" >
                message_identifier,
            </if>
            <if test="acceptInstitutionCode != null" >
                accept_institution_code,
            </if>
            <if test="payInstitutionCode != null" >
                pay_institution_code,
            </if>
            <if test="receiveInstitutionCode != null" >
                receive_institution_code,
            </if>
            <if test="currencyCode != null" >
                currency_code,
            </if>
            <if test="orderAmount != null" >
                order_amount,
            </if>
            <if test="checkType != null" >
                check_type,
            </if>
            <if test="bankOrderNo != null" >
                bank_order_no,
            </if>
            <if test="bankRefundNo != null" >
                bank_refund_no,
            </if>
            <if test="orderStatus != null" >
                order_status,
            </if>
            <if test="tradeDesc != null" >
                trade_desc,
            </if>
            <if test="checkStatus != null" >
                check_status,
            </if>
            <if test="checkCompleteTime != null" >
                check_complete_time,
            </if>
            <if test="tmSmp != null" >
                tm_smp,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="outOrderNo != null" >
                #{outOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="refundOrderNo != null" >
                #{refundOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="checkFileDate != null" >
                #{checkFileDate,jdbcType=VARCHAR},
            </if>
            <if test="orderCompleteTime != null" >
                #{orderCompleteTime,jdbcType=VARCHAR},
            </if>
            <if test="messageNumber != null" >
                #{messageNumber,jdbcType=VARCHAR},
            </if>
            <if test="messageIdentifier != null" >
                #{messageIdentifier,jdbcType=VARCHAR},
            </if>
            <if test="acceptInstitutionCode != null" >
                #{acceptInstitutionCode,jdbcType=VARCHAR},
            </if>
            <if test="payInstitutionCode != null" >
                #{payInstitutionCode,jdbcType=VARCHAR},
            </if>
            <if test="receiveInstitutionCode != null" >
                #{receiveInstitutionCode,jdbcType=VARCHAR},
            </if>
            <if test="currencyCode != null" >
                #{currencyCode,jdbcType=VARCHAR},
            </if>
            <if test="orderAmount != null" >
                #{orderAmount,jdbcType=DECIMAL},
            </if>
            <if test="checkType != null" >
                #{checkType,jdbcType=VARCHAR},
            </if>
            <if test="bankOrderNo != null" >
                #{bankOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="bankRefundNo != null" >
                #{bankRefundNo,jdbcType=VARCHAR},
            </if>
            <if test="orderStatus != null" >
                #{orderStatus,jdbcType=VARCHAR},
            </if>
            <if test="tradeDesc != null" >
                #{tradeDesc,jdbcType=VARCHAR},
            </if>
            <if test="checkStatus != null" >
                #{checkStatus,jdbcType=VARCHAR},
            </if>
            <if test="checkCompleteTime != null" >
                #{checkCompleteTime,jdbcType=VARCHAR},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.cmpay.dceppay.entity.check.CheckFileDetailDO" >
        update check_file_detail
        <set >
            <if test="checkFileDate != null" >
                check_file_date = #{checkFileDate,jdbcType=VARCHAR},
            </if>
            <if test="orderCompleteTime != null" >
                order_complete_time = #{orderCompleteTime,jdbcType=VARCHAR},
            </if>
            <if test="messageNumber != null" >
                message_number = #{messageNumber,jdbcType=VARCHAR},
            </if>
            <if test="messageIdentifier != null" >
                message_identifier = #{messageIdentifier,jdbcType=VARCHAR},
            </if>
            <if test="acceptInstitutionCode != null" >
                accept_institution_code = #{acceptInstitutionCode,jdbcType=VARCHAR},
            </if>
            <if test="payInstitutionCode != null" >
                pay_institution_code = #{payInstitutionCode,jdbcType=VARCHAR},
            </if>
            <if test="receiveInstitutionCode != null" >
                receive_institution_code = #{receiveInstitutionCode,jdbcType=VARCHAR},
            </if>
            <if test="currencyCode != null" >
                currency_code = #{currencyCode,jdbcType=VARCHAR},
            </if>
            <if test="orderAmount != null" >
                order_amount = #{orderAmount,jdbcType=DECIMAL},
            </if>
            <if test="checkType != null" >
                check_type = #{checkType,jdbcType=VARCHAR},
            </if>
            <if test="bankOrderNo != null" >
                bank_order_no = #{bankOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="bankRefundNo != null" >
                bank_refund_no = #{bankRefundNo,jdbcType=VARCHAR},
            </if>
            <if test="orderStatus != null" >
                order_status = #{orderStatus,jdbcType=VARCHAR},
            </if>
            <if test="tradeDesc != null" >
                trade_desc = #{tradeDesc,jdbcType=VARCHAR},
            </if>
            <if test="checkStatus != null" >
                check_status = #{checkStatus,jdbcType=VARCHAR},
            </if>
            <if test="checkCompleteTime != null" >
                check_complete_time = #{checkCompleteTime,jdbcType=VARCHAR},
            </if>
            <if test="tmSmp != null" >
                tm_smp = #{tmSmp,jdbcType=VARCHAR},
            </if>
        </set>
        where out_order_no = #{outOrderNo,jdbcType=VARCHAR}
          and refund_order_no = #{refundOrderNo,jdbcType=VARCHAR}
    </update>

    <select id="find" resultMap="BaseResultMap" parameterType="com.cmpay.dceppay.entity.check.CheckFileDetailDO" >
        select 
        <include refid="Base_Column_List" />
        from check_file_detail
        <where >
            <if test="outOrderNo != null" >
                and out_order_no = #{outOrderNo,jdbcType=VARCHAR}
            </if>
            <if test="refundOrderNo != null" >
                and refund_order_no = #{refundOrderNo,jdbcType=VARCHAR}
            </if>
            <if test="checkFileDate != null" >
                and check_file_date = #{checkFileDate,jdbcType=VARCHAR}
            </if>
            <if test="orderCompleteTime != null" >
                and order_complete_time = #{orderCompleteTime,jdbcType=VARCHAR}
            </if>
            <if test="messageNumber != null" >
                and message_number = #{messageNumber,jdbcType=VARCHAR}
            </if>
            <if test="messageIdentifier != null" >
                and message_identifier = #{messageIdentifier,jdbcType=VARCHAR}
            </if>
            <if test="acceptInstitutionCode != null" >
                and accept_institution_code = #{acceptInstitutionCode,jdbcType=VARCHAR}
            </if>
            <if test="payInstitutionCode != null" >
                and pay_institution_code = #{payInstitutionCode,jdbcType=VARCHAR}
            </if>
            <if test="receiveInstitutionCode != null" >
                and receive_institution_code = #{receiveInstitutionCode,jdbcType=VARCHAR}
            </if>
            <if test="currencyCode != null" >
                and currency_code = #{currencyCode,jdbcType=VARCHAR}
            </if>
            <if test="orderAmount != null" >
                and order_amount = #{orderAmount,jdbcType=DECIMAL}
            </if>
            <if test="checkType != null" >
                and check_type = #{checkType,jdbcType=VARCHAR}
            </if>
            <if test="bankOrderNo != null" >
                and bank_order_no = #{bankOrderNo,jdbcType=VARCHAR}
            </if>
            <if test="bankRefundNo != null" >
                and bank_refund_no = #{bankRefundNo,jdbcType=VARCHAR}
            </if>
            <if test="orderStatus != null" >
                and order_status = #{orderStatus,jdbcType=VARCHAR}
            </if>
            <if test="tradeDesc != null" >
                and trade_desc = #{tradeDesc,jdbcType=VARCHAR}
            </if>
            <if test="checkStatus != null" >
                and check_status = #{checkStatus,jdbcType=VARCHAR}
            </if>
            <if test="checkCompleteTime != null" >
                and check_complete_time = #{checkCompleteTime,jdbcType=VARCHAR}
            </if>
            <if test="tmSmp != null" >
                and tm_smp = #{tmSmp,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
</mapper>