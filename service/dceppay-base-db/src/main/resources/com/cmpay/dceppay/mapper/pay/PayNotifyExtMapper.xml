<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.dceppay.dao.pay.IPayNotifyExtDao">


    <resultMap id="BaseResultMap" type="com.cmpay.dceppay.entity.pay.PayNotifyDO">
        <id column="out_order_no" property="outOrderNo" jdbcType="VARCHAR"/>
        <id column="refund_order_no" property="refundOrderNo" jdbcType="VARCHAR"/>
        <result column="notify_date" property="notifyDate" jdbcType="VARCHAR"/>
        <result column="notify_time" property="notifyTime" jdbcType="VARCHAR"/>
        <result column="trade_jrn_no" property="tradeJrnNo" jdbcType="VARCHAR"/>
        <result column="order_date" property="orderDate" jdbcType="VARCHAR"/>
        <result column="order_time" property="orderTime" jdbcType="VARCHAR"/>
        <result column="channel_code" property="channelCode" jdbcType="VARCHAR"/>
        <result column="pay_way" property="payWay" jdbcType="VARCHAR"/>
        <result column="scene" property="scene" jdbcType="VARCHAR"/>
        <result column="bus_type" property="busType" jdbcType="VARCHAR"/>
        <result column="account_date" property="accountDate" jdbcType="VARCHAR"/>
        <result column="order_amount" property="orderAmount" jdbcType="DECIMAL"/>
        <result column="notify_url" property="notifyUrl" jdbcType="VARCHAR"/>
        <result column="bank_order_no" property="bankOrderNo" jdbcType="VARCHAR"/>
        <result column="order_complete_time" property="orderCompleteTime" jdbcType="VARCHAR"/>
        <result column="notify_type" property="notifyType" jdbcType="VARCHAR"/>
        <result column="notify_status" property="notifyStatus" jdbcType="VARCHAR"/>
        <result column="err_msg_cd" property="errMsgCd" jdbcType="VARCHAR"/>
        <result column="err_msg_info" property="errMsgInfo" jdbcType="VARCHAR"/>
        <result column="notify_complete_time" property="notifyCompleteTime" jdbcType="VARCHAR"/>
        <result column="notify_count" property="notifyCount" jdbcType="INTEGER"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="tm_smp" property="tmSmp" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        out_order_no
        , refund_order_no, notify_date, notify_time, trade_jrn_no, order_date,
        order_time, channel_code, pay_way, scene, bus_type, account_date, order_amount, notify_url,
        bank_order_no, order_complete_time, notify_type, notify_status, err_msg_cd, err_msg_info,
        notify_complete_time, notify_count, remark, tm_smp
    </sql>

    <select id="queryWaitNotifyRecord" resultMap="BaseResultMap"
            parameterType="com.cmpay.dceppay.entity.pay.PayNotifyQueryDO">
        select
        <include refid="Base_Column_List"/>
        from pay_notify
        WHERE notify_date >= #{requestDate,jdbcType=VARCHAR}
        AND CONCAT(notify_date, notify_time) >= #{orderTimeBegin,jdbcType=VARCHAR}
        AND CONCAT(notify_date, notify_time) &lt;= #{orderTimeEnd,jdbcType=VARCHAR}
        AND notify_status ='FAIL'
        AND notify_count &lt; #{notifyCount,jdbcType=INTEGER}
        LIMIT #{total,jdbcType=INTEGER}
    </select>
    <update id="updateNotifyCount" parameterType="com.cmpay.dceppay.entity.pay.PayNotifyDO">
        update pay_notify
        set notify_count = notify_count + 1,
            tm_smp       = #{tmSmp,jdbcType=VARCHAR}
        where out_order_no = #{outOrderNo,jdbcType=VARCHAR}
          and refund_order_no = #{refundOrderNo,jdbcType=VARCHAR}
    </update>
    <update id="updateNotifySuccess" parameterType="com.cmpay.dceppay.entity.pay.PayNotifyDO">
        update pay_notify
        set notify_status        = #{notifyStatus,jdbcType=VARCHAR},
            notify_complete_time = #{notifyCompleteTime,jdbcType=VARCHAR},
            notify_count         = notify_count + 1,
            tm_smp               = #{tmSmp,jdbcType=VARCHAR}
        where out_order_no = #{outOrderNo,jdbcType=VARCHAR}
          and refund_order_no = #{refundOrderNo,jdbcType=VARCHAR}
    </update>

</mapper>