<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.dceppay.dao.check.ICheckErrorDetailExtDao">

    <resultMap id="BaseResultMap" type="com.cmpay.dceppay.entity.check.CheckErrorDetailDO" >
        <id column="out_order_no" property="outOrderNo" jdbcType="VARCHAR" />
        <id column="refund_order_no" property="refundOrderNo" jdbcType="VARCHAR" />
        <result column="check_file_date" property="checkFileDate" jdbcType="VARCHAR" />
        <result column="error_type" property="errorType" jdbcType="VARCHAR" />
        <result column="error_time" property="errorTime" jdbcType="VARCHAR" />
        <result column="error_handle_type" property="errorHandleType" jdbcType="VARCHAR" />
        <result column="error_process_time" property="errorProcessTime" jdbcType="VARCHAR" />
        <result column="trade_amount" property="tradeAmount" jdbcType="DECIMAL" />
        <result column="check_amount" property="checkAmount" jdbcType="DECIMAL" />
        <result column="error_status" property="errorStatus" jdbcType="VARCHAR" />
        <result column="trade_type" property="tradeType" jdbcType="VARCHAR" />
        <result column="merchant_no" property="merchantNo" jdbcType="VARCHAR" />
        <result column="org_merchant_no" property="orgMerchantNo" jdbcType="VARCHAR" />
        <result column="doubt_flag" property="doubtFlag" jdbcType="VARCHAR" />
        <result column="remark" property="remark" jdbcType="VARCHAR" />
        <result column="update_id" property="updateId" jdbcType="VARCHAR" />
        <result column="tm_smp" property="tmSmp" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        out_order_no, refund_order_no, check_file_date, error_type, error_time, error_handle_type,
        error_process_time, trade_amount, check_amount, error_status, trade_type, merchant_no,
        org_merchant_no, doubt_flag, remark, update_id, tm_smp
    </sql>


    <select id="findDoubleList" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from check_error
        where
        check_file_date = #{checkFileDate,jdbcType=VARCHAR}
        and error_type in ('PAYMENT_SHORT_DOUBT','REFUND_LONG_DOUBT')
    </select>


    <update id="updateDoubleComplete" parameterType="com.cmpay.dceppay.entity.check.CheckErrorDetailDO">
        update check_error
        <set>
            check_amount = #{checkAmount,jdbcType=DECIMAL},
            error_handle_type = #{errorHandleType,jdbcType=VARCHAR},
            error_process_time = #{errorProcessTime,jdbcType=VARCHAR},
            check_amount = #{checkAmount,jdbcType=DECIMAL},
            error_status = #{errorStatus,jdbcType=VARCHAR},
            tm_smp = #{tmSmp,jdbcType=VARCHAR}
        </set>
        where out_order_no = #{outOrderNo,jdbcType=VARCHAR}
        and refund_order_no = #{refundOrderNo,jdbcType=VARCHAR}
    </update>


    <update id="updateAmountError" parameterType="com.cmpay.dceppay.entity.check.CheckErrorDetailDO">
        update check_error
        <set>
            trade_amount = #{tradeAmount,jdbcType=DECIMAL},
            check_amount = #{checkAmount,jdbcType=DECIMAL},
            error_type = #{errorType,jdbcType=VARCHAR},
            tm_smp = #{tmSmp,jdbcType=VARCHAR}
        </set>
        where out_order_no = #{outOrderNo,jdbcType=VARCHAR}
        and refund_order_no = #{refundOrderNo,jdbcType=VARCHAR}
    </update>

    <update id="updatePaymentShort" parameterType="com.cmpay.dceppay.entity.check.CheckErrorDetailDO">
        update check_error
        <set>
            doubt_flag = #{doubtFlag,jdbcType=VARCHAR},
            error_type = #{errorType,jdbcType=VARCHAR},
            tm_smp = #{tmSmp,jdbcType=VARCHAR}
        </set>
        where out_order_no = #{outOrderNo,jdbcType=VARCHAR}
        and refund_order_no = #{refundOrderNo,jdbcType=VARCHAR}
    </update>

    <update id="updateRefundLong" parameterType="com.cmpay.dceppay.entity.check.CheckErrorDetailDO">
        update check_error
        <set>
            doubt_flag = #{doubtFlag,jdbcType=VARCHAR},
            error_type = #{errorType,jdbcType=VARCHAR},
            tm_smp = #{tmSmp,jdbcType=VARCHAR}
        </set>
        where out_order_no = #{outOrderNo,jdbcType=VARCHAR}
        and refund_order_no = #{refundOrderNo,jdbcType=VARCHAR}
    </update>


    <update id="cancelError" parameterType="com.cmpay.dceppay.entity.check.CheckErrorDetailDO">
        update check_error
        <set>
            error_handle_type = #{errorHandleType,jdbcType=VARCHAR},
            error_status = #{errorStatus,jdbcType=VARCHAR},
            error_process_time = #{errorProcessTime,jdbcType=VARCHAR},
            update_id = #{updateId,jdbcType=VARCHAR},
            tm_smp = #{tmSmp,jdbcType=VARCHAR}
        </set>
        where out_order_no = #{outOrderNo,jdbcType=VARCHAR}
        and refund_order_no = #{refundOrderNo,jdbcType=VARCHAR}
    </update>

    <update id="updateAmount" parameterType="com.cmpay.dceppay.entity.check.CheckErrorDetailDO">
        update check_error
        <set>
            error_handle_type = #{errorHandleType,jdbcType=VARCHAR},
            error_status = #{errorStatus,jdbcType=VARCHAR},
            error_process_time = #{errorProcessTime,jdbcType=VARCHAR},
            trade_amount = #{tradeAmount,jdbcType=DECIMAL},
            check_amount = #{checkAmount,jdbcType=DECIMAL},
            update_id = #{updateId,jdbcType=VARCHAR},
            tm_smp = #{tmSmp,jdbcType=VARCHAR}
        </set>
        where out_order_no = #{outOrderNo,jdbcType=VARCHAR}
        and refund_order_no = #{refundOrderNo,jdbcType=VARCHAR}
    </update>


    <update id="cancelRefund" parameterType="com.cmpay.dceppay.entity.check.CheckErrorDetailDO">
        update check_error
        <set>
            error_handle_type = #{errorHandleType,jdbcType=VARCHAR},
            error_status = #{errorStatus,jdbcType=VARCHAR},
            error_process_time = #{errorProcessTime,jdbcType=VARCHAR},
            update_id = #{updateId,jdbcType=VARCHAR},
            tm_smp = #{tmSmp,jdbcType=VARCHAR}
        </set>
        where out_order_no = #{outOrderNo,jdbcType=VARCHAR}
        and refund_order_no = #{refundOrderNo,jdbcType=VARCHAR}
    </update>
</mapper>