<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.dceppay.dao.bus.IBusMessageExtDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.dceppay.entity.bus.BusMessageDO" >
        <id column="message_id" property="messageId" jdbcType="VARCHAR" />
        <result column="message_type" property="messageType" jdbcType="VARCHAR" />
        <result column="message_date" property="messageDate" jdbcType="VARCHAR" />
        <result column="message_time" property="messageTime" jdbcType="VARCHAR" />
        <result column="sender" property="sender" jdbcType="VARCHAR" />
        <result column="receiver" property="receiver" jdbcType="VARCHAR" />
        <result column="message_content" property="messageContent" jdbcType="VARCHAR" />
        <result column="remark" property="remark" jdbcType="VARCHAR" />
        <result column="operate_id" property="operateId" jdbcType="VARCHAR" />
        <result column="tm_smp" property="tmSmp" jdbcType="VARCHAR" />
    </resultMap>
    <sql id="Base_Column_List" >
        message_id, message_type, message_date, message_time, sender, receiver, message_content,
        remark, operate_id, tm_smp
    </sql>

</mapper>