<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.dceppay.dao.mng.ITaskStatusDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.dceppay.entity.mng.TaskStatusDO" >
        <id column="task_date" property="taskDate" jdbcType="VARCHAR" />
        <id column="task_key" property="taskKey" jdbcType="VARCHAR" />
        <result column="task_desc" property="taskDesc" jdbcType="VARCHAR" />
        <result column="status" property="status" jdbcType="VARCHAR" />
        <result column="result" property="result" jdbcType="VARCHAR" />
        <result column="created_time" property="createdTime" jdbcType="VARCHAR" />
        <result column="updated_time" property="updatedTime" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        task_date, task_key, task_desc, status, result, created_time, updated_time
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="com.cmpay.dceppay.entity.mng.TaskStatusDOKey" >
        select 
        <include refid="Base_Column_List" />
        from task_status
        where task_date = #{taskDate,jdbcType=VARCHAR}
          and task_key = #{taskKey,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="com.cmpay.dceppay.entity.mng.TaskStatusDOKey" >
        delete from task_status
        where task_date = #{taskDate,jdbcType=VARCHAR}
          and task_key = #{taskKey,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.cmpay.dceppay.entity.mng.TaskStatusDO" >
        insert into task_status
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="taskDate != null" >
                task_date,
            </if>
            <if test="taskKey != null" >
                task_key,
            </if>
            <if test="taskDesc != null" >
                task_desc,
            </if>
            <if test="status != null" >
                status,
            </if>
            <if test="result != null" >
                result,
            </if>
            <if test="createdTime != null" >
                created_time,
            </if>
            <if test="updatedTime != null" >
                updated_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="taskDate != null" >
                #{taskDate,jdbcType=VARCHAR},
            </if>
            <if test="taskKey != null" >
                #{taskKey,jdbcType=VARCHAR},
            </if>
            <if test="taskDesc != null" >
                #{taskDesc,jdbcType=VARCHAR},
            </if>
            <if test="status != null" >
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="result != null" >
                #{result,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null" >
                #{createdTime,jdbcType=VARCHAR},
            </if>
            <if test="updatedTime != null" >
                #{updatedTime,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.cmpay.dceppay.entity.mng.TaskStatusDO" >
        update task_status
        <set >
            <if test="taskDesc != null" >
                task_desc = #{taskDesc,jdbcType=VARCHAR},
            </if>
            <if test="status != null" >
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="result != null" >
                result = #{result,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null" >
                created_time = #{createdTime,jdbcType=VARCHAR},
            </if>
            <if test="updatedTime != null" >
                updated_time = #{updatedTime,jdbcType=VARCHAR},
            </if>
        </set>
        where task_date = #{taskDate,jdbcType=VARCHAR}
          and task_key = #{taskKey,jdbcType=VARCHAR}
    </update>

    <select id="find" resultMap="BaseResultMap" parameterType="com.cmpay.dceppay.entity.mng.TaskStatusDO" >
        select 
        <include refid="Base_Column_List" />
        from task_status
        <where >
            <if test="taskDate != null" >
                and task_date = #{taskDate,jdbcType=VARCHAR}
            </if>
            <if test="taskKey != null" >
                and task_key = #{taskKey,jdbcType=VARCHAR}
            </if>
            <if test="taskDesc != null" >
                and task_desc = #{taskDesc,jdbcType=VARCHAR}
            </if>
            <if test="status != null" >
                and status = #{status,jdbcType=VARCHAR}
            </if>
            <if test="result != null" >
                and result = #{result,jdbcType=VARCHAR}
            </if>
            <if test="createdTime != null" >
                and created_time = #{createdTime,jdbcType=VARCHAR}
            </if>
            <if test="updatedTime != null" >
                and updated_time = #{updatedTime,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
</mapper>