<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmpay.dceppay.dao.mng.IDcepLineManagementDao">
    
    <resultMap id="BaseResultMap" type="com.cmpay.dceppay.entity.mng.DcepLineManagementDO">
        <id column="id" jdbcType="VARCHAR" property="id" />
        <result column="line_mapping_address" jdbcType="VARCHAR" property="lineMappingAddress" />
        <result column="line_name" jdbcType="VARCHAR" property="lineName" />
        <result column="institution_line_address" jdbcType="VARCHAR" property="institutionLineAddress" />
        <result column="pboc_line_address" jdbcType="VARCHAR" property="pbocLineAddress" />
        <result column="pboc_idc_code" jdbcType="VARCHAR" property="pbocIdcCode" />
        <result column="pboc_real_address" jdbcType="VARCHAR" property="pbocRealAddress" />
        <result column="is_default" jdbcType="CHAR" property="isDefault" />
        <result column="business_type" jdbcType="VARCHAR" property="businessType" />
        <result column="health_check_enabled" jdbcType="CHAR" property="healthCheckEnabled" />
        <result column="network_status" jdbcType="VARCHAR" property="networkStatus" />
        <result column="service_status" jdbcType="VARCHAR" property="serviceStatus" />
        <result column="probe_result" jdbcType="VARCHAR" property="probeResult" />
        <result column="last_health_check_time" jdbcType="TIMESTAMP" property="lastHealthCheckTime" />
        <result column="health_check_fail_start_time" jdbcType="TIMESTAMP" property="healthCheckFailStartTime" />
        <result column="consecutive_fail_count" jdbcType="INTEGER" property="consecutiveFailCount" />
        <result column="response_time_avg" jdbcType="BIGINT" property="responseTimeAvg" />
        <result column="status" jdbcType="CHAR" property="status" />
        <result column="remark" jdbcType="VARCHAR" property="remark" />
        <result column="create_user_no" jdbcType="VARCHAR" property="createUserNo" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_user_no" jdbcType="VARCHAR" property="updateUserNo" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
        id, line_mapping_address, line_name, institution_line_address, pboc_line_address,
        pboc_idc_code, pboc_real_address, is_default, business_type, health_check_enabled,
        network_status, service_status, probe_result, last_health_check_time, 
        health_check_fail_start_time, consecutive_fail_count, response_time_avg, status,
        remark, create_user_no, create_time, update_user_no, update_time
    </sql>

    <select id="selectEnabledHealthCheckLines" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM dcep_line_management
        WHERE health_check_enabled = 'Y' AND status = 'A'
        ORDER BY business_type, response_time_avg ASC
    </select>

    <select id="selectDefaultLineByBusinessType" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM dcep_line_management
        WHERE business_type = #{businessType} AND is_default = 'Y' AND status = 'A'
        LIMIT 1
    </select>

    <select id="selectFastestLineByBusinessType" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM dcep_line_management
        WHERE business_type = #{businessType} AND status = 'A' AND network_status = 'NORMAL'
        ORDER BY response_time_avg ASC
        LIMIT 1
    </select>

    <update id="updateNetworkStatus">
        UPDATE dcep_line_management
        SET network_status = #{networkStatus},
            consecutive_fail_count = #{consecutiveFailCount},
            health_check_fail_start_time = #{healthCheckFailStartTime},
            response_time_avg = #{responseTimeAvg},
            last_health_check_time = NOW(),
            update_time = NOW()
        WHERE id = #{id}
    </update>

</mapper>
