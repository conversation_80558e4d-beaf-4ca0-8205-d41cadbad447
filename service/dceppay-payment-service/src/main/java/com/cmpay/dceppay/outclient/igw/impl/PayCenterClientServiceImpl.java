package com.cmpay.dceppay.outclient.igw.impl;

import com.cmpay.cashier.dto.PaymentReqDTO;
import com.cmpay.cashier.dto.PaymentRspDTO;
import com.cmpay.dceppay.bo.notify.PaymentSendNotifyBO;
import com.cmpay.dceppay.bo.notify.PaymentSendNotifyResultBO;
import com.cmpay.dceppay.outclient.igw.IPayCenterClientService;
import com.cmpay.dceppay.utils.ExceptionHandlerUtil;
import com.cmpay.igw.cashier.client.CashierGwClient;
import com.cmpay.lemon.common.utils.JudgeUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/9/27 16:04
 */
@Service
@Slf4j
public class PayCenterClientServiceImpl implements IPayCenterClientService {
    @Autowired
    private CashierGwClient cashierGwClient;

    @Override
    public PaymentSendNotifyResultBO paymentNotify(PaymentSendNotifyBO paymentSendNotifyBO) {
        PaymentReqDTO paymentReqDTO = new PaymentReqDTO();
        paymentReqDTO.setPaymentCreateDate(paymentSendNotifyBO.getOrderDate());
        paymentReqDTO.setOrderNo(paymentSendNotifyBO.getOutOrderNo());
        paymentReqDTO.setSupplyAmount(paymentSendNotifyBO.getTotalAmount());
        paymentReqDTO.setDigitalNo(paymentSendNotifyBO.getBankOrderNo());
        paymentReqDTO.setDigitalJournalNo(paymentSendNotifyBO.getTradeJrnNo());
        PaymentSendNotifyResultBO notifyResultBO = new PaymentSendNotifyResultBO();
        try {
            PaymentRspDTO paymentRspDTO = cashierGwClient.receiveCyberPaymentNotification(paymentReqDTO);
            if (JudgeUtils.isNotSuccess(paymentRspDTO)) {
                if (paymentRspDTO.getNotifyDealFlag()) {
                    notifyResultBO.setNotifySuccess(true);
                } else {
                    notifyResultBO.setNotifySuccess(false);
                    log.error("通知支付中台失败：{}", paymentRspDTO.getMsgCd() + paymentRspDTO.getMsgInfo());
                }
            } else {
                notifyResultBO.setNotifySuccess(true);
            }
        } catch (Exception e) {
            log.error("通知支付中台失败：{}" + ExceptionHandlerUtil.getExceptionMsg(e));
            notifyResultBO.setNotifySuccess(false);
        }
        return notifyResultBO;
    }
}
