package com.cmpay.dceppay.service.account.ext.impl;

import com.cmpay.dceppay.bo.account.AccountTreatmentBO;
import com.cmpay.dceppay.bo.pay.PayAccountBO;
import com.cmpay.dceppay.bo.pay.PayAccountDetailBO;
import com.cmpay.dceppay.service.account.ext.IPayAccountDataService;
import com.cmpay.dceppay.util.BeanConvertUtils;
import org.springframework.stereotype.Service;

import static com.cmpay.account.enums.ApplicationIdEnum.DCEP;

/**
 * <AUTHOR>
 * @date 2024/9/17 9:26
 */
@Service
public class PayAccountDataServiceImpl implements IPayAccountDataService {
    @Override
    public PayAccountBO getPayAccountBO(AccountTreatmentBO accountTreatmentBO) {
        PayAccountBO accountBO = new PayAccountBO();
        accountBO.setOrderNo(accountTreatmentBO.getOrderNo());
        accountBO.setOrderDate(accountTreatmentBO.getTradeDate());
        accountBO.setOrderType(accountTreatmentBO.getOrderType());
        accountBO.setBusType(accountTreatmentBO.getBusType());
        accountBO.setBusChannel(accountTreatmentBO.getBusChannel());
        accountBO.setTradeType(accountTreatmentBO.getTradeType());
        accountBO.setBusCode(accountTreatmentBO.getTradeCode());
        accountBO.setBusJrnNo(accountTreatmentBO.getBusinessJrnNo());
        accountBO.setAccountFrom(DCEP.getCode());
        accountBO.setOrderAmount(accountTreatmentBO.getOrderAmount());
        accountBO.setPayAccountDetailBOList(BeanConvertUtils.convertList(accountTreatmentBO.getAccountDetailBOS(), PayAccountDetailBO.class));
        return accountBO;
    }
}
