package com.cmpay.dceppay.service.mng.impl;

import com.cmpay.channel.data.Request;
import com.cmpay.channel.data.Response;
import com.cmpay.dceppay.bo.mng.DcepLoginBO;
import com.cmpay.dceppay.client.DceppayCgwOutClient;
import com.cmpay.dceppay.dto.dcep.login.LoginRsp;
import com.cmpay.dceppay.dto.dcep.login.request.LoginSoapEnvelopRequest;
import com.cmpay.dceppay.enums.dcep.DcepLoginTypeEnum;
import com.cmpay.dceppay.service.mng.IDcepLoginService;
import com.cmpay.dceppay.service.mng.ext.ILoginExtService;
import com.cmpay.framework.data.request.GenericDTO;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/11/1 9:51
 */
@Service
public class DcepLoginServiceImpl implements IDcepLoginService {
    @Autowired
    private ILoginExtService loginExtService;
    @Autowired
    private DceppayCgwOutClient dceppayCgwOutClient;

    @Override
    public void login(DcepLoginBO dcepLoginBO) {
        LoginSoapEnvelopRequest loginSoapEnvelopRequest = loginExtService.bulidLoginParam(DcepLoginTypeEnum.OT00.name());
        Request request = loginExtService.bulidLoginRequest(loginSoapEnvelopRequest);
        //请求网关
        GenericRspDTO<Response> genericRspDTO = dceppayCgwOutClient.request(GenericDTO.newChildInstanceWithBody(GenericDTO.class, request));
        //处理网关响应对象
        if (JudgeUtils.isNotSuccess(genericRspDTO)) {
            BusinessException.throwBusinessException(genericRspDTO);
        }
        LoginRsp loginRsp = Optional.of(genericRspDTO)
                .map(GenericRspDTO::getBody)
                .map(Response::getResult).map(x -> (LoginRsp) x)
                .orElse(new LoginRsp());
        loginExtService.handleLoginInResponse(dcepLoginBO, loginRsp);
    }

    @Override
    public void loginOut(DcepLoginBO dcepLoginBO) {
        LoginSoapEnvelopRequest loginSoapEnvelopRequest = loginExtService.bulidLoginParam(DcepLoginTypeEnum.OT01.name());
        Request request = loginExtService.bulidLoginRequest(loginSoapEnvelopRequest);
        //请求网关
        GenericRspDTO<Response> genericRspDTO = dceppayCgwOutClient.request(GenericDTO.newChildInstanceWithBody(GenericDTO.class, request));
        //处理网关响应对象
        if (JudgeUtils.isNotSuccess(genericRspDTO)) {
            BusinessException.throwBusinessException(genericRspDTO);
        }
        LoginRsp loginRsp = Optional.of(genericRspDTO)
                .map(GenericRspDTO::getBody)
                .map(Response::getResult).map(x -> (LoginRsp) x)
                .orElse(new LoginRsp());
        loginExtService.handleLoginOutResponse(dcepLoginBO, loginRsp);
    }
}
