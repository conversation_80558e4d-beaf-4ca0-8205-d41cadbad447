package com.cmpay.dceppay.service.channel.ext.impl;

import com.cmpay.channel.data.Request;
import com.cmpay.dceppay.bo.closeorder.CloseOrderBO;
import com.cmpay.dceppay.bo.closeorder.CloseOrderRspBO;
import com.cmpay.dceppay.bo.pay.PayOrderCloseUpdateBO;
import com.cmpay.dceppay.bo.pay.PayOrderDBBO;
import com.cmpay.dceppay.channel.DcepChannelEnum;
import com.cmpay.dceppay.config.DceppayConfig;
import com.cmpay.dceppay.constant.dcep.DcepResponseStatusConstant;
import com.cmpay.dceppay.dto.dcep.closeorder.CloseOrderRsp;
import com.cmpay.dceppay.dto.dcep.closeorder.request.CloseOrderBody;
import com.cmpay.dceppay.dto.dcep.closeorder.request.CloseOrderOriginalGroupHeader;
import com.cmpay.dceppay.dto.dcep.closeorder.request.CloseOrderSoapEnvelopRequest;
import com.cmpay.dceppay.dto.dcep.closeorder.response.CloseOrderResponseInformation;
import com.cmpay.dceppay.dto.dcep.closeorder.response.CloseOrderSoapEnvelopResponse;
import com.cmpay.dceppay.dto.dcep.faut.Fault;
import com.cmpay.dceppay.dto.dcep.faut.FaultSoapEnvelopResponse;
import com.cmpay.dceppay.dto.dcep.common.RequestGroupHeader;
import com.cmpay.dceppay.dto.dcep.common.SoapHeader;
import com.cmpay.dceppay.enums.MsgCodeEnum;
import com.cmpay.dceppay.enums.dcep.DcepMessageTypeEnum;
import com.cmpay.dceppay.enums.pay.OrderStatusEnum;
import com.cmpay.dceppay.service.channel.ext.ICloseOrderExtService;
import com.cmpay.dceppay.service.ext.DceppayParamService;
import com.cmpay.dceppay.service.pay.IPayOrderDBService;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.utils.LemonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/9/5 15:43
 */
@Service
@Slf4j
public class CloseOrderExtServiceImpl implements ICloseOrderExtService {

    @Autowired
    private IPayOrderDBService payOrderDBService;
    @Autowired
    private DceppayParamService dceppayParamService;
    @Autowired
    private DceppayConfig dceppayConfig;

    @Override
    public CloseOrderSoapEnvelopRequest buildOrderCloseRequestParam(PayOrderDBBO payOrder, CloseOrderBO closeOrderBO) {
        CloseOrderSoapEnvelopRequest closeOrderSoapEnvelopRequest = new CloseOrderSoapEnvelopRequest();
        String walletId = payOrder.getWalletId();
        if (JudgeUtils.isBlank(walletId)) {
            walletId = dceppayConfig.getWalletId();
        }
        //报文头
        String messageIdentification = dceppayParamService.getMessageIdentification(DcepMessageTypeEnum.ORDER_CLOSE_REQUEST.getValue(), walletId);
        SoapHeader soapHeader = dceppayParamService.getCommonSoapHear(DcepMessageTypeEnum.ORDER_CLOSE_REQUEST.getMessageType(), messageIdentification);
        BeanUtils.copyProperties(closeOrderSoapEnvelopRequest, soapHeader);
        //业务头
        CloseOrderBody closeOrderBody = new CloseOrderBody();
        RequestGroupHeader requestGroupHeader = dceppayParamService.getRequestGroupHeader(DcepMessageTypeEnum.ORDER_CLOSE_REQUEST.getMessageType(),messageIdentification);
        closeOrderBody.setRequestGroupHeader(requestGroupHeader);
        //原交易组件
        CloseOrderOriginalGroupHeader originalGroupHeader = new CloseOrderOriginalGroupHeader();
        originalGroupHeader.setOriginalMessageIdentification(payOrder.getMessageIdentification());
        originalGroupHeader.setOriginalMessageType(DcepMessageTypeEnum.UNIFIED_ORDER_REQUEST.getMessageType());
        originalGroupHeader.setOriginalInstructingParty(dceppayConfig.getSenderCode());
        originalGroupHeader.setOriginalMerchantNo(payOrder.getOrgMerchantNo());
        originalGroupHeader.setOriginalOutOrderNo(payOrder.getOutOrderNo());
        originalGroupHeader.setOriginalOrderNo(payOrder.getBankOrderNo());
        originalGroupHeader.setOriginalAmount(payOrder.getOrderAmount());
        closeOrderBody.setOriginalGroupHeader(originalGroupHeader);

        closeOrderSoapEnvelopRequest.setCloseOrderBody(closeOrderBody);
        return closeOrderSoapEnvelopRequest;
    }


    @Override
    public Request bulidRefundRequest(CloseOrderSoapEnvelopRequest closeOrderSoapEnvelopRequest) {
        Request request = new Request();
        request.setRequestId(LemonUtils.getRequestId());
        request.setRoute(DcepChannelEnum.DCEP_PAY);
        request.setBusiType(DcepChannelEnum.ORDER_CLOSE.getName());
        request.setSource(DcepChannelEnum.DCEP_PAY);
        request.setTarget(closeOrderSoapEnvelopRequest);
        return request;
    }

    @Override
    public CloseOrderRspBO handleResponse(CloseOrderBO closeOrderBO, CloseOrderRsp closeOrderRsp) {
        if (JudgeUtils.isNull(closeOrderRsp)
                || (JudgeUtils.isNull(closeOrderRsp.getCloseOrderSoapEnvelopResponse())
                && JudgeUtils.isNull(closeOrderRsp.getFaultSoapEnvelopResponse()))
        ) {
            log.error("请求互联互通关闭订单响应为空：{}", closeOrderRsp);
            BusinessException.throwBusinessException(MsgCodeEnum.CLOSE_ORDER_RESPONSE_NULL);
        }
        CloseOrderRspBO closeOrderRspBO = new CloseOrderRspBO();
        closeOrderRspBO.setOutOrderNo(closeOrderBO.getOutOrderNo());
        FaultSoapEnvelopResponse faultSoapEnvelopResponse = closeOrderRsp.getFaultSoapEnvelopResponse();
        if (JudgeUtils.isNotNull(faultSoapEnvelopResponse) && JudgeUtils.isNotNull(faultSoapEnvelopResponse.getFault())) {
            Fault fault = faultSoapEnvelopResponse.getFault();
            String errorCode = fault.getFaultCode();
            String errorMessage = fault.getFaultString() + (JudgeUtils.isNotBlank(fault.getDetail()) ? fault.getDetail() : "");
            handleFaultResponse(errorCode, errorMessage, closeOrderRspBO);
        } else {
            CloseOrderSoapEnvelopResponse closeOrderSoapEnvelopResponse = closeOrderRsp.getCloseOrderSoapEnvelopResponse();
            CloseOrderResponseInformation responseInformation = closeOrderSoapEnvelopResponse.getSoapBody().getResponseInformation();
            String responseStatus = responseInformation.getResponseStatus();
            if (JudgeUtils.equals(responseStatus, DcepResponseStatusConstant.CLOSE_ORDER_SUCCESS)) {
                handleSuccessResponse(closeOrderRspBO);
            } else {
                handleFailureResponse(responseInformation, closeOrderRspBO);
            }
        }
        return closeOrderRspBO;
    }

    private void handleSuccessResponse(CloseOrderRspBO closeOrderRspBO) {
        closeOrderRspBO.setStatus(OrderStatusEnum.TRADE_CLOSED.name());
        PayOrderCloseUpdateBO closeUpdateBO = new PayOrderCloseUpdateBO();
        closeUpdateBO.setOutOrderNo(closeOrderRspBO.getOutOrderNo());
        try {
            int updateCount = payOrderDBService.updateTradeClose(closeUpdateBO);
            if (updateCount != 1) {
                log.error("请求互联互通关闭订单成功，更新订单失败，更新记录数：{}", updateCount);
            }
        } catch (Exception e) {
            log.error("请求互联互通关闭订单成功，更新订单失败：{}", e.getCause());
        }
    }

    private void handleFailureResponse(CloseOrderResponseInformation responseInformation, CloseOrderRspBO closeOrderRspBO) {
        log.error("请求互联互通关闭订单失败，返回信息：{}", responseInformation);
        String errorCode = responseInformation.getRejectCode();
        String errorMessage = responseInformation.getRejectInformation();
        closeOrderRspBO.setStatus(OrderStatusEnum.CLOSE_FAIL.name());
        closeOrderRspBO.setErrMsgCd(errorCode);
        closeOrderRspBO.setErrMsgInfo(errorMessage);
        BusinessException.throwBusinessException(MsgCodeEnum.CLOSE_ORDER_RESPONSE_FAIL);
    }

    private void handleFaultResponse(String errorCode, String errorMessage, CloseOrderRspBO closeOrderRspBO) {
        closeOrderRspBO.setStatus(OrderStatusEnum.CLOSE_FAIL.name());
        closeOrderRspBO.setErrMsgCd(errorCode);
        closeOrderRspBO.setErrMsgInfo(errorMessage);
        BusinessException.throwBusinessException(MsgCodeEnum.CLOSE_ORDER_RESPONSE_FAULT);
    }
}
