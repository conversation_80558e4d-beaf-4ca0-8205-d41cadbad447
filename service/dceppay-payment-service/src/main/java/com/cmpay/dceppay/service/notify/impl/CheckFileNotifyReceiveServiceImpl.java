package com.cmpay.dceppay.service.notify.impl;

import com.cmpay.dceppay.bo.notify.CheckFileNotifyBO;
import com.cmpay.dceppay.bo.notify.CheckFileNotifyRspBO;
import com.cmpay.dceppay.bo.check.CheckFileSummaryBO;
import com.cmpay.dceppay.constant.common.SeparatorConstants;
import com.cmpay.dceppay.constant.dcep.DcepResponseStatusConstant;
import com.cmpay.dceppay.enums.MsgCodeEnum;
import com.cmpay.dceppay.service.check.ext.ICheckFileSummaryService;
import com.cmpay.dceppay.service.notify.ICheckFileNotifyReceiveService;
import com.cmpay.dceppay.util.DateTimeUtil;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/9/23 9:03
 */
@Service
@Slf4j
public class CheckFileNotifyReceiveServiceImpl implements ICheckFileNotifyReceiveService {
    @Autowired
    private ICheckFileSummaryService checkFileSummaryService;


    @Override
    public CheckFileNotifyRspBO checkFileNotify(CheckFileNotifyBO checkFileNotifyBO) {
        CheckFileNotifyRspBO checkFileNotifyRspBO = new CheckFileNotifyRspBO();
        BeanUtils.copyProperties(checkFileNotifyRspBO, checkFileNotifyBO);
        try {
            CheckFileSummaryBO checkFileSummaryBO = getCheckFileSummaryBO(checkFileNotifyBO);
            checkFileSummaryService.registerSummaryInfo(checkFileSummaryBO);
        } catch (Exception e) {
            log.error("接收对账汇总核对通知处理失败：{}", e.getCause());
            BusinessException.throwBusinessException(MsgCodeEnum.CHECK_FILE_NOTIFY_HANDLE_ERROR);
        }
        checkFileNotifyRspBO.setProcessStatus(DcepResponseStatusConstant.NOTIFY_HANDLE_SUCCESS);
        return checkFileNotifyRspBO;
    }

    private CheckFileSummaryBO getCheckFileSummaryBO(CheckFileNotifyBO checkFileNotifyBO) {
        CheckFileSummaryBO checkFileSummaryBO = new CheckFileSummaryBO();
        if (JudgeUtils.isNotNull(checkFileNotifyBO.getCheckDate())) {
            checkFileSummaryBO.setCheckFileDate(checkFileNotifyBO.getCheckDate().replace(SeparatorConstants.BETWEEN_LINE, SeparatorConstants.NULL_STRING));
        }
        checkFileSummaryBO.setFileCount(checkFileNotifyBO.getFileInfoNumber());
        checkFileSummaryBO.setFilePath(checkFileNotifyBO.getFilePath());
        checkFileSummaryBO.setFileNameList(checkFileNotifyBO.getFileName());
        checkFileSummaryBO.setTotalAmount(checkFileNotifyBO.getCountAmount());
        checkFileSummaryBO.setTotalCount(checkFileNotifyBO.getCountNumber());
        checkFileSummaryBO.setPaymentAmount(checkFileNotifyBO.getPaymentCountAmount());
        checkFileSummaryBO.setPaymentCount(checkFileNotifyBO.getPaymentCountNumber());
        checkFileSummaryBO.setRefundCount(checkFileNotifyBO.getRefundCountNumber());
        checkFileSummaryBO.setRefundAmount(checkFileNotifyBO.getRefundCountAmount());
        checkFileSummaryBO.setInstitutionCode(checkFileNotifyBO.getOriginalInstructingParty());
        checkFileSummaryBO.setDigitalEnv(checkFileNotifyBO.getDigitalEnv());
        return checkFileSummaryBO;
    }
}
