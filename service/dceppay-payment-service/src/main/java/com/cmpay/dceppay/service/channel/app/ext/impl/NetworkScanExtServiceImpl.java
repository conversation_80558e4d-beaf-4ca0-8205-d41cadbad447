package com.cmpay.dceppay.service.channel.app.ext.impl;

import com.cmpay.channel.data.Request;
import com.cmpay.dceppay.bo.mng.BusParamBO;
import com.cmpay.dceppay.channel.DcepChannelEnum;
import com.cmpay.dceppay.config.DceppayConfig;
import com.cmpay.dceppay.constant.mng.BusParamTypeConstants;
import com.cmpay.dceppay.dto.dcep.common.SoapHeader;
import com.cmpay.dceppay.dto.dcep.faut.Fault;
import com.cmpay.dceppay.dto.dcep.faut.FaultSoapEnvelopResponse;
import com.cmpay.dceppay.dto.dcep.networkscan.NetWorkScanRsp;
import com.cmpay.dceppay.dto.dcep.networkscan.request.NetworkScanBody;
import com.cmpay.dceppay.dto.dcep.networkscan.request.NetworkScanRequest;
import com.cmpay.dceppay.dto.dcep.networkscan.request.ProbeInformation;
import com.cmpay.dceppay.dto.dcep.networkscan.response.CityZone;
import com.cmpay.dceppay.dto.dcep.networkscan.response.DCEPProcessResponse;
import com.cmpay.dceppay.dto.dcep.networkscan.response.GlobalZone;
import com.cmpay.dceppay.dto.dcep.networkscan.response.NetworkScanResponse;
import com.cmpay.dceppay.enums.dcep.DcepMessageTypeEnum;
import com.cmpay.dceppay.service.cache.IdcInformationCacheService;
import com.cmpay.dceppay.service.channel.app.ext.INetworkScanExtService;
import com.cmpay.dceppay.service.ext.DceppayParamService;
import com.cmpay.dceppay.service.mng.IIDCParamService;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.utils.LemonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/10/8 14:38
 */
@Service
@Slf4j
public class NetworkScanExtServiceImpl implements INetworkScanExtService {
    @Autowired
    private DceppayConfig dceppayConfig;
    @Autowired
    private DceppayParamService dceppayParamService;
    @Autowired
    private IIDCParamService busParamService;
    @Autowired
    private IdcInformationCacheService idcInformationCacheService;

    @Override
    public NetworkScanRequest buildNetworkScanRequestParam() {
        NetworkScanRequest networkScanRequest = new NetworkScanRequest();
        //报文头
        String messageIdentification = dceppayParamService.getMessageIdentification(DcepMessageTypeEnum.DETECTION_REQUEST.getValue(), dceppayConfig.getWalletId());
        SoapHeader soapHeader = dceppayParamService.getCommonSoapHear(DcepMessageTypeEnum.DETECTION_REQUEST.getMessageType(), messageIdentification);
        BeanUtils.copyProperties(networkScanRequest, soapHeader);
        NetworkScanBody networkScanBody = new NetworkScanBody();
        ProbeInformation probeInformation = new ProbeInformation();
        probeInformation.setInstructingDirectParty(dceppayConfig.getSenderCode());
        networkScanBody.setProbeInformation(probeInformation);
        networkScanRequest.setSoapBody(networkScanBody);
        return networkScanRequest;
    }

    @Override
    public Request bulidNetworkScanRequest(NetworkScanRequest networkScanRequest) {
        Request request = new Request();
        request.setRequestId(LemonUtils.getRequestId());
        request.setRoute(DcepChannelEnum.DCEP_PAY);
        request.setBusiType(DcepChannelEnum.NETWORK_SCAN.getName());
        request.setSource(DcepChannelEnum.DCEP_PAY);
        request.setTarget(networkScanRequest);
        return request;
    }

    @Override
    public void handleResponse(NetWorkScanRsp networkScanRsp) {
        FaultSoapEnvelopResponse faultSoapEnvelopResponse = networkScanRsp.getFaultSoapEnvelopResponse();
        if (JudgeUtils.isNotNull(faultSoapEnvelopResponse) && JudgeUtils.isNotNull(faultSoapEnvelopResponse.getFault())) {
            Fault fault = faultSoapEnvelopResponse.getFault();
            String errorCode = fault.getFaultCode();
            String errorMessage = fault.getFaultString() + (JudgeUtils.isNotBlank(fault.getDetail()) ? fault.getDetail() : "");
            log.error("networkScanResponseError:{}", errorCode + errorMessage);
            return;
        }
        NetworkScanResponse networkScanResponse = networkScanRsp.getNetworkScanResponse();
        DCEPProcessResponse processResponse = networkScanResponse.getProcessResponse();
        if (JudgeUtils.isNull(networkScanResponse.getProcessResponse())) {
            log.warn("networkScan response is null:{}", processResponse);
            return;
        }
        CityZone cityZone = processResponse.getCityZone();
        if (JudgeUtils.isNotNull(cityZone)) {
            String idcInformation = cityZone.getIdcInformation();
            String firstIdc = idcInformation.split("|")[0];
            if (JudgeUtils.isNotNull(firstIdc)) {
                //缓存
                BusParamBO busParamBO = busParamService.getIdcInfoByKey(BusParamTypeConstants.IDC_CZONE, idcInformation);
                if (JudgeUtils.isNull(busParamBO)) {
                    return;
                }
                String paramCode = busParamBO.getParamCode();
                idcInformationCacheService.addCityZoneIdcInformationCache(paramCode);
            }
        }
        GlobalZone globalZone = processResponse.getGlobalZone();
        if (JudgeUtils.isNotNull(globalZone)) {
            String idcInformation = globalZone.getIdcInformation();
            String firstIdc = idcInformation.split("|")[0];

            if (JudgeUtils.isNotNull(firstIdc)) {
                //缓存
                BusParamBO busParamBO = busParamService.getIdcInfoByKey(BusParamTypeConstants.IDC_GZONE, idcInformation);
                if (JudgeUtils.isNull(busParamBO)) {
                    return;
                }
                String paramCode = busParamBO.getParamCode();
                idcInformationCacheService.addGlobalZoneIdcInformationCache(paramCode);
            }
        }
    }
}
