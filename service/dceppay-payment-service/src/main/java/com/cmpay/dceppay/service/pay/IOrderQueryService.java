package com.cmpay.dceppay.service.pay;

import com.cmpay.dceppay.bo.paymentquery.PaymentOrderQueryBO;
import com.cmpay.dceppay.bo.paymentquery.PaymentOrderResultBO;
import com.cmpay.dceppay.bo.refundquery.RefundOrderQueryBO;
import com.cmpay.dceppay.bo.refundquery.RefundOrderResultBO;

/**
 * <AUTHOR>
 * @date 2024/8/30 17:23
 * 订单查询service
 */
public interface IOrderQueryService {
    /**
     * 支付订单查询
     *
     * @param paymentOrderQueryBO
     * @return
     */
    PaymentOrderResultBO paymentQuery(PaymentOrderQueryBO paymentOrderQueryBO);

    /**
     * 退款订单查询
     *
     * @param refundOrderQueryBO
     * @return
     */
    RefundOrderResultBO refundQuery(RefundOrderQueryBO refundOrderQueryBO);
}
