package com.cmpay.dceppay.service.pay.impl;

import com.cmpay.dceppay.bo.check.CheckFileDetailBO;
import com.cmpay.dceppay.bo.notify.RefundNotifyBO;
import com.cmpay.dceppay.bo.pay.RefundOrderDBBO;
import com.cmpay.dceppay.bo.refundquery.RefundOrderResultBO;
import com.cmpay.dceppay.service.pay.IRefundOrderDataService;
import com.cmpay.dceppay.util.DateTimeUtil;
import com.cmpay.lemon.common.utils.JudgeUtils;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/9/17 17:31
 */
@Service
public class RefundOrderDataServiceImpl implements IRefundOrderDataService {
    @Override
    public RefundOrderResultBO buildRefundOrderResult(RefundNotifyBO refundNotifyBO) {
        RefundOrderResultBO refundOrderResultBO = new RefundOrderResultBO();
        refundOrderResultBO.setOutRefundNo(refundNotifyBO.getOutRefundNo());
        refundOrderResultBO.setRefundAmount(refundNotifyBO.getTransactionAmount());
        refundOrderResultBO.setFinishDateTime(refundNotifyBO.getTransactionFinishTime());
        refundOrderResultBO.setErrMsgCd(refundNotifyBO.getRejectCode());
        refundOrderResultBO.setErrMsgInfo(refundNotifyBO.getRejectInformation());
        return refundOrderResultBO;
    }

    @Override
    public RefundOrderDBBO buildUpdateRefundOrderDBBO(RefundOrderResultBO refundOrderResultBO, RefundOrderDBBO refundOrderDBBO) {
        refundOrderDBBO.setBankRefundNo(refundOrderResultBO.getBankRefundNo());
        try {
            refundOrderDBBO.setOrderCompleteTime(DateTimeUtil.changeISODateTime(refundOrderResultBO.getFinishDateTime()));
        } catch (Exception e) {
            refundOrderDBBO.setOrderCompleteTime(refundOrderResultBO.getFinishDateTime());
        }
        if (JudgeUtils.isNotNull(refundOrderResultBO.getErrMsgCd())) {
            refundOrderDBBO.setErrMsgInfo(refundOrderResultBO.getErrMsgInfo());
            refundOrderDBBO.setErrMsgCd(refundOrderResultBO.getErrMsgCd());
        }
        return refundOrderDBBO;
    }

    @Override
    public RefundOrderResultBO buildRefundResult(CheckFileDetailBO checkFileDetailBO) {
        RefundOrderResultBO refundOrderResultBO = new RefundOrderResultBO();
        refundOrderResultBO.setOutRefundNo(checkFileDetailBO.getRefundOrderNo());
        refundOrderResultBO.setBankRefundNo(checkFileDetailBO.getBankRefundNo());
        refundOrderResultBO.setOutOrderNo(checkFileDetailBO.getOutOrderNo());
        refundOrderResultBO.setRefundAmount(checkFileDetailBO.getOrderAmount());
        refundOrderResultBO.setFinishDateTime(checkFileDetailBO.getOrderCompleteTime());
        return refundOrderResultBO;
    }
}
