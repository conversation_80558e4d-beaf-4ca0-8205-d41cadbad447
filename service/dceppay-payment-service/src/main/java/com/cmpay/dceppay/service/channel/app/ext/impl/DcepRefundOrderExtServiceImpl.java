package com.cmpay.dceppay.service.channel.app.ext.impl;

import com.cmpay.channel.data.Request;
import com.cmpay.dceppay.bo.pay.RefundOrderDBBO;
import com.cmpay.dceppay.bo.refund.RefundOrderBO;
import com.cmpay.dceppay.bo.refund.RefundOrderRepBO;
import com.cmpay.dceppay.bo.refund.RefundOrderRspBO;
import com.cmpay.dceppay.channel.DcepChannelEnum;
import com.cmpay.dceppay.config.DceppayConfig;
import com.cmpay.dceppay.constant.dcep.DcepConstants;
import com.cmpay.dceppay.constant.dcep.DcepResponseStatusConstant;
import com.cmpay.dceppay.dto.dcep.common.OriginalGroupHeader;
import com.cmpay.dceppay.dto.dcep.common.RequestGroupHeader;
import com.cmpay.dceppay.dto.dcep.common.SoapHeader;
import com.cmpay.dceppay.dto.dcep.faut.Fault;
import com.cmpay.dceppay.dto.dcep.faut.FaultSoapEnvelopResponse;
import com.cmpay.dceppay.dto.dcep.refundorder.RefundOrderRsp;
import com.cmpay.dceppay.dto.dcep.refundorder.request.OriginalOrderInformation;
import com.cmpay.dceppay.dto.dcep.refundorder.request.RefundInformation;
import com.cmpay.dceppay.dto.dcep.refundorder.request.RefundOrderBody;
import com.cmpay.dceppay.dto.dcep.refundorder.request.RefundOrderSoapEnvelopRequest;
import com.cmpay.dceppay.dto.dcep.refundorder.response.RefundOrderResponseInformation;
import com.cmpay.dceppay.dto.dcep.refundorder.response.RefundOrderSoapEnvelopResponse;
import com.cmpay.dceppay.enums.MsgCodeEnum;
import com.cmpay.dceppay.enums.dcep.BusinessCategoryCodeEnum;
import com.cmpay.dceppay.enums.dcep.DcepMessageTypeEnum;
import com.cmpay.dceppay.enums.pay.OrderStatusEnum;
import com.cmpay.dceppay.enums.pay.RefundCancleEnum;
import com.cmpay.dceppay.service.channel.app.ext.IDcepRefundOrderExtService;
import com.cmpay.dceppay.service.ext.DceppayParamService;
import com.cmpay.dceppay.service.pay.IPayRefundDBService;
import com.cmpay.dceppay.service.pay.IPayRefundService;
import com.cmpay.dceppay.util.DateTimeUtil;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.utils.LemonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/9/5 14:31
 */
@Service
@Slf4j
public class DcepRefundOrderExtServiceImpl implements IDcepRefundOrderExtService {
    @Autowired
    private DceppayConfig dceppayConfig;
    @Autowired
    private IPayRefundDBService refundDBService;
    @Autowired
    private IPayRefundService refundService;
    @Autowired
    private DceppayParamService dceppayParamService;


    @Override
    public RefundOrderSoapEnvelopRequest buildRefundRequestParam(RefundOrderDBBO refundOrderBO) {
        String walletId = refundOrderBO.getWalletId();
        if (JudgeUtils.isBlank(walletId)) {
            walletId = dceppayConfig.getWalletId();
        }
        RefundOrderSoapEnvelopRequest refundOrderSoapEnvelopRequest = new RefundOrderSoapEnvelopRequest();

        //报文头
        String messageIdentification = dceppayParamService.getMessageIdentification(DcepMessageTypeEnum.ORDER_REFUND_REQUEST.getValue(), walletId);
        SoapHeader soapHeader = dceppayParamService.getCommonSoapHear(DcepMessageTypeEnum.ORDER_REFUND_REQUEST.getMessageType(), messageIdentification);
        BeanUtils.copyProperties(refundOrderSoapEnvelopRequest, soapHeader);
        RefundOrderBody refundOrderBody = new RefundOrderBody();
        //业务头组件
        RequestGroupHeader requestGroupHeader = dceppayParamService.getRequestGroupHeader(DcepMessageTypeEnum.ORDER_REFUND_REQUEST.getMessageType(),messageIdentification);
        refundOrderBody.setRequestGroupHeader(requestGroupHeader);
        //原报文主键组件
        setOriginalGroupHeader(refundOrderBO, refundOrderBody);
        //原交易信息
        setOriginalOrderInfo(refundOrderBO, refundOrderBody);
        //订单退款信息
        setRefundInfo(refundOrderBO, refundOrderBody);
        refundOrderSoapEnvelopRequest.setRefundOrderBody(refundOrderBody);
        return refundOrderSoapEnvelopRequest;
    }

    private static void setRefundInfo(RefundOrderDBBO refundOrderBO, RefundOrderBody refundOrderBody) {
        RefundInformation refundInformation = new RefundInformation();
        refundInformation.setOutRefundNo(refundOrderBO.getRefundOrderNo());
        refundInformation.setOutRefundTime(DateTimeUtil.getCurrentISODateTime());
        refundInformation.setTransactionAmount(refundOrderBO.getRefundAmount());
        refundInformation.setTransactionBizType(BusinessCategoryCodeEnum.REFUND.getCode());
        refundInformation.setTransactionCategoryCode(DcepConstants.BUSINESS_CATEGORY_TYPE_REFUND);
        refundInformation.setRefundDescription(refundOrderBO.getRefundReason());
        refundOrderBody.setRefundInformation(refundInformation);
    }

    private static void setOriginalOrderInfo(RefundOrderDBBO refundOrderBO, RefundOrderBody refundOrderBody) {
        OriginalOrderInformation orderInformation = new OriginalOrderInformation();
        orderInformation.setOriginalOrderNo(refundOrderBO.getBankOrderNo());
        orderInformation.setOriginalOutOrderNo(refundOrderBO.getOutOrderNo());
        orderInformation.setOriginalMerchantNo(refundOrderBO.getOrgMerchantNo());
        orderInformation.setOriginalAmount(refundOrderBO.getOrderAmount());
        refundOrderBody.setOriginalOrderInformation(orderInformation);
    }

    private void setOriginalGroupHeader(RefundOrderDBBO refundOrderBO, RefundOrderBody refundOrderBody) {
        OriginalGroupHeader originalGroupHeader = new OriginalGroupHeader();
        originalGroupHeader.setOriginalMessageIdentification(refundOrderBO.getOriginalMessageIdentification());
        originalGroupHeader.setOriginalMessageType(DcepMessageTypeEnum.UNIFIED_ORDER_REQUEST.getMessageType());
        originalGroupHeader.setOriginalInstructingParty(dceppayConfig.getSenderCode());
        refundOrderBody.setOriginalGroupHeader(originalGroupHeader);
    }

    @Override
    public Request bulidRefundRequest(RefundOrderSoapEnvelopRequest refundOrderSoapEnvelopRequest) {
        Request request = new Request();
        request.setRequestId(LemonUtils.getRequestId());
        request.setRoute(DcepChannelEnum.DCEP_PAY);
        request.setBusiType(DcepChannelEnum.REFUND_ORDER.getName());
        request.setSource(DcepChannelEnum.DCEP_PAY);
        request.setTarget(refundOrderSoapEnvelopRequest);
        return request;
    }

    @Override
    public RefundOrderRspBO handleResponse(RefundOrderRsp refundOrderRsp) {
        if (JudgeUtils.isNull(refundOrderRsp)
                || (JudgeUtils.isNull(refundOrderRsp.getRefundOrderSoapEnvelopResponse())
                && JudgeUtils.isNull(refundOrderRsp.getFaultSoapEnvelopResponse()))
        ) {
            log.error("请求互联互通订单退款失败：响应为空");
            BusinessException.throwBusinessException(MsgCodeEnum.REFUND_ORDER_RESPONSE_NULL);
        }
        RefundOrderRspBO refundOrderRspBO = new RefundOrderRspBO();
        FaultSoapEnvelopResponse faultSoapEnvelopResponse = refundOrderRsp.getFaultSoapEnvelopResponse();
        if (JudgeUtils.isNotNull(faultSoapEnvelopResponse) && JudgeUtils.isNotNull(faultSoapEnvelopResponse.getFault())) {
            Fault fault = faultSoapEnvelopResponse.getFault();
            String errorCode = fault.getFaultCode();
            String errorMessage = fault.getFaultString() + (JudgeUtils.isNotBlank(fault.getDetail()) ? fault.getDetail() : "");
            refundOrderRspBO.setErrMsgCd(errorCode);
            refundOrderRspBO.setErrMsgInfo(errorMessage);
            log.error("请求互联互通订单退款失败：{}", errorCode + errorMessage);
            BusinessException.throwBusinessException(MsgCodeEnum.REFUND_ORDER_RESPONSE_FAULT);
        } else {
            RefundOrderSoapEnvelopResponse refundOrderSoapEnvelopResponse = refundOrderRsp.getRefundOrderSoapEnvelopResponse();
            RefundOrderResponseInformation responseInformation = refundOrderSoapEnvelopResponse.getSoapBody().getResponseInformation();
            String responseStatus = responseInformation.getResponseStatus();
            if (JudgeUtils.equals(responseStatus, DcepResponseStatusConstant.REFUND_ORDER_RESPONSE_SUCCESS)) {
                refundOrderRspBO.setRefundStatus(OrderStatusEnum.REFUND_WAIT.name());
                refundOrderRspBO.setRefundAmount(responseInformation.getTransactionAmount());
                refundOrderRspBO.setBankRefundNo(responseInformation.getRefundNo());
            } else if (JudgeUtils.equals(responseStatus, DcepResponseStatusConstant.REFUND_ORDER_RESPONSE_FAIL)) {
                String messageCd = responseInformation.getRejectCode();
                String errorMessageInfo = responseInformation.getRejectInformation();
                refundOrderRspBO.setErrMsgCd(messageCd);
                refundOrderRspBO.setErrMsgInfo(errorMessageInfo);
                log.error("请求互联互通订单退款失败：退款受理失败{}", messageCd + errorMessageInfo);
            } else {
                refundOrderRspBO.setErrMsgCd(responseInformation.getRejectCode());
                refundOrderRspBO.setErrMsgInfo(responseInformation.getRejectInformation());
                log.error("请求互联互通订单退款失败：{}", responseInformation.getRejectCode() + responseInformation.getRejectInformation());
                BusinessException.throwBusinessException(MsgCodeEnum.REFUND_ORDER_RESPONSE_INVALID);
            }
        }
        return refundOrderRspBO;
    }

    @Override
    public RefundOrderDBBO registerPendingRefundOrder(RefundOrderBO refundOrderBO, RefundOrderRepBO refundOrderRepBO) {
        RefundOrderDBBO refundOrder = getRefundOrderDBBO(refundOrderBO, refundOrderRepBO);
        refundOrder.setStatus(OrderStatusEnum.REFUND_PEND.name());
        refundDBService.addRefundOrder(refundOrder);
        return refundOrder;
    }

    @Override
    public void updateRefundAccept(RefundOrderDBBO refundOrderBO, RefundOrderRspBO refundOrderRspBO) {
        OrderStatusEnum orderStatusEnum = OrderStatusEnum.valueOf(refundOrderRspBO.getRefundStatus());
        if (JudgeUtils.isNull(orderStatusEnum)) {
            return;
        }
        switch (orderStatusEnum) {
            case REFUND_WAIT:
                refundOrderBO.setBankRefundNo(refundOrderRspBO.getBankRefundNo());
                refundDBService.updateRefundWait(refundOrderBO);
                refundService.handleAcceptRefund(refundOrderBO);
            default:
                break;
        }
    }

    private static RefundOrderDBBO getRefundOrderDBBO(RefundOrderBO refundOrderBO, RefundOrderRepBO refundOrderRepBO) {
        RefundOrderDBBO refundOrder = new RefundOrderDBBO();
        refundOrder.setRefundOrderNo(refundOrderBO.getRefundOrderNo());
        refundOrder.setOutOrderNo(refundOrderBO.getOutOrderNo());
        refundOrder.setRefundDate(refundOrderBO.getRefundDate());
        refundOrder.setRefundTime(refundOrderBO.getRefundTime());
        refundOrder.setChannelCode(refundOrderBO.getChannelCode());
        refundOrder.setPayWay(refundOrderBO.getPayWay());
        refundOrder.setScene(refundOrderBO.getScene());
        refundOrder.setBusType(refundOrderBO.getBusType());
        refundOrder.setMerchantNo(refundOrderBO.getMerchantNo());
        refundOrder.setOrgMerchantNo(refundOrderBO.getOrgMerchantNo());
        refundOrder.setWalletId(refundOrderBO.getWalletId());
        refundOrder.setAccountDate(refundOrderRepBO.getAccountDate());
        refundOrder.setOrderAmount(refundOrderBO.getOrderAmount());
        refundOrder.setBankOrderNo(refundOrderBO.getBankOrderNo());
        refundOrder.setRefundAmount(refundOrderRepBO.getRefundAmount());
        refundOrder.setNotifyUrl(refundOrderRepBO.getNotifyUrl());
        refundOrder.setRefundReason(refundOrderRepBO.getRefundReason());
        refundOrder.setExtra(refundOrderRepBO.getExtra());
        refundOrder.setCancelFlag(JudgeUtils.isNull(refundOrderRepBO.getCancelFlag()) ? RefundCancleEnum.N.name() : refundOrderRepBO.getCancelFlag());
        refundOrder.setBizType(refundOrderBO.getBizType());
        refundOrder.setBizCategory(refundOrderBO.getBizCategory());
        return refundOrder;
    }


}
