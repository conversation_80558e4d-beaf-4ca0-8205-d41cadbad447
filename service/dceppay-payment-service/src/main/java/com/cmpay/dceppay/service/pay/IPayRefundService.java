package com.cmpay.dceppay.service.pay;

import com.cmpay.dceppay.bo.notify.RefundNotifyRspBO;
import com.cmpay.dceppay.bo.pay.RefundOrderDBBO;
import com.cmpay.dceppay.bo.refund.RefundOrderBO;
import com.cmpay.dceppay.bo.refund.RefundOrderRepBO;
import com.cmpay.dceppay.bo.refundquery.RefundOrderResultBO;
import com.cmpay.dceppay.enums.pay.OrderStatusEnum;

/**
 * <AUTHOR>
 * @date 2024/9/5 9:47
 */
public interface IPayRefundService {
    /**
     * 订单信息检查
     * @param refundOrderRepBO
     * @return
     */
    RefundOrderBO checkAndQueryOrder(RefundOrderRepBO refundOrderRepBO);

    /**
     * 退款成功订单处理
     * @param refundOrderResultBO
     * @param refundOrderDBBO
     */
    void handleSuccessRefundOrder(RefundOrderResultBO refundOrderResultBO, RefundOrderDBBO refundOrderDBBO);

    /**
     * 退款失败订单处理
     * @param refundOrderResultBO
     * @param refundOrderDBBO
     */
    void handleFailRefundOrder(RefundOrderResultBO refundOrderResultBO, RefundOrderDBBO refundOrderDBBO);

    /**
     * 退款受理成功订单处理
     * @param refundOrderDBBO
     */
    void handleAcceptRefund(RefundOrderDBBO refundOrderDBBO);

    boolean checkRefundStatus(OrderStatusEnum orderStatusEnum, RefundOrderDBBO refundOrder, RefundNotifyRspBO refundNotifyRspBO);
}
