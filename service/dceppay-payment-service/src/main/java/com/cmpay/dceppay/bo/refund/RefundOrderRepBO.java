package com.cmpay.dceppay.bo.refund;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/8/30 11:28
 * 订单退款请求参数
 */
@Data
public class RefundOrderRepBO {
    // 订单号
    private String outOrderNo;

    // 退款请求号
    private String outRefundNo;

    // 退款请求日期
    private String tradeDate;

    // 会计日期
    private String accountDate;

    // 退款金额
    private BigDecimal refundAmount;

    // 退款原因
    private String refundReason;

    // 退款结果通知url
    private String notifyUrl;

    // 保留字段
    private String extra;

    // 撤单标识
    private String cancelFlag;

    //渠道编号
    private String channelCode;

}
