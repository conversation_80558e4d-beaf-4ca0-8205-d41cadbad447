package com.cmpay.dceppay.service.pay;

import com.cmpay.dceppay.bo.closeorder.CloseOrderBO;
import com.cmpay.dceppay.bo.closeorder.CloseOrderRspBO;
import com.cmpay.dceppay.bo.payment.UnifiedOrderReqBO;
import com.cmpay.dceppay.bo.payment.UnifiedOrderRspBO;
import com.cmpay.dceppay.bo.refund.RefundOrderCheckBO;
import com.cmpay.dceppay.bo.refund.RefundOrderRepBO;
import com.cmpay.dceppay.bo.refund.RefundOrderRspBO;

/**
 * <AUTHOR>
 * @date 2024/8/30 16:17
 */
public interface IPaymentService {
    /**
     * 统一下单
     * @param unifiedOrderReqBO
     * @return
     */
    UnifiedOrderRspBO unifiedOrder(UnifiedOrderReqBO unifiedOrderReqBO);

    /**
     * 订单退款
     * @param refundOrderRepBO
     * @return
     */
    RefundOrderRspBO refundOrder(RefundOrderRepBO refundOrderRepBO);

    /**
     * 订单关闭
     * @param closeOrderBO
     * @return
     */
    CloseOrderRspBO closeOrder(CloseOrderBO closeOrderBO);

    /**
     * 订单退款预检查
     * @param refundOrderCheckBO
     */
    void refundOrderCheck(RefundOrderCheckBO refundOrderCheckBO);
}
