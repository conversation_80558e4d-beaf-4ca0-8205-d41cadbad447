package com.cmpay.dceppay.service.pay.impl;

import com.cmpay.dceppay.bo.account.AccountTreatmentResultBO;
import com.cmpay.dceppay.bo.common.CheckResultBO;
import com.cmpay.dceppay.bo.notify.PaymentNotifyRspBO;
import com.cmpay.dceppay.bo.pay.MerchantRouteBO;
import com.cmpay.dceppay.bo.pay.PayOrderDBBO;
import com.cmpay.dceppay.bo.payment.UnifiedOrderReqBO;
import com.cmpay.dceppay.bo.paymentquery.PaymentOrderResultBO;
import com.cmpay.dceppay.stream.client.DceppayAsyncClient;
import com.cmpay.dceppay.constant.dcep.DcepResponseStatusConstant;
import com.cmpay.dceppay.enums.MsgCodeEnum;
import com.cmpay.dceppay.enums.pay.AccountProcessStatusEnum;
import com.cmpay.dceppay.enums.pay.OrderStatusEnum;
import com.cmpay.dceppay.service.account.IDcepAccountService;
import com.cmpay.dceppay.service.pay.*;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 2024/9/3 18:01
 */
@Service
public class PayOrderServiceImpl implements IPayOrderService {
    @Autowired
    private IPayOrderCheckService payOrderCheckService;
    @Autowired
    private IPayOrderDBService payOrderDBService;
    @Autowired
    private IMerchantRouteService merchantRouteService;
    @Autowired
    private IDcepAccountService accountService;
    @Autowired
    private IPayOrderDataService payOrderDataService;
    @Autowired
    private IPaySettlementDBService paySettlementDBService;
    @Autowired
    private DceppayAsyncClient asyncClient;

    @Override
    public MerchantRouteBO checkAndQueryMerchantRoute(UnifiedOrderReqBO unifiedOrderReqBO) {
        CheckResultBO checkResult = payOrderCheckService.unifiedOrderRequestCheck(unifiedOrderReqBO);
        if (!checkResult.isCheckFlag()) {
            BusinessException.throwBusinessException(checkResult.getMessageCode());
        }
        MerchantRouteBO merchantRouteBO = merchantRouteService.checkRoute(unifiedOrderReqBO.getBusType());
        if (JudgeUtils.isNull(merchantRouteBO)) {
            BusinessException.throwBusinessException(MsgCodeEnum.MERCHANT_ROUTE_NOT_EXIST.getMsgCd());
        }
        PayOrderDBBO payOrderDBBO=payOrderDBService.getByOutOrderNo(unifiedOrderReqBO.getOutOrderNo());
        if (JudgeUtils.isNotNull(payOrderDBBO)) {
            merchantRouteBO.setMessageIdentification(payOrderDBBO.getMessageIdentification());
        }
        return merchantRouteBO;
    }

    @Override
    public void handleSuccessPayOrder(PaymentOrderResultBO paymentOrderResultBO, PayOrderDBBO payOrderDBBO) {
        //账务处理
        AccountTreatmentResultBO accountTreatmentResultBO = accountService.handPaymentSuccessAccount(payOrderDBBO);
        if (JudgeUtils.notEquals(accountTreatmentResultBO.getAccountHandleStatus(), AccountProcessStatusEnum.SUCCESS.name())) {
            BusinessException.throwBusinessException(MsgCodeEnum.PAYMENT_ACCOUNT_STATUS_WAIT);
        }
        payOrderDBBO.setAccountDate(accountTreatmentResultBO.getAccountDate());
        payOrderDBBO.setOrderCompleteTime(paymentOrderResultBO.getFinishDateTime());
        paySettlementDBService.registerPaySettlement(payOrderDBBO);
        updatePayOrderRecord(paymentOrderResultBO, payOrderDBBO);
        //异步通知
        payOrderDBBO.setStatus(OrderStatusEnum.TRADE_SUCCESS.name());
        asyncClient.asyncNotify(payOrderDBBO);

    }


    private void updatePayOrderRecord(PaymentOrderResultBO paymentOrderResultBO, PayOrderDBBO payOrderDBBO) {
        PayOrderDBBO updatePayOrderDBBO = payOrderDataService.buildUpdatePayOrderDBBO(paymentOrderResultBO, payOrderDBBO);
        int updateNumber = payOrderDBService.updateTradeSuccess(updatePayOrderDBBO);
        if (updateNumber != 1) {
            BusinessException.throwBusinessException(MsgCodeEnum.UPDATE_ORDER_SUCCESS_ERROR);
        }
    }

    @Override
    public void handleFailPayOrder(PaymentOrderResultBO paymentOrderResultBO, PayOrderDBBO payOrderDBBO) {
        updatePayOrderFailRecord(paymentOrderResultBO, payOrderDBBO);
    }

    @Override
    public boolean checkPaymentStatus(OrderStatusEnum orderStatusEnum, PayOrderDBBO order, PaymentNotifyRspBO paymentNotifyRspBO) {
        if (JudgeUtils.equals(orderStatusEnum.name(), order.getStatus())) {
            paymentNotifyRspBO.setProcessStatus(DcepResponseStatusConstant.NOTIFY_HANDLE_SUCCESS);
            return true;
        }
        //非等待支付以及退款关闭状态的支付通知不处理
        if (!JudgeUtils.equalsAny(order.getStatus(),OrderStatusEnum.WAIT_PAY.name(),OrderStatusEnum.TRADE_CLOSED.name())) {
            BusinessException.throwBusinessException(MsgCodeEnum.PAYMENT_ORDER_STATUS_INVALID);
            return true;
        }
        return false;
    }


    private void updatePayOrderFailRecord(PaymentOrderResultBO paymentOrderResultBO, PayOrderDBBO payOrderDBBO) {
        PayOrderDBBO updatePayOrderDBBO = payOrderDataService.buildUpdatePayOrderDBBO(paymentOrderResultBO, payOrderDBBO);
        int updateNumber = payOrderDBService.updateTradeFail(updatePayOrderDBBO);
        if (updateNumber != 1) {
            BusinessException.throwBusinessException(MsgCodeEnum.UPDATE_ORDER_FAIL_ERROR);
        }
    }


}
