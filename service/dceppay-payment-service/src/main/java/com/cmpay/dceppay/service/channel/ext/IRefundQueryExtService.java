package com.cmpay.dceppay.service.channel.ext;

import com.cmpay.channel.data.Request;
import com.cmpay.dceppay.bo.refundquery.RefundOrderQueryBO;
import com.cmpay.dceppay.bo.refundquery.RefundOrderResultBO;
import com.cmpay.dceppay.dto.dcep.refundorderquery.RefundOrderQueryRsp;
import com.cmpay.dceppay.dto.dcep.refundorderquery.request.RefundOrderQuerySoapEnvelopRequest;
import com.cmpay.dceppay.dto.dcep.refundorderquery.response.RefundOrderQuerySoapEnvelopResponse;

/**
 * <AUTHOR>
 * @date 2024/9/10 18:49
 */
public interface IRefundQueryExtService {
    RefundOrderQuerySoapEnvelopRequest buildRefundQueryRequestParam(RefundOrderQueryBO refundOrderQueryBO);

    Request bulidRefundQueryRequest(RefundOrderQuerySoapEnvelopRequest refundOrderQuerySoapEnvelopRequest);

    RefundOrderResultBO handleResponse(RefundOrderQueryBO refundOrderQueryBO, RefundOrderQueryRsp refundOrderQueryRsp);
}
