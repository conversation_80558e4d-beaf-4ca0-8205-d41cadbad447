package com.cmpay.dceppay.service.channel.app.ext;

import com.cmpay.channel.data.Request;
import com.cmpay.dceppay.bo.pay.MerchantRouteBO;
import com.cmpay.dceppay.bo.pay.PayOrderDBBO;
import com.cmpay.dceppay.bo.payment.UnifiedOrderReqBO;
import com.cmpay.dceppay.bo.payment.UnifiedOrderRspBO;
import com.cmpay.dceppay.dto.dcep.unifiedorder.UnifiedOrderRsp;
import com.cmpay.dceppay.dto.dcep.unifiedorder.request.UnifiedOrderSoapEnvelopRequest;
import com.cmpay.dceppay.dto.dcep.unifiedorder.response.UnifiedOrderSoapEnvelopResponse;

/**
 * <AUTHOR>
 * @date 2024/9/5 9:03
 */
public interface IDcepUnifiedOrderExtService {
    /**
     * 获取构建数币下单请求参数
     *
     * @param merchantRouteBO
     * @param unifiedOrderReqBO
     * @return
     */
    UnifiedOrderSoapEnvelopRequest buildUnifiedOrderRequestParam(MerchantRouteBO merchantRouteBO, UnifiedOrderReqBO unifiedOrderReqBO);

    /**
     * 构建网关请求
     *
     * @param unifiedOrderSoapEnvelopRequest
     * @return
     */
    Request bulidUnifiedOrderRequest(UnifiedOrderSoapEnvelopRequest unifiedOrderSoapEnvelopRequest);

    /**
     * 处理网关响应
     *
     * @param unifiedOrderRsp
     * @return
     */
    UnifiedOrderRspBO handleResponse(MerchantRouteBO merchantRouteBO,UnifiedOrderReqBO unifiedOrderReqBO, UnifiedOrderSoapEnvelopRequest unifiedOrderSoapEnvelopRequest, UnifiedOrderRsp unifiedOrderRsp);


    void registerOrderSafely(MerchantRouteBO merchantRouteBO, UnifiedOrderSoapEnvelopRequest unifiedOrderSoapEnvelopRequest, UnifiedOrderReqBO unifiedOrderReqBO, UnifiedOrderRsp unifiedOrderRsp);
}
