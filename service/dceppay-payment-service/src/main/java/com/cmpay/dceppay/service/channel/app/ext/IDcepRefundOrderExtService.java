package com.cmpay.dceppay.service.channel.app.ext;

import com.cmpay.channel.data.Request;
import com.cmpay.dceppay.bo.pay.RefundOrderDBBO;
import com.cmpay.dceppay.bo.refund.RefundOrderBO;
import com.cmpay.dceppay.bo.refund.RefundOrderRepBO;
import com.cmpay.dceppay.bo.refund.RefundOrderRspBO;
import com.cmpay.dceppay.dto.dcep.refundorder.RefundOrderRsp;
import com.cmpay.dceppay.dto.dcep.refundorder.request.RefundOrderSoapEnvelopRequest;

/**
 * <AUTHOR>
 * @date 2024/9/5 14:31
 */
public interface IDcepRefundOrderExtService {
    /**
     * 异步退款，登记退款状态待发起
     *
     * @param refundOrderBO
     * @param refundOrderRepBO 退款请求
     */
    RefundOrderDBBO registerPendingRefundOrder(RefundOrderBO refundOrderBO, RefundOrderRepBO refundOrderRepBO);

    /**
     * 退款网关请求参数构建
     *
     * @param refundOrderDBBO
     * @return
     */
    RefundOrderSoapEnvelopRequest buildRefundRequestParam(RefundOrderDBBO refundOrderDBBO);

    /**
     * 退款网关请求构建
     *
     * @param refundOrderSoapEnvelopRequest
     * @return
     */
    Request bulidRefundRequest(RefundOrderSoapEnvelopRequest refundOrderSoapEnvelopRequest);

    /**
     * @param refundOrderRsp
     * @return
     */
    RefundOrderRspBO handleResponse(RefundOrderRsp refundOrderRsp);


    /**
     * 订单受理成功状态更新
     * @param refundOrderBO
     * @param refundOrderRspBO
     */
    void updateRefundAccept(RefundOrderDBBO refundOrderBO, RefundOrderRspBO refundOrderRspBO);
}
