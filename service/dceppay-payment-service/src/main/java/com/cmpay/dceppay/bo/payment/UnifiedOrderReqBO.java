package com.cmpay.dceppay.bo.payment;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/8/30 9:47
 * 数币收单下单请求接口
 */
@Data
public class UnifiedOrderReqBO {
    // 交易流水号
    private String tradeJrnNo;

    // 订单号
    private String outOrderNo;

    // 支付方式
    private String payWay;

    // 支付场景
    private String scene;

    // 订单总金额
    private BigDecimal totalAmount;

    // 商品名称
    private String productName;

    // 商品描述
    private String productDesc;

    // 订单日期
    private String orderDate;

    // 订单时间
    private String orderTime;

    // 订单失效时间
    private String orderTimeExpire;

    // 业务类型
    private String busType;

    // 会计日期
    private String accountDate;

    // 通知URL
    private String notifyUrl;

    // 页面通知URL
    private String pageNotifyUrl;

    // 保留字段
    private String extra;
    private String channelCode;
}
