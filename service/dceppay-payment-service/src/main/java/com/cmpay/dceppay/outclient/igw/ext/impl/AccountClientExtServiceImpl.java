package com.cmpay.dceppay.outclient.igw.ext.impl;

import com.cmpay.account.dto.AccountDebitOrCreditDTO;
import com.cmpay.account.enums.CapitalTypeEnum;
import com.cmpay.account.enums.DebitOrCreditFlag;
import com.cmpay.dceppay.bo.account.AccountDetailBO;
import com.cmpay.dceppay.bo.account.AccountTreatmentBO;
import com.cmpay.dceppay.constant.pay.AccountConstants;
import com.cmpay.dceppay.outclient.igw.ext.IAccountClientExtService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/14 8:39
 */
@Service
public class AccountClientExtServiceImpl implements IAccountClientExtService {
    @Override
    public List<AccountDebitOrCreditDTO> getAccountList(AccountTreatmentBO accountTreatmentBO, List<AccountDetailBO> accountDetailBOS) {
        List<AccountDebitOrCreditDTO> accountDebitOrCreditDTOS = new ArrayList<>();
        for (AccountDetailBO accountDetailBO : accountDetailBOS) {
            AccountDebitOrCreditDTO accountDebitOrCreditDTO = new AccountDebitOrCreditDTO();
            accountDebitOrCreditDTO.setAccountNo(accountDetailBO.getAccountNo());
            accountDebitOrCreditDTO.setAccountName(accountDetailBO.getAccountName());
            accountDebitOrCreditDTO.setDebitOrCreditFlag(DebitOrCreditFlag.type(accountDetailBO.getDcFlag()));
            accountDebitOrCreditDTO.setCapitalType(CapitalTypeEnum.CASH);
            accountDebitOrCreditDTO.setAmount(accountDetailBO.getAmount());
            accountDebitOrCreditDTO.setTradeCode(accountTreatmentBO.getTradeCode());
            accountDebitOrCreditDTO.setTradeDate(accountTreatmentBO.getTradeDate());
            accountDebitOrCreditDTO.setTradeTime(accountTreatmentBO.getTradeTime());
            accountDebitOrCreditDTO.setTradeOppCode(accountDetailBO.getOppAccountNo());
            accountDebitOrCreditDTO.setTradeOppName(accountDetailBO.getOppAccountName());
            accountDebitOrCreditDTO.setTradeOppType(AccountConstants.OPP_TYPE_INNER_ACCOUNT);
            accountDebitOrCreditDTO.setTradeDescribe(AccountConstants.TRADE_DESC_REMARK);
            accountDebitOrCreditDTO.setRemark(AccountConstants.TRADE_DESC_REMARK);
            accountDebitOrCreditDTOS.add(accountDebitOrCreditDTO);
        }
        return accountDebitOrCreditDTOS;
    }
}
