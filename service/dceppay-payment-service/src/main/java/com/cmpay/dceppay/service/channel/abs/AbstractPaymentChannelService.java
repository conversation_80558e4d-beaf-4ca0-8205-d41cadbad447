package com.cmpay.dceppay.service.channel.abs;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Optional;

import javax.annotation.Resource;

import com.cmpay.channel.data.Request;
import com.cmpay.channel.data.Response;
import com.cmpay.dceppay.bo.closeorder.CloseOrderBO;
import com.cmpay.dceppay.bo.closeorder.CloseOrderRspBO;
import com.cmpay.dceppay.bo.notify.PaymentNotifyBO;
import com.cmpay.dceppay.bo.notify.PaymentNotifyRspBO;
import com.cmpay.dceppay.bo.notify.RefundNotifyBO;
import com.cmpay.dceppay.bo.notify.RefundNotifyRspBO;
import com.cmpay.dceppay.bo.pay.MerchantRouteBO;
import com.cmpay.dceppay.bo.pay.PayOrderDBBO;
import com.cmpay.dceppay.bo.pay.RefundOrderDBBO;
import com.cmpay.dceppay.bo.payment.AppUrlContextBO;
import com.cmpay.dceppay.bo.payment.AppUrlSourceInfo;
import com.cmpay.dceppay.bo.payment.UnifiedOrderReqBO;
import com.cmpay.dceppay.bo.payment.UnifiedOrderRspBO;
import com.cmpay.dceppay.bo.paymentquery.PaymentOrderQueryBO;
import com.cmpay.dceppay.bo.paymentquery.PaymentOrderResultBO;
import com.cmpay.dceppay.bo.refund.RefundOrderBO;
import com.cmpay.dceppay.bo.refund.RefundOrderRepBO;
import com.cmpay.dceppay.bo.refund.RefundOrderRspBO;
import com.cmpay.dceppay.bo.refundquery.RefundOrderQueryBO;
import com.cmpay.dceppay.bo.refundquery.RefundOrderResultBO;
import com.cmpay.dceppay.client.DceppayCgwOutClient;
import com.cmpay.dceppay.config.DceppayConfig;
import com.cmpay.dceppay.constant.dcep.DcepConstants;
import com.cmpay.dceppay.constant.dcep.DcepResponseStatusConstant;
import com.cmpay.dceppay.dto.dcep.closeorder.CloseOrderRsp;
import com.cmpay.dceppay.dto.dcep.closeorder.request.CloseOrderSoapEnvelopRequest;
import com.cmpay.dceppay.dto.dcep.refundorder.RefundOrderRsp;
import com.cmpay.dceppay.dto.dcep.refundorder.request.RefundOrderSoapEnvelopRequest;
import com.cmpay.dceppay.dto.dcep.refundorderquery.RefundOrderQueryRsp;
import com.cmpay.dceppay.dto.dcep.refundorderquery.request.RefundOrderQuerySoapEnvelopRequest;
import com.cmpay.dceppay.dto.dcep.unifiedorder.UnifiedOrderRsp;
import com.cmpay.dceppay.dto.dcep.unifiedorder.request.UnifiedOrderSoapEnvelopRequest;
import com.cmpay.dceppay.dto.dcep.unifiedorder.response.UnifiedOrderSoapEnvelopResponse;
import com.cmpay.dceppay.dto.dcep.unifiedorderquery.UnifiedOrderQueryRsp;
import com.cmpay.dceppay.dto.dcep.unifiedorderquery.request.OrderQuerySoapEnvelopRequest;
import com.cmpay.dceppay.enums.MsgCodeEnum;
import com.cmpay.dceppay.enums.pay.OrderStatusEnum;
import com.cmpay.dceppay.service.channel.IChannelSupportService;
import com.cmpay.dceppay.service.channel.IPaymentChannelService;
import com.cmpay.dceppay.service.channel.ext.ICloseOrderExtService;
import com.cmpay.dceppay.service.channel.ext.IDcepRefundOrderExtService;
import com.cmpay.dceppay.service.channel.ext.IDcepUnifiedOrderExtService;
import com.cmpay.dceppay.service.channel.ext.IOrderQueryExtService;
import com.cmpay.dceppay.service.channel.ext.IRefundQueryExtService;
import com.cmpay.dceppay.service.pay.IPayOrderDataService;
import com.cmpay.dceppay.service.pay.IPayOrderService;
import com.cmpay.dceppay.service.pay.IPayRefundService;
import com.cmpay.dceppay.service.pay.IRefundOrderDataService;
import com.cmpay.dceppay.stream.client.DceppayAsyncClient;
import com.cmpay.dceppay.util.DateTimeUtil;
import com.cmpay.dceppay.utils.ExceptionHandlerUtil;
import com.cmpay.framework.data.request.GenericDTO;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className AbstractPaymentChannelService
 * @date 2025/9/24
 * @description
 */
@Slf4j
public abstract class AbstractPaymentChannelService implements IPaymentChannelService, IChannelSupportService {

    @Resource
    private DceppayCgwOutClient dceppayCgwOutClient;
    @Resource
    private IDcepUnifiedOrderExtService dcepUnifiedOrderExtService;
    @Resource
    private IDcepRefundOrderExtService dcepRefundOrderExtService;
    @Resource
    private ICloseOrderExtService closeOrderExtService;
    @Resource
    private IOrderQueryExtService orderQueryExtService;
    @Resource
    private IRefundQueryExtService refundQueryExtService;
    @Resource
    private IPayOrderService payOrderService;
    @Resource
    private IPayOrderDataService payOrderDataService;
    @Resource
    private IRefundOrderDataService refundOrderDataService;
    @Resource
    private IPayRefundService refundService;
    @Resource
    private DceppayAsyncClient asyncClient;
    @Resource
    private DceppayConfig dceppayConfig;

    @Override
    public UnifiedOrderRspBO order(MerchantRouteBO merchantRouteBO, UnifiedOrderReqBO unifiedOrderReqBO) {
        // 封装网关请求参数对象
        UnifiedOrderSoapEnvelopRequest unifiedOrderSoapEnvelopRequest
            = dcepUnifiedOrderExtService.buildUnifiedOrderRequestParam(merchantRouteBO, unifiedOrderReqBO, this);
        Request request = dcepUnifiedOrderExtService.bulidUnifiedOrderRequest(unifiedOrderSoapEnvelopRequest);
        UnifiedOrderRsp unifiedOrderRsp = new UnifiedOrderRsp();
        try {
            // 请求网关
            GenericRspDTO<Response> genericRspDTO = dceppayCgwOutClient.request(
                GenericDTO.newChildInstanceWithBody(GenericDTO.class, request));
            // 处理网关响应对象
            if (JudgeUtils.isNotSuccess(genericRspDTO)) {
                BusinessException.throwBusinessException(MsgCodeEnum.TRANSACTION_REQUEST_FAIL);
            }
            unifiedOrderRsp = Optional.ofNullable(genericRspDTO)
                .map(GenericRspDTO::getBody)
                .map(Response::getResult)
                .map(x -> (UnifiedOrderRsp)x)
                .orElse(new UnifiedOrderRsp());
            return dcepUnifiedOrderExtService.handleResponse(merchantRouteBO, unifiedOrderReqBO,
                unifiedOrderSoapEnvelopRequest, unifiedOrderRsp, this);
        } catch (Exception e) {
            ExceptionHandlerUtil.throwRequestDcepError(e);
        } finally {
            dcepUnifiedOrderExtService.registerOrderSafely(merchantRouteBO, unifiedOrderSoapEnvelopRequest,
                unifiedOrderReqBO, unifiedOrderRsp);
        }
        return null;
    }

    @Override
    public RefundOrderRspBO refund(RefundOrderBO refundOrderBO, RefundOrderRepBO refundOrderRepBO) {
        RefundOrderDBBO refundOrderDBBO = null;
        try {
            refundOrderDBBO = dcepRefundOrderExtService.registerPendingRefundOrder(refundOrderBO, refundOrderRepBO);
        } catch (Exception e) {
            BusinessException.throwBusinessException(MsgCodeEnum.REFUND_ORDER_REGISTER_FAIL);
        }
        RefundOrderRspBO refundOrderRspBO = new RefundOrderRspBO();
        refundOrderRspBO.setOutRefundNo(refundOrderBO.getRefundOrderNo());
        refundOrderRspBO.setOutOrderNo(refundOrderBO.getOutOrderNo());
        refundOrderRspBO.setRefundAmount(refundOrderRepBO.getRefundAmount());
        refundOrderDBBO.setOriginalMessageIdentification(refundOrderBO.getOriginalMessageIdentification());
        asyncClient.asyncRefund(refundOrderDBBO);
        return refundOrderRspBO;
    }

    @Override
    public CloseOrderRspBO close(PayOrderDBBO payOrder, CloseOrderBO closeOrderBO) {
        CloseOrderSoapEnvelopRequest closeOrderSoapEnvelopRequest = closeOrderExtService.buildOrderCloseRequestParam(
            payOrder, closeOrderBO);
        Request request = closeOrderExtService.bulidRefundRequest(closeOrderSoapEnvelopRequest);
        //请求网关
        GenericRspDTO<Response> genericRspDTO = dceppayCgwOutClient.request(
            GenericDTO.newChildInstanceWithBody(GenericDTO.class, request));
        //处理网关响应对象
        if (JudgeUtils.isNotSuccess(genericRspDTO)) {
            BusinessException.throwBusinessException(genericRspDTO);
        }
        CloseOrderRsp closeOrderRsp = Optional.of(genericRspDTO)
            .map(GenericRspDTO::getBody)
            .map(Response::getResult).map(x -> (CloseOrderRsp)x)
            .orElse(new CloseOrderRsp());
        return closeOrderExtService.handleResponse(closeOrderBO, closeOrderRsp);

    }

    @Override
    public PaymentOrderResultBO paymentQuery(PaymentOrderQueryBO paymentOrderQueryBO) {
        OrderQuerySoapEnvelopRequest orderQuerySoapEnvelopRequest = orderQueryExtService.buildOrderQueryRequestParam(
            paymentOrderQueryBO);
        Request request = orderQueryExtService.bulidOrderQueryRequest(orderQuerySoapEnvelopRequest);
        GenericRspDTO<Response> genericRspDTO = dceppayCgwOutClient.request(
            GenericDTO.newChildInstanceWithBody(GenericDTO.class, request));
        //处理网关响应对象
        if (JudgeUtils.isNotSuccess(genericRspDTO)) {
            BusinessException.throwBusinessException(genericRspDTO);
        }
        UnifiedOrderQueryRsp unifiedOrderQueryRsp = Optional.of(genericRspDTO)
            .map(GenericRspDTO::getBody)
            .map(Response::getResult).map(x -> (UnifiedOrderQueryRsp)x)
            .orElse(new UnifiedOrderQueryRsp());
        return orderQueryExtService.handleResponse(paymentOrderQueryBO, unifiedOrderQueryRsp);

    }

    @Override
    public RefundOrderResultBO refundQuery(RefundOrderQueryBO refundOrderQueryBO) {
        RefundOrderQuerySoapEnvelopRequest refundOrderQuerySoapEnvelopRequest
            = refundQueryExtService.buildRefundQueryRequestParam(refundOrderQueryBO);
        Request request = refundQueryExtService.bulidRefundQueryRequest(refundOrderQuerySoapEnvelopRequest);
        GenericRspDTO<Response> genericRspDTO = dceppayCgwOutClient.request(
            GenericDTO.newChildInstanceWithBody(GenericDTO.class, request));
        //处理网关响应对象
        if (JudgeUtils.isNotSuccess(genericRspDTO)) {
            BusinessException.throwBusinessException(genericRspDTO);
        }
        RefundOrderQueryRsp refundOrderRsp = Optional.of(genericRspDTO)
            .map(GenericRspDTO::getBody)
            .map(Response::getResult).map(x -> (RefundOrderQueryRsp)x)
            .orElse(new RefundOrderQueryRsp());
        return refundQueryExtService.handleResponse(refundOrderQueryBO, refundOrderRsp);

    }

    @Override
    public PaymentNotifyRspBO paymentNotify(PayOrderDBBO order, PaymentNotifyBO paymentNotifyBO) {
        PaymentNotifyRspBO paymentNotifyRspBO = new PaymentNotifyRspBO();
        BeanUtils.copyProperties(paymentNotifyRspBO, paymentNotifyBO);
        String responseStatus = paymentNotifyBO.getResponseStatus();
        PaymentOrderResultBO paymentOrderResultBO = payOrderDataService.buildPaymentOrderResult(paymentNotifyBO);
        order.setReceiveNotifyTime(DateTimeUtil.getCurrentDateTimeStr());
        try {
            switch (responseStatus) {
                case DcepResponseStatusConstant.PAY_ORDER_SUCCESS:
                    if (payOrderService.checkPaymentStatus(OrderStatusEnum.TRADE_SUCCESS, order, paymentNotifyRspBO)) {
                        break;
                    }
                    if (paymentNotifyBO.getTransactionAmount().equals(order.getOrderAmount())) {
                        payOrderService.handleSuccessPayOrder(paymentOrderResultBO, order);
                        paymentNotifyRspBO.setProcessStatus(DcepResponseStatusConstant.NOTIFY_HANDLE_SUCCESS);
                    } else {
                        BusinessException.throwBusinessException(MsgCodeEnum.PAYMENT_ORDER_NOTIFY_AMOUNT_ERROR);
                    }
                    break;
                case DcepResponseStatusConstant.PAY_ORDER_FAIL:
                    if (payOrderService.checkPaymentStatus(OrderStatusEnum.TRADE_FAIL, order, paymentNotifyRspBO)) {
                        break;
                    }
                    if (paymentNotifyBO.getTransactionAmount().equals(order.getOrderAmount())) {
                        payOrderService.handleFailPayOrder(paymentOrderResultBO, order);
                        paymentNotifyRspBO.setProcessStatus(DcepResponseStatusConstant.NOTIFY_HANDLE_SUCCESS);
                    } else {
                        BusinessException.throwBusinessException(MsgCodeEnum.PAYMENT_ORDER_NOTIFY_AMOUNT_ERROR);
                    }
                    break;
                default:
                    log.error("互联互通支付订单通知业务回执状态非法: {}", responseStatus);
                    BusinessException.throwBusinessException(MsgCodeEnum.PAYMENT_ORDER_NOTIFY_STATUS_INVALID);
            }
        } catch (Exception e) {
            log.error("处理互联互通支付订单通知失败: {}", e.getCause());
            ExceptionHandlerUtil.throwRequestDcepError(e);
        }
        return paymentNotifyRspBO;
    }

    @Override
    public RefundNotifyRspBO refundNotify(RefundOrderDBBO refundOrder, RefundNotifyBO refundNotifyBO) {
        RefundNotifyRspBO refundNotifyRspBO = new RefundNotifyRspBO();
        BeanUtils.copyProperties(refundNotifyRspBO, refundNotifyBO);
        String responseStatus = refundNotifyBO.getResponseStatus();
        RefundOrderResultBO refundOrderResultBO = refundOrderDataService.buildRefundOrderResult(refundNotifyBO);
        refundOrder.setReceiveNotifyTime(DateTimeUtil.getCurrentDateTimeStr());
        try {
            switch (responseStatus) {
                case DcepResponseStatusConstant.PREFUND_ORDER_SUCCESS:
                    if (refundService.checkRefundStatus(OrderStatusEnum.REFUND_SUCCESS, refundOrder,
                        refundNotifyRspBO)) {
                        break;
                    }
                    if (refundNotifyBO.getTransactionAmount().equals(refundOrder.getRefundAmount())) {
                        refundService.handleSuccessRefundOrder(refundOrderResultBO, refundOrder);
                        refundNotifyRspBO.setProcessStatus(DcepResponseStatusConstant.NOTIFY_HANDLE_SUCCESS);
                    } else {
                        BusinessException.throwBusinessException(MsgCodeEnum.REFUND_ORDER_NOTIFY_AMOUNT_ERROR);
                    }
                    break;
                case DcepResponseStatusConstant.REFUND_ORDER_FAIL:
                    if (refundService.checkRefundStatus(OrderStatusEnum.REFUND_FAIL, refundOrder, refundNotifyRspBO)) {
                        break;
                    }
                    if (refundNotifyBO.getTransactionAmount().equals(refundOrder.getRefundAmount())) {
                        refundService.handleFailRefundOrder(refundOrderResultBO, refundOrder);
                        refundNotifyRspBO.setProcessStatus(DcepResponseStatusConstant.NOTIFY_HANDLE_SUCCESS);
                    } else {
                        BusinessException.throwBusinessException(MsgCodeEnum.REFUND_ORDER_NOTIFY_AMOUNT_ERROR);
                    }
                    break;
                default:
                    log.error("互联互通退款订单通知业务回执状态非法: {}", responseStatus);
                    BusinessException.throwBusinessException(MsgCodeEnum.REFUND_ORDER_NOTIFY_STATUS_INVALID);
            }
        } catch (Exception e) {
            log.error("处理互联互通支付退款通知失败: {}", e.getCause());
            ExceptionHandlerUtil.throwRequestDcepError(e);
        }
        return refundNotifyRspBO;
    }

    @Override
    public void refundAsync(RefundOrderDBBO refundOrderBO) {
        RefundOrderSoapEnvelopRequest refundOrderSoapEnvelopRequest = dcepRefundOrderExtService.buildRefundRequestParam(
            refundOrderBO);
        Request request = dcepRefundOrderExtService.bulidRefundRequest(refundOrderSoapEnvelopRequest);
        GenericRspDTO<Response> genericRspDTO;
        RefundOrderRsp refundOrderRsp;
        try {
            genericRspDTO = dceppayCgwOutClient.request(GenericDTO.newChildInstanceWithBody(GenericDTO.class, request));
            if (JudgeUtils.isNotSuccess(genericRspDTO)) {
                BusinessException.throwBusinessException(genericRspDTO);
            }
            refundOrderRsp = Optional.ofNullable(genericRspDTO)
                .map(GenericRspDTO::getBody)
                .map(Response::getResult)
                .filter(RefundOrderRsp.class::isInstance)
                .map(RefundOrderRsp.class::cast)
                .orElse(new RefundOrderRsp());
            RefundOrderRspBO refundOrderRspBO = dcepRefundOrderExtService.handleResponse(refundOrderRsp);
            refundOrderBO.setMessageIdentification(
                refundOrderSoapEnvelopRequest.getRefundOrderBody().getRequestGroupHeader().getMessageIdentification());
            dcepRefundOrderExtService.updateRefundAccept(refundOrderBO, refundOrderRspBO);
        } catch (Exception e) {
            ExceptionHandlerUtil.throwRequestDcepError(e);
        }
    }

    @Override
    public void handleSuccessResponse(UnifiedOrderReqBO unifiedOrderReqBO, MerchantRouteBO merchantRouteBO,
        UnifiedOrderSoapEnvelopResponse unifiedOrderSoapEnvelopResponse, UnifiedOrderRspBO unifiedOrderRspBO) {
        handlePayUrl(unifiedOrderReqBO, merchantRouteBO, unifiedOrderSoapEnvelopResponse, unifiedOrderRspBO);
    }

    private void handlePayUrl(UnifiedOrderReqBO unifiedOrderReqBO, MerchantRouteBO merchantRouteBO,
        UnifiedOrderSoapEnvelopResponse unifiedOrderSoapEnvelopResponse, UnifiedOrderRspBO unifiedOrderRspBO) {
        String invokeUrl = unifiedOrderSoapEnvelopResponse.getSoapBody().getInvokeInformation().getInvokeUrl();
        AppUrlContextBO contextBO = new AppUrlContextBO();
        contextBO.setAppId(dceppayConfig.getAppId());
        contextBO.setAcqAgtInstnId(dceppayConfig.getSenderCode());
        contextBO.setBizType(DcepConstants.BIZ_TYPE_APP);
        contextBO.setCdtrPtyId(dceppayConfig.getCreditorCode());
        contextBO.setMrchntNo(merchantRouteBO.getServiceProviderId());
        contextBO.setEncryptInfo(
            unifiedOrderSoapEnvelopResponse.getSoapBody().getInvokeInformation().getEncryptInformation());
        contextBO.setEncryptKey(unifiedOrderSoapEnvelopResponse.getHeader().getDgtlEnvlp());
        unifiedOrderRspBO.setPayUrl(getPayUrl(invokeUrl, contextBO, unifiedOrderReqBO));
    }

    private String getPayUrl(String invokeUrl, AppUrlContextBO contextBO, UnifiedOrderReqBO unifiedOrderReqBO) {
        AppUrlSourceInfo sourceInfo = new AppUrlSourceInfo(unifiedOrderReqBO.getOutOrderNo(),
            unifiedOrderReqBO.getOrderDate());
        ObjectMapper objectMapper = new ObjectMapper();
        String urlEncodedString = null;
        String sourceApplication = null;
        String sourceInfoJsonString = null;
        try {
            String jsonString = objectMapper.writeValueAsString(contextBO);
            // 对JSON字符串进行URL编码
            urlEncodedString = URLEncoder.encode(jsonString, StandardCharsets.UTF_8.toString());
            sourceApplication = URLEncoder.encode(unifiedOrderReqBO.getPageNotifyUrl(),
                StandardCharsets.UTF_8.toString());
            sourceInfoJsonString = URLEncoder.encode(objectMapper.writeValueAsString(sourceInfo),
                StandardCharsets.UTF_8.toString());
        } catch (JsonProcessingException e) {
            log.error("【APP拉起支付下单失败】业务信息转JSON字符串异常:{} ", e.getCause());
            BusinessException.throwBusinessException(MsgCodeEnum.APP_CONTEXT_INVALID);
        } catch (UnsupportedEncodingException e) {
            log.error("【APP拉起支付下单失败】url encode 编码异常:{} ", e.getCause());
            BusinessException.throwBusinessException(MsgCodeEnum.URL_ENCODE_ERROR, e.getCause(), "*");
        }
        StringBuffer payUrlBuffer = new StringBuffer();
        payUrlBuffer.append(invokeUrl)
            .append("?")
            .append("sourceApplication=").append(sourceApplication)
            .append("&")
            .append("sourceInfo=").append(sourceInfoJsonString)
            .append("&")
            .append("context=").append(urlEncodedString);
        return payUrlBuffer.toString();
    }
}
