package com.cmpay.dceppay.service.pay;

import com.cmpay.dceppay.bo.common.CheckResultBO;
import com.cmpay.dceppay.bo.pay.PayOrderDBBO;
import com.cmpay.dceppay.bo.refund.RefundOrderBO;
import com.cmpay.dceppay.bo.refund.RefundOrderRepBO;

/**
 * <AUTHOR>
 * @date 2024/9/5 10:26
 */
public interface IPayRefundOrderCheckService {
    /**
     * 退款订单请求校验
     * @param refundOrderRepBO
     * @return
     */
    CheckResultBO refundOrderAmountCheck(RefundOrderRepBO refundOrderRepBO);

    boolean isOrderOverRefundLimit(String orderDate);

    RefundOrderBO orderStatusCheck(PayOrderDBBO payOrderDBBO, RefundOrderRepBO refundOrderRepBO);
}
