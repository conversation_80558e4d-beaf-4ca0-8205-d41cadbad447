package com.cmpay.dceppay.bo.payment;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/10/22 14:05
 */
@Data
public class AppUrlContextBO {
    //{"appId":"xxx","acqAgtInstnId":"xxx","bizType":"BT01","cdtrPtyId":"xxx","encryptKey":"xxx","encryptInfo":"xxx"];
    //公管平台记录的应用AppID，由运营机构分配
    private  String appId;
    //填写受理服务机构金融机构编码
    private  String acqAgtInstnId;
    //BT01:H5拉起支付
    //BT02：外部App拉起支付
    //不区分支付并推送子钱包业务类型，与支付业务类型保持一致
    private  String bizType;
    //收款运营机构编号（金融机构代码）
    private  String cdtrPtyId;
    //收款运营机构分配的商户号
    private String mrchntNo;
    //dcep.302报文头中的数字信封（DgtlEnvlp）字段，受理服务机构透传即可
    private  String encryptKey;
    //填写dcep.302报文中的加密信息，受理服务机构无需解密，透传即可
    private  String encryptInfo;
}
