package com.cmpay.dceppay.service.channel.app.ext.impl;

import com.cmpay.channel.data.Request;
import com.cmpay.dceppay.bo.pay.MerchantRouteBO;
import com.cmpay.dceppay.bo.pay.PayOrderDBBO;
import com.cmpay.dceppay.bo.payment.AppUrlContextBO;
import com.cmpay.dceppay.bo.payment.AppUrlSourceInfo;
import com.cmpay.dceppay.bo.payment.UnifiedOrderReqBO;
import com.cmpay.dceppay.bo.payment.UnifiedOrderRspBO;
import com.cmpay.dceppay.channel.DcepChannelEnum;
import com.cmpay.dceppay.config.DceppayConfig;
import com.cmpay.dceppay.constant.dcep.DcepConstants;
import com.cmpay.dceppay.constant.dcep.DcepResponseStatusConstant;
import com.cmpay.dceppay.dto.dcep.common.RequestGroupHeader;
import com.cmpay.dceppay.dto.dcep.common.SoapHeader;
import com.cmpay.dceppay.dto.dcep.faut.Fault;
import com.cmpay.dceppay.dto.dcep.faut.FaultSoapEnvelopResponse;
import com.cmpay.dceppay.dto.dcep.unifiedorder.UnifiedOrderRsp;
import com.cmpay.dceppay.dto.dcep.unifiedorder.request.*;
import com.cmpay.dceppay.dto.dcep.unifiedorder.response.ResponseInformation;
import com.cmpay.dceppay.dto.dcep.unifiedorder.response.UnifiedOrderSoapEnvelopResponse;
import com.cmpay.dceppay.enums.MsgCodeEnum;
import com.cmpay.dceppay.enums.dcep.BusinessCategoryCodeEnum;
import com.cmpay.dceppay.enums.dcep.CreatingOrderTypeEnum;
import com.cmpay.dceppay.enums.dcep.DcepMessageTypeEnum;
import com.cmpay.dceppay.enums.dcep.TransactionTypeEnum;
import com.cmpay.dceppay.enums.pay.OrderStatusEnum;
import com.cmpay.dceppay.service.channel.app.ext.IDcepUnifiedOrderExtService;
import com.cmpay.dceppay.service.ext.DceppayParamService;
import com.cmpay.dceppay.service.pay.IPayOrderDBService;
import com.cmpay.dceppay.util.DateTimeUtil;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.utils.LemonUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;


/**
 * <AUTHOR>
 * @date 2024/9/3 14:09
 */
@Service
@Slf4j
public class DcepUnifiedOrderExtServiceImpl implements IDcepUnifiedOrderExtService {
    @Autowired
    private DceppayConfig dceppayConfig;
    @Autowired
    private IPayOrderDBService payOrderDBService;
    @Autowired
    private DceppayParamService dceppayParamService;

    @Override
    public Request bulidUnifiedOrderRequest(UnifiedOrderSoapEnvelopRequest unifiedOrderSoapEnvelopRequest) {
        Request request = new Request();
        request.setRequestId(LemonUtils.getRequestId());
        request.setRoute(DcepChannelEnum.DCEP_PAY);
        request.setBusiType(DcepChannelEnum.UNIFIED_ORDER.getName());
        request.setSource(DcepChannelEnum.DCEP_PAY);
        request.setTarget(unifiedOrderSoapEnvelopRequest);
        return request;
    }


    private PayOrderDBBO getPayOrderDBBO(MerchantRouteBO merchantRouteBO, UnifiedOrderSoapEnvelopRequest unifiedOrderSoapEnvelopRequest, UnifiedOrderReqBO unifiedOrderReqBO, UnifiedOrderRsp unifiedOrderRsp) {
        PayOrderDBBO order = new PayOrderDBBO();
        order.setTradeJrnNo(unifiedOrderReqBO.getTradeJrnNo());
        order.setOutOrderNo(unifiedOrderSoapEnvelopRequest.getSoapBody().getOrderInformation().getOutOrderNumber());
        order.setOrderDate(unifiedOrderReqBO.getOrderDate());
        order.setOrderTime(unifiedOrderReqBO.getOrderTime());
        order.setOrderTimeExpire(unifiedOrderReqBO.getOrderTimeExpire());
        order.setChannelCode(unifiedOrderReqBO.getChannelCode());
        order.setPayWay(unifiedOrderReqBO.getPayWay());
        order.setScene(unifiedOrderReqBO.getScene());
        order.setOrderType(unifiedOrderSoapEnvelopRequest.getSoapBody().getTransactionInformation().getCreatingOrderType());
        order.setAccountDate(unifiedOrderReqBO.getAccountDate());
        order.setProductName(unifiedOrderReqBO.getProductName());
        order.setProductDesc(unifiedOrderReqBO.getProductDesc());
        order.setOrderAmount(unifiedOrderReqBO.getTotalAmount());
        order.setBusType(unifiedOrderReqBO.getBusType());
        order.setOrgMerchantNo(unifiedOrderSoapEnvelopRequest.getSoapBody().getCreditorInformation().getMerchantNo());
        order.setMerchantNo(merchantRouteBO.getMerchantNo());
        order.setWalletId(unifiedOrderSoapEnvelopRequest.getSoapBody().getCreditorInformation().getMerchantWalletIdentification());
        order.setBizType(unifiedOrderSoapEnvelopRequest.getSoapBody().getTransactionInformation().getTransactionBizType());
        order.setBizCategory(unifiedOrderSoapEnvelopRequest.getSoapBody().getTransactionInformation().getTransactionCategoryCode());
        order.setTerminalNo(unifiedOrderSoapEnvelopRequest.getSoapBody().getCreditorInformation().getTerminalNumber());
        order.setTerminalIp(unifiedOrderSoapEnvelopRequest.getSoapBody().getCreditorInformation().getTerminalIP());
        order.setTerminalLocation(unifiedOrderSoapEnvelopRequest.getSoapBody().getMerchantTerminalInformation().getTerminalLocationInformation());
        order.setNotifyUrl(unifiedOrderReqBO.getNotifyUrl());
        order.setPageNotifyUrl(unifiedOrderReqBO.getPageNotifyUrl());
        order.setStatus(OrderStatusEnum.WAIT_PAY.name());
        order.setExtra(unifiedOrderReqBO.getExtra());
        order.setMessageIdentification(unifiedOrderSoapEnvelopRequest.getSoapBody().getRequestGroupHeader().getMessageIdentification());
        UnifiedOrderSoapEnvelopResponse unifiedOrderSoapEnvelopResponse = unifiedOrderRsp.getUnifiedOrderSoapEnvelopResponse();
        if (JudgeUtils.isNotNull(unifiedOrderSoapEnvelopResponse)
                && JudgeUtils.isNotNull(unifiedOrderSoapEnvelopResponse.getSoapBody())
                && JudgeUtils.isNotNull(unifiedOrderSoapEnvelopResponse.getSoapBody().getResponseInformation())
        ) {
            ResponseInformation responseInformation = unifiedOrderSoapEnvelopResponse.getSoapBody().getResponseInformation();
            String responseStatus = responseInformation.getResponsionStatus();
            if (JudgeUtils.equals(responseStatus, DcepResponseStatusConstant.UNIFIED_ORDER_RESPONSE_SUCCESS)) {
                order.setBankOrderNo(unifiedOrderSoapEnvelopResponse.getSoapBody().getResponseInformation().getOrderNumber());
            } else {
                order.setErrMsgCd(responseInformation.getRejectCode());
                order.setErrMsgInfo(responseInformation.getRejectInformation());
                order.setStatus(OrderStatusEnum.TRADE_FAIL.name());
            }
        } else {
            FaultSoapEnvelopResponse faultSoapEnvelopResponse = unifiedOrderRsp.getFaultSoapEnvelopResponse();
            if (JudgeUtils.isNotNull(faultSoapEnvelopResponse) && JudgeUtils.isNotNull(faultSoapEnvelopResponse.getFault())) {
                Fault fault = faultSoapEnvelopResponse.getFault();
                String errorCode = fault.getFaultCode();
                String errorMessage = fault.getFaultString() + (JudgeUtils.isNotBlank(fault.getDetail()) ? fault.getDetail() : "");
                order.setErrMsgCd(errorCode);
                order.setErrMsgInfo(errorMessage);
            }
        }
        return order;
    }

    @Override
    public void registerOrderSafely(MerchantRouteBO merchantRouteBO, UnifiedOrderSoapEnvelopRequest unifiedOrderSoapEnvelopRequest, UnifiedOrderReqBO unifiedOrderReqBO, UnifiedOrderRsp unifiedOrderRsp) {
        PayOrderDBBO payOrderDBBO = new PayOrderDBBO();
        try {
            payOrderDBBO = getPayOrderDBBO(merchantRouteBO, unifiedOrderSoapEnvelopRequest, unifiedOrderReqBO, unifiedOrderRsp);
            if (JudgeUtils.isNull(merchantRouteBO.getMessageIdentification())) {
                //订单不存在
                registerOrder(payOrderDBBO);
            }
        } catch (Exception e) {
            log.error("APP拉起支付下单数据登记失败,失败原因：{},订单数据：{}", e.getCause(), payOrderDBBO);
        }
    }

    @Override
    public UnifiedOrderRspBO handleResponse(MerchantRouteBO merchantRouteBO, UnifiedOrderReqBO unifiedOrderReqBO, UnifiedOrderSoapEnvelopRequest unifiedOrderSoapEnvelopRequest, UnifiedOrderRsp unifiedOrderRsp) {
        isNullResponse(unifiedOrderRsp);
        UnifiedOrderRspBO unifiedOrderRspBO = new UnifiedOrderRspBO();
        unifiedOrderRspBO.setTotalAmount(unifiedOrderReqBO.getTotalAmount());
        unifiedOrderRspBO.setOutOrderNo(unifiedOrderReqBO.getOutOrderNo());
        unifiedOrderRspBO.setExtra(unifiedOrderReqBO.getExtra());

        FaultSoapEnvelopResponse faultSoapEnvelopResponse = unifiedOrderRsp.getFaultSoapEnvelopResponse();
        if (JudgeUtils.isNotNull(faultSoapEnvelopResponse) && JudgeUtils.isNotNull(faultSoapEnvelopResponse.getFault())) {
            handleFaultResponse(faultSoapEnvelopResponse.getFault(), unifiedOrderRspBO);
        } else {
            UnifiedOrderSoapEnvelopResponse unifiedOrderSoapEnvelopResponse = unifiedOrderRsp.getUnifiedOrderSoapEnvelopResponse();
            ResponseInformation responseInformation = unifiedOrderSoapEnvelopResponse.getSoapBody().getResponseInformation();
            String responseStatus = responseInformation.getResponsionStatus();
            unifiedOrderRspBO.setBankOrderNo(unifiedOrderSoapEnvelopResponse.getSoapBody().getResponseInformation().getOrderNumber());
            unifiedOrderRspBO.setBankOrderTime(unifiedOrderSoapEnvelopResponse.getSoapBody().getResponseInformation().getOrderTime());
            if (JudgeUtils.equals(responseStatus, DcepResponseStatusConstant.UNIFIED_ORDER_RESPONSE_SUCCESS)) {
                handleSuccessResponse(unifiedOrderReqBO, merchantRouteBO, unifiedOrderSoapEnvelopResponse, unifiedOrderRspBO);
            } else if (JudgeUtils.equals(responseStatus, DcepResponseStatusConstant.UNIFIED_ORDER_RESPONSE_FAIL)) {
                handleFailResponse(responseInformation, unifiedOrderRspBO);
            } else {
                handleInvalidResponse(unifiedOrderRspBO, responseInformation, responseStatus);
            }
        }
        return unifiedOrderRspBO;
    }

    private static void handleInvalidResponse(UnifiedOrderRspBO unifiedOrderRspBO, ResponseInformation responseInformation, String responseStatus) {
        unifiedOrderRspBO.setErrMsgCd(responseInformation.getRejectCode());
        unifiedOrderRspBO.setErrMsgInfo(responseInformation.getRejectInformation());
        BusinessException.throwBusinessException(MsgCodeEnum.UNIFIED_ORDER_RESPONSE_INVALID);
    }

    private static void handleFailResponse(ResponseInformation responseInformation, UnifiedOrderRspBO unifiedOrderRspBO) {
        String messageCd = responseInformation.getRejectCode();
        String errorMessageInfo = responseInformation.getRejectInformation();
        unifiedOrderRspBO.setErrMsgCd(messageCd);
        unifiedOrderRspBO.setErrMsgInfo(errorMessageInfo);
        BusinessException.throwBusinessException(MsgCodeEnum.UNIFIED_ORDER_RESPONSE_FAIL);
    }

    private void handleSuccessResponse(UnifiedOrderReqBO unifiedOrderReqBO, MerchantRouteBO merchantRouteBO, UnifiedOrderSoapEnvelopResponse unifiedOrderSoapEnvelopResponse, UnifiedOrderRspBO unifiedOrderRspBO) {
        String invokeUrl = unifiedOrderSoapEnvelopResponse.getSoapBody().getInvokeInformation().getInvokeUrl();
        AppUrlContextBO contextBO = new AppUrlContextBO();
        contextBO.setAppId(dceppayConfig.getAppId());
        contextBO.setAcqAgtInstnId(dceppayConfig.getSenderCode());
        contextBO.setBizType(DcepConstants.BIZ_TYPE_APP);
        contextBO.setCdtrPtyId(dceppayConfig.getCreditorCode());
        contextBO.setMrchntNo(merchantRouteBO.getServiceProviderId());
        contextBO.setEncryptInfo(unifiedOrderSoapEnvelopResponse.getSoapBody().getInvokeInformation().getEncryptInformation());
        contextBO.setEncryptKey(unifiedOrderSoapEnvelopResponse.getHeader().getDgtlEnvlp());
        unifiedOrderRspBO.setPayUrl(getPayUrl(invokeUrl, contextBO, unifiedOrderReqBO));
    }

    private static void handleFaultResponse(Fault fault, UnifiedOrderRspBO unifiedOrderRspBO) {
        String errorCode = fault.getFaultCode();
        String errorMessage = fault.getFaultString() + (JudgeUtils.isNotBlank(fault.getDetail()) ? fault.getDetail() : "");
        unifiedOrderRspBO.setErrMsgCd(errorCode);
        unifiedOrderRspBO.setErrMsgInfo(errorMessage);
        BusinessException.throwBusinessException(MsgCodeEnum.UNIFIED_ORDER_RESPONSE_FAULT);
    }

    private static void isNullResponse(UnifiedOrderRsp unifiedOrderRsp) {
        if (JudgeUtils.isNull(unifiedOrderRsp)
                || (JudgeUtils.isNull(unifiedOrderRsp.getUnifiedOrderSoapEnvelopResponse())
                && JudgeUtils.isNull(unifiedOrderRsp.getFaultSoapEnvelopResponse()))
        ) {
            log.error("请求互联互通统一下单响应为空：{}", unifiedOrderRsp);
            BusinessException.throwBusinessException(MsgCodeEnum.UNIFIED_ORDER_RESPONSE_NULL);
        }
    }


    @Override
    public UnifiedOrderSoapEnvelopRequest buildUnifiedOrderRequestParam(MerchantRouteBO merchantRouteBO, UnifiedOrderReqBO unifiedOrderReqBO) {
        String walletId = merchantRouteBO.getWalletId();
        if (JudgeUtils.isBlank(walletId)) {
            walletId = dceppayConfig.getWalletId();
        }
        UnifiedOrderSoapEnvelopRequest unifiedOrderSoapEnvelopRequest = new UnifiedOrderSoapEnvelopRequest();
        //报文头
        String messageIdentification = merchantRouteBO.getMessageIdentification();
        if (JudgeUtils.isNull(messageIdentification)) {
            messageIdentification = dceppayParamService.getMessageIdentification(DcepMessageTypeEnum.UNIFIED_ORDER_REQUEST.getValue(), walletId);
        }
        SoapHeader soapHeader = dceppayParamService.getCommonSoapHear(DcepMessageTypeEnum.UNIFIED_ORDER_REQUEST.getMessageType(), messageIdentification);
        BeanUtils.copyProperties(unifiedOrderSoapEnvelopRequest, soapHeader);

        UnifiedOrderBody unifiedOrderBody = new UnifiedOrderBody();
        //业务头组件
        RequestGroupHeader requestGroupHeader = dceppayParamService.getRequestGroupHeader(DcepMessageTypeEnum.UNIFIED_ORDER_REQUEST.getMessageType(),messageIdentification);
        unifiedOrderBody.setRequestGroupHeader(requestGroupHeader);
        //交易信息
        setTransactionInfo(merchantRouteBO, unifiedOrderReqBO, unifiedOrderBody);
        //受理方机构信息
        AcquiringAgentInformation acquiringAgentInformation = new AcquiringAgentInformation();
        acquiringAgentInformation.setAcquiringAgentInstitutionIdentification(dceppayConfig.getSenderCode());
        acquiringAgentInformation.setAcquiringAgentName(dceppayConfig.getSenderName());
        unifiedOrderBody.setAcquiringAgentInformation(acquiringAgentInformation);
        //收款运营机构信息
        setCreditInfo(merchantRouteBO, unifiedOrderBody);
        //商户受理终端信息
        setMerchantTerminalInfo(unifiedOrderBody);
        //订单信息
        setOrderInfo(unifiedOrderReqBO, unifiedOrderBody);
        //附加信息
        unifiedOrderBody.setAttach(unifiedOrderReqBO.getExtra());
        unifiedOrderSoapEnvelopRequest.setSoapBody(unifiedOrderBody);

        return unifiedOrderSoapEnvelopRequest;
    }

    private static void setOrderInfo(UnifiedOrderReqBO unifiedOrderReqBO, UnifiedOrderBody unifiedOrderBody) {
        OrderInformation orderInformation = new OrderInformation();
        orderInformation.setOutOrderTime(DateTimeUtil.getCurrentISODateTime());
        orderInformation.setOutOrderNumber(unifiedOrderReqBO.getOutOrderNo());
        orderInformation.setMerchantOrderNumber(unifiedOrderReqBO.getOutOrderNo());
        orderInformation.setGoodsName(unifiedOrderReqBO.getProductName());
        orderInformation.setPlatformName(DcepConstants.PLATFORM_NAME);
        orderInformation.setTradePlace(DcepConstants.TERMINAL_DEVICE_INFORMATION_URL);
        orderInformation.setOrderTimeExpire(DateTimeUtil.getISODateTime(unifiedOrderReqBO.getOrderTimeExpire()));
        unifiedOrderBody.setOrderInformation(orderInformation);
    }

    private void setMerchantTerminalInfo(UnifiedOrderBody unifiedOrderBody) {
        MerchantTerminalInformation merchantTerminalInformation = new MerchantTerminalInformation();
        merchantTerminalInformation.setTerminalLocationInformation(DcepConstants.TERMINAL_DEVICE_INFORMATION_URL);
        merchantTerminalInformation.setMerchantBusinessAddress(DcepConstants.MERCHANT_BUSINESS_ADDRESS);
        merchantTerminalInformation.setMerchantLocationCode(DcepConstants.MERCHANT_LOCATION_CODE);
        unifiedOrderBody.setMerchantTerminalInformation(merchantTerminalInformation);
    }

    private void setCreditInfo(MerchantRouteBO merchantRouteBO, UnifiedOrderBody unifiedOrderBody) {
        CreditorInformation creditorInformation = new CreditorInformation();
        creditorInformation.setCreditorInstitutionIdentification(dceppayConfig.getCreditorCode());
        creditorInformation.setMerchantWalletIdentification(merchantRouteBO.getWalletId());
        creditorInformation.setMerchantProperty(DcepConstants.MERCHANT_PROPERTY_MP03);
        creditorInformation.setMerchantNo(merchantRouteBO.getOperatorMerchantId());
        creditorInformation.setMerchantName(merchantRouteBO.getMerchantName());
        creditorInformation.setMerchantAbbrName(merchantRouteBO.getMerchantShortName());
        creditorInformation.setMerchantCategoryCode(merchantRouteBO.getMerchantCategory());
        creditorInformation.setMerchantIDType(merchantRouteBO.getMerchantLicenseType());
        creditorInformation.setMerchantIDNumber(merchantRouteBO.getMerchantLicense());
        //受理服务机构内部接口编号
        creditorInformation.setTerminalNumber(DcepMessageTypeEnum.UNIFIED_ORDER_REQUEST.getMessageType());
        creditorInformation.setTerminalIP(LemonUtils.getClientIp());
        unifiedOrderBody.setCreditorInformation(creditorInformation);
    }


    private void setTransactionInfo(MerchantRouteBO merchantRouteBO, UnifiedOrderReqBO unifiedOrderReqBO, UnifiedOrderBody unifiedOrderBody) {
        TransactionInformation transactionInformation = new TransactionInformation();
        transactionInformation.setTransactionType(TransactionTypeEnum.APP.getValue());
        transactionInformation.setCreatingOrderType(CreatingOrderTypeEnum.APPLY_API_ADDRESS.getCode());
        transactionInformation.setTransactionAmount(unifiedOrderReqBO.getTotalAmount());
        transactionInformation.setTransactionBizType(BusinessCategoryCodeEnum.PAYMENT.getCode());
        transactionInformation.setTransactionCategoryCode(BusinessCategoryCodeEnum.PAYMENT.getCode() + DcepConstants.BUSINESS_TYPE_EXTEND_CODE + merchantRouteBO.getMerchantCategory());
        unifiedOrderBody.setTransactionInformation(transactionInformation);
    }

    private int registerOrder(PayOrderDBBO order) {
        return payOrderDBService.insertOrder(order);
    }

    private String getPayUrl(String invokeUrl, AppUrlContextBO contextBO, UnifiedOrderReqBO unifiedOrderReqBO) {
        AppUrlSourceInfo sourceInfo = new AppUrlSourceInfo(unifiedOrderReqBO.getOutOrderNo(), unifiedOrderReqBO.getOrderDate());
        ObjectMapper objectMapper = new ObjectMapper();
        String urlEncodedString = null;
        String sourceApplication = null;
        String sourceInfoJsonString = null;
        try {
            String jsonString = objectMapper.writeValueAsString(contextBO);
            // 对JSON字符串进行URL编码
            urlEncodedString = URLEncoder.encode(jsonString, StandardCharsets.UTF_8.toString());
            sourceApplication = URLEncoder.encode(unifiedOrderReqBO.getPageNotifyUrl(), StandardCharsets.UTF_8.toString());
            sourceInfoJsonString = URLEncoder.encode(objectMapper.writeValueAsString(sourceInfo), StandardCharsets.UTF_8.toString());
        } catch (JsonProcessingException e) {
            log.error("【APP拉起支付下单失败】业务信息转JSON字符串异常:{} ", e.getCause());
            BusinessException.throwBusinessException(MsgCodeEnum.APP_CONTEXT_INVALID);
        } catch (UnsupportedEncodingException e) {
            log.error("【APP拉起支付下单失败】url encode 编码异常:{} ", e.getCause());
            BusinessException.throwBusinessException(MsgCodeEnum.URL_ENCODE_ERROR, e.getCause(), "*");
        }
        StringBuffer payUrlBuffer = new StringBuffer();
        payUrlBuffer.append(invokeUrl)
                .append("?")
                .append("sourceApplication=").append(sourceApplication)
                .append("&")
                .append("sourceInfo=").append(sourceInfoJsonString)
                .append("&")
                .append("context=").append(urlEncodedString);
        return payUrlBuffer.toString();
    }
}
