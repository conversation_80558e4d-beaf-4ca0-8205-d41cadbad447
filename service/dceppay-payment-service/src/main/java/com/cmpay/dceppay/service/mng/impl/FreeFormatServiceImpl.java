package com.cmpay.dceppay.service.mng.impl;

import com.cmpay.channel.data.Request;
import com.cmpay.channel.data.Response;
import com.cmpay.dceppay.bo.mng.FreeFormatBO;
import com.cmpay.dceppay.client.DceppayCgwOutClient;
import com.cmpay.dceppay.dto.dcep.freeformat.FreeFormatRsp;
import com.cmpay.dceppay.dto.dcep.freeformat.request.FreeFormatSoapEnvelopRequest;
import com.cmpay.dceppay.service.mng.IFreeFormatService;
import com.cmpay.dceppay.service.mng.ext.IFreeFormatExtService;
import com.cmpay.dceppay.utils.ExceptionHandlerUtil;
import com.cmpay.framework.data.request.GenericDTO;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/11/1 16:38
 */
@Service
public class FreeFormatServiceImpl  implements IFreeFormatService {
    @Autowired
    private IFreeFormatExtService freeFormatExtService;
    @Autowired
    private DceppayCgwOutClient dceppayCgwOutClient;
    @Override
    public void sendFreeFormat(FreeFormatBO freeFormatBO) {
        FreeFormatSoapEnvelopRequest freeFormatSoapEnvelopRequest = freeFormatExtService.buildRequestParam(freeFormatBO);
        Request request = freeFormatExtService.bulidRequest(freeFormatSoapEnvelopRequest);
        //请求网关
        GenericRspDTO<Response> genericRspDTO;
        try {
            genericRspDTO = dceppayCgwOutClient.request(GenericDTO.newChildInstanceWithBody(GenericDTO.class, request));
            //处理网关响应对象
            if (JudgeUtils.isNotSuccess(genericRspDTO)) {
                BusinessException.throwBusinessException(genericRspDTO);
            }
            FreeFormatRsp freeFormatRsp = Optional.of(genericRspDTO)
                    .map(GenericRspDTO::getBody)
                    .map(Response::getResult).map(x -> (FreeFormatRsp) x)
                    .orElse(new FreeFormatRsp());
            freeFormatExtService.handleResponse(freeFormatRsp, freeFormatBO);
        } catch (Exception e) {
            ExceptionHandlerUtil.throwRequestDcepError(e);
        }
    }
}
