package com.cmpay.dceppay.stream.handler;

import com.cmpay.dceppay.bo.pay.PayOrderDBBO;
import com.cmpay.dceppay.service.notify.INotifySendService;
import com.cmpay.lemon.framework.data.DefaultCmdDTO;
import com.cmpay.lemon.framework.stream.MessageHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Created on 2019/05/13
 *
 * <AUTHOR>
 */
@Component("notifyHandler")
@Slf4j
public class NotifyHandler implements MessageHandler<PayOrderDBBO, DefaultCmdDTO<PayOrderDBBO>> {

    @Autowired
    private INotifySendService notifySendService;

    /**
     * 支付成功 通知应用
     *
     * @param cmpayCmdDTO
     */
    @Override
    public void onMessageReceive(DefaultCmdDTO<PayOrderDBBO> cmpayCmdDTO) {
        PayOrderDBBO payOrderDBBO = cmpayCmdDTO.getBody();
        notifySendService.sendPaymentSuccessNotify(payOrderDBBO);
    }
}
