package com.cmpay.dceppay.service.notify;

import com.cmpay.dceppay.bo.notify.PaymentNotifyBO;
import com.cmpay.dceppay.bo.notify.PaymentNotifyRspBO;
import com.cmpay.dceppay.bo.notify.RefundNotifyBO;
import com.cmpay.dceppay.bo.notify.RefundNotifyRspBO;

/**
 * <AUTHOR>
 * @date 2024/8/30 17:19
 * 接收支付机构通知处理类
 */
public interface INotifyReceiveService {
    /**
     * 支付结果通知处理
     *
     * @param paymentNotifyBO
     * @return
     */

    PaymentNotifyRspBO paymentNotify(PaymentNotifyBO paymentNotifyBO);

    /**
     * 退款结果通知处理
     *
     * @param refundNotifyBO
     * @return
     */
    RefundNotifyRspBO refundNotify(RefundNotifyBO refundNotifyBO);
}
