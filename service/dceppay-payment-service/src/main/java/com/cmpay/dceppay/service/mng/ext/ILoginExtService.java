package com.cmpay.dceppay.service.mng.ext;

import com.cmpay.channel.data.Request;
import com.cmpay.dceppay.bo.mng.DcepLoginBO;
import com.cmpay.dceppay.dto.dcep.login.LoginRsp;
import com.cmpay.dceppay.dto.dcep.login.request.LoginSoapEnvelopRequest;

/**
 * <AUTHOR>
 * @date 2024/11/1 10:13
 */
public interface ILoginExtService {
    LoginSoapEnvelopRequest bulidLoginParam(String name);

    Request bulidLoginRequest(LoginSoapEnvelopRequest loginSoapEnvelopRequest);

    void handleLoginInResponse(DcepLoginBO dcepLoginBO, LoginRsp loginRsp);

    void handleLoginOutResponse(DcepLoginBO dcepLoginBO, LoginRsp loginRsp);
}
