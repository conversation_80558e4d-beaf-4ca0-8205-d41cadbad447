package com.cmpay.dceppay.service.channel.app.ext.impl;

import com.cmpay.channel.data.Request;
import com.cmpay.dceppay.bo.refundquery.RefundOrderQueryBO;
import com.cmpay.dceppay.bo.refundquery.RefundOrderResultBO;
import com.cmpay.dceppay.channel.DcepChannelEnum;
import com.cmpay.dceppay.config.DceppayConfig;
import com.cmpay.dceppay.constant.dcep.DcepResponseStatusConstant;
import com.cmpay.dceppay.dto.dcep.common.*;
import com.cmpay.dceppay.dto.dcep.faut.Fault;
import com.cmpay.dceppay.dto.dcep.faut.FaultSoapEnvelopResponse;
import com.cmpay.dceppay.dto.dcep.refundorderquery.RefundOrderQueryRsp;
import com.cmpay.dceppay.dto.dcep.refundorderquery.request.RefundOrderQueryBody;
import com.cmpay.dceppay.dto.dcep.refundorderquery.request.RefundOrderQueryOriginalGroupHeader;
import com.cmpay.dceppay.dto.dcep.refundorderquery.request.RefundOrderQuerySoapEnvelopRequest;
import com.cmpay.dceppay.dto.dcep.refundorderquery.response.RefundOrderQuerySoapEnvelopResponse;
import com.cmpay.dceppay.dto.dcep.refundorderquery.response.RefundQueryResponse;
import com.cmpay.dceppay.enums.MsgCodeEnum;
import com.cmpay.dceppay.enums.dcep.DcepMessageTypeEnum;
import com.cmpay.dceppay.enums.pay.OrderStatusEnum;
import com.cmpay.dceppay.service.channel.app.ext.IRefundQueryExtService;
import com.cmpay.dceppay.service.ext.DceppayParamService;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.utils.LemonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/9/10 18:55
 */
@Service
@Slf4j
public class RefundQueryExtServiceImpl implements IRefundQueryExtService {
    @Autowired
    private DceppayConfig dceppayConfig;
    @Autowired
    private DceppayParamService dceppayParamService;

    @Override
    public RefundOrderQuerySoapEnvelopRequest buildRefundQueryRequestParam(RefundOrderQueryBO refundOrderQueryBO) {
        RefundOrderQuerySoapEnvelopRequest refundOrderQuerySoapEnvelopRequest = new RefundOrderQuerySoapEnvelopRequest();
        String walletId = refundOrderQueryBO.getWalletId();
        if (JudgeUtils.isBlank(walletId)) {
            walletId = dceppayConfig.getWalletId();
        }
        //报文头
        String messageIdentification = dceppayParamService.getMessageIdentification(DcepMessageTypeEnum.REFUND_RESULT_QUERY_REQUEST.getValue(), walletId);
        SoapHeader soapHeader = dceppayParamService.getCommonSoapHear(DcepMessageTypeEnum.REFUND_RESULT_QUERY_REQUEST.getMessageType(), messageIdentification);
        BeanUtils.copyProperties(refundOrderQuerySoapEnvelopRequest, soapHeader);

        RefundOrderQueryBody queryBody = new RefundOrderQueryBody();
        RequestGroupHeader requestGroupHeader = dceppayParamService.getRequestGroupHeader(DcepMessageTypeEnum.REFUND_RESULT_QUERY_REQUEST.getMessageType(),messageIdentification);
        queryBody.setRequestGroupHeader(requestGroupHeader);

        RefundOrderQueryOriginalGroupHeader orderQueryOriginalGroupHeader = new RefundOrderQueryOriginalGroupHeader();
        orderQueryOriginalGroupHeader.setOriginalMessageIdentification(refundOrderQueryBO.getOriginalMessageIdentification());
        orderQueryOriginalGroupHeader.setOriginalInstructingParty(dceppayConfig.getSenderCode());
        orderQueryOriginalGroupHeader.setOriginalMessageType(DcepMessageTypeEnum.ORDER_REFUND_REQUEST.getMessageType());
        orderQueryOriginalGroupHeader.setOriginalMerchantNo(refundOrderQueryBO.getMerchantId());
        orderQueryOriginalGroupHeader.setOriginalRefundNo(refundOrderQueryBO.getBankRefundNo());
        orderQueryOriginalGroupHeader.setOriginalOutRefundNo(refundOrderQueryBO.getOutRefundNo());

        queryBody.setOriginalGroupHeader(orderQueryOriginalGroupHeader);

        refundOrderQuerySoapEnvelopRequest.setSoapBody(queryBody);
        return refundOrderQuerySoapEnvelopRequest;
    }

    @Override
    public Request bulidRefundQueryRequest(RefundOrderQuerySoapEnvelopRequest refundOrderQuerySoapEnvelopRequest) {
        Request request = new Request();
        request.setRequestId(LemonUtils.getRequestId());
        request.setRoute(DcepChannelEnum.DCEP_PAY);
        request.setBusiType(DcepChannelEnum.REFUND_QUERY.getName());
        request.setSource(DcepChannelEnum.DCEP_PAY);
        request.setTarget(refundOrderQuerySoapEnvelopRequest);
        return request;
    }

    @Override
    public RefundOrderResultBO handleResponse(RefundOrderQueryBO refundOrderQueryBO, RefundOrderQueryRsp refundOrderQueryRsp) {
        RefundOrderResultBO refundOrderResultBO = new RefundOrderResultBO();
        if (isResponseEmpty(refundOrderQueryRsp)) {
            BusinessException.throwBusinessException(MsgCodeEnum.REFUND_QUERY_RESPONSE_NULL);
        }
        FaultSoapEnvelopResponse faultSoapEnvelopResponse = refundOrderQueryRsp.getFaultSoapEnvelopResponse();
        if (JudgeUtils.isNotNull(faultSoapEnvelopResponse) && JudgeUtils.isNotNull(faultSoapEnvelopResponse.getFault())) {
            Fault fault = faultSoapEnvelopResponse.getFault();
            String errorCode = fault.getFaultCode();
            String errorMessage = fault.getFaultString() + (JudgeUtils.isNotBlank(fault.getDetail()) ? fault.getDetail() : "");
            handleQueryFault(refundOrderResultBO, errorCode, errorMessage);
        } else {
            RefundOrderQuerySoapEnvelopResponse refundOrderQuerySoapEnvelopResponse = refundOrderQueryRsp.getRefundOrderQuerySoapEnvelopResponse();
            RefundQueryResponse orderQueryResponse = refundOrderQuerySoapEnvelopResponse.getSoapBody();
            OrderQueryReference orderQueryReference = orderQueryResponse.getOrderQueryReference();
            String queryResult = orderQueryReference.getQueryResult();
            switch (queryResult) {
                case DcepResponseStatusConstant.REFUND_ORDER_QUERY_SUCCESS:
                    handleQuerySuccess(refundOrderResultBO, orderQueryResponse);
                    break;
                case DcepResponseStatusConstant.REFUND_ORDER_QUERY_FAIL:
                    handleQueryFail(refundOrderResultBO, refundOrderQuerySoapEnvelopResponse);
                    break;
                default:
                    log.error("请求互联互通退款结果查询接口查询处理状态非法,查询处理状态值：{}", queryResult);
                    break;
            }
        }
        return refundOrderResultBO;
    }


    private boolean isResponseEmpty(RefundOrderQueryRsp refundOrderQueryRsp) {
        return JudgeUtils.isNullAny(refundOrderQueryRsp) ||
                (JudgeUtils.isNull(refundOrderQueryRsp.getRefundOrderQuerySoapEnvelopResponse())
                        && JudgeUtils.isNull(refundOrderQueryRsp.getFaultSoapEnvelopResponse())
                );
    }

    private void handleQuerySuccess(RefundOrderResultBO refundOrderResultBO, RefundQueryResponse orderQueryResponse) {
        QuerySuccessResult querySuccessResult = orderQueryResponse.getQuerySuccessResult();
        if (JudgeUtils.isNull(querySuccessResult)) {
            setErrorMessage(refundOrderResultBO, MsgCodeEnum.REFUND_ORDER_QUERY_BUSINESS_RESPONSE_NULL);
            log.error("请求互联互通退款结果查询接口受理成功，返回应答的原业务信息为空");
            return;
        }
        String tranResult = querySuccessResult.getTranResult();
        switch (tranResult) {
            case DcepResponseStatusConstant.PREFUND_ORDER_SUCCESS:
                setCommonResponse(refundOrderResultBO, orderQueryResponse);
                refundOrderResultBO.setRefundStatus(OrderStatusEnum.REFUND_SUCCESS.name());
                break;
            case DcepResponseStatusConstant.REFUND_ORDER_FAIL:
                setCommonResponse(refundOrderResultBO, orderQueryResponse);
                refundOrderResultBO.setRefundStatus(OrderStatusEnum.REFUND_FAIL.name());
                break;
            case DcepResponseStatusConstant.PREFUND_ORDER_WAIT:
                setCommonResponse(refundOrderResultBO, orderQueryResponse);
                log.warn("请求互联互通退款结果查询接口成功,{}：处理中（受理成功待退款）",DcepResponseStatusConstant.PREFUND_ORDER_WAIT);
                break;
            default:
                log.error("请求互联互通退款结果查询接口响应非法,原业务状态值：{}", tranResult);
                break;
        }
    }


    private void handleQueryFail(RefundOrderResultBO refundOrderResultBO, RefundOrderQuerySoapEnvelopResponse response) {
        OptionError optionError = response.getSoapBody().getOptionError();
        if (JudgeUtils.isNotNull(optionError)) {
            setErrorMessage(refundOrderResultBO, optionError.getRejectCode(), optionError.getRejectInformation());
            log.error("请求互联互通退款结果查询接口失败：{}", response);
        }
    }

    private void handleQueryFault(RefundOrderResultBO refundOrderResultBO, String errorCode, String errorMessage) {
        setErrorMessage(refundOrderResultBO, errorCode, errorMessage);
        log.error("请求互联互通退款结果查询接口失败：{}", errorCode + errorMessage);
    }

    private void setCommonResponse(RefundOrderResultBO refundOrderResultBO, RefundQueryResponse orderQueryResponse) {
        refundOrderResultBO.setOutRefundNo(orderQueryResponse.getOriginalRefundTransactionInformation().getOriginalOutRefundNumber());
        refundOrderResultBO.setFinishDateTime(orderQueryResponse.getOriginalRefundTransactionInformation().getOriginalTransactionFinishTime());
        refundOrderResultBO.setRefundAmount(orderQueryResponse.getOriginalRefundTransactionInformation().getOriginalTransactionAmount());
        refundOrderResultBO.setBankRefundNo(orderQueryResponse.getOriginalRefundTransactionInformation().getOriginalOrderNumber());
    }

    private void setErrorMessage(RefundOrderResultBO refundOrderResultBO, MsgCodeEnum msgCodeEnum) {
        refundOrderResultBO.setErrMsgCd(msgCodeEnum.getMsgCd());
        refundOrderResultBO.setErrMsgInfo(msgCodeEnum.getMsgInfo());
    }

    private void setErrorMessage(RefundOrderResultBO paymentOrderQueryRspBO, String errorCode, String errorMessage) {
        paymentOrderQueryRspBO.setErrMsgCd(errorCode);
        paymentOrderQueryRspBO.setErrMsgInfo(errorMessage);
    }


}
