package com.cmpay.dceppay.service.pay.impl;

import com.cmpay.dceppay.bo.common.CheckResultBO;
import com.cmpay.dceppay.bo.payment.UnifiedOrderReqBO;
import com.cmpay.dceppay.enums.MsgCodeEnum;
import com.cmpay.dceppay.enums.pay.ChannelCodeEnum;
import com.cmpay.dceppay.enums.pay.PayWayEnum;
import com.cmpay.dceppay.service.pay.IPayOrderCheckService;
import com.cmpay.lemon.common.utils.JudgeUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

import static com.cmpay.dceppay.utils.AmountUtils.isValidAmount;


/**
 * <AUTHOR>
 * @date 2024/9/4 13:43
 */
@Service
public class PayOrderCheckServiceImpl implements IPayOrderCheckService {

    public CheckResultBO unifiedOrderRequestCheck(UnifiedOrderReqBO unifiedOrderReqBO) {
        String payWay = unifiedOrderReqBO.getPayWay() + unifiedOrderReqBO.getScene();
        CheckResultBO checkResult = checkPayWay(payWay);
        if (checkResult.isCheckFlag()) {
            checkResult = checkOrderAmount(unifiedOrderReqBO.getTotalAmount());
        }
        if (checkResult.isCheckFlag()) {
            if (JudgeUtils.isBlank(unifiedOrderReqBO.getChannelCode())) {
                unifiedOrderReqBO.setChannelCode(ChannelCodeEnum.PAY_CENTER.getChannelCode());
                checkResult = checkChannelCode(unifiedOrderReqBO.getChannelCode());
            }
        }
        return checkResult;
    }


    /**
     * 订单金额检查
     *
     * @param totalAmount
     * @return
     */
    private CheckResultBO checkOrderAmount(BigDecimal totalAmount) {
        CheckResultBO result = new CheckResultBO();
        // 检查金额是否为null、大于0且小数位为2
        boolean isValidAmount = isValidAmount(totalAmount);
        result.setCheckFlag(isValidAmount);
        if (!isValidAmount) {
            result.setMessageCode(MsgCodeEnum.ORDER_TOTAL_AMOUNT_INVALID.getMsgCd());
        }
        return result;
    }

    /**
     * 支付方式检查
     *
     * @param payWay
     * @return
     */
    private CheckResultBO checkPayWay(String payWay) {
        CheckResultBO unifiedOrderRequestCheckResult = new CheckResultBO();
        PayWayEnum payWayEnum = PayWayEnum.getPayWayEnum(payWay);
        boolean isValidPayWay = !JudgeUtils.isNull(payWayEnum);
        unifiedOrderRequestCheckResult.setCheckFlag(isValidPayWay);
        if (!isValidPayWay) {
            unifiedOrderRequestCheckResult.setMessageCode(MsgCodeEnum.PAY_WAY_ERROR.getMsgCd());
        }
        return unifiedOrderRequestCheckResult;
    }

    private CheckResultBO checkChannelCode(String channelCode) {
        CheckResultBO unifiedOrderRequestCheckResult = new CheckResultBO();
        ChannelCodeEnum channelCodeEnum = ChannelCodeEnum.getChannelCodeEnum(channelCode);
        boolean isValidChannel = !JudgeUtils.isNull(channelCodeEnum);
        unifiedOrderRequestCheckResult.setCheckFlag(isValidChannel);
        if (!isValidChannel) {
            unifiedOrderRequestCheckResult.setMessageCode(MsgCodeEnum.CHANNEl_CODE_INVALID.getMsgCd());
        }
        return unifiedOrderRequestCheckResult;
    }
}
