package com.cmpay.dceppay.service.channel.app.ext;

import com.cmpay.channel.data.Request;
import com.cmpay.dceppay.bo.closeorder.CloseOrderBO;
import com.cmpay.dceppay.bo.closeorder.CloseOrderRspBO;
import com.cmpay.dceppay.bo.pay.PayOrderDBBO;
import com.cmpay.dceppay.dto.dcep.closeorder.CloseOrderRsp;
import com.cmpay.dceppay.dto.dcep.closeorder.request.CloseOrderSoapEnvelopRequest;
import com.cmpay.dceppay.dto.dcep.closeorder.response.CloseOrderSoapEnvelopResponse;

/**
 * <AUTHOR>
 * @date 2024/9/5 15:43
 */
public interface ICloseOrderExtService {
    CloseOrderSoapEnvelopRequest buildOrderCloseRequestParam(PayOrderDBBO payOrder, CloseOrderBO closeOrderBO);

    Request bulidRefundRequest(CloseOrderSoapEnvelopRequest closeOrderSoapEnvelopRequest);

    CloseOrderRspBO handleResponse(CloseOrderBO closeOrderBO,CloseOrderRsp closeOrderRsp);
}
