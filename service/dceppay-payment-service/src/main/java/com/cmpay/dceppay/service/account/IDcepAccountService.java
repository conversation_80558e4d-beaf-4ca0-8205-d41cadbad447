package com.cmpay.dceppay.service.account;

import com.cmpay.dceppay.bo.account.AccountTreatmentQueryResponseBO;
import com.cmpay.dceppay.bo.account.AccountTreatmentResultBO;
import com.cmpay.dceppay.bo.account.PayOrderAccountBO;
import com.cmpay.dceppay.bo.pay.PayAccountRecordBO;
import com.cmpay.dceppay.bo.pay.PayOrderDBBO;
import com.cmpay.dceppay.bo.pay.RefundOrderDBBO;

/**
 * <AUTHOR>
 * @date 2024/9/11 16:40
 * 数币账务处理service
 */
public interface IDcepAccountService {
    /**
     * 支付成功账务处理
     *
     * @param payOrderDBBO 订单信息
     */
    AccountTreatmentResultBO handPaymentSuccessAccount(PayOrderDBBO payOrderDBBO);

    /**
     * 补单账务处理
     *
     * @param payOrderDBBO
     */
    AccountTreatmentResultBO orderBackFillAccountRegister(PayOrderDBBO payOrderDBBO);

    /**
     * 退款受理成功账务登记
     *
     * @param refundOrder
     */
    AccountTreatmentResultBO refundAcceptAccountRegister(RefundOrderDBBO refundOrder);

    /**
     * 互联互通返回退款成功账务登记
     *
     * @param refundOrder
     */
    AccountTreatmentResultBO refundSuccessAccountRegister(RefundOrderDBBO refundOrder);

    /**
     * 退款撤单账务处理
     *
     * @param refundOrder
     */
    AccountTreatmentResultBO refundCancelAccountRegister(RefundOrderDBBO refundOrder);

    /**
     * 对账文件支付总金额
     *
     * @param accountBO
     */
    AccountTreatmentResultBO paymentCheckAccountRegister(PayOrderAccountBO accountBO);

    /**
     * 对账文件退款总金额
     *
     * @param accountBO
     */
    AccountTreatmentResultBO refundCheckAccountRegister(PayOrderAccountBO accountBO);

    /**
     * 账务查询
     * @param accountRecordBO
     * @return
     */

    AccountTreatmentQueryResponseBO queryAccountTreatment(PayAccountRecordBO accountRecordBO);


    PayOrderAccountBO buildPayOrderAccountBO(PayOrderDBBO payOrderDBBO);

    PayOrderAccountBO buildRefundOrderAccountBO(RefundOrderDBBO refundOrderDBBO);


}
