package com.cmpay.dceppay.service.notify.busmessage;

import com.cmpay.dceppay.bo.notify.busmessage.BusMessageNotifyBO;
import com.cmpay.dceppay.bo.notify.busmessage.BusMessageNotifyRspBO;
import com.cmpay.dceppay.bo.notify.busmessage.FinancialStatusChangeNotifyBO;

/**
 * <AUTHOR>
 * @date 2024/10/30 16:29
 */
public interface IBusMessageService {

    /**
     * 业务权限变更通知
     * @param busMessageNotifyBO
     * @return
     */
    BusMessageNotifyRspBO businessAuthority(BusMessageNotifyBO busMessageNotifyBO);

    /**
     * 机构变更通知
     * @param notifyBO
     * @return
     */
    BusMessageNotifyRspBO financialCodeChange(BusMessageNotifyBO notifyBO);

    /**
     * 机构状态变更通知
     * @param notifyBO
     * @return
     */
    BusMessageNotifyRspBO financialStatusChange(FinancialStatusChangeNotifyBO notifyBO);

    /**
     * 自由格式通知
     * @param notifyBO
     * @return
     */

    BusMessageNotifyRspBO freeFormatNotify(BusMessageNotifyBO notifyBO);
}
