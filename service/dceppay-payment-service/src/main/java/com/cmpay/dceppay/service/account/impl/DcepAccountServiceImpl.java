package com.cmpay.dceppay.service.account.impl;

import com.cmpay.account.enums.DebitOrCreditFlag;
import com.cmpay.dceppay.bo.account.*;
import com.cmpay.dceppay.bo.pay.PayAccountBO;
import com.cmpay.dceppay.bo.pay.PayAccountRecordBO;
import com.cmpay.dceppay.bo.pay.PayOrderDBBO;
import com.cmpay.dceppay.bo.pay.RefundOrderDBBO;
import com.cmpay.dceppay.constant.pay.AccountConstants;
import com.cmpay.dceppay.enums.common.IdGenKeyEnum;
import com.cmpay.dceppay.enums.pay.AccountInfoEnum;
import com.cmpay.dceppay.enums.pay.AccountOrderTypeEnum;
import com.cmpay.dceppay.enums.pay.AccountProcessStatusEnum;
import com.cmpay.dceppay.enums.pay.AccountTradeCodeEnum;
import com.cmpay.dceppay.outclient.igw.IAccountClientService;
import com.cmpay.dceppay.service.account.IDcepAccountService;
import com.cmpay.dceppay.service.account.ext.IPayAccountDataService;
import com.cmpay.dceppay.service.pay.IPayAccountRecordDBService;
import com.cmpay.dceppay.service.pay.IPayAccountService;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.utils.IdGenUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/13 11:36
 */
@Service
@Slf4j
public class DcepAccountServiceImpl implements IDcepAccountService {

    @Autowired
    private IAccountClientService accountClientService;
    @Autowired
    private IPayAccountService payAccountService;
    @Autowired
    private IPayAccountDataService payAccountDataService;
    @Autowired
    private IPayAccountRecordDBService accountRecordDBService;

    @Override
    public AccountTreatmentResultBO handPaymentSuccessAccount(PayOrderDBBO payOrderDBBO) {
        PayOrderAccountBO accountBO = buildPayOrderAccountBO(payOrderDBBO);
        AccountTreatmentBO accountTreatmentBO = getAccountTreatmentBO(accountBO, AccountTradeCodeEnum.DCEP0001, AccountOrderTypeEnum.PAYMENT);
        return handleAccount(accountTreatmentBO);
    }

    @Override
    public AccountTreatmentResultBO orderBackFillAccountRegister(PayOrderDBBO payOrderDBBO) {
        PayOrderAccountBO accountBO = buildPayOrderAccountBO(payOrderDBBO);
        AccountTreatmentBO accountTreatmentBO = getAccountTreatmentBO(accountBO, AccountTradeCodeEnum.DCEP0004, AccountOrderTypeEnum.PAYMENT);
        return handleAccount(accountTreatmentBO);

    }

    @Override
    public AccountTreatmentResultBO refundAcceptAccountRegister(RefundOrderDBBO refundOrder) {
        PayOrderAccountBO accountBO = buildRefundOrderAccountBO(refundOrder);
        AccountTreatmentBO accountTreatmentBO = getAccountTreatmentBO(accountBO, AccountTradeCodeEnum.DCEP0006, AccountOrderTypeEnum.REFUND);
        return handleAccount(accountTreatmentBO);
    }

    @Override
    public AccountTreatmentResultBO refundSuccessAccountRegister(RefundOrderDBBO refundOrder) {
        PayOrderAccountBO accountBO = buildRefundOrderAccountBO(refundOrder);
        AccountTreatmentBO accountTreatmentBO = getAccountTreatmentBO(accountBO, AccountTradeCodeEnum.DCEP0002, AccountOrderTypeEnum.REFUND);
        return handleAccount(accountTreatmentBO);
    }

    @Override
    public AccountTreatmentResultBO refundCancelAccountRegister(RefundOrderDBBO refundOrder) {
        PayOrderAccountBO accountBO = buildRefundOrderAccountBO(refundOrder);
        AccountTreatmentBO accountTreatmentBO = getAccountTreatmentBO(accountBO, AccountTradeCodeEnum.DCEP0005, AccountOrderTypeEnum.REFUND);
        return handleAccount(accountTreatmentBO);
    }

    @Override
    public AccountTreatmentResultBO paymentCheckAccountRegister(PayOrderAccountBO payOrderCheckAccountBO) {
        AccountTreatmentBO accountTreatmentBO = getAccountTreatmentBO(payOrderCheckAccountBO, AccountTradeCodeEnum.DCEP0003, AccountOrderTypeEnum.PAYMENT);
        return handleAccount(accountTreatmentBO);
    }

    @Override
    public AccountTreatmentResultBO refundCheckAccountRegister(PayOrderAccountBO refundOrderCheckAccountBO) {
        AccountTreatmentBO accountTreatmentBO = getAccountTreatmentBO(refundOrderCheckAccountBO, AccountTradeCodeEnum.DCEP0003, AccountOrderTypeEnum.REFUND);
        return handleAccount(accountTreatmentBO);
    }

    private AccountTreatmentResultBO handleAccount(AccountTreatmentBO accountTreatmentBO) {
        PayAccountBO payAccountBO = payAccountDataService.getPayAccountBO(accountTreatmentBO);
        PayAccountRecordBO payOrderAccountDBBO = payAccountService.getByUk(payAccountBO);
        AccountTreatmentResultBO accountTreatmentResultBO = new AccountTreatmentResultBO();
        if (JudgeUtils.isNull(payOrderAccountDBBO)) {
            payOrderAccountDBBO = payAccountService.registerWaitAccount(payAccountBO);
            accountTreatmentResultBO = accountClientService.accountTreatment(accountTreatmentBO);
            if (JudgeUtils.equals(accountTreatmentResultBO.getAccountHandleStatus(), AccountProcessStatusEnum.SUCCESS.name())) {
                payOrderAccountDBBO.setAccountDate(accountTreatmentResultBO.getAccountDate());
                accountRecordDBService.updateAccountSuccess(payOrderAccountDBBO);
            }
        } else if (JudgeUtils.equals(payOrderAccountDBBO.getHandleStatus(), AccountProcessStatusEnum.NORECORD.name())) {
            accountTreatmentResultBO = accountClientService.accountTreatment(accountTreatmentBO);
            if (JudgeUtils.equals(accountTreatmentResultBO.getAccountHandleStatus(), AccountProcessStatusEnum.SUCCESS.name())) {
                payOrderAccountDBBO.setAccountDate(accountTreatmentResultBO.getAccountDate());
                accountRecordDBService.updateAccountSuccess(payOrderAccountDBBO);
            }
        } else {
            if (JudgeUtils.equals(payOrderAccountDBBO.getHandleStatus(), AccountProcessStatusEnum.SUCCESS.name())) {
                accountTreatmentResultBO.setAccountHandleStatus(AccountProcessStatusEnum.SUCCESS.name());
                accountTreatmentResultBO.setAccountDate(payOrderAccountDBBO.getAccountDate());
            }
        }
        return accountTreatmentResultBO;
    }

    @Override
    public AccountTreatmentQueryResponseBO queryAccountTreatment(PayAccountRecordBO accountRecordBO) {
        AccountTreatmentQueryRequestBO accountTreatmentQueryRequestBO = new AccountTreatmentQueryRequestBO();
        BeanUtils.copyProperties(accountTreatmentQueryRequestBO, accountRecordBO);
        return accountClientService.accountQuery(accountTreatmentQueryRequestBO);
    }

    @Override
    public PayOrderAccountBO buildPayOrderAccountBO(PayOrderDBBO payOrderDBBO) {
        PayOrderAccountBO accountBO = new PayOrderAccountBO();
        accountBO.setOrderNo(payOrderDBBO.getOutOrderNo());
        accountBO.setOrderDate(payOrderDBBO.getOrderDate());
        accountBO.setOrderTime(payOrderDBBO.getOrderTime());
        accountBO.setAmount(payOrderDBBO.getOrderAmount());
        return accountBO;
    }

    @Override
    public PayOrderAccountBO buildRefundOrderAccountBO(RefundOrderDBBO refundOrderDBBO) {
        PayOrderAccountBO accountBO = new PayOrderAccountBO();
        accountBO.setOrderNo(refundOrderDBBO.getRefundOrderNo());
        accountBO.setOrderDate(refundOrderDBBO.getRefundDate());
        accountBO.setOrderTime(refundOrderDBBO.getRefundTime());
        accountBO.setAmount(refundOrderDBBO.getRefundAmount());
        return accountBO;
    }


    private AccountTreatmentBO getAccountTreatmentBO(PayOrderAccountBO accountBO, AccountTradeCodeEnum tradeCode, AccountOrderTypeEnum orderTypeCode) {
        AccountTreatmentBO accountTreatmentBO = createAccountTreatmentRequestBO(accountBO, tradeCode, orderTypeCode);
        accountTreatmentBO.setAccountDetailBOS(createAccountDetails(accountBO, tradeCode, orderTypeCode));
        accountTreatmentBO.setOrderAmount(accountBO.getAmount());
        return accountTreatmentBO;
    }

    private AccountTreatmentBO createAccountTreatmentRequestBO(PayOrderAccountBO accountBO, AccountTradeCodeEnum tradeCode, AccountOrderTypeEnum orderTypeCode) {
        AccountTreatmentBO requestBO = new AccountTreatmentBO();
        requestBO.setOrderNo(accountBO.getOrderNo());
        requestBO.setTradeDate(accountBO.getOrderDate());
        requestBO.setTradeTime(accountBO.getOrderTime());
        requestBO.setBusinessJrnNo(generateBusinessJrnNo());
        requestBO.setOrderType(orderTypeCode.getValue());
        requestBO.setBusType(AccountConstants.BUS_TYPE_DCEP);
        requestBO.setBusChannel(AccountConstants.BUS_CHANNEL_CAS);
        requestBO.setTradeType(AccountConstants.TRADE_TYPE_DCEP);
        requestBO.setTradeCode(tradeCode.name());
        return requestBO;
    }

    private List<AccountDetailBO> createAccountDetails(PayOrderAccountBO accountBO, AccountTradeCodeEnum tradeCode, AccountOrderTypeEnum orderTypeCode) {
        List<AccountDetailBO> accountDetailBOS = new ArrayList<>();
        switch (tradeCode) {
            case DCEP0001:
            case DCEP0004:
                //借：应收账款-渠道充值款-数字人民币 贷：其他应付款-暂收款项-数字人民币
                addAccountDetails(accountDetailBOS, accountBO, AccountInfoEnum.DIGITAL_CURRENCY_CHANNEL_CHARGE, AccountInfoEnum.OTHER_PAYABLES_TEMP_RECEIVABLES);
                break;
            case DCEP0002:
                //借：其他应付款-中转挂账-退款申请 贷：其他应付款-中转挂账-银行退款
                addAccountDetails(accountDetailBOS, accountBO, AccountInfoEnum.ESCROW_REFUND_APPLICATION, AccountInfoEnum.ESCROW_BANK_REFUND);
                break;
            case DCEP0003:
                if (JudgeUtils.equals(orderTypeCode.getValue(), AccountOrderTypeEnum.PAYMENT.getValue())) {
                    //借：应收账款-待结算款-数字人民币 贷：应收账款-渠道充值款-数字人民币
                    addAccountDetails(accountDetailBOS, accountBO, AccountInfoEnum.ACCOUNTS_RECEIVABLE_SETTLEMENT, AccountInfoEnum.DIGITAL_CURRENCY_CHANNEL_CHARGE);
                } else if (JudgeUtils.equals(orderTypeCode.getValue(), AccountOrderTypeEnum.REFUND.getValue())) {
                    //借：其他应付款-中转挂账-银行退款 贷：应收账款-待结算款-数字人民币
                    addAccountDetails(accountDetailBOS, accountBO, AccountInfoEnum.ESCROW_BANK_REFUND, AccountInfoEnum.ACCOUNTS_RECEIVABLE_SETTLEMENT);
                }
                break;
            case DCEP0005:
                //借：其他应付款-暂收款项-数字人民币 贷：其他应付款-中转挂账-退款申请
                addAccountDetails(accountDetailBOS, accountBO, AccountInfoEnum.OTHER_PAYABLES_TEMP_RECEIVABLES, AccountInfoEnum.ESCROW_REFUND_APPLICATION);
                break;
            case DCEP0006:
                //借：其他应付款-商户账户-消费退款 贷：其他应付款-中转挂账-退款申请
                addAccountDetails(accountDetailBOS, accountBO, AccountInfoEnum.MERCHANT_ACCOUNT_CONSUMPTION_REFUND, AccountInfoEnum.ESCROW_REFUND_APPLICATION);
                break;
            default:
                throw new IllegalArgumentException("Unsupported trade code: " + tradeCode);
        }
        return accountDetailBOS;
    }

    private void addAccountDetails(List<AccountDetailBO> accountDetailBOS, PayOrderAccountBO accountBO, AccountInfoEnum debitAccount, AccountInfoEnum creditAccount) {
        accountDetailBOS.add(createDebitAccount(accountBO, debitAccount, creditAccount));
        accountDetailBOS.add(createCreditAccount(accountBO, debitAccount, creditAccount));
    }

    private AccountDetailBO createDebitAccount(PayOrderAccountBO accountBO, AccountInfoEnum debitAccountEnum, AccountInfoEnum creditAccountEnum) {
        return new AccountDetailBO(DebitOrCreditFlag.DEBIT.getValue(),
                debitAccountEnum.getAccountNo(),
                debitAccountEnum.getAccountName(),
                accountBO.getAmount(),
                creditAccountEnum.getAccountNo(),
                creditAccountEnum.getAccountName());
    }

    private AccountDetailBO createCreditAccount(PayOrderAccountBO accountBO, AccountInfoEnum debitAccountEnum, AccountInfoEnum creditAccountEnum) {
        return new AccountDetailBO(DebitOrCreditFlag.CREDIT.getValue(),
                creditAccountEnum.getAccountNo(),
                creditAccountEnum.getAccountName(),
                accountBO.getAmount(),
                debitAccountEnum.getAccountNo(),
                debitAccountEnum.getAccountName());
    }

    private String generateBusinessJrnNo() {
        return IdGenUtils.generateIdWithShortDateTime(String.valueOf(IdGenKeyEnum.ACCOUNT_BUS_JRN_NO), IdGenKeyEnum.ACCOUNT_BUS_JRN_NO_LENGTH);
    }


}
