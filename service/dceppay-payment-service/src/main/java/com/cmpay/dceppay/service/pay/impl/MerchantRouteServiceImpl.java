package com.cmpay.dceppay.service.pay.impl;

import com.cmpay.dceppay.bo.pay.MerchantRouteBO;
import com.cmpay.dceppay.bo.smartmerchant.ICBCMerchantBO;
import com.cmpay.dceppay.bo.smartmerchant.ICBCServiceProviderBO;
import com.cmpay.dceppay.constant.icbc.ICBCConstants;
import com.cmpay.dceppay.enums.common.StatusEnum;
import com.cmpay.dceppay.enums.icbc.MerchantOnboardStatusEnum;
import com.cmpay.dceppay.service.pay.IMerchantRouteService;
import com.cmpay.dceppay.service.pay.IPayMerchantRouteDBService;
import com.cmpay.dceppay.service.smartmerchant.IICBCMerchantDBService;
import com.cmpay.dceppay.service.smartmerchant.IICBCServiceProviderDBService;
import com.cmpay.lemon.common.utils.JudgeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/9/3 17:55
 */
@Service
public class MerchantRouteServiceImpl implements IMerchantRouteService {
    @Autowired
    private IPayMerchantRouteDBService merchantRouteDBService;
    @Autowired
    private IICBCMerchantDBService iicbcMerchantDBService;
    @Autowired
    private IICBCServiceProviderDBService serviceProviderDBService;

    @Override
    public MerchantRouteBO checkRoute(String busCode) {
        MerchantRouteBO merchantRouteBO = merchantRouteDBService.getByBusCode(busCode);
        if (JudgeUtils.isNull(merchantRouteBO) || StatusEnum.DISABLED.getStatus().equals(merchantRouteBO.getRouteStatus())) {
            return null;
        }
        ICBCMerchantBO merchantBO = iicbcMerchantDBService.getMerchantByMerchantID(merchantRouteBO.getMerchantNo());
        if (isInvalidMerchant(merchantBO)) {
            return null;
        }
        if (isServiceMerchant(merchantBO)) {
            ICBCServiceProviderBO serviceProviderBO = serviceProviderDBService.getServiceProviderInfoByServiceID(merchantBO.getServiceId());
            if (isInvalidServiceProvider(serviceProviderBO)) {
                return null;
            }
            merchantRouteBO.setServiceProviderId(serviceProviderBO.getServiceProviderId());
        }
        merchantRouteBO.setOperatorMerchantId(merchantBO.getOperatorMerchantId());
        merchantRouteBO.setMerchantName(merchantBO.getMerchantName());
        merchantRouteBO.setMerchantShortName(merchantBO.getMerchantShortName());
        merchantRouteBO.setMerchantCategory(merchantBO.getMerchantCategory());
        merchantRouteBO.setMerchantLicenseType(merchantBO.getMerchantLicenseType());
        merchantRouteBO.setMerchantLicense(merchantBO.getMerchantLicense());
        merchantRouteBO.setWalletId(merchantBO.getProtocolWalletId());
        return merchantRouteBO;
    }

    private boolean isInvalidMerchant(ICBCMerchantBO merchantBO) {
        return JudgeUtils.isNull(merchantBO)
                || JudgeUtils.notEquals(StatusEnum.ENABLED.getStatus(), merchantBO.getServiceStatus())
                || JudgeUtils.notEquals(MerchantOnboardStatusEnum.ONBOARD_SUCCESS.getStatus(), merchantBO.getOnboardStatus());
    }

    private boolean isServiceMerchant(ICBCMerchantBO merchantBO) {
        return JudgeUtils.equals(merchantBO.getServiceMerchantFlag(), String.valueOf(ICBCConstants.IS_SERVICE_MERCHANT));
    }

    private boolean isInvalidServiceProvider(ICBCServiceProviderBO serviceProviderBO) {
        return JudgeUtils.isNull(serviceProviderBO)
                || JudgeUtils.notEquals(StatusEnum.ENABLED.getStatus(), serviceProviderBO.getServiceStatus())
                || JudgeUtils.notEquals(MerchantOnboardStatusEnum.ONBOARD_SUCCESS.getStatus(), serviceProviderBO.getOnboardStatus());
    }

}
