package com.cmpay.dceppay.service.pay.impl;

import com.cmpay.dceppay.bo.pay.PayOrderDBBO;
import com.cmpay.dceppay.enums.MsgCodeEnum;
import com.cmpay.dceppay.enums.pay.OrderStatusEnum;
import com.cmpay.dceppay.service.pay.ICloseOrderService;
import com.cmpay.dceppay.service.pay.IPayOrderDBService;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/9/5 14:40
 */
@Service
public class CloseOrderServiceImpl implements ICloseOrderService {
    @Autowired
    private IPayOrderDBService payOrderDBService;

    @Override
    public PayOrderDBBO checkOrderStatus(String outOrderNo) {
        PayOrderDBBO payOrderDBBO = payOrderDBService.getByOutOrderNo(outOrderNo);
        if (JudgeUtils.isNull(payOrderDBBO)) {
            BusinessException.throwBusinessException(MsgCodeEnum.ORG_ORDER_NOT_EXISTS.getMsgCd());
        }
        String status = payOrderDBBO.getStatus();
        if (!OrderStatusEnum.WAIT_PAY.name().equals(status)) {
            BusinessException.throwBusinessException(MsgCodeEnum.PAYMENT_ORDER_STATUS_CANNOT_CLOSE.getMsgCd());
        }
        return payOrderDBBO;
    }
}
