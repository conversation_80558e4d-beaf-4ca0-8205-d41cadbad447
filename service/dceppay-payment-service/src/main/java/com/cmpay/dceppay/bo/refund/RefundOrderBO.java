package com.cmpay.dceppay.bo.refund;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/9/5 9:49
 */
@Data
public class RefundOrderBO {
    /**
     * @Fields refundOrderNo 退款请求号
     */
    private String refundOrderNo;
    /**
     * @Fields outOrderNo 原交易订单号
     */
    private String outOrderNo;
    /**
     * 机构订单号
     */
    private String bankOrderNo;
    /**
     * @Fields refundDate 退款提交日期
     */
    private String refundDate;
    /**
     * @Fields refundTime 退款提交时间
     */
    private String refundTime;
    /**
     * @Fields channelCode 支付渠道号
     */
    private String channelCode;
    /**
     * @Fields payWay 支付方式
     */
    private String payWay;
    /**
     * @Fields scene 支付场景
     */
    private String scene;
    /**
     * @Fields busType 业务类型
     */
    private String busType;
    /**
     * @Fields merchantNo 商户编号
     */
    private String merchantNo;
    /**
     * @Fields orgMerchantNo 运营机构商户号
     */
    private String orgMerchantNo;
    /**
     * @Fields walletId 商户钱包id
     */
    private String walletId;
    /**
     * @Fields orderAmount 原订单金额
     */
    private BigDecimal orderAmount;
    /**
     * @Fields refundAmount 退款金额
     */
    private BigDecimal refundAmount;
    /**
     * @Fields originalMessageIdentification 支付订单报文标识号
     */
    private String originalMessageIdentification;
    /**
     * @Fields bizType 业务类型
     */
    private String bizType;
    /**
     * @Fields bizCategory 业务种类
     */
    private String bizCategory;
    // 退款原因
    private String refundReason;
    private String messageIdentification;

}
