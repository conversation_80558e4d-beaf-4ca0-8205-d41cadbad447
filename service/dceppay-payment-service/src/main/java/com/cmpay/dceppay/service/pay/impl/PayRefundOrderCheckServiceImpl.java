package com.cmpay.dceppay.service.pay.impl;

import com.cmpay.dceppay.bo.common.CheckResultBO;
import com.cmpay.dceppay.bo.pay.PayOrderDBBO;
import com.cmpay.dceppay.bo.refund.RefundOrderBO;
import com.cmpay.dceppay.bo.refund.RefundOrderRepBO;
import com.cmpay.dceppay.enums.MsgCodeEnum;
import com.cmpay.dceppay.enums.pay.OrderStatusEnum;
import com.cmpay.dceppay.service.mng.IBusParamService;
import com.cmpay.dceppay.service.pay.IPayRefundDBService;
import com.cmpay.dceppay.service.pay.IPayRefundOrderCheckService;
import com.cmpay.dceppay.util.DateTimeUtil;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.EnumSet;

import static com.cmpay.dceppay.utils.AmountUtils.isValidAmount;

/**
 * <AUTHOR>
 * @date 2024/9/5 10:26
 */
@Service
public class PayRefundOrderCheckServiceImpl implements IPayRefundOrderCheckService {
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");

    @Autowired
    private IPayRefundDBService refundDBService;
    @Autowired
    private IBusParamService paramService;

    @Override
    public CheckResultBO refundOrderAmountCheck(RefundOrderRepBO refundOrderRepBO) {
        CheckResultBO result = new CheckResultBO();
        boolean isValidAmount = isValidAmount(refundOrderRepBO.getRefundAmount());
        result.setCheckFlag(isValidAmount);
        if (!isValidAmount) {
            result.setMessageCode(MsgCodeEnum.ORDER_TOTAL_AMOUNT_INVALID.getMsgCd());
        }
        return result;
    }

    @Override
    public boolean isOrderOverRefundLimit(String orderDateStr) {
        LocalDate orderDate = LocalDate.parse(orderDateStr, DATE_FORMATTER);
        LocalDate currentDate = LocalDate.now();
        return ChronoUnit.DAYS.between(orderDate, currentDate) > paramService.getRefundLimitDay();
    }

    @Override
    public RefundOrderBO orderStatusCheck(PayOrderDBBO payOrderDBBO, RefundOrderRepBO refundOrderRepBO) {
        checkOrderStatus(payOrderDBBO, refundOrderRepBO);
        RefundOrderBO refundOrderBO = new RefundOrderBO();
        refundOrderBO.setOriginalMessageIdentification(payOrderDBBO.getMessageIdentification());
        refundOrderBO.setRefundOrderNo(refundOrderRepBO.getOutRefundNo());
        refundOrderBO.setOutOrderNo(payOrderDBBO.getOutOrderNo());
        refundOrderBO.setBankOrderNo(payOrderDBBO.getBankOrderNo());
        refundOrderBO.setMerchantNo(payOrderDBBO.getMerchantNo());
        refundOrderBO.setOrgMerchantNo(payOrderDBBO.getOrgMerchantNo());
        refundOrderBO.setOrderAmount(payOrderDBBO.getOrderAmount());
        refundOrderBO.setWalletId(payOrderDBBO.getWalletId());
        refundOrderBO.setBusType(payOrderDBBO.getBusType());
        refundOrderBO.setPayWay(payOrderDBBO.getPayWay());
        refundOrderBO.setScene(payOrderDBBO.getScene());
        refundOrderBO.setChannelCode(JudgeUtils.isNull(refundOrderRepBO.getChannelCode()) ? payOrderDBBO.getChannelCode() : refundOrderRepBO.getChannelCode());
        refundOrderBO.setBankOrderNo(payOrderDBBO.getBankOrderNo());
        refundOrderBO.setRefundDate(DateTimeUtil.getCurrentDateStr());
        refundOrderBO.setRefundTime(DateTimeUtil.getCurrentTimeStr());
        refundOrderBO.setBizType(payOrderDBBO.getBizType());
        refundOrderBO.setBizCategory(payOrderDBBO.getBizCategory());
        return refundOrderBO;
    }

    private void checkOrderStatus(PayOrderDBBO payOrderDBBO, RefundOrderRepBO refundOrderRepBO) {
        //支付成功，部分退款成功，退款等待状态的支付订单才允许退款
        if (!EnumSet.of(OrderStatusEnum.TRADE_SUCCESS, OrderStatusEnum.REFUND_PART, OrderStatusEnum.REFUND_WAIT)
                .contains(OrderStatusEnum.valueOf(payOrderDBBO.getStatus()))) {
            BusinessException.throwBusinessException(MsgCodeEnum.PAYMENT_ORDER_STATUS_CANNOT_REFUND);
        }
        BigDecimal alreadyRefundAmount = refundDBService.sumRefundAmount(refundOrderRepBO.getOutOrderNo());
        BigDecimal refundMoney = alreadyRefundAmount.add(refundOrderRepBO.getRefundAmount());
        if (refundMoney.compareTo(payOrderDBBO.getOrderAmount()) > 0) {
            BusinessException.throwBusinessException(MsgCodeEnum.REFUND_AMOUNT_ERROR);
        }
    }
}
