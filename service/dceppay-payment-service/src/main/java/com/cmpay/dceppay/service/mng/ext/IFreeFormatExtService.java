package com.cmpay.dceppay.service.mng.ext;

import com.cmpay.channel.data.Request;
import com.cmpay.dceppay.bo.mng.FreeFormatBO;
import com.cmpay.dceppay.dto.dcep.freeformat.FreeFormatRsp;
import com.cmpay.dceppay.dto.dcep.freeformat.request.FreeFormatSoapEnvelopRequest;

/**
 * <AUTHOR>
 * @date 2024/11/5 15:26
 */
public interface IFreeFormatExtService {
    FreeFormatSoapEnvelopRequest buildRequestParam(FreeFormatBO freeFormatBO);

    Request bulidRequest(FreeFormatSoapEnvelopRequest freeFormatSoapEnvelopRequest);

    void handleResponse(FreeFormatRsp freeFormatRsp, FreeFormatBO freeFormatBO);
}
