package com.cmpay.dceppay.service.pay;

import com.cmpay.dceppay.bo.check.CheckFileDetailBO;
import com.cmpay.dceppay.bo.notify.RefundNotifyBO;
import com.cmpay.dceppay.bo.pay.RefundOrderDBBO;
import com.cmpay.dceppay.bo.refundquery.RefundOrderResultBO;

/**
 * <AUTHOR>
 * @date 2024/9/17 17:30
 */
public interface IRefundOrderDataService {
    RefundOrderResultBO buildRefundOrderResult(RefundNotifyBO refundNotifyBO);

    RefundOrderDBBO buildUpdateRefundOrderDBBO(RefundOrderResultBO refundOrderResultBO, RefundOrderDBBO refundOrderDBBO);

    RefundOrderResultBO buildRefundResult(CheckFileDetailBO checkFileDetailBO);
}
