package com.cmpay.dceppay.service.channel;

import com.cmpay.dceppay.bo.closeorder.CloseOrderBO;
import com.cmpay.dceppay.bo.closeorder.CloseOrderRspBO;
import com.cmpay.dceppay.bo.notify.PaymentNotifyBO;
import com.cmpay.dceppay.bo.notify.PaymentNotifyRspBO;
import com.cmpay.dceppay.bo.notify.RefundNotifyBO;
import com.cmpay.dceppay.bo.notify.RefundNotifyRspBO;
import com.cmpay.dceppay.bo.pay.MerchantRouteBO;
import com.cmpay.dceppay.bo.pay.PayOrderDBBO;
import com.cmpay.dceppay.bo.pay.RefundOrderDBBO;
import com.cmpay.dceppay.bo.payment.UnifiedOrderReqBO;
import com.cmpay.dceppay.bo.payment.UnifiedOrderRspBO;
import com.cmpay.dceppay.bo.paymentquery.PaymentOrderQueryBO;
import com.cmpay.dceppay.bo.paymentquery.PaymentOrderResultBO;
import com.cmpay.dceppay.bo.refund.RefundOrderBO;
import com.cmpay.dceppay.bo.refund.RefundOrderRepBO;
import com.cmpay.dceppay.bo.refund.RefundOrderRspBO;
import com.cmpay.dceppay.bo.refundquery.RefundOrderQueryBO;
import com.cmpay.dceppay.bo.refundquery.RefundOrderResultBO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/2 9:29
 */
public interface IPaymentChannelService {
    UnifiedOrderRspBO order(MerchantRouteBO merchantRouteBO, UnifiedOrderReqBO unifiedOrderReqBO);

    /**
     * 退款申请
     * @param refundOrderBO
     * @param refundOrderRepBO
     * @return
     */
    RefundOrderRspBO refund(RefundOrderBO refundOrderBO, RefundOrderRepBO refundOrderRepBO);

    CloseOrderRspBO close(PayOrderDBBO payOrder, CloseOrderBO closeOrderBO);

    /**
     * 支付订单查询
     *
     * @param paymentOrderQueryBO
     * @return
     */
    PaymentOrderResultBO paymentQuery(PaymentOrderQueryBO paymentOrderQueryBO);

    /**
     * 退款订单查询
     *
     * @param refundOrderQueryBO
     * @return
     */
    RefundOrderResultBO refundQuery(RefundOrderQueryBO refundOrderQueryBO);

    PaymentNotifyRspBO paymentNotify(PayOrderDBBO order,PaymentNotifyBO paymentNotifyBO);

    RefundNotifyRspBO refundNotify(RefundOrderDBBO refundOrder, RefundNotifyBO refundNotifyBO);

    /**
     * 异步退款发起
     * @param refundOrderDBBO
     */
    void refundAsync(RefundOrderDBBO refundOrderDBBO);
}
