package com.cmpay.dceppay.service.channel.ext;

import com.cmpay.channel.data.Request;
import com.cmpay.dceppay.bo.paymentquery.PaymentOrderQueryBO;
import com.cmpay.dceppay.bo.paymentquery.PaymentOrderResultBO;
import com.cmpay.dceppay.dto.dcep.unifiedorderquery.UnifiedOrderQueryRsp;
import com.cmpay.dceppay.dto.dcep.unifiedorderquery.reponse.OrderQuerySoapEnvelopResponse;
import com.cmpay.dceppay.dto.dcep.unifiedorderquery.request.OrderQuerySoapEnvelopRequest;

/**
 * <AUTHOR>
 * @date 2024/9/10 16:37
 */
public interface IOrderQueryExtService {
    OrderQuerySoapEnvelopRequest buildOrderQueryRequestParam(PaymentOrderQueryBO paymentOrderQueryBO);

    Request bulidOrderQueryRequest(OrderQuerySoapEnvelopRequest orderQuerySoapEnvelopRequest);

    PaymentOrderResultBO handleResponse(PaymentOrderQueryBO paymentOrderQueryBO,    UnifiedOrderQueryRsp unifiedOrderQueryRsp );
}
