package com.cmpay.dceppay.service.pay.impl;

import com.cmpay.dceppay.bo.account.AccountTreatmentResultBO;
import com.cmpay.dceppay.bo.common.CheckResultBO;
import com.cmpay.dceppay.bo.notify.RefundNotifyRspBO;
import com.cmpay.dceppay.bo.pay.MerchantRouteBO;
import com.cmpay.dceppay.bo.pay.PayOrderDBBO;
import com.cmpay.dceppay.bo.pay.RefundOrderDBBO;
import com.cmpay.dceppay.bo.refund.RefundOrderBO;
import com.cmpay.dceppay.bo.refund.RefundOrderRepBO;
import com.cmpay.dceppay.bo.refund.RefundOrderRspBO;
import com.cmpay.dceppay.bo.refundquery.RefundOrderResultBO;
import com.cmpay.dceppay.constant.dcep.DcepResponseStatusConstant;
import com.cmpay.dceppay.enums.MsgCodeEnum;
import com.cmpay.dceppay.enums.pay.AccountProcessStatusEnum;
import com.cmpay.dceppay.enums.pay.OrderStatusEnum;
import com.cmpay.dceppay.enums.pay.RefundCancleEnum;
import com.cmpay.dceppay.service.account.IDcepAccountService;
import com.cmpay.dceppay.service.channel.app.ext.IDcepRefundOrderExtService;
import com.cmpay.dceppay.service.pay.*;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/9/5 9:51
 */
@Service
@Slf4j
public class PayRefundServiceImpl implements IPayRefundService {
    @Autowired
    private IPayOrderDBService payOrderDBService;
    @Autowired
    private IPayRefundOrderCheckService refundOrderCheckService;
    @Autowired
    private IRefundOrderDataService refundOrderDataService;
    @Autowired
    private IPayRefundDBService refundDBService;
    @Autowired
    private IDcepAccountService accountService;
    @Autowired
    private IPaySettlementDBService paySettlementDBService;
    @Autowired
    private IMerchantRouteService merchantRouteService;
    @Autowired
    private IDcepRefundOrderExtService dcepRefundOrderExtService;

    @Override
    public RefundOrderBO checkAndQueryOrder(RefundOrderRepBO refundOrderRepBO) {

        //校验参数的合法性：退款金额大于0的数字，精度2；
        CheckResultBO checkResult = refundOrderCheckService.refundOrderAmountCheck(refundOrderRepBO);
        if (!checkResult.isCheckFlag()) {
            BusinessException.throwBusinessException(checkResult.getMessageCode());
        }
        //4、	根据退款请求号查询退款表数据RefundService.getByRefundNO(String refundOrderNo)，若存在，报错退出“退款订单号已存在”；
        if (JudgeUtils.isNotNull(refundDBService.getByOutRefundNo(refundOrderRepBO.getOutRefundNo()))) {
            BusinessException.throwBusinessException(MsgCodeEnum.REFUND_ORDER_ALREADY_EXIST.getMsgCd());
        }
        //5、	根据订单号查询订单表数据PaymentService.getByOrderNO(String outOrderNO)，若不存在，报错退出“支付订单不存在”；
        PayOrderDBBO payOrderDBBO = payOrderDBService.getByOutOrderNo(refundOrderRepBO.getOutOrderNo());
        if (JudgeUtils.isNull(payOrderDBBO)) {
            BusinessException.throwBusinessException(MsgCodeEnum.ORG_ORDER_NOT_EXISTS.getMsgCd());
        }
        MerchantRouteBO merchantRouteBO = merchantRouteService.checkRoute(payOrderDBBO.getBusType());
        if (JudgeUtils.isNull(merchantRouteBO)) {
            BusinessException.throwBusinessException(MsgCodeEnum.MERCHANT_ROUTE_NOT_EXIST.getMsgCd());
        }
        // 判断支付订单是否已超过180天，若已超过180天，报错退出“已超过退款时限，请联系商户走线下退款”；反之进入下一步骤；（退款时限180天考虑做成参数配置）
        boolean refundOverLimit = refundOrderCheckService.isOrderOverRefundLimit(payOrderDBBO.getOrderDate());
        if (refundOverLimit) {
            BusinessException.throwBusinessException(MsgCodeEnum.ORDER_REFUND_PARAM_TIME_OUT.getMsgCd());
        }

        return refundOrderCheckService.orderStatusCheck(payOrderDBBO, refundOrderRepBO);

    }

    @Override
    public void handleSuccessRefundOrder(RefundOrderResultBO refundOrderResultBO, RefundOrderDBBO refundOrderDBBO) {
        if (JudgeUtils.equals(refundOrderDBBO.getStatus(),OrderStatusEnum.REFUND_PEND.name())){
            RefundOrderRspBO refundOrderRspBO=new RefundOrderRspBO();
            refundOrderRspBO.setRefundStatus(OrderStatusEnum.REFUND_WAIT.name());
            dcepRefundOrderExtService.updateRefundAccept(refundOrderDBBO, refundOrderRspBO);
        }
        AccountTreatmentResultBO resultBO = accountService.refundSuccessAccountRegister(refundOrderDBBO);
        handleRefundAccountTreatmentResult(resultBO, refundOrderDBBO);
        updateOrgOrderStatus(refundOrderDBBO);
        //退款订单表更新
        updateRefundOrderRecord(refundOrderResultBO, refundOrderDBBO);
    }

    private void updateOrgOrderStatus(RefundOrderDBBO refundOrderDBBO) {
        BigDecimal alreadyRefundTotalAmount = refundDBService.sumRefundSuccessAmount(refundOrderDBBO.getOutOrderNo());
        // 本次退款总金额 =已退款金额+本次退款金额
        BigDecimal totalRefundAmount = alreadyRefundTotalAmount.add(refundOrderDBBO.getRefundAmount());
        PayOrderDBBO updateOrderDBBO = new PayOrderDBBO();
        updateOrderDBBO.setOutOrderNo(refundOrderDBBO.getOutOrderNo());
        if (totalRefundAmount.compareTo(refundOrderDBBO.getOrderAmount()) == 0) {
            updateOrderDBBO.setStatus(OrderStatusEnum.REFUND_SUCCESS.name());
        } else if (totalRefundAmount.compareTo(refundOrderDBBO.getOrderAmount()) < 0) {
            updateOrderDBBO.setStatus(OrderStatusEnum.REFUND_PART.name());
        } else {
            log.error("refund amount error, current refund amount: {}, already refund amount: {}, origin order amount: {}",
                    refundOrderDBBO.getRefundAmount(), totalRefundAmount, refundOrderDBBO.getOrderAmount());
            BusinessException.throwBusinessException(MsgCodeEnum.REFUND_AMOUNT_ERROR);
        }
        payOrderDBService.updateRefundInfo(updateOrderDBBO, refundOrderDBBO.getRefundAmount());
    }


    private void handleRefundAccountTreatmentResult(AccountTreatmentResultBO resultBO, RefundOrderDBBO refundOrderDBBO) {
        if (JudgeUtils.equals(resultBO.getAccountHandleStatus(), AccountProcessStatusEnum.SUCCESS.name())) {
            refundOrderDBBO.setAccountDate(resultBO.getAccountDate());
        } else {
            BusinessException.throwBusinessException(MsgCodeEnum.REFUND_ACCOUNT_STATUS_WAIT);
        }
    }

    private void updateRefundOrderRecord(RefundOrderResultBO refundOrderResultBO, RefundOrderDBBO refundOrderDBBO) {
        RefundOrderDBBO updateRefundOrderDBBO = refundOrderDataService.buildUpdateRefundOrderDBBO(refundOrderResultBO, refundOrderDBBO);
        int updateNumber = refundDBService.updateRefundSuccess(updateRefundOrderDBBO);
        if (updateNumber != 1) {
            BusinessException.throwBusinessException(MsgCodeEnum.UPDATE_REFUND_SUCCESS_ERROR);
        }
    }

    @Override
    public void handleFailRefundOrder(RefundOrderResultBO refundOrderResultBO, RefundOrderDBBO refundOrderDBBO) {
        updateRefundOrderFailRecord(refundOrderResultBO, refundOrderDBBO);
    }


    private void updateRefundOrderFailRecord(RefundOrderResultBO refundOrderResultBO, RefundOrderDBBO refundOrderDBBO) {
        RefundOrderDBBO updateRefundOrderDBBO = refundOrderDataService.buildUpdateRefundOrderDBBO(refundOrderResultBO, refundOrderDBBO);
        int updateNumber = refundDBService.updateRefundFail(updateRefundOrderDBBO);
        if (updateNumber != 1) {
            BusinessException.throwBusinessException(MsgCodeEnum.UPDATE_REFUND_FAIL_ERROR);
        }
    }


    @Override
    public void handleAcceptRefund(RefundOrderDBBO refundOrderDBBO) {
        AccountTreatmentResultBO resultBO = new AccountTreatmentResultBO();
        if (JudgeUtils.equals(refundOrderDBBO.getCancelFlag(), RefundCancleEnum.N.name())) {
            resultBO = accountService.refundAcceptAccountRegister(refundOrderDBBO);
        } else if (JudgeUtils.equals(refundOrderDBBO.getCancelFlag(), RefundCancleEnum.Y.name())) {
            resultBO = accountService.refundCancelAccountRegister(refundOrderDBBO);
        }
        if (JudgeUtils.notEquals(resultBO.getAccountHandleStatus(), AccountProcessStatusEnum.SUCCESS.name())) {
            BusinessException.throwBusinessException(MsgCodeEnum.PAYMENT_ACCOUNT_STATUS_WAIT);
        }
        handleRefundAcceptAccountTreatmentResult(resultBO, refundOrderDBBO);
    }

    @Override
    public boolean checkRefundStatus(OrderStatusEnum orderStatusEnum, RefundOrderDBBO refundOrder, RefundNotifyRspBO refundNotifyRspBO) {
        if (JudgeUtils.equals(orderStatusEnum.name(), refundOrder.getStatus())) {
            refundNotifyRspBO.setProcessStatus(DcepResponseStatusConstant.NOTIFY_HANDLE_SUCCESS);
            return true;
        }
        if (!JudgeUtils.equalsAny(refundOrder.getStatus(),OrderStatusEnum.REFUND_WAIT.name(),OrderStatusEnum.REFUND_PEND.name())) {
            BusinessException.throwBusinessException(MsgCodeEnum.PAYMENT_ORDER_STATUS_INVALID);
            return true;
        }
        return false;
    }

    private void handleRefundAcceptAccountTreatmentResult(AccountTreatmentResultBO accountTreatmentBO, RefundOrderDBBO refundOrderDBBO) {
        if (JudgeUtils.equals(accountTreatmentBO.getAccountHandleStatus(), AccountProcessStatusEnum.SUCCESS.name())) {
            refundOrderDBBO.setAccountDate(accountTreatmentBO.getAccountDate());
            paySettlementDBService.registerRefundSettlement(refundOrderDBBO);
            refundDBService.updateAccountDate(refundOrderDBBO);
        }
    }


}
