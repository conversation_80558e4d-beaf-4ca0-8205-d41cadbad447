package com.cmpay.dceppay.service.notify.busmessage.impl;

import com.cmpay.dceppay.bo.mng.BusParamBO;
import com.cmpay.dceppay.enums.dcep.FinancialStatusEnum;
import com.cmpay.dceppay.service.mng.IFinancialBusParamService;
import com.cmpay.dceppay.service.notify.busmessage.IFinancialStatusService;
import com.cmpay.lemon.common.utils.JudgeUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/10/31 10:55
 */
@Service
@Slf4j
public class IFinancialStatusServiceImpl implements IFinancialStatusService {
    @Autowired
    private IFinancialBusParamService paramService;

    @Override
    public void setFinancialStatusOff(String operateId) {
        BusParamBO busParamBO = getFinancialStatus();
        if (JudgeUtils.equals(busParamBO.getParamCode(), FinancialStatusEnum.ON.name())) {
            busParamBO.setOperateId(operateId);
            paramService.updateFinancialStatusOff(busParamBO);
        }

    }

    @Override
    public void setFinancialStatusOn(String operateId) {
        BusParamBO busParamBO = getFinancialStatus();
        if (JudgeUtils.equals(busParamBO.getParamCode(), FinancialStatusEnum.OFF.name())) {
            busParamBO.setOperateId(operateId);
            paramService.updateFinancialStatusOn(busParamBO);
        }
    }

    private BusParamBO getFinancialStatus() {
        BusParamBO busParamBO = paramService.getFinancialStatus();
        String status = busParamBO.getParamCode();
        if (JudgeUtils.isNull(status)) {
            log.error("【FINANCIAL_STATUS机构状态】参数未配置");
        }
        return busParamBO;
    }
}
