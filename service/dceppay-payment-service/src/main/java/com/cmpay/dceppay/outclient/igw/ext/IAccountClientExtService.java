package com.cmpay.dceppay.outclient.igw.ext;

import com.cmpay.account.dto.AccountDebitOrCreditDTO;
import com.cmpay.dceppay.bo.account.AccountDetailBO;
import com.cmpay.dceppay.bo.account.AccountTreatmentBO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/14 8:38
 */
public interface IAccountClientExtService {
    /**
     * 获取请求账务处理-账务数组对象
     * @param accountTreatmentBO
     * @param accountDetailBOS
     * @return
     */
    List<AccountDebitOrCreditDTO> getAccountList(AccountTreatmentBO accountTreatmentBO, List<AccountDetailBO> accountDetailBOS);
}
