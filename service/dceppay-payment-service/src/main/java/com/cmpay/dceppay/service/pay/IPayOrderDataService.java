package com.cmpay.dceppay.service.pay;

import com.cmpay.dceppay.bo.check.CheckFileDetailBO;
import com.cmpay.dceppay.bo.notify.PaymentNotifyBO;
import com.cmpay.dceppay.bo.pay.PayOrderDBBO;
import com.cmpay.dceppay.bo.paymentquery.PaymentOrderResultBO;

/**
 * <AUTHOR>
 * @date 2024/9/17 16:37
 */
public interface IPayOrderDataService {
    PaymentOrderResultBO buildPaymentOrderResult(PaymentNotifyBO paymentNotifyBO);

    PayOrderDBBO buildUpdatePayOrderDBBO(PaymentOrderResultBO paymentOrderResultBO, PayOrderDBBO payOrderDBBO);

    PaymentOrderResultBO buildPayOrderResult(CheckFileDetailBO checkFileDetailBO);
}
