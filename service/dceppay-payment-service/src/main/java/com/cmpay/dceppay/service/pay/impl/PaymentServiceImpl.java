package com.cmpay.dceppay.service.pay.impl;

import com.cmpay.dceppay.bo.closeorder.CloseOrderBO;
import com.cmpay.dceppay.bo.closeorder.CloseOrderRspBO;
import com.cmpay.dceppay.bo.pay.MerchantRouteBO;
import com.cmpay.dceppay.bo.pay.PayOrderDBBO;
import com.cmpay.dceppay.bo.payment.UnifiedOrderReqBO;
import com.cmpay.dceppay.bo.payment.UnifiedOrderRspBO;
import com.cmpay.dceppay.bo.refund.RefundOrderBO;
import com.cmpay.dceppay.bo.refund.RefundOrderCheckBO;
import com.cmpay.dceppay.bo.refund.RefundOrderRepBO;
import com.cmpay.dceppay.bo.refund.RefundOrderRspBO;
import com.cmpay.dceppay.service.channel.IPaymentChannelService;
import com.cmpay.dceppay.service.pay.ICloseOrderService;
import com.cmpay.dceppay.service.pay.IPayOrderService;
import com.cmpay.dceppay.service.pay.IPayRefundService;
import com.cmpay.dceppay.service.pay.IPaymentService;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.framework.lock.DistributedLocked;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/8/30 16:18
 */
@Service
@Slf4j
public class PaymentServiceImpl implements IPaymentService {
    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private IPayOrderService payOrderService;
    @Autowired
    private IPayRefundService refundService;
    @Autowired
    private ICloseOrderService closeOrderService;


    @Override
    public UnifiedOrderRspBO unifiedOrder(UnifiedOrderReqBO unifiedOrderReqBO) {
        MerchantRouteBO routeBO = payOrderService.checkAndQueryMerchantRoute(unifiedOrderReqBO);
        String channelName = unifiedOrderReqBO.getPayWay() + unifiedOrderReqBO.getScene();
        IPaymentChannelService paymentChannelService = (IPaymentChannelService) applicationContext.getBean(channelName);
        return paymentChannelService.order(routeBO, unifiedOrderReqBO);
    }


    @Override
    @DistributedLocked(lockName = "'refundOrder:'+#refundOrderRepBO.getOutOrderNo()", leaseTime = 40, waitTime = 10)
    public RefundOrderRspBO refundOrder(RefundOrderRepBO refundOrderRepBO) {
        RefundOrderBO refundOrderBO = refundService.checkAndQueryOrder(refundOrderRepBO);
        String channelName = refundOrderBO.getPayWay() + refundOrderBO.getScene();
        IPaymentChannelService paymentChannelService = (IPaymentChannelService) applicationContext.getBean(channelName);
        return paymentChannelService.refund(refundOrderBO, refundOrderRepBO);
    }

    @Override
    public CloseOrderRspBO closeOrder(CloseOrderBO closeOrderBO) {
        PayOrderDBBO payOrder = closeOrderService.checkOrderStatus(closeOrderBO.getOutOrderNo());
        String channelName = payOrder.getPayWay() + payOrder.getScene();
        IPaymentChannelService paymentChannelService = (IPaymentChannelService) applicationContext.getBean(channelName);
        return paymentChannelService.close(payOrder, closeOrderBO);
    }

    @Override
    public void refundOrderCheck(RefundOrderCheckBO refundOrderCheckBO) {
        RefundOrderRepBO refundOrderRepBO = BeanUtils.copyPropertiesReturnDest(new RefundOrderRepBO(), refundOrderCheckBO);
        refundService.checkAndQueryOrder(refundOrderRepBO);
    }
}
