package com.cmpay.dceppay.service.pay;

import com.cmpay.dceppay.bo.notify.PaymentNotifyRspBO;
import com.cmpay.dceppay.bo.pay.MerchantRouteBO;
import com.cmpay.dceppay.bo.pay.PayOrderDBBO;
import com.cmpay.dceppay.bo.payment.UnifiedOrderReqBO;
import com.cmpay.dceppay.bo.paymentquery.PaymentOrderResultBO;
import com.cmpay.dceppay.enums.pay.OrderStatusEnum;

/**
 * <AUTHOR>
 * @date 2024/9/3 18:01
 */
public interface IPayOrderService {

    MerchantRouteBO checkAndQueryMerchantRoute(UnifiedOrderReqBO unifiedOrderReqBO);

    /**
     * 查询成功，通知成功订单处理
     * @param paymentOrderResultBO 订单查询通知结果
     * @param payOrderDBBO  数据库订单对象
     */
    void  handleSuccessPayOrder(PaymentOrderResultBO paymentOrderResultBO, PayOrderDBBO payOrderDBBO);

    /**
     * 支付失败订单处理
     * @param paymentOrderResultBO
     * @param payOrderDBBO
     */

    void handleFailPayOrder(PaymentOrderResultBO paymentOrderResultBO, PayOrderDBBO payOrderDBBO);

    boolean checkPaymentStatus(OrderStatusEnum orderStatusEnum, PayOrderDBBO order, PaymentNotifyRspBO paymentNotifyRspBO);
}
