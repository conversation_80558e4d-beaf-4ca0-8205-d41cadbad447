package com.cmpay.dceppay.service.mng.ext.impl;

import com.cmpay.channel.data.Request;
import com.cmpay.dceppay.bo.bus.BusMessageBO;
import com.cmpay.dceppay.bo.mng.FreeFormatBO;
import com.cmpay.dceppay.channel.DcepChannelEnum;
import com.cmpay.dceppay.config.DceppayConfig;
import com.cmpay.dceppay.constant.dcep.DcepResponseStatusConstant;
import com.cmpay.dceppay.dto.dcep.CommonConfirmation.CommonConfirmationBody;
import com.cmpay.dceppay.dto.dcep.CommonConfirmation.CommonConfirmationInformation;
import com.cmpay.dceppay.dto.dcep.common.RequestGroupHeader;
import com.cmpay.dceppay.dto.dcep.common.SoapHeader;
import com.cmpay.dceppay.dto.dcep.freeformat.FreeFormatRsp;
import com.cmpay.dceppay.dto.dcep.freeformat.request.FreeFormatBody;
import com.cmpay.dceppay.dto.dcep.freeformat.request.FreeFormatInformation;
import com.cmpay.dceppay.dto.dcep.freeformat.request.FreeFormatSoapEnvelopRequest;
import com.cmpay.dceppay.enums.MsgCodeEnum;
import com.cmpay.dceppay.enums.dcep.DcepMessageTypeEnum;
import com.cmpay.dceppay.service.bus.IBusMessageDBService;
import com.cmpay.dceppay.service.ext.DceppayParamService;
import com.cmpay.dceppay.service.mng.ext.IFreeFormatExtService;
import com.cmpay.dceppay.util.DateTimeUtil;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.utils.LemonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/11/5 15:26
 */
@Service
@Slf4j
public class IFreeFormatExtServiceImpl implements IFreeFormatExtService {
    @Autowired
    private DceppayConfig dceppayConfig;
    @Autowired
    private DceppayParamService dceppayParamService;
    @Autowired
    private IBusMessageDBService messageDBService;

    @Override
    public FreeFormatSoapEnvelopRequest buildRequestParam(FreeFormatBO freeFormatBO) {
        FreeFormatSoapEnvelopRequest freeFormatSoapEnvelopRequest = new FreeFormatSoapEnvelopRequest();
        //报文头
        String messageIdentification = dceppayParamService.getMessageIdentification(DcepMessageTypeEnum.FREE_FORMAT_REQUEST.getValue(), dceppayConfig.getWalletId());
        SoapHeader soapHeader = dceppayParamService.getCommonSoapHear(DcepMessageTypeEnum.FREE_FORMAT_REQUEST.getMessageType(), messageIdentification);
        BeanUtils.copyProperties(freeFormatSoapEnvelopRequest, soapHeader);
        FreeFormatBody freeFormatBody = new FreeFormatBody();
        RequestGroupHeader requestGroupHeader = dceppayParamService.getRequestGroupHeader(DcepMessageTypeEnum.FREE_FORMAT_REQUEST.getMessageType(), messageIdentification);
        freeFormatBody.setRequestGroupHeader(requestGroupHeader);

        FreeFormatInformation freeFormatInformation = new FreeFormatInformation();
        freeFormatInformation.setMessageContent(freeFormatBO.getMessageContent());
        freeFormatBody.setFreeFormatInformation(freeFormatInformation);

        freeFormatSoapEnvelopRequest.setFreeFormatBody(freeFormatBody);
        return freeFormatSoapEnvelopRequest;
    }

    @Override
    public Request bulidRequest(FreeFormatSoapEnvelopRequest freeFormatSoapEnvelopRequest) {
        Request request = new Request();
        request.setRequestId(LemonUtils.getRequestId());
        request.setRoute(DcepChannelEnum.DCEP_PAY);
        request.setBusiType(DcepChannelEnum.FREE_FORMAT.getName());
        request.setSource(DcepChannelEnum.DCEP_PAY);
        request.setTarget(freeFormatSoapEnvelopRequest);
        return request;
    }

    @Override
    public void handleResponse(FreeFormatRsp freeFormatRsp, FreeFormatBO freeFormatBO) {
        if (JudgeUtils.isNull(freeFormatRsp) || JudgeUtils.isNull(freeFormatRsp.getCommonConfirmationResponse())) {
            log.error("发送自由格式消息互联互通响应为空：{}", freeFormatRsp);
            BusinessException.throwBusinessException(MsgCodeEnum.FREE_FORMAT_DCEP_ERROR);
        }
        CommonConfirmationBody commonConfirmationBody = freeFormatRsp.getCommonConfirmationResponse().getCommonConfirmationBody();
        if (JudgeUtils.isNotNull(commonConfirmationBody) && JudgeUtils.isNotNull(commonConfirmationBody.getCommonConfirmationInformation())) {
            CommonConfirmationInformation confirmationInformation = commonConfirmationBody.getCommonConfirmationInformation();
            if (JudgeUtils.equals(confirmationInformation.getProcessStatus(), DcepResponseStatusConstant.SUCCESS)) {
                try {
                    messageDBService.addBusMessage(getBusMessage(freeFormatBO));
                } catch (Exception e) {
                    log.error("发送自由格式消息至互联互通成功，登记业务信息表失败：{}", e.getCause());
                    BusinessException.throwBusinessException(MsgCodeEnum.FREE_FORMAT_EXCEPTION);
                }
            } else {
                BusinessException.throwBusinessException(MsgCodeEnum.FREE_FORMAT_DCEP_FAIL, confirmationInformation.getProcessCode() + confirmationInformation.getRejectInformation());
            }
        } else {
            BusinessException.throwBusinessException(MsgCodeEnum.FREE_FORMAT_DCEP_ERROR);
        }
    }

    private BusMessageBO getBusMessage(FreeFormatBO freeFormatBO) {
        BusMessageBO busMessageBO = new BusMessageBO();
        busMessageBO.setMessageDate(DateTimeUtil.getCurrentDateStr());
        busMessageBO.setMessageTime(DateTimeUtil.getCurrentDateTimeStr());
        busMessageBO.setMessageType(DcepMessageTypeEnum.FREE_FORMAT_REQUEST.getMessageType());
        busMessageBO.setSender(dceppayConfig.getSenderCode());
        busMessageBO.setReceiver(dceppayConfig.getReceiverCode());
        busMessageBO.setMessageContent(freeFormatBO.getMessageContent());
        busMessageBO.setOperateId(freeFormatBO.getOperateId());
        return busMessageBO;
    }
}
