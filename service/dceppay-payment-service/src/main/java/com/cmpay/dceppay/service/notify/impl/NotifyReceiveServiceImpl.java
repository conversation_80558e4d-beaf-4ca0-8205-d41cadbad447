package com.cmpay.dceppay.service.notify.impl;

import com.cmpay.dceppay.bo.notify.PaymentNotifyBO;
import com.cmpay.dceppay.bo.notify.PaymentNotifyRspBO;
import com.cmpay.dceppay.bo.notify.RefundNotifyBO;
import com.cmpay.dceppay.bo.notify.RefundNotifyRspBO;
import com.cmpay.dceppay.bo.pay.PayOrderDBBO;
import com.cmpay.dceppay.bo.pay.RefundOrderDBBO;
import com.cmpay.dceppay.enums.MsgCodeEnum;
import com.cmpay.dceppay.service.channel.IPaymentChannelService;
import com.cmpay.dceppay.service.notify.INotifyReceiveService;
import com.cmpay.dceppay.service.pay.IPayOrderDBService;
import com.cmpay.dceppay.service.pay.IPayRefundDBService;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.lock.DistributedLocked;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/9/17 15:48
 */
@Service
public class NotifyReceiveServiceImpl implements INotifyReceiveService {
    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private IPayOrderDBService orderDBService;
    @Autowired
    private IPayRefundDBService refundDBService;

    @Override
    @DistributedLocked(lockName = "'paymentNotify:'+#paymentNotifyBO.getOutOrderNumber()", leaseTime = 40, waitTime = 10)
    public PaymentNotifyRspBO paymentNotify(PaymentNotifyBO paymentNotifyBO) {
        PayOrderDBBO order = orderDBService.getByOutOrderNo(paymentNotifyBO.getOutOrderNumber());
        if (JudgeUtils.isNull(order)) {
            BusinessException.throwBusinessException(MsgCodeEnum.ORG_ORDER_NOT_EXISTS);
        }
        String channelName = order.getPayWay() + order.getScene();
        IPaymentChannelService paymentChannelService = (IPaymentChannelService) applicationContext.getBean(channelName);
        return paymentChannelService.paymentNotify(order, paymentNotifyBO);
    }

    @Override
    @DistributedLocked(lockName = "'refundNotify:'+#refundNotifyBO.getOutRefundNo()", leaseTime = 40, waitTime = 10)
    public RefundNotifyRspBO refundNotify(RefundNotifyBO refundNotifyBO) {
        RefundOrderDBBO refundOrder = refundDBService.getByOutRefundNo(refundNotifyBO.getOutRefundNo());
        if (JudgeUtils.isNull(refundOrder)) {
            BusinessException.throwBusinessException(MsgCodeEnum.REFUND_ORDER_NOT_EXISTS);
        }
        String channelName = refundOrder.getPayWay() + refundOrder.getScene();
        IPaymentChannelService paymentChannelService = (IPaymentChannelService) applicationContext.getBean(channelName);
        return paymentChannelService.refundNotify(refundOrder, refundNotifyBO);
    }
}
