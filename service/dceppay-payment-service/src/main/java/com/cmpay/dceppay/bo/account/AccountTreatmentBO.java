package com.cmpay.dceppay.bo.account;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/11 13:56
 * 账务处理请求对象
 */
@Data
public class AccountTreatmentBO {
    //订单号
    private String orderNo;
    //订单类型
    private String orderType;
    //业务流水号
    private String businessJrnNo;
    //订单日期
    private String tradeDate;
    //订单时间
    private String tradeTime;
    // 业务类型 P101
    private String busType;
    //y业务渠道  CAS
    private String busChannel;
    // 交易类型 P1
    private String tradeType;
    // 业务交易码 AccountTradeCodeEnum
    private String tradeCode;

    private List<AccountDetailBO> accountDetailBOS;

    private BigDecimal orderAmount;;

}
