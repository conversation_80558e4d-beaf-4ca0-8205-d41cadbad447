package com.cmpay.dceppay.bo.refundquery;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/8/30 11:41
 * 退款订单查询响应对象
 */
@Data
public class RefundOrderResultBO {

    private  String outOrderNo;
    private String outRefundNo;
    private String finishDateTime;
    private BigDecimal refundAmount;
    private  String bankRefundNo;
    private String refundStatus;
    private String errMsgCd;
    private String errMsgInfo;
    private String refundReason;
    private String extra;

}
