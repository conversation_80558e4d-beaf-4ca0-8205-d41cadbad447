package com.cmpay.dceppay.service.notify.impl;

import com.cmpay.dceppay.bo.notify.NotifyKeyBO;
import com.cmpay.dceppay.bo.notify.PaymentSendNotifyBO;
import com.cmpay.dceppay.bo.notify.PaymentSendNotifyResultBO;
import com.cmpay.dceppay.bo.pay.PayNotifyBO;
import com.cmpay.dceppay.bo.pay.PayOrderDBBO;
import com.cmpay.dceppay.constant.pay.NotifyConstants;
import com.cmpay.dceppay.constant.pay.PaymentConstants;
import com.cmpay.dceppay.enums.pay.ChannelCodeEnum;
import com.cmpay.dceppay.enums.pay.OrderTypeEnum;
import com.cmpay.dceppay.outclient.igw.IPayCenterClientService;
import com.cmpay.dceppay.service.notify.INotifySendDataService;
import com.cmpay.dceppay.service.notify.INotifySendService;
import com.cmpay.dceppay.service.pay.IPayNotifyDBService;
import com.cmpay.dceppay.service.pay.IPayOrderDBService;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/9/17 9:33
 */
@Service
@Slf4j
public class NotifySendServiceImpl implements INotifySendService {
    @Autowired
    private IPayCenterClientService payCenterClientService;
    @Autowired
    private IPayOrderDBService payOrderDBService;
    @Autowired
    private IPayNotifyDBService notifyDBService;

    @Autowired
    private INotifySendDataService notifySendDataService;

    @Override
    public void sendPaymentSuccessNotify(PayOrderDBBO payOrderDBBO) {
        if (JudgeUtils.notEquals(payOrderDBBO.getChannelCode(), ChannelCodeEnum.PAY_CENTER.getChannelCode())) {
            log.warn("非支付中台订单，无需下发支付结果通知");
            return;
        }
        PaymentSendNotifyBO paymentSendNotifyBO = notifySendDataService.getPaymentSendSuccessNotifyBO(payOrderDBBO);
        PaymentSendNotifyResultBO sendNotifyResultBO = payCenterClientService.paymentNotify(paymentSendNotifyBO);
        PayNotifyBO payNotifyBO = getNotifyBO(payOrderDBBO);
        if (sendNotifyResultBO.isNotifySuccess()) {
            payOrderDBService.updateNotifyInfo(payNotifyBO.getOutOrderNo());
            notifyDBService.updateNotifySuccess(payNotifyBO);
        } else {
            PayNotifyBO notifyBO = notifyDBService.getByKey(BeanUtils.copyPropertiesReturnDest(new NotifyKeyBO(), payNotifyBO));
            if (JudgeUtils.isNull(notifyBO)) {
                notifyDBService.addNotifyRecord(payNotifyBO);
            } else {
                notifyDBService.updateNotifyCount(payNotifyBO);
            }
        }
    }


    private PayNotifyBO getNotifyBO(PayOrderDBBO payOrder) {
        PayNotifyBO payNotifyBO = new PayNotifyBO();
        BeanUtils.copyProperties(payNotifyBO, payOrder);
        payNotifyBO.setRefundOrderNo(PaymentConstants.DEFAULT_REFUND_NO);
        payNotifyBO.setNotifyStatus(NotifyConstants.NOTIFY_STATUS_FAIL);
        payNotifyBO.setNotifyType(OrderTypeEnum.PAYMENT.name());
        return payNotifyBO;
    }

}
