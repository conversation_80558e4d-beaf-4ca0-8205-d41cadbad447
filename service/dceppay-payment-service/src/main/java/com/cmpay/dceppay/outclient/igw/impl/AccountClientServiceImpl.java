package com.cmpay.dceppay.outclient.igw.impl;

import com.cmpay.account.dto.*;
import com.cmpay.account.enums.ApplicationIdEnum;
import com.cmpay.dceppay.bo.account.*;
import com.cmpay.dceppay.constant.pay.AccountConstants;
import com.cmpay.dceppay.enums.MsgCodeEnum;
import com.cmpay.dceppay.enums.pay.AccountProcessStatusEnum;
import com.cmpay.dceppay.outclient.igw.IAccountClientService;
import com.cmpay.dceppay.outclient.igw.ext.IAccountClientExtService;
import com.cmpay.igw.account.client.AccountGwClient;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2024/9/11 13:53
 */
@Service
@Slf4j
public class AccountClientServiceImpl implements IAccountClientService {
    @Autowired
    private AccountGwClient accountGwClient;
    @Autowired
    private IAccountClientExtService accountClientExtService;


    @Override
    public AccountTreatmentResultBO accountTreatment(AccountTreatmentBO accountTreatmentBO) {
        AccountingTreatmentReqDTO accountingTreatmentReqDTO = new AccountingTreatmentReqDTO();
        accountingTreatmentReqDTO.setOrderNo(accountTreatmentBO.getOrderNo());
        accountingTreatmentReqDTO.setOrderType(accountTreatmentBO.getOrderType());
        accountingTreatmentReqDTO.setBusinessType(accountTreatmentBO.getBusType());
        accountingTreatmentReqDTO.setBusinessChannel(accountTreatmentBO.getBusChannel());
        accountingTreatmentReqDTO.setTradeType(accountTreatmentBO.getTradeType());
        accountingTreatmentReqDTO.setBusinessJrnNo(accountTreatmentBO.getBusinessJrnNo());
        accountingTreatmentReqDTO.setBusinessTradeDate(accountTreatmentBO.getTradeDate());
        accountingTreatmentReqDTO.setBusinessTradeCode(accountTreatmentBO.getTradeCode());
        accountingTreatmentReqDTO.setActionFrom(ApplicationIdEnum.valueOf(AccountConstants.ACCOUNT_FROM_DCEP));
        List<AccountDetailBO> accountDetailBOS = accountTreatmentBO.getAccountDetailBOS();
        if (JudgeUtils.isNull(accountDetailBOS)) {
            log.error("请求账务处理失败：账务数组为空：{}", accountTreatmentBO);
            BusinessException.throwBusinessException(MsgCodeEnum.ACCOUNT_CENTER_RESPONSE_ERROR);
        }
        List<AccountDebitOrCreditDTO> accountDebitOrCreditDTOS = accountClientExtService.getAccountList(accountTreatmentBO, accountDetailBOS);
        accountingTreatmentReqDTO.setAccountRecords(accountDebitOrCreditDTOS);
        accountingTreatmentReqDTO.setAccountRecordsLength(accountDebitOrCreditDTOS.size());
        AccountingTreatmentRspDTO rspDTO = accountGwClient.accountingTreatment(accountingTreatmentReqDTO);
        if (JudgeUtils.isNotSuccess(rspDTO)) {
            log.error("请求账务中心进行账务处理失败：{}", rspDTO);
            BusinessException.throwBusinessException(MsgCodeEnum.ACCOUNT_CENTER_RESPONSE_ERROR);
        }
        AccountTreatmentResultBO resultBO = new AccountTreatmentResultBO();
        resultBO.setBusinessJrnNo(accountTreatmentBO.getBusinessJrnNo());
        if (JudgeUtils.equals(AccountConstants.ACM_EXISTS_FLG_SUCCESS, rspDTO.getAcmExistsFlag())) {
            resultBO.setAccountHandleStatus(AccountProcessStatusEnum.SUCCESS.name());
            resultBO.setAccountDate(rspDTO.getAccountingDate());
        } else {
            resultBO.setAccountHandleStatus(AccountProcessStatusEnum.WAIT.name());
        }
        return resultBO;
    }


    @Override
    public AccountTreatmentQueryResponseBO accountQuery(AccountTreatmentQueryRequestBO queryRequestBO) {
        AccountingTreatmentQueryReqDTO accountingTreatmentQueryReqDTO = new AccountingTreatmentQueryReqDTO();
        accountingTreatmentQueryReqDTO.setBusinessJrnNo(queryRequestBO.getJrnNo());
        accountingTreatmentQueryReqDTO.setOrderNo(queryRequestBO.getOrderNo());
        accountingTreatmentQueryReqDTO.setBusinessTradeDate(queryRequestBO.getOrderDate());
        AccountingTreatmentQueryRspDTO accountingTreatmentQueryRspDTO = accountGwClient.queryAccountingTreatment(accountingTreatmentQueryReqDTO);
        if (JudgeUtils.isNotSuccess(accountingTreatmentQueryRspDTO)) {
            log.error("请求账务中心进行账务查询处理失败：{}", accountingTreatmentQueryRspDTO);
            BusinessException.throwBusinessException(MsgCodeEnum.ACCOUNT_CENTER_RESPONSE_ERROR);
        }
        AccountTreatmentQueryResponseBO accountTreatmentQueryResponseBO = new AccountTreatmentQueryResponseBO();
        if (JudgeUtils.equals(AccountConstants.ACM_EXISTS_FLG_SUCCESS, accountingTreatmentQueryRspDTO.getAcmExistsFlag())) {
            accountTreatmentQueryResponseBO.setAccountHandleStatus(AccountProcessStatusEnum.SUCCESS.name());
            accountTreatmentQueryResponseBO.setAccountDate(accountingTreatmentQueryRspDTO.getAccountDate());
        } else if (JudgeUtils.equals(AccountConstants.ACM_EXISTS_FLG_CANCEL_REVERSE_SUCCESS, accountingTreatmentQueryRspDTO.getAcmExistsFlag())) {
            accountTreatmentQueryResponseBO.setAccountHandleStatus(AccountProcessStatusEnum.REVERSE.name());
        } else if (JudgeUtils.equals(AccountConstants.ACM_NOT_EXISTS_FLG, accountingTreatmentQueryRspDTO.getAcmExistsFlag())) {
            accountTreatmentQueryResponseBO.setAccountHandleStatus(AccountProcessStatusEnum.NORECORD.name());
        } else {
            accountTreatmentQueryResponseBO.setAccountHandleStatus(AccountProcessStatusEnum.WAIT.name());
        }
        return accountTreatmentQueryResponseBO;
    }

    @Override
    public void accountCancel(AccountTreatmentCancelBO cancelBO) {
        AccountingTreatmentCancelReqDTO cancelReqDTO = new AccountingTreatmentCancelReqDTO();
        cancelReqDTO.setBusinessJrnNo(cancelBO.getJrnNo());
        cancelReqDTO.setOrderNo(cancelBO.getOrderNo());
        cancelReqDTO.setBusinessTradeDate(cancelBO.getOrderDate());
        RespNobodyDTO respNobodyDTO = accountGwClient.accountingTreatmentCancel(cancelReqDTO);
        if (JudgeUtils.isNotSuccess(respNobodyDTO)) {
            log.error("请求账务中心进行账务处理冲正失败：{}", respNobodyDTO);
        }
    }

    @Override
    public void accountReverse(AccountTreatmentReverseBO reverseBO) {
        AccountingTreatmentReverseReqDTO reverseReqDTO = new AccountingTreatmentReverseReqDTO();
        reverseReqDTO.setBusinessJrnNo(reverseBO.getJrnNo());
        reverseReqDTO.setOrderNo(reverseBO.getOrderNo());
        reverseReqDTO.setBusinessTradeDate(reverseBO.getOrderDate());
        RespNobodyDTO respNobodyDTO = accountGwClient.accountingTreatmentReverse(reverseReqDTO);
        if (JudgeUtils.isNotSuccess(respNobodyDTO)) {
            log.error("请求账务中心进行账务处理撤销失败：{}", respNobodyDTO);
        }
    }
}
