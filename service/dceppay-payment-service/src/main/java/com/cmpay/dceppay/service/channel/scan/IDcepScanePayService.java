package com.cmpay.dceppay.service.channel.scan;

import com.cmpay.dceppay.bo.pay.MerchantRouteBO;
import com.cmpay.dceppay.bo.payment.UnifiedOrderReqBO;
import com.cmpay.dceppay.bo.payment.UnifiedOrderRspBO;
import com.cmpay.dceppay.dto.dcep.unifiedorder.request.TransactionInformation;
import com.cmpay.dceppay.dto.dcep.unifiedorder.response.UnifiedOrderSoapEnvelopResponse;
import com.cmpay.dceppay.enums.dcep.CreatingOrderTypeEnum;
import com.cmpay.dceppay.enums.dcep.TransactionTypeEnum;
import com.cmpay.dceppay.service.channel.abs.AbstractPaymentChannelService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className IDcepScanePayService
 * @date 2025/9/19
 * @description 数币收单扫码支付
 * <p>
 * 扫码支付流程和app支付流程一致，只是下单接口需要对交易信息进行处理，
 * 特殊处理处理放到DcepUnifiedOrderExtServiceImpl中进行了处理，
 * 因此当前类没有重写方法
 */
@Service(value = "dcepscan")
public class IDcepScanePayService extends AbstractPaymentChannelService {

    @Override
    public void handleSuccessResponse(UnifiedOrderReqBO unifiedOrderReqBO, MerchantRouteBO merchantRouteBO,
        UnifiedOrderSoapEnvelopResponse unifiedOrderSoapEnvelopResponse, UnifiedOrderRspBO unifiedOrderRspBO) {
        String qrCode = unifiedOrderSoapEnvelopResponse.getSoapBody().getQrCodeInto().getQrCode();
        unifiedOrderRspBO.setQrCodeUrl(qrCode);
    }

    @Override
    public void getTransactionInfo(TransactionInformation transactionInformation) {
        transactionInformation.setTransactionType(TransactionTypeEnum.SCAN_PAY.getValue());
        transactionInformation.setCreatingOrderType(CreatingOrderTypeEnum.APPLY_DYNAMIC_CODE.getCode());
    }
}
