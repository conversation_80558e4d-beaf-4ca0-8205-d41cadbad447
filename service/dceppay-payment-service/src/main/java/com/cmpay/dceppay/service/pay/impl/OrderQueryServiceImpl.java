package com.cmpay.dceppay.service.pay.impl;

import com.cmpay.dceppay.bo.pay.PayOrderDBBO;
import com.cmpay.dceppay.bo.pay.RefundOrderDBBO;
import com.cmpay.dceppay.bo.paymentquery.PaymentOrderQueryBO;
import com.cmpay.dceppay.bo.paymentquery.PaymentOrderResultBO;
import com.cmpay.dceppay.bo.refundquery.RefundOrderQueryBO;
import com.cmpay.dceppay.bo.refundquery.RefundOrderResultBO;
import com.cmpay.dceppay.enums.MsgCodeEnum;
import com.cmpay.dceppay.enums.pay.OrderStatusEnum;
import com.cmpay.dceppay.service.pay.IOrderQueryService;
import com.cmpay.dceppay.service.pay.IPayOrderDBService;
import com.cmpay.dceppay.service.pay.IPayRefundDBService;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/9/6 9:59
 */
@Service
public class OrderQueryServiceImpl implements IOrderQueryService {
    @Autowired
    private IPayOrderDBService payOrderDBService;
    @Autowired
    private IPayRefundDBService refundOrderDBService;


    @Override
    public PaymentOrderResultBO paymentQuery(PaymentOrderQueryBO paymentOrderQueryBO) {
        PayOrderDBBO payOrderDBBO = payOrderDBService.getByOutOrderNo(paymentOrderQueryBO.getOutOrderNo());
        if (JudgeUtils.isNull(payOrderDBBO)) {
            BusinessException.throwBusinessException(MsgCodeEnum.ORG_ORDER_NOT_EXISTS);
        }
        return getPaymentOrderQueryRspBO(payOrderDBBO);
    }


    @Override
    public RefundOrderResultBO refundQuery(RefundOrderQueryBO refundOrderQueryBO) {
        RefundOrderDBBO refundOrderDBBO = refundOrderDBService.getByOutRefundNo(refundOrderQueryBO.getOutRefundNo());
        if (JudgeUtils.isNull(refundOrderDBBO)) {
            BusinessException.throwBusinessException(MsgCodeEnum.REFUND_ORDER_NOT_EXISTS);
        }
        return getRefundOrderQueryRspBO(refundOrderDBBO);
    }

    private RefundOrderResultBO getRefundOrderQueryRspBO(RefundOrderDBBO refundOrderDBBO) {
        RefundOrderResultBO refundOrderResultBO = new RefundOrderResultBO();
        refundOrderResultBO.setOutOrderNo(refundOrderDBBO.getOutOrderNo());
        refundOrderResultBO.setOutRefundNo(refundOrderDBBO.getRefundOrderNo());
        refundOrderResultBO.setRefundAmount(refundOrderDBBO.getRefundAmount());
        refundOrderResultBO.setRefundStatus(refundOrderDBBO.getStatus());
        refundOrderResultBO.setRefundReason(refundOrderDBBO.getRefundReason());
        refundOrderResultBO.setFinishDateTime(refundOrderDBBO.getOrderCompleteTime());
        refundOrderResultBO.setExtra(refundOrderDBBO.getExtra());
        refundOrderResultBO.setErrMsgCd(refundOrderDBBO.getErrMsgCd());
        refundOrderResultBO.setErrMsgInfo(refundOrderDBBO.getErrMsgInfo());
        return refundOrderResultBO;
    }

    private static PaymentOrderResultBO getPaymentOrderQueryRspBO(PayOrderDBBO payOrderDBBO) {
        PaymentOrderResultBO orderQueryRspBO = new PaymentOrderResultBO();
        orderQueryRspBO.setOutOrderNo(payOrderDBBO.getOutOrderNo());
        orderQueryRspBO.setTotalAmount(payOrderDBBO.getOrderAmount());
        orderQueryRspBO.setFinishDateTime(payOrderDBBO.getOrderCompleteTime());
        orderQueryRspBO.setExtra(payOrderDBBO.getExtra());
        orderQueryRspBO.setBankOrderNo(payOrderDBBO.getBankOrderNo());
        orderQueryRspBO.setErrMsgCd(payOrderDBBO.getErrMsgCd());
        orderQueryRspBO.setErrMsgInfo(payOrderDBBO.getErrMsgInfo());
        String orderStatus = payOrderDBBO.getStatus();
        orderQueryRspBO.setOrderStatus(orderStatus);
        if (JudgeUtils.equals(OrderStatusEnum.WAIT_PAY.name(), orderStatus)) {
            LocalDateTime currentDateTime = DateTimeUtils.parseLocalDateTime(DateTimeUtils.getCurrentDateTimeStr());
            if (JudgeUtils.isNotNull(payOrderDBBO.getOrderTimeExpire())) {
                boolean isExpired = currentDateTime.isAfter(DateTimeUtils.parseLocalDateTime(payOrderDBBO.getOrderTimeExpire()));
                if (isExpired) {
                    orderQueryRspBO.setOrderStatus(OrderStatusEnum.TRADE_CLOSED.name());
                }
            }
        }
        return orderQueryRspBO;
    }
}
