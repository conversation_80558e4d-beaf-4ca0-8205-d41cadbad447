package com.cmpay.dceppay.bo.notify;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/8/30 15:46
 */
@Data
public class PaymentNotifyBO extends NotifyBaseBO {
    /**
     * 业务回执状态
     * PR00:支付成功
     * PR01:支付失败
     */
    private String responseStatus;

    /**
     * 业务拒绝码 当业务回执状态为“PR01”时必填
     */
    private String rejectCode;

    /**
     * 业务拒绝信息
     */
    private String rejectInformation;

    /**
     * 订单金额
     */
    private BigDecimal transactionAmount;

    /**
     * 商户订单号 必输
     */
    private String outOrderNumber;

    /**
     * 订单号 必输
     */
    private String orderNumber;

    /**
     * 交易完成时间  业务回执状态为PR00时必填,由收款运营机构填写
     */
    private String transactionFinishTime;


}
