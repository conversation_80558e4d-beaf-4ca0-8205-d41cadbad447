package com.cmpay.dceppay.service.pay;

import com.cmpay.dceppay.bo.common.CheckResultBO;
import com.cmpay.dceppay.bo.payment.UnifiedOrderReqBO;

/**
 * <AUTHOR>
 * @date 2024/9/4 13:38
 * 订单校验service
 */
public interface IPayOrderCheckService {
    /**
     * 下单请求参数校验
     * @param unifiedOrderReqBO
     * @return
     */
    CheckResultBO unifiedOrderRequestCheck(UnifiedOrderReqBO unifiedOrderReqBO);


}
