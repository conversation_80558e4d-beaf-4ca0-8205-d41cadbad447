package com.cmpay.dceppay.stream.client;

import com.cmpay.dceppay.bo.pay.PayOrderDBBO;
import com.cmpay.dceppay.bo.pay.RefundOrderDBBO;
import com.cmpay.lemon.framework.stream.Source;
import com.cmpay.lemon.framework.stream.StreamClient;


/**
 * <AUTHOR>
 * @date 2024/11/14 8:33
 * 异步消费应用
 */
@StreamClient("dceppay-schedule")
public interface DceppayAsyncClient {
    /**
     * 支付结果异步通知
     * @param payOrderDBBO
     */
    @Source(handlerBeanName = "notifyHandler", group = "dceppay-schedule", prefix = "mirror.")
    void asyncNotify(PayOrderDBBO payOrderDBBO);


    /**
     * 退款异步发起
     * @param refundOrderBO
     */
    @Source(handlerBeanName = "refundHandler", group = "dceppay-schedule", prefix = "mirror.")
    void asyncRefund(RefundOrderDBBO refundOrderBO);

}
