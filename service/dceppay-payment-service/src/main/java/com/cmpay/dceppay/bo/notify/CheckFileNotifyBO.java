package com.cmpay.dceppay.bo.notify;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/23 9:17
 */
@Data
public class CheckFileNotifyBO extends NotifyBaseBO{
    /**
     * 对账日期
     */
    private String checkDate;
    /**
     * 总笔数
     */
    private int countNumber;

    /**
     * 总金额
     */
    private BigDecimal countAmount;

    /**
     * 支付总笔数
     */
    private int paymentCountNumber;

    /**
     * 支付总金额
     */
    private BigDecimal paymentCountAmount;

    /**
     * 退款总笔数
     */
    private int refundCountNumber;

    /**
     * 退款总金额
     */
    private BigDecimal refundCountAmount;

    /**
     * 总文件数
     */
    private int fileInfoNumber;

    /**
     * 文件路径 A;B;
     */
    private String filePath;
    /**
     * 文件名称列表 A1|A2；B1|B2;
     */
    private String fileName;
    private String digitalEnv;
}
