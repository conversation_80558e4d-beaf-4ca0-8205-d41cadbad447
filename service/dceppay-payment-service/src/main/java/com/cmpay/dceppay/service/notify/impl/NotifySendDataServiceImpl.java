package com.cmpay.dceppay.service.notify.impl;

import com.cmpay.dceppay.bo.notify.PaymentSendNotifyBO;
import com.cmpay.dceppay.bo.pay.PayOrderDBBO;
import com.cmpay.dceppay.service.notify.INotifySendDataService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/9/17 15:13
 */
@Service
public class NotifySendDataServiceImpl implements INotifySendDataService {
    @Override
    public PaymentSendNotifyBO getPaymentSendSuccessNotifyBO(PayOrderDBBO payOrderDBBO) {
        PaymentSendNotifyBO paymentSendNotifyBO = new PaymentSendNotifyBO();
        paymentSendNotifyBO.setOrderDate(payOrderDBBO.getOrderDate());
        paymentSendNotifyBO.setTotalAmount(payOrderDBBO.getOrderAmount());
        paymentSendNotifyBO.setBankOrderNo(payOrderDBBO.getBankOrderNo());
        paymentSendNotifyBO.setOutOrderNo(payOrderDBBO.getOutOrderNo());
        paymentSendNotifyBO.setTradeJrnNo(payOrderDBBO.getTradeJrnNo());
        return paymentSendNotifyBO;
    }

}
