package com.cmpay.dceppay.service.ext;

import com.cmpay.dceppay.config.DceppayConfig;
import com.cmpay.dceppay.constant.dcep.DcepUrlConstants;
import com.cmpay.dceppay.dto.dcep.common.RequestGroupHeader;
import com.cmpay.dceppay.dto.dcep.common.SoapHeader;
import com.cmpay.dceppay.enums.MsgCodeEnum;
import com.cmpay.dceppay.enums.common.IdGenKeyEnum;
import com.cmpay.dceppay.enums.dcep.DcepMessageTypeEnum;
import com.cmpay.dceppay.service.mng.IDcepRequestUrlService;
import com.cmpay.dceppay.util.DateTimeUtil;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.utils.IdGenUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/9/6 14:36
 */
@Slf4j
@Service
public class DceppayParamService {
    @Autowired
    private DceppayConfig dceppayConfig;

    @Autowired
    private IDcepRequestUrlService requestUrlService;

    /**
     * 获取报文标识号
     *
     * @return 8位日期：yyyymmdd
     * 3位机构标识
     * 1位跨行标识:0表示机构内交易 1表示跨机构交易
     * 3位报文编号：dcep.xxx.001.01中的xxx
     * 14位序列号:机构生成
     * 2位控制码: 取钱包ID控制位，请求报文栏位为否，固定“00”，响应报文控制码与请求报文控制码一致
     * 1位环境标志位：0测试环境 1生产环境，2 生产环境压测数据
     */
    public String getMessageIdentification(String messageType, String walletId) {
        String date = DateTimeUtil.getCurrentDateStr();
        // 3位机构标识
        String institutionId = dceppayConfig.getInstitutionId();
        //14位序列号 6+8
        String sequenceNumber = DateTimeUtil.getCurrentTimeStr() + IdGenUtils.generateId(String.valueOf(IdGenKeyEnum.MESSAGE_SEQ_NO), IdGenKeyEnum.MESSAGE_SEQ_NO_LENGTH);
        // 2位控制码
        String controlCode = walletId.substring(0, 2);
        String interbankFlag = dceppayConfig.getInterbankFlag();
        String environmentFlag = dceppayConfig.getEnvironmentFlag();
        return date + institutionId + interbankFlag + messageType + sequenceNumber + controlCode + environmentFlag;
    }

    public String getCommunicationIdentifier(String messageIdentification) {
        return messageIdentification + IdGenUtils.generateId(String.valueOf(IdGenKeyEnum.COMMUNICATION_SEQ_NO), IdGenKeyEnum.COMMUNICATION_SEQ_NO_LENGTH);
    }

    public RequestGroupHeader getRequestGroupHeader(String messageType,String messageIdentification) {
        RequestGroupHeader requestGroupHeader = new RequestGroupHeader();
        requestGroupHeader.setMessageIdentification(messageIdentification);
        requestGroupHeader.setCreationDateTime(DateTimeUtil.getCurrentISODateTime());
        requestGroupHeader.setInstructingDirectParty(dceppayConfig.getSenderCode());
        if (JudgeUtils.equalsAny(messageType,
                DcepMessageTypeEnum.FREE_FORMAT_REQUEST.getMessageType(),
                DcepMessageTypeEnum.LOGIN_REQUEST.getMessageType()
        )) {
            requestGroupHeader.setInstructedDirectParty(dceppayConfig.getReceiverCode());
        } else {
            requestGroupHeader.setInstructedDirectParty(dceppayConfig.getCreditorCode());
        }
        return requestGroupHeader;
    }

    public SoapHeader getCommonSoapHear(String messageType, String messageIdentification) {
        SoapHeader soapHeader = new SoapHeader();
        soapHeader.setVersion(dceppayConfig.getVersion());
        soapHeader.setSendDateTm(DateTimeUtil.getCurrentISODateTime());
        soapHeader.setMessageType(messageType);
        soapHeader.setMsgSN(getCommunicationIdentifier(messageIdentification));
        soapHeader.setSender(dceppayConfig.getSenderCode());
        if (JudgeUtils.equalsAny(messageType,
                DcepMessageTypeEnum.FREE_FORMAT_REQUEST.getMessageType(),
                DcepMessageTypeEnum.LOGIN_REQUEST.getMessageType()
        )) {
            soapHeader.setReceiver(dceppayConfig.getReceiverCode());
        } else {
            soapHeader.setReceiver(dceppayConfig.getCreditorCode());
        }
        DcepMessageTypeEnum messageTypeEnum = DcepMessageTypeEnum.getByMessageType(messageType);
        if (JudgeUtils.isNull(messageTypeEnum)) {
            BusinessException.throwBusinessException(MsgCodeEnum.MESSAGE_TYPE_ERROR);
        }
        soapHeader.setSignSN(dceppayConfig.getSignSN());
        if (JudgeUtils.equals(messageType, DcepMessageTypeEnum.DETECTION_REQUEST.getMessageType())) {
            soapHeader.setRequestUrl(requestUrlService.getRequestUrl(messageTypeEnum.getIdcInfo()) + DcepUrlConstants.DCEP_EXPLORING_URL);
        } else {
            soapHeader.setRequestUrl(requestUrlService.getRequestUrl(messageTypeEnum.getIdcInfo()) + DcepUrlConstants.DCEP_BUS_URL);
        }
        return soapHeader;
    }


}
