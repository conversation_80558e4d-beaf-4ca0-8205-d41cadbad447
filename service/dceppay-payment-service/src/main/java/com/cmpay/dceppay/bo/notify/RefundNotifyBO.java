package com.cmpay.dceppay.bo.notify;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/8/30 15:48
 */
@Data
public class RefundNotifyBO extends NotifyBaseBO {
    /**
     * 业务回执状态
     * PR00:退款成功
     * PR01:退款失败
     */
    private String responseStatus;

    /**
     * 业务拒绝码 当业务回执状态为“PR01”时必填
     */
    private String rejectCode;

    /**
     * 业务拒绝信息
     */
    private String rejectInformation;

    /**
     * 退款金额
     */
    private BigDecimal transactionAmount;

    /**
     * 商户退款单号 必输
     */
    private String outRefundNo;

    /**
     * 订单号 必输
     */
    private String orderNumber;

    /**
     * 交易完成时间  业务回执状态为PR00时必填,由收款运营机构填写
     */
    private String transactionFinishTime;
}
