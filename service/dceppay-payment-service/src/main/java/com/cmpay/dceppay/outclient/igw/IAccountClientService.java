package com.cmpay.dceppay.outclient.igw;

import com.cmpay.dceppay.bo.account.*;

/**
 * <AUTHOR>
 * @date 2024/9/11 13:52
 */
public interface IAccountClientService {
    /**
     * 账务登记
     * @param accountTreatmentBO
     * @return
     */
    AccountTreatmentResultBO accountTreatment(AccountTreatmentBO accountTreatmentBO);

    /**
     * 账务查询
     * @param queryRequestBO
     * @return
     */
    AccountTreatmentQueryResponseBO accountQuery(AccountTreatmentQueryRequestBO queryRequestBO);

    /**
     * 账务冲正
     * @param accountTreatmentCancelBO
     */
    void accountCancel(AccountTreatmentCancelBO accountTreatmentCancelBO);

    /**
     * 账务撤销
     * @param accountTreatmentReverseBO
     */
    void accountReverse(AccountTreatmentReverseBO accountTreatmentReverseBO);
}
