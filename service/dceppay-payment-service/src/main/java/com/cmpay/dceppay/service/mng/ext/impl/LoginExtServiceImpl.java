package com.cmpay.dceppay.service.mng.ext.impl;

import com.cmpay.channel.data.Request;
import com.cmpay.dceppay.bo.mng.DcepLoginBO;
import com.cmpay.dceppay.channel.DcepChannelEnum;
import com.cmpay.dceppay.config.DceppayConfig;
import com.cmpay.dceppay.constant.common.CommonConstants;
import com.cmpay.dceppay.constant.dcep.DcepResponseStatusConstant;
import com.cmpay.dceppay.dto.dcep.CommonConfirmation.CommonConfirmationBody;
import com.cmpay.dceppay.dto.dcep.CommonConfirmation.CommonConfirmationInformation;
import com.cmpay.dceppay.dto.dcep.CommonConfirmation.CommonConfirmationSoapEnvelopResponse;
import com.cmpay.dceppay.dto.dcep.common.RequestGroupHeader;
import com.cmpay.dceppay.dto.dcep.common.SoapHeader;
import com.cmpay.dceppay.dto.dcep.login.LoginRsp;
import com.cmpay.dceppay.dto.dcep.login.request.LoginBody;
import com.cmpay.dceppay.dto.dcep.login.request.LoginInformation;
import com.cmpay.dceppay.dto.dcep.login.request.LoginSoapEnvelopRequest;
import com.cmpay.dceppay.dto.dcep.login.response.LoginResponseInformation;
import com.cmpay.dceppay.dto.dcep.login.response.LoginSoapEnvelopResponse;
import com.cmpay.dceppay.enums.MsgCodeEnum;
import com.cmpay.dceppay.enums.dcep.DcepLoginTypeEnum;
import com.cmpay.dceppay.enums.dcep.DcepMessageTypeEnum;
import com.cmpay.dceppay.service.ext.DceppayParamService;
import com.cmpay.dceppay.service.mng.ext.ILoginExtService;
import com.cmpay.dceppay.service.notify.busmessage.IFinancialStatusService;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.utils.LemonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/11/1 10:14
 */
@Slf4j
@Service
public class LoginExtServiceImpl implements ILoginExtService {
    @Autowired
    private DceppayConfig dceppayConfig;
    @Autowired
    private DceppayParamService dceppayParamService;
    @Autowired
    private IFinancialStatusService financialStatusService;

    @Override
    public LoginSoapEnvelopRequest bulidLoginParam(String type) {
        LoginSoapEnvelopRequest loginSoapEnvelopRequest = new LoginSoapEnvelopRequest();
        //报文头
        String messageIdentification = dceppayParamService.getMessageIdentification(DcepMessageTypeEnum.LOGIN_REQUEST.getValue(), dceppayConfig.getWalletId());
        SoapHeader soapHeader = dceppayParamService.getCommonSoapHear(DcepMessageTypeEnum.LOGIN_REQUEST.getMessageType(), messageIdentification);
        BeanUtils.copyProperties(loginSoapEnvelopRequest, soapHeader);
        LoginBody loginBody = new LoginBody();
        RequestGroupHeader requestGroupHeader = dceppayParamService.getRequestGroupHeader(DcepMessageTypeEnum.LOGIN_REQUEST.getMessageType(),messageIdentification);
        requestGroupHeader.setInstructedDirectParty(dceppayConfig.getReceiverCode());
        loginBody.setRequestGroupHeader(requestGroupHeader);

        LoginInformation loginInformation = new LoginInformation();
        loginInformation.setLoginOperationType(type);
        loginBody.setLoginInformation(loginInformation);

        loginSoapEnvelopRequest.setLoginBody(loginBody);
        return loginSoapEnvelopRequest;
    }

    @Override
    public Request bulidLoginRequest(LoginSoapEnvelopRequest loginSoapEnvelopRequest) {
        Request request = new Request();
        request.setRequestId(LemonUtils.getRequestId());
        request.setRoute(DcepChannelEnum.DCEP_PAY);
        request.setBusiType(DcepChannelEnum.LOGIN.getName());
        request.setSource(DcepChannelEnum.DCEP_PAY);
        request.setTarget(loginSoapEnvelopRequest);
        return request;
    }

    @Override
    public void handleLoginInResponse(DcepLoginBO dcepLoginBO, LoginRsp loginRsp) {
        if (JudgeUtils.isNull(loginRsp)
                || (JudgeUtils.isNull(loginRsp.getLoginResponse())
                && JudgeUtils.isNull(loginRsp.getCommonConfirmationResponse()))
        ) {
            log.error("登录互联互通平台响应为空：{}", loginRsp);
            BusinessException.throwBusinessException(MsgCodeEnum.LOGIN_DCEP_RESPONSE_NULL);
        }
        CommonConfirmationSoapEnvelopResponse commonConfirmResponse = loginRsp.getCommonConfirmationResponse();

        if (JudgeUtils.isNotNull(commonConfirmResponse)) {
            //数币处理失败，业务检查未通过（未登录/无权限），返回900报文作为异常应答
            CommonConfirmationBody commonConfirmationBody = commonConfirmResponse.getCommonConfirmationBody();
            handleCommonConfirmationResponse(commonConfirmationBody);
        } else {
            LoginSoapEnvelopResponse loginSoapEnvelopResponse = loginRsp.getLoginResponse();
            LoginResponseInformation responseInformation = loginSoapEnvelopResponse.getSoapBody().getLoginResponseInformation();
            String responseStatus = responseInformation.getProcessStatus();
            if (JudgeUtils.equals(responseStatus, DcepResponseStatusConstant.LOGIN_SUCCESS)) {
                handleLoginInSuccessResponse(dcepLoginBO, responseInformation);
            } else {
                handleLoginInFailureResponse(responseInformation);
            }
        }
    }


    @Override
    public void handleLoginOutResponse(DcepLoginBO dcepLoginBO, LoginRsp loginRsp) {
        if (JudgeUtils.isNull(loginRsp)
                || (JudgeUtils.isNull(loginRsp.getLoginResponse())
                && JudgeUtils.isNull(loginRsp.getCommonConfirmationResponse()))
        ) {
            log.error("登出互联互通平台响应为空：{}", loginRsp);
            BusinessException.throwBusinessException(MsgCodeEnum.LOGINOUT_DCEP_RESPONSE_NULL);
        }
        CommonConfirmationSoapEnvelopResponse commonConfirmResponse = loginRsp.getCommonConfirmationResponse();
        if (JudgeUtils.isNotNull(commonConfirmResponse)) {
            //数币处理失败，业务检查未通过（未登录/无权限），返回900报文作为异常应答
            CommonConfirmationBody commonConfirmationBody = commonConfirmResponse.getCommonConfirmationBody();
            handleLoginOutCommonConfirmationResponse(commonConfirmationBody);
        } else {
            LoginSoapEnvelopResponse loginSoapEnvelopResponse = loginRsp.getLoginResponse();
            LoginResponseInformation responseInformation = loginSoapEnvelopResponse.getSoapBody().getLoginResponseInformation();
            String responseStatus = responseInformation.getProcessStatus();
            if (JudgeUtils.equals(responseStatus, DcepResponseStatusConstant.LOGIN_OUT_SUCCESS)) {
                handleLoginOutSuccessResponse(dcepLoginBO, responseInformation);
            } else {
                handleLoginOutFailureResponse(responseInformation);
            }
        }
    }

    private void handleLoginInFailureResponse(LoginResponseInformation responseInformation) {
        throwException(responseInformation.getProcessStatus(), responseInformation.getProcessCode(), responseInformation.getRejectInformation(), MsgCodeEnum.LOGIN_DCEP_ERROR);
    }

    private void handleLoginOutFailureResponse(LoginResponseInformation responseInformation) {
        throwException(responseInformation.getProcessStatus(), responseInformation.getProcessCode(), responseInformation.getRejectInformation(), MsgCodeEnum.LOGINOUT_DCEP_ERROR);
    }


    private void handleLoginInSuccessResponse(DcepLoginBO dcepLoginBO, LoginResponseInformation responseInformation) {
        String loginType = responseInformation.getLoginOperationType();
        if (JudgeUtils.equals(loginType, DcepLoginTypeEnum.OT00.name())) {
            if (JudgeUtils.isNull(dcepLoginBO)) {
                financialStatusService.setFinancialStatusOn(CommonConstants.SYS_ADMIN_USER);
            } else {
                financialStatusService.setFinancialStatusOn(dcepLoginBO.getOperateId());
            }
        }
    }

    private void handleLoginOutSuccessResponse(DcepLoginBO dcepLoginBO, LoginResponseInformation responseInformation) {
        String loginType = responseInformation.getLoginOperationType();
        if (JudgeUtils.equals(loginType, DcepLoginTypeEnum.OT01.name())) {
            financialStatusService.setFinancialStatusOff(dcepLoginBO.getOperateId());
        }
    }

    private void handleCommonConfirmationResponse(CommonConfirmationBody commonConfirmationBody) {
        if (JudgeUtils.isNotNull(commonConfirmationBody)) {
            CommonConfirmationInformation confirmationInformation = commonConfirmationBody.getCommonConfirmationInformation();
            if (JudgeUtils.isNotNull(confirmationInformation)) {
                throwException(confirmationInformation.getProcessStatus(), confirmationInformation.getProcessCode(), confirmationInformation.getRejectInformation(), MsgCodeEnum.LOGIN_DCEP_ERROR);
            } else {
                BusinessException.throwBusinessException(MsgCodeEnum.LOGIN_DCEP_ERROR);
            }
        } else {
            BusinessException.throwBusinessException(MsgCodeEnum.LOGIN_DCEP_ERROR);
        }
    }

    private void handleLoginOutCommonConfirmationResponse(CommonConfirmationBody commonConfirmationBody) {
        if (JudgeUtils.isNotNull(commonConfirmationBody)) {
            CommonConfirmationInformation confirmationInformation = commonConfirmationBody.getCommonConfirmationInformation();
            if (JudgeUtils.isNotNull(confirmationInformation)) {
                throwException(confirmationInformation.getProcessStatus(), confirmationInformation.getProcessCode(), confirmationInformation.getRejectInformation(), MsgCodeEnum.LOGINOUT_DCEP_ERROR);
            } else {
                BusinessException.throwBusinessException(MsgCodeEnum.LOGINOUT_DCEP_ERROR);
            }
        } else {
            BusinessException.throwBusinessException(MsgCodeEnum.LOGINOUT_DCEP_ERROR);
        }
    }

    private static void throwException(String status, String code, String responseInformation, MsgCodeEnum msgCodeEnum) {
        String errorMessageInfo = "业务处理状态【" + status
                + "】业务处理码【" + code
                + "】业务处理信息【" + responseInformation + "】";
        log.error(errorMessageInfo);
        BusinessException.throwBusinessException(msgCodeEnum);
    }

}
