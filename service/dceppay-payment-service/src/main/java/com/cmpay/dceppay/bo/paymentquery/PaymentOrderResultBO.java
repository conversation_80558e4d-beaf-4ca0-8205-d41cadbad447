package com.cmpay.dceppay.bo.paymentquery;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/8/30 11:41
 * 支付订单查询响应对象
 */
@Data
public class PaymentOrderResultBO {
    private String outOrderNo;
    private BigDecimal totalAmount;
    private String finishDateTime;
    private String orderStatus;
    private String extra;
    private String bankOrderNo;
    private String errMsgCd;
    private String errMsgInfo;


}
