package com.cmpay.dceppay.bo.payment;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/8/30 10:39
 * 数币收单下单响应参数
 * 用 @EqualsAndHashCode(callSuper = true) 确保生成的 equals 和 hashCode 方法包含父类的字段。
 */
@Data
public class UnifiedOrderRspBO  {

    // 订单总金额
    private BigDecimal totalAmount;
    // 订单号
    private String outOrderNo;

    // 订单时间
    private String bankOrderTime;

    // 机构订单号
    private String bankOrderNo;

    // 机构支付错误码
    private String errMsgCd;

    // 机构支付错误码信息
    private String errMsgInfo;

    // 调用地址
    private String payUrl;

    // 保留字段
    private String extra;
}

