package com.cmpay.dceppay.stream.handler;

import com.cmpay.dceppay.bo.pay.RefundOrderDBBO;
import com.cmpay.dceppay.service.channel.IPaymentChannelService;
import com.cmpay.lemon.framework.data.DefaultCmdDTO;
import com.cmpay.lemon.framework.stream.MessageHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/11/25 11:12
 */
@Slf4j
@Component("refundHandler")
public class RefundHandler implements MessageHandler<RefundOrderDBBO, DefaultCmdDTO<RefundOrderDBBO>> {

    @Autowired
    private ApplicationContext applicationContext;

    /**
     * 支付成功 通知应用
     *
     * @param cmpayCmdDTO
     */
    @Override
    public void onMessageReceive(DefaultCmdDTO<RefundOrderDBBO> cmpayCmdDTO) {
        RefundOrderDBBO refundOrderBO = cmpayCmdDTO.getBody();
        String channelName = refundOrderBO.getPayWay() + refundOrderBO.getScene();
        IPaymentChannelService paymentChannelService = (IPaymentChannelService) applicationContext.getBean(channelName);
        paymentChannelService.refundAsync(refundOrderBO);
    }
}
