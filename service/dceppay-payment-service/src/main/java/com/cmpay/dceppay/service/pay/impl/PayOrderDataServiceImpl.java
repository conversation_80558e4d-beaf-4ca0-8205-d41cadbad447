package com.cmpay.dceppay.service.pay.impl;

import com.cmpay.dceppay.bo.check.CheckFileDetailBO;
import com.cmpay.dceppay.bo.notify.PaymentNotifyBO;
import com.cmpay.dceppay.bo.pay.PayOrderDBBO;
import com.cmpay.dceppay.bo.paymentquery.PaymentOrderResultBO;
import com.cmpay.dceppay.service.pay.IPayOrderDataService;
import com.cmpay.dceppay.util.DateTimeUtil;
import com.cmpay.lemon.common.utils.JudgeUtils;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/9/17 16:37
 */
@Service
public class PayOrderDataServiceImpl implements IPayOrderDataService {

    @Override
    public PaymentOrderResultBO buildPaymentOrderResult(PaymentNotifyBO paymentNotifyBO) {
        PaymentOrderResultBO paymentOrderResultBO=new PaymentOrderResultBO();
        paymentOrderResultBO.setOutOrderNo(paymentNotifyBO.getOutOrderNumber());
        paymentOrderResultBO.setBankOrderNo(paymentNotifyBO.getOrderNumber());
        paymentOrderResultBO.setTotalAmount(paymentNotifyBO.getTransactionAmount());
        paymentOrderResultBO.setFinishDateTime(DateTimeUtil.changeISODateTime(paymentNotifyBO.getTransactionFinishTime()));
        paymentOrderResultBO.setErrMsgCd(paymentNotifyBO.getRejectCode());
        paymentOrderResultBO.setErrMsgInfo(paymentNotifyBO.getRejectInformation());
        return paymentOrderResultBO;
    }

    @Override
    public PayOrderDBBO buildUpdatePayOrderDBBO(PaymentOrderResultBO paymentOrderResultBO, PayOrderDBBO payOrderDBBO) {
        payOrderDBBO.setBankOrderNo(paymentOrderResultBO.getBankOrderNo());
        if (JudgeUtils.isNotNull(paymentOrderResultBO.getErrMsgCd())) {
            payOrderDBBO.setErrMsgInfo(paymentOrderResultBO.getErrMsgInfo());
            payOrderDBBO.setErrMsgCd(paymentOrderResultBO.getErrMsgCd());
        }
        return payOrderDBBO;
    }

    @Override
    public PaymentOrderResultBO buildPayOrderResult(CheckFileDetailBO checkFileDetailBO) {
        PaymentOrderResultBO paymentOrderResultBO=new PaymentOrderResultBO();
        paymentOrderResultBO.setOutOrderNo(checkFileDetailBO.getOutOrderNo());
        paymentOrderResultBO.setBankOrderNo(checkFileDetailBO.getBankOrderNo());
        paymentOrderResultBO.setTotalAmount(checkFileDetailBO.getOrderAmount());
        paymentOrderResultBO.setFinishDateTime(checkFileDetailBO.getOrderCompleteTime());
        return paymentOrderResultBO;
    }
}
