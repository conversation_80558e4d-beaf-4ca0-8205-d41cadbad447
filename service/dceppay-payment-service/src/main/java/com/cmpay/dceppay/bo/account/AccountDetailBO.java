package com.cmpay.dceppay.bo.account;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/9/11 15:46
 */
@Data
public class AccountDetailBO {
    // 借贷方向，D 表示借方，C 表示贷方
    private String dcFlag;
    // 账号
    private String accountNo;
    // 账户名称
    private String accountName;
    //金额
    private BigDecimal amount;
    // 交易对手账号
    private String oppAccountNo;
    // 交易对手账户名称
    private String oppAccountName;

    public AccountDetailBO(String dcFlag, String accountNo, String accountName, BigDecimal amount, String oppAccountNo, String oppAccountName) {
        this.dcFlag = dcFlag;
        this.accountNo = accountNo;
        this.accountName = accountName;
        this.amount = amount;
        this.oppAccountNo = oppAccountNo;
        this.oppAccountName = oppAccountName;

    }

}
