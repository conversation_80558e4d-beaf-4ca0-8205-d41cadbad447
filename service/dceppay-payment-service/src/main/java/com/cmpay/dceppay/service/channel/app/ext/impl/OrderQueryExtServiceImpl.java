package com.cmpay.dceppay.service.channel.app.ext.impl;

import com.cmpay.channel.data.Request;
import com.cmpay.dceppay.bo.paymentquery.PaymentOrderQueryBO;
import com.cmpay.dceppay.bo.paymentquery.PaymentOrderResultBO;
import com.cmpay.dceppay.channel.DcepChannelEnum;
import com.cmpay.dceppay.config.DceppayConfig;
import com.cmpay.dceppay.constant.dcep.DcepResponseStatusConstant;
import com.cmpay.dceppay.dto.dcep.common.*;
import com.cmpay.dceppay.dto.dcep.faut.Fault;
import com.cmpay.dceppay.dto.dcep.faut.FaultSoapEnvelopResponse;
import com.cmpay.dceppay.dto.dcep.unifiedorderquery.UnifiedOrderQueryRsp;
import com.cmpay.dceppay.dto.dcep.unifiedorderquery.reponse.OrderQueryResponse;
import com.cmpay.dceppay.dto.dcep.unifiedorderquery.reponse.OrderQuerySoapEnvelopResponse;
import com.cmpay.dceppay.dto.dcep.unifiedorderquery.request.OrderQueryBody;
import com.cmpay.dceppay.dto.dcep.unifiedorderquery.request.OrderQueryOriginalGroupHeader;
import com.cmpay.dceppay.dto.dcep.unifiedorderquery.request.OrderQuerySoapEnvelopRequest;
import com.cmpay.dceppay.enums.MsgCodeEnum;
import com.cmpay.dceppay.enums.PaymentTypeEnum;
import com.cmpay.dceppay.enums.dcep.DcepMessageTypeEnum;
import com.cmpay.dceppay.enums.pay.OrderStatusEnum;
import com.cmpay.dceppay.service.channel.app.ext.IOrderQueryExtService;
import com.cmpay.dceppay.service.ext.DceppayParamService;
import com.cmpay.dceppay.util.DateTimeUtil;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.utils.LemonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/9/10 16:37
 */
@Service
@Slf4j
public class OrderQueryExtServiceImpl implements IOrderQueryExtService {
    @Autowired
    private DceppayConfig dceppayConfig;
    @Autowired
    private DceppayParamService dceppayParamService;

    @Override
    public OrderQuerySoapEnvelopRequest buildOrderQueryRequestParam(PaymentOrderQueryBO paymentOrderQueryBO) {
        OrderQuerySoapEnvelopRequest orderQuerySoapEnvelopRequest = new OrderQuerySoapEnvelopRequest();
        String walletId = paymentOrderQueryBO.getWalletId();
        if (JudgeUtils.isBlank(walletId)) {
            walletId = dceppayConfig.getWalletId();
        }
        //报文头
        String messageIdentification = dceppayParamService.getMessageIdentification(DcepMessageTypeEnum.PAYMENT_RESULT_QUERY_REQUEST.getValue(), walletId);
        SoapHeader soapHeader = dceppayParamService.getCommonSoapHear(DcepMessageTypeEnum.PAYMENT_RESULT_QUERY_REQUEST.getMessageType(), messageIdentification);
        BeanUtils.copyProperties(orderQuerySoapEnvelopRequest, soapHeader);

        OrderQueryBody queryBody = new OrderQueryBody();
        RequestGroupHeader requestGroupHeader = dceppayParamService.getRequestGroupHeader(DcepMessageTypeEnum.PAYMENT_RESULT_QUERY_REQUEST.getMessageType(),messageIdentification);
        queryBody.setRequestGroupHeader(requestGroupHeader);

        OrderQueryOriginalGroupHeader orderQueryOriginalGroupHeader = new OrderQueryOriginalGroupHeader();
        orderQueryOriginalGroupHeader.setOriginalMessageIdentification(paymentOrderQueryBO.getOriginalMessageIdentification());
        orderQueryOriginalGroupHeader.setOriginalInstructingParty(dceppayConfig.getSenderCode());
        orderQueryOriginalGroupHeader.setOriginalMessageType(DcepMessageTypeEnum.UNIFIED_ORDER_REQUEST.getMessageType());
        orderQueryOriginalGroupHeader.setOriginalMerchantNo(paymentOrderQueryBO.getMerchantId());
        orderQueryOriginalGroupHeader.setOriginalOutOrderNo(paymentOrderQueryBO.getOutOrderNo());
        orderQueryOriginalGroupHeader.setOriginalOrderNo(paymentOrderQueryBO.getBankOrderNo());
        orderQueryOriginalGroupHeader.setRpFlag(PaymentTypeEnum.RECEIVING_INSTITUTION.getValue());
        queryBody.setOriginalGroupHeader(orderQueryOriginalGroupHeader);

        orderQuerySoapEnvelopRequest.setSoapBody(queryBody);

        return orderQuerySoapEnvelopRequest;
    }

    @Override
    public Request bulidOrderQueryRequest(OrderQuerySoapEnvelopRequest orderQuerySoapEnvelopRequest) {
        Request request = new Request();
        request.setRequestId(LemonUtils.getRequestId());
        request.setRoute(DcepChannelEnum.DCEP_PAY);
        request.setBusiType(DcepChannelEnum.ORDER_QUERY.getName());
        request.setSource(DcepChannelEnum.DCEP_PAY);
        request.setTarget(orderQuerySoapEnvelopRequest);
        return request;
    }

    @Override
    public PaymentOrderResultBO handleResponse(PaymentOrderQueryBO paymentOrderQueryBO, UnifiedOrderQueryRsp unifiedOrderQueryRsp) {
        PaymentOrderResultBO paymentOrderResultBO = new PaymentOrderResultBO();
        if (isResponseEmpty(unifiedOrderQueryRsp)) {
            BusinessException.throwBusinessException(MsgCodeEnum.ORDER_QUERY_RESPONSE_NULL);
        }
        FaultSoapEnvelopResponse faultSoapEnvelopResponse = unifiedOrderQueryRsp.getFaultSoapEnvelopResponse();
        if (JudgeUtils.isNotNull(faultSoapEnvelopResponse) && JudgeUtils.isNotNull(faultSoapEnvelopResponse.getFault())) {
            Fault fault = faultSoapEnvelopResponse.getFault();
            String errorCode = fault.getFaultCode();
            String errorMessage = fault.getFaultString() + (JudgeUtils.isNotBlank(fault.getDetail()) ? fault.getDetail() : "");
            handleQueryFault(paymentOrderResultBO, errorCode, errorMessage);
        } else {
            OrderQuerySoapEnvelopResponse orderQuerySoapEnvelopResponse = unifiedOrderQueryRsp.getOrderQuerySoapEnvelopResponse();
            OrderQueryResponse orderQueryResponse = orderQuerySoapEnvelopResponse.getSoapBody();
            OrderQueryReference orderQueryReference = orderQueryResponse.getOrderQueryReference();
            String queryResult = orderQueryReference.getQueryResult();
            switch (queryResult) {
                case DcepResponseStatusConstant.QUERY_ORDER_SUCCESS:
                    handleQuerySuccess(paymentOrderResultBO, orderQueryResponse);
                    break;
                case DcepResponseStatusConstant.QUERY_ORDER_FAIL:
                    handleQueryFail(paymentOrderResultBO, orderQuerySoapEnvelopResponse);
                    break;
                default:
                    log.error("请求互联互通支付结果查询接口查询处理状态非法,查询处理状态值：{}", queryResult);
                    break;
            }
        }
        return paymentOrderResultBO;
    }

    private void handleQueryFault(PaymentOrderResultBO paymentOrderResultBO, String errorCode, String errorMessage) {
        setErrorMessage(paymentOrderResultBO, errorCode, errorMessage);
        log.error("请求互联互通支付结果查询接口异常：{}", errorCode + errorMessage);
    }

    private boolean isResponseEmpty(UnifiedOrderQueryRsp unifiedOrderQueryRsp) {
        return JudgeUtils.isNullAny(unifiedOrderQueryRsp) ||
                (JudgeUtils.isNull(unifiedOrderQueryRsp.getOrderQuerySoapEnvelopResponse())
                        && JudgeUtils.isNull(unifiedOrderQueryRsp.getFaultSoapEnvelopResponse())
                );
    }

    private void handleQuerySuccess(PaymentOrderResultBO paymentOrderResultBO, OrderQueryResponse orderQueryResponse) {
        QuerySuccessResult querySuccessResult = orderQueryResponse.getQuerySuccessResult();
        if (JudgeUtils.isNull(querySuccessResult)) {
            setErrorMessage(paymentOrderResultBO, MsgCodeEnum.ORDER_QUERY_BUSINESS_RESPONSE_NULL);
            log.error("请求互联互通支付结果查询接口受理成功，返回应答的原业务信息为空");
            return;
        }
        String tranResult = querySuccessResult.getTranResult();
        switch (tranResult) {
            case DcepResponseStatusConstant.PAY_ORDER_SUCCESS:
                setCommonResponse(paymentOrderResultBO, orderQueryResponse);
                paymentOrderResultBO.setOrderStatus(OrderStatusEnum.TRADE_SUCCESS.name());
                break;
            case DcepResponseStatusConstant.PAY_ORDER_FAIL:
                setCommonResponse(paymentOrderResultBO, orderQueryResponse);
                paymentOrderResultBO.setOrderStatus(OrderStatusEnum.TRADE_FAIL.name());
                break;
            case DcepResponseStatusConstant.PAY_ORDER_WAIT:
                setCommonResponse(paymentOrderResultBO, orderQueryResponse);
                log.warn("请求互联互通支付结果查询接口成功,{}：处理中（受理成功待支付）",DcepResponseStatusConstant.PAY_ORDER_WAIT);
                break;
            default:
                log.error("请求互联互通支付结果查询接口响应非法,原业务状态值：{}", tranResult);
                break;
        }
    }

    private void handleQueryFail(PaymentOrderResultBO paymentOrderResultBO, OrderQuerySoapEnvelopResponse response) {
        OptionError optionError = response.getSoapBody().getOptionError();
        if (JudgeUtils.isNotNull(optionError)) {
            setErrorMessage(paymentOrderResultBO, optionError.getRejectCode(), optionError.getRejectInformation());
            log.error("请求互联互通支付结果查询接口失败：{}", response);
        }
    }

    private void setCommonResponse(PaymentOrderResultBO paymentOrderResultBO, OrderQueryResponse orderQueryResponse) {
        paymentOrderResultBO.setOutOrderNo(orderQueryResponse.getOriginalPaymentTransactionInformation().getOriginalOutOrderNumber());
        paymentOrderResultBO.setBankOrderNo(orderQueryResponse.getOriginalPaymentTransactionInformation().getOriginalOrderNumber());
        paymentOrderResultBO.setFinishDateTime(DateTimeUtil.changeISODateTime(orderQueryResponse.getOriginalPaymentTransactionInformation().getOriginalTransactionFinishTime()));
        paymentOrderResultBO.setTotalAmount(orderQueryResponse.getOriginalPaymentTransactionInformation().getOriginalTransactionAmount());
    }

    private void setErrorMessage(PaymentOrderResultBO paymentOrderResultBO, MsgCodeEnum msgCode) {
        paymentOrderResultBO.setErrMsgCd(msgCode.getMsgCd());
        paymentOrderResultBO.setErrMsgInfo(msgCode.getMsgInfo());
    }

    private void setErrorMessage(PaymentOrderResultBO paymentOrderResultBO, String errorCode, String errorMessage) {
        paymentOrderResultBO.setErrMsgCd(errorCode);
        paymentOrderResultBO.setErrMsgInfo(errorMessage);
    }
}
