package com.cmpay.dceppay.service.notify.busmessage.impl;

import com.cmpay.dceppay.bo.bus.BusMessageBO;
import com.cmpay.dceppay.bo.notify.busmessage.BusMessageNotifyBO;
import com.cmpay.dceppay.bo.notify.busmessage.BusMessageNotifyRspBO;
import com.cmpay.dceppay.bo.notify.busmessage.FinancialStatusChangeNotifyBO;
import com.cmpay.dceppay.config.DceppayConfig;
import com.cmpay.dceppay.constant.common.CommonConstants;
import com.cmpay.dceppay.constant.dcep.DcepResponseStatusConstant;
import com.cmpay.dceppay.enums.MsgCodeEnum;
import com.cmpay.dceppay.enums.dcep.DcepMessageTypeEnum;
import com.cmpay.dceppay.enums.dcep.DcepFinancialStatusEnum;
import com.cmpay.dceppay.service.bus.IBusMessageDBService;
import com.cmpay.dceppay.service.notify.busmessage.IBusMessageService;
import com.cmpay.dceppay.service.notify.busmessage.IFinancialStatusService;
import com.cmpay.dceppay.util.DateTimeUtil;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/10/30 16:32
 */
@Slf4j
@Service
public class BusMessageServiceImpl implements IBusMessageService {
    @Autowired
    private IBusMessageDBService busMessageDBService;
    @Autowired
    private DceppayConfig dceppayConfig;
    @Autowired
    private IFinancialStatusService financialStatusService;

    @Override
    public BusMessageNotifyRspBO businessAuthority(BusMessageNotifyBO busMessageNotifyBO) {
        checkParam(checkBusinessAuthorityParam(busMessageNotifyBO), busMessageNotifyBO, MsgCodeEnum.BUSINESS_AUTHORITY_NOTIFY_ERROR);
        addBusMessage(busMessageNotifyBO, MsgCodeEnum.BUSINESS_AUTHORITY_NOTIFY_ERROR);
        return returnSuccessHandleInfo(busMessageNotifyBO);
    }

    @Override
    public BusMessageNotifyRspBO financialCodeChange(BusMessageNotifyBO notifyBO) {
        checkParam(checkFinancialCodeChangeParam(notifyBO), notifyBO, MsgCodeEnum.FINANCIAL_CODE_CHANGE_NOTIFY_ERROR);
        addBusMessage(notifyBO, MsgCodeEnum.FINANCIAL_CODE_CHANGE_NOTIFY_ERROR);
        return returnSuccessHandleInfo(notifyBO);
    }


    @Override
    public BusMessageNotifyRspBO financialStatusChange(FinancialStatusChangeNotifyBO notifyBO) {
        checkParam(checkFinancialStatusChangeParam(notifyBO), notifyBO, MsgCodeEnum.FINANCIAL_STATUS_CHANGE_NOTIFY_ERROR);
        if (JudgeUtils.equals(notifyBO.getPartyIdentification(), dceppayConfig.getSenderCode())) {
            handleFinancialStatus(notifyBO);
        }
        addBusMessage(notifyBO, MsgCodeEnum.FINANCIAL_STATUS_CHANGE_NOTIFY_ERROR);
        return returnSuccessHandleInfo(notifyBO);
    }


    @Override
    public BusMessageNotifyRspBO freeFormatNotify(BusMessageNotifyBO notifyBO) {
        checkParam(checkFreeFormatChangeParam(notifyBO), notifyBO, MsgCodeEnum.FREE_FORMAT_ERROR);
        addBusMessage(notifyBO, MsgCodeEnum.FREE_FORMAT_ERROR);
        return returnSuccessHandleInfo(notifyBO);
    }

    private void handleFinancialStatus(FinancialStatusChangeNotifyBO notifyBO) {
        if (JudgeUtils.equalsAny(notifyBO.getType(), DcepFinancialStatusEnum.ST03.name(), DcepFinancialStatusEnum.ST04.name())) {
            financialStatusService.setFinancialStatusOff(CommonConstants.SYS_ADMIN_USER);
        } else if (JudgeUtils.equals(notifyBO.getType(), DcepFinancialStatusEnum.ST02.name())) {
            financialStatusService.setFinancialStatusOn(CommonConstants.SYS_ADMIN_USER);
        }
    }

    private void checkParam(boolean busMessageNotifyBO, BusMessageNotifyBO busMessageNotifyBO1, MsgCodeEnum errorCode) {
        if (!busMessageNotifyBO) {
            log.info("通知参数校验不通过：{}", busMessageNotifyBO1);
            BusinessException.throwBusinessException(errorCode);
        }
    }

    private static BusMessageNotifyRspBO returnSuccessHandleInfo(BusMessageNotifyBO busMessageNotifyBO) {
        BusMessageNotifyRspBO busMessageNotifyRspBO = new BusMessageNotifyRspBO();
        BeanUtils.copyProperties(busMessageNotifyRspBO, busMessageNotifyBO);
        busMessageNotifyRspBO.setProcessStatus(DcepResponseStatusConstant.NOTIFY_HANDLE_SUCCESS);
        return busMessageNotifyRspBO;
    }


    private void addBusMessage(BusMessageNotifyBO notifyBO, MsgCodeEnum freeFormatError) {
        BusMessageBO busMessageBO = getBusMessageBO(notifyBO);
        try {
            busMessageDBService.addBusMessage(busMessageBO);
        } catch (Exception e) {
            BusinessException.throwBusinessException(freeFormatError);
        }
    }

    private BusMessageBO getBusMessageBO(BusMessageNotifyBO busMessageNotifyBO) {
        BusMessageBO busMessageBO = new BusMessageBO();
        BeanUtils.copyProperties(busMessageBO, busMessageNotifyBO);
        try {
            if (JudgeUtils.isNotBlank(busMessageNotifyBO.getMessageTime())) {
                String messageTime = DateTimeUtil.changeISODateTime(busMessageBO.getMessageTime());
                busMessageBO.setMessageTime(messageTime);
                busMessageBO.setMessageDate(messageTime.substring(0, 8));
            } else {
                busMessageBO.setMessageTime(DateTimeUtil.getCurrentDateTimeStr());
                busMessageBO.setMessageDate(DateTimeUtil.getCurrentDateStr());
            }
        } catch (Exception e) {
            busMessageBO.setMessageTime(DateTimeUtil.getCurrentDateTimeStr());
            busMessageBO.setMessageDate(DateTimeUtil.getCurrentDateStr());
        }
        return busMessageBO;
    }

    private boolean checkBusinessAuthorityParam(BusMessageNotifyBO busMessageNotifyBO) {
        if (!busMessageNotifyBO.getMessageType().startsWith(DcepMessageTypeEnum.BUSINESS_AUTHORITY)) {
            return false;
        }
        return true;
    }

    private boolean checkFinancialCodeChangeParam(BusMessageNotifyBO notifyBO) {
        if (!notifyBO.getMessageType().startsWith(DcepMessageTypeEnum.FINANCIAL_CODE_CHANGE)) {
            return false;
        }
        return true;
    }

    private boolean checkFinancialStatusChangeParam(BusMessageNotifyBO notifyBO) {
        if (!notifyBO.getMessageType().startsWith(DcepMessageTypeEnum.FINANCIAL_STATUS_CHANGE)) {
            return false;
        }
        return true;
    }

    private boolean checkFreeFormatChangeParam(BusMessageNotifyBO notifyBO) {
        if (!notifyBO.getMessageType().startsWith(DcepMessageTypeEnum.FREE_FORMAT)) {
            return false;
        }
        return true;
    }
}
