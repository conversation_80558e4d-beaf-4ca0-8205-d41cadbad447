package com.cmpay.dceppay.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Created on 2018/12/21
 *
 * @author: wulinfeng
 */
@Configuration
@ConfigurationProperties(prefix = "dceppay")
@Data
public class DceppayConfig {

    private String version;
    /**
     * 发起方机构
     */
    private String senderName;
    private String senderCode;
    /**
     * 接收方机构
     */
    private String receiverName;
    private String receiverCode;

    private String creditorName;
    private String creditorCode;
    /**
     * 签名证书序列号
     */
    private String signSN;
    private String interbankFlag;
    private String environmentFlag;
    private String walletId;

    private String appId;
    /**
     * 机构标识
     */
    private String institutionId;
}
