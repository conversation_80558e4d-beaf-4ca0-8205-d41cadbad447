package com.cmpay.dceppay.service.channel.app;

import com.cmpay.channel.data.Request;
import com.cmpay.channel.data.Response;
import com.cmpay.dceppay.bo.closeorder.CloseOrderBO;
import com.cmpay.dceppay.bo.closeorder.CloseOrderRspBO;
import com.cmpay.dceppay.bo.notify.PaymentNotifyBO;
import com.cmpay.dceppay.bo.notify.PaymentNotifyRspBO;
import com.cmpay.dceppay.bo.notify.RefundNotifyBO;
import com.cmpay.dceppay.bo.notify.RefundNotifyRspBO;
import com.cmpay.dceppay.bo.pay.MerchantRouteBO;
import com.cmpay.dceppay.bo.pay.PayOrderDBBO;
import com.cmpay.dceppay.bo.pay.RefundOrderDBBO;
import com.cmpay.dceppay.bo.payment.UnifiedOrderReqBO;
import com.cmpay.dceppay.bo.payment.UnifiedOrderRspBO;
import com.cmpay.dceppay.bo.paymentquery.PaymentOrderQueryBO;
import com.cmpay.dceppay.bo.paymentquery.PaymentOrderResultBO;
import com.cmpay.dceppay.bo.refund.RefundOrderBO;
import com.cmpay.dceppay.bo.refund.RefundOrderRepBO;
import com.cmpay.dceppay.bo.refund.RefundOrderRspBO;
import com.cmpay.dceppay.bo.refundquery.RefundOrderQueryBO;
import com.cmpay.dceppay.bo.refundquery.RefundOrderResultBO;
import com.cmpay.dceppay.client.DceppayCgwOutClient;
import com.cmpay.dceppay.constant.dcep.DcepResponseStatusConstant;
import com.cmpay.dceppay.dto.dcep.closeorder.CloseOrderRsp;
import com.cmpay.dceppay.dto.dcep.closeorder.request.CloseOrderSoapEnvelopRequest;
import com.cmpay.dceppay.dto.dcep.refundorder.RefundOrderRsp;
import com.cmpay.dceppay.dto.dcep.refundorder.request.RefundOrderSoapEnvelopRequest;
import com.cmpay.dceppay.dto.dcep.refundorderquery.RefundOrderQueryRsp;
import com.cmpay.dceppay.dto.dcep.refundorderquery.request.RefundOrderQuerySoapEnvelopRequest;
import com.cmpay.dceppay.dto.dcep.unifiedorder.UnifiedOrderRsp;
import com.cmpay.dceppay.dto.dcep.unifiedorder.request.UnifiedOrderSoapEnvelopRequest;
import com.cmpay.dceppay.dto.dcep.unifiedorderquery.UnifiedOrderQueryRsp;
import com.cmpay.dceppay.dto.dcep.unifiedorderquery.request.OrderQuerySoapEnvelopRequest;
import com.cmpay.dceppay.enums.MsgCodeEnum;
import com.cmpay.dceppay.enums.pay.OrderStatusEnum;
import com.cmpay.dceppay.service.channel.IPaymentChannelService;
import com.cmpay.dceppay.service.channel.app.ext.*;
import com.cmpay.dceppay.service.pay.IPayOrderDataService;
import com.cmpay.dceppay.service.pay.IPayOrderService;
import com.cmpay.dceppay.service.pay.IPayRefundService;
import com.cmpay.dceppay.service.pay.IRefundOrderDataService;
import com.cmpay.dceppay.stream.client.DceppayAsyncClient;
import com.cmpay.dceppay.util.DateTimeUtil;
import com.cmpay.dceppay.utils.ExceptionHandlerUtil;
import com.cmpay.framework.data.request.GenericDTO;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/9/2 9:34
 * 数币app拉起支付
 */
@Service("dcepapp")
@Slf4j
public class IDcepAppPayService implements IPaymentChannelService {
    @Autowired
    private DceppayCgwOutClient dceppayCgwOutClient;
    @Autowired
    private IDcepUnifiedOrderExtService dcepUnifiedOrderExtService;
    @Autowired
    private IDcepRefundOrderExtService dcepRefundOrderExtService;
    @Autowired
    private ICloseOrderExtService closeOrderExtService;
    @Autowired
    private IOrderQueryExtService orderQueryExtService;
    @Autowired
    private IRefundQueryExtService refundQueryExtService;
    @Autowired
    private IPayOrderService payOrderService;
    @Autowired
    private IPayOrderDataService payOrderDataService;
    @Autowired
    private IRefundOrderDataService refundOrderDataService;
    @Autowired
    private IPayRefundService refundService;
    @Autowired
    private DceppayAsyncClient asyncClient;

    @Override
    public UnifiedOrderRspBO order(MerchantRouteBO merchantRouteBO, UnifiedOrderReqBO unifiedOrderReqBO) {
        // 封装网关请求参数对象
        UnifiedOrderSoapEnvelopRequest unifiedOrderSoapEnvelopRequest = dcepUnifiedOrderExtService.buildUnifiedOrderRequestParam(merchantRouteBO, unifiedOrderReqBO);
        Request request = dcepUnifiedOrderExtService.bulidUnifiedOrderRequest(unifiedOrderSoapEnvelopRequest);
        UnifiedOrderRsp unifiedOrderRsp = new UnifiedOrderRsp();
        try {
            // 请求网关
            GenericRspDTO<Response> genericRspDTO = dceppayCgwOutClient.request(GenericDTO.newChildInstanceWithBody(GenericDTO.class, request));
            // 处理网关响应对象
            if (JudgeUtils.isNotSuccess(genericRspDTO)) {
                BusinessException.throwBusinessException(MsgCodeEnum.TRANSACTION_REQUEST_FAIL);
            }
            unifiedOrderRsp = Optional.ofNullable(genericRspDTO)
                    .map(GenericRspDTO::getBody)
                    .map(Response::getResult)
                    .map(x -> (UnifiedOrderRsp) x)
                    .orElse(new UnifiedOrderRsp());
            return dcepUnifiedOrderExtService.handleResponse(merchantRouteBO, unifiedOrderReqBO, unifiedOrderSoapEnvelopRequest, unifiedOrderRsp);
        } catch (Exception e) {
            ExceptionHandlerUtil.throwRequestDcepError(e);
        } finally {
            dcepUnifiedOrderExtService.registerOrderSafely(merchantRouteBO, unifiedOrderSoapEnvelopRequest, unifiedOrderReqBO, unifiedOrderRsp);
        }
        return null;
    }

    @Override
    public RefundOrderRspBO refund(RefundOrderBO refundOrderBO, RefundOrderRepBO refundOrderRepBO) {
        RefundOrderDBBO refundOrderDBBO = null;
        try {
            refundOrderDBBO = dcepRefundOrderExtService.registerPendingRefundOrder(refundOrderBO, refundOrderRepBO);
        } catch (Exception e) {
            BusinessException.throwBusinessException(MsgCodeEnum.REFUND_ORDER_REGISTER_FAIL);
        }
        RefundOrderRspBO refundOrderRspBO = new RefundOrderRspBO();
        refundOrderRspBO.setOutRefundNo(refundOrderBO.getRefundOrderNo());
        refundOrderRspBO.setOutOrderNo(refundOrderBO.getOutOrderNo());
        refundOrderRspBO.setRefundAmount(refundOrderRepBO.getRefundAmount());
        refundOrderDBBO.setOriginalMessageIdentification(refundOrderBO.getOriginalMessageIdentification());
        asyncClient.asyncRefund(refundOrderDBBO);
        return refundOrderRspBO;
    }

    @Override
    public CloseOrderRspBO close(PayOrderDBBO payOrder, CloseOrderBO closeOrderBO) {
        CloseOrderSoapEnvelopRequest closeOrderSoapEnvelopRequest = closeOrderExtService.buildOrderCloseRequestParam(payOrder, closeOrderBO);
        Request request = closeOrderExtService.bulidRefundRequest(closeOrderSoapEnvelopRequest);
        //请求网关
        GenericRspDTO<Response> genericRspDTO = dceppayCgwOutClient.request(GenericDTO.newChildInstanceWithBody(GenericDTO.class, request));
        //处理网关响应对象
        if (JudgeUtils.isNotSuccess(genericRspDTO)) {
            BusinessException.throwBusinessException(genericRspDTO);
        }
        CloseOrderRsp closeOrderRsp = Optional.of(genericRspDTO)
                .map(GenericRspDTO::getBody)
                .map(Response::getResult).map(x -> (CloseOrderRsp) x)
                .orElse(new CloseOrderRsp());
        return closeOrderExtService.handleResponse(closeOrderBO, closeOrderRsp);


    }

    @Override
    public PaymentOrderResultBO paymentQuery(PaymentOrderQueryBO paymentOrderQueryBO) {
        OrderQuerySoapEnvelopRequest orderQuerySoapEnvelopRequest = orderQueryExtService.buildOrderQueryRequestParam(paymentOrderQueryBO);
        Request request = orderQueryExtService.bulidOrderQueryRequest(orderQuerySoapEnvelopRequest);
        GenericRspDTO<Response> genericRspDTO = dceppayCgwOutClient.request(GenericDTO.newChildInstanceWithBody(GenericDTO.class, request));
        //处理网关响应对象
        if (JudgeUtils.isNotSuccess(genericRspDTO)) {
            BusinessException.throwBusinessException(genericRspDTO);
        }
        UnifiedOrderQueryRsp unifiedOrderQueryRsp = Optional.of(genericRspDTO)
                .map(GenericRspDTO::getBody)
                .map(Response::getResult).map(x -> (UnifiedOrderQueryRsp) x)
                .orElse(new UnifiedOrderQueryRsp());
        return orderQueryExtService.handleResponse(paymentOrderQueryBO, unifiedOrderQueryRsp);

    }

    @Override
    public RefundOrderResultBO refundQuery(RefundOrderQueryBO refundOrderQueryBO) {
        RefundOrderQuerySoapEnvelopRequest refundOrderQuerySoapEnvelopRequest = refundQueryExtService.buildRefundQueryRequestParam(refundOrderQueryBO);
        Request request = refundQueryExtService.bulidRefundQueryRequest(refundOrderQuerySoapEnvelopRequest);
        GenericRspDTO<Response> genericRspDTO = dceppayCgwOutClient.request(GenericDTO.newChildInstanceWithBody(GenericDTO.class, request));
        //处理网关响应对象
        if (JudgeUtils.isNotSuccess(genericRspDTO)) {
            BusinessException.throwBusinessException(genericRspDTO);
        }
        RefundOrderQueryRsp refundOrderRsp = Optional.of(genericRspDTO)
                .map(GenericRspDTO::getBody)
                .map(Response::getResult).map(x -> (RefundOrderQueryRsp) x)
                .orElse(new RefundOrderQueryRsp());
        return refundQueryExtService.handleResponse(refundOrderQueryBO, refundOrderRsp);

    }

    @Override
    public PaymentNotifyRspBO paymentNotify(PayOrderDBBO order, PaymentNotifyBO paymentNotifyBO) {
        PaymentNotifyRspBO paymentNotifyRspBO = new PaymentNotifyRspBO();
        BeanUtils.copyProperties(paymentNotifyRspBO, paymentNotifyBO);
        String responseStatus = paymentNotifyBO.getResponseStatus();
        PaymentOrderResultBO paymentOrderResultBO = payOrderDataService.buildPaymentOrderResult(paymentNotifyBO);
        order.setReceiveNotifyTime(DateTimeUtil.getCurrentDateTimeStr());
        try {
            switch (responseStatus) {
                case DcepResponseStatusConstant.PAY_ORDER_SUCCESS:
                    if (payOrderService.checkPaymentStatus(OrderStatusEnum.TRADE_SUCCESS, order, paymentNotifyRspBO)) {
                        break;
                    }
                    if (paymentNotifyBO.getTransactionAmount().equals(order.getOrderAmount())) {
                        payOrderService.handleSuccessPayOrder(paymentOrderResultBO, order);
                        paymentNotifyRspBO.setProcessStatus(DcepResponseStatusConstant.NOTIFY_HANDLE_SUCCESS);
                    } else {
                        BusinessException.throwBusinessException(MsgCodeEnum.PAYMENT_ORDER_NOTIFY_AMOUNT_ERROR);
                    }
                    break;
                case DcepResponseStatusConstant.PAY_ORDER_FAIL:
                    if (payOrderService.checkPaymentStatus(OrderStatusEnum.TRADE_FAIL, order, paymentNotifyRspBO)) {
                        break;
                    }
                    if (paymentNotifyBO.getTransactionAmount().equals(order.getOrderAmount())) {
                        payOrderService.handleFailPayOrder(paymentOrderResultBO, order);
                        paymentNotifyRspBO.setProcessStatus(DcepResponseStatusConstant.NOTIFY_HANDLE_SUCCESS);
                    } else {
                        BusinessException.throwBusinessException(MsgCodeEnum.PAYMENT_ORDER_NOTIFY_AMOUNT_ERROR);
                    }
                    break;
                default:
                    log.error("互联互通支付订单通知业务回执状态非法: {}", responseStatus);
                    BusinessException.throwBusinessException(MsgCodeEnum.PAYMENT_ORDER_NOTIFY_STATUS_INVALID);
            }
        } catch (Exception e) {
            log.error("处理互联互通支付订单通知失败: {}", e.getCause());
            ExceptionHandlerUtil.throwRequestDcepError(e);
        }
        return paymentNotifyRspBO;
    }

    @Override
    public RefundNotifyRspBO refundNotify(RefundOrderDBBO refundOrder, RefundNotifyBO refundNotifyBO) {
        RefundNotifyRspBO refundNotifyRspBO = new RefundNotifyRspBO();
        BeanUtils.copyProperties(refundNotifyRspBO, refundNotifyBO);
        String responseStatus = refundNotifyBO.getResponseStatus();
        RefundOrderResultBO refundOrderResultBO = refundOrderDataService.buildRefundOrderResult(refundNotifyBO);
        refundOrder.setReceiveNotifyTime(DateTimeUtil.getCurrentDateTimeStr());
        try {
            switch (responseStatus) {
                case DcepResponseStatusConstant.PREFUND_ORDER_SUCCESS:
                    if (refundService.checkRefundStatus(OrderStatusEnum.REFUND_SUCCESS, refundOrder, refundNotifyRspBO)) {
                        break;
                    }
                    if (refundNotifyBO.getTransactionAmount().equals(refundOrder.getRefundAmount())) {
                        refundService.handleSuccessRefundOrder(refundOrderResultBO, refundOrder);
                        refundNotifyRspBO.setProcessStatus(DcepResponseStatusConstant.NOTIFY_HANDLE_SUCCESS);
                    } else {
                        BusinessException.throwBusinessException(MsgCodeEnum.REFUND_ORDER_NOTIFY_AMOUNT_ERROR);
                    }
                    break;
                case DcepResponseStatusConstant.REFUND_ORDER_FAIL:
                    if (refundService.checkRefundStatus(OrderStatusEnum.REFUND_FAIL, refundOrder, refundNotifyRspBO)) {
                        break;
                    }
                    if (refundNotifyBO.getTransactionAmount().equals(refundOrder.getRefundAmount())) {
                        refundService.handleFailRefundOrder(refundOrderResultBO, refundOrder);
                        refundNotifyRspBO.setProcessStatus(DcepResponseStatusConstant.NOTIFY_HANDLE_SUCCESS);
                    } else {
                        BusinessException.throwBusinessException(MsgCodeEnum.REFUND_ORDER_NOTIFY_AMOUNT_ERROR);
                    }
                    break;
                default:
                    log.error("互联互通退款订单通知业务回执状态非法: {}", responseStatus);
                    BusinessException.throwBusinessException(MsgCodeEnum.REFUND_ORDER_NOTIFY_STATUS_INVALID);
            }
        } catch (Exception e) {
            log.error("处理互联互通支付退款通知失败: {}", e.getCause());
            ExceptionHandlerUtil.throwRequestDcepError(e);
        }
        return refundNotifyRspBO;
    }

    @Override
    public void refundAsync(RefundOrderDBBO refundOrderBO) {
        RefundOrderSoapEnvelopRequest refundOrderSoapEnvelopRequest = dcepRefundOrderExtService.buildRefundRequestParam(refundOrderBO);
        Request request = dcepRefundOrderExtService.bulidRefundRequest(refundOrderSoapEnvelopRequest);
        GenericRspDTO<Response> genericRspDTO;
        RefundOrderRsp refundOrderRsp;
        try {
            genericRspDTO = dceppayCgwOutClient.request(GenericDTO.newChildInstanceWithBody(GenericDTO.class, request));
            if (JudgeUtils.isNotSuccess(genericRspDTO)) {
                BusinessException.throwBusinessException(genericRspDTO);
            }
            refundOrderRsp = Optional.ofNullable(genericRspDTO)
                    .map(GenericRspDTO::getBody)
                    .map(Response::getResult)
                    .filter(RefundOrderRsp.class::isInstance)
                    .map(RefundOrderRsp.class::cast)
                    .orElse(new RefundOrderRsp());
            RefundOrderRspBO refundOrderRspBO = dcepRefundOrderExtService.handleResponse(refundOrderRsp);
            refundOrderBO.setMessageIdentification(refundOrderSoapEnvelopRequest.getRefundOrderBody().getRequestGroupHeader().getMessageIdentification());
            dcepRefundOrderExtService.updateRefundAccept(refundOrderBO, refundOrderRspBO);
        } catch (Exception e) {
            ExceptionHandlerUtil.throwRequestDcepError(e);
        }
    }
}
