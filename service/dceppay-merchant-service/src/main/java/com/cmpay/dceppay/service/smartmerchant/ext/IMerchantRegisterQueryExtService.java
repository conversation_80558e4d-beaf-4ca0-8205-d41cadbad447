package com.cmpay.dceppay.service.smartmerchant.ext;

import com.cmpay.channel.data.Request;
import com.cmpay.dceppay.bo.smartmerchant.MerchantRegisterQueryReqBO;
import com.cmpay.dceppay.bo.smartmerchant.MerchantRegisterQueryRspBO;
import com.cmpay.dceppay.dto.icbc.smartpay.MerchantRegisterQueryRequest;
import com.cmpay.dceppay.dto.icbc.smartpay.MerchantRegisterQueryResponse;

/**
 * <AUTHOR>
 * @date 2024/8/28 15:50
 */
public interface IMerchantRegisterQueryExtService {

    /**
     * 获取网关请求参数
     * @param registerQueryRequest
     * @return
     */
    Request getRequest(MerchantRegisterQueryRequest registerQueryRequest);

    /**
     * 网关响应处理
     * @param registerQueryResponse
     * @return
     */
    MerchantRegisterQueryRspBO handleResponse(MerchantRegisterQueryResponse registerQueryResponse);
}
