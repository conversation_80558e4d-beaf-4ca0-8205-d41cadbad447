package com.cmpay.dceppay.service.smartmerchant.ext.impl;

import com.cmpay.channel.data.Request;
import com.cmpay.dceppay.bo.smartmerchant.MerchantRegisterQueryRspBO;
import com.cmpay.dceppay.channel.IcbcChannelEnum;
import com.cmpay.dceppay.constant.icbc.ICBCConstants;
import com.cmpay.dceppay.dto.icbc.smartpay.MerchantRegisterQueryRequest;
import com.cmpay.dceppay.dto.icbc.smartpay.MerchantRegisterQueryResponse;
import com.cmpay.dceppay.enums.MsgCodeEnum;
import com.cmpay.dceppay.service.smartmerchant.ext.IMerchantRegisterQueryExtService;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.framework.utils.LemonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/8/28 15:56
 */
@Service
@Slf4j
public class MerchantRegisterQueryExtServiceImpl implements IMerchantRegisterQueryExtService {

    @Override
    public Request getRequest(MerchantRegisterQueryRequest registerQueryRequest) {
        Request request = new Request();
        request.setRequestId(LemonUtils.getRequestId());
        request.setRoute(IcbcChannelEnum.ICBC_MERCHANT);
        request.setBusiType(IcbcChannelEnum.MERCHANT_REGISTER_QUERY.getName());
        request.setSource(IcbcChannelEnum.ICBC_MERCHANT);
        request.setTarget(registerQueryRequest);
        return request;
    }

    @Override
    public MerchantRegisterQueryRspBO handleResponse(MerchantRegisterQueryResponse registerQueryResponse) {
        int returnCode = registerQueryResponse.getReturnCode();
        int resultCode = registerQueryResponse.getResultCode();
        if (resultCode == ICBCConstants.RETURN_CODE_SUCCESS && returnCode == ICBCConstants.RETURN_CODE_SUCCESS) {
            return successDeal(registerQueryResponse);
        } else {
            return failDeal(registerQueryResponse, returnCode);
        }
    }

    private static MerchantRegisterQueryRspBO failDeal(MerchantRegisterQueryResponse registerQueryResponse, int returnCode) {
        String returnMsg = registerQueryResponse.getReturnMsg();
        String errorCode = registerQueryResponse.getErrCode();
        String errorMessage = registerQueryResponse.getErrCodeDes();
        String errorInfo = "服务商进件查询返回失败，返回码：" + returnCode + "，返回码说明：" + returnMsg + "；错误码：" + errorCode + "，错误码说明：" + errorMessage;
        log.error(errorInfo);
        BusinessException.throwBusinessException(MsgCodeEnum.MERCHANT_ONBOARD_QUERY_FAIL, errorInfo,"*");
        return new MerchantRegisterQueryRspBO();
    }

    private static MerchantRegisterQueryRspBO successDeal(MerchantRegisterQueryResponse registerQueryResponse) {
        MerchantRegisterQueryRspBO registerQueryRspBO = new MerchantRegisterQueryRspBO();
        BeanUtils.copyProperties(registerQueryRspBO, registerQueryResponse);
        return registerQueryRspBO;

    }
}
