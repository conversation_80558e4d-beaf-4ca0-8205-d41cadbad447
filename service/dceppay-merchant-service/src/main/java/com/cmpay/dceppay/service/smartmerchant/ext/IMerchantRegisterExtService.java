package com.cmpay.dceppay.service.smartmerchant.ext;

import com.cmpay.channel.data.Request;
import com.cmpay.dceppay.bo.smartmerchant.ICBCMerchantBO;
import com.cmpay.dceppay.bo.smartmerchant.MerchantRegisterReqBO;
import com.cmpay.dceppay.bo.smartmerchant.MerchantRegisterRspBO;
import com.cmpay.dceppay.dto.icbc.smartpay.MerchantRegisterRequest;
import com.cmpay.dceppay.dto.icbc.smartpay.MerchantRegisterResponse;

/**
 * <AUTHOR>
 * @date 2024/8/28 14:07
 * 工行智能收款商户进件拓展业务处理类
 */
public interface IMerchantRegisterExtService {
    /**
     * 获取工行商户进件网关请求对象
     *
     * @param registerReqBO
     * @return MerchantRegisterRequest
     */
    MerchantRegisterRequest getMerchantRegisterRequest(MerchantRegisterReqBO registerReqBO);

    /**
     * 获取工行商户进件网关请求参数
     *
     * @param registerRequest
     * @return Request
     */
    Request getRequest(MerchantRegisterRequest registerRequest);

    /**
     * 工行商户进件响应处理
     *
     * @param registerResponse
     * @return MerchantRegisterRspBO
     */
    MerchantRegisterRspBO handleResponse(MerchantRegisterResponse registerResponse, ICBCMerchantBO icbcMerchantBO );
}
