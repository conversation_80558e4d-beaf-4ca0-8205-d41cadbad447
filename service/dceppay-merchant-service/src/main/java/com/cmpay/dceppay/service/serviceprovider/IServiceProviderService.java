package com.cmpay.dceppay.service.serviceprovider;

import com.cmpay.dceppay.bo.serviceprovider.ServiceProviderRegisterRepBO;
import com.cmpay.dceppay.bo.serviceprovider.ServiceProviderRegisterRspBO;

/**
 * <AUTHOR>
 * @date 2024/8/27 14:31
 */
public interface IServiceProviderService {
    /**
     * 服务商进件
     * @param serviceProviderRegisterRepBO
     * @return
     */
    ServiceProviderRegisterRspBO register(ServiceProviderRegisterRepBO serviceProviderRegisterRepBO);
}
