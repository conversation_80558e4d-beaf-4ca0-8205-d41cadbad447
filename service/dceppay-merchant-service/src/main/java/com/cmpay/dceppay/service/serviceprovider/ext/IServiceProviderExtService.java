package com.cmpay.dceppay.service.serviceprovider.ext;

import com.cmpay.channel.data.Request;
import com.cmpay.dceppay.bo.serviceprovider.ServiceProviderRegisterRepBO;
import com.cmpay.dceppay.bo.serviceprovider.ServiceProviderRegisterRspBO;
import com.cmpay.dceppay.bo.smartmerchant.ICBCServiceProviderBO;
import com.cmpay.dceppay.dto.icbc.smartpay.PayDigitalWalletOutServiceRegisterRequest;
import com.cmpay.dceppay.dto.icbc.smartpay.PayDigitalWalletOutServiceRegisterResponse;

/**
 * <AUTHOR>
 * @date 2024/8/28 15:30
 */
public interface IServiceProviderExtService {
    /**
     * 服务商信息检查
     * @param serviceId
     * @param operatorId
     * @return
     */
    ICBCServiceProviderBO checkServiceProviderInfo(String serviceId,String operatorId);

    /**
     *获取网关请求对象
     * @param serviceProviderRegisterRepBO
     * @return
     */
    PayDigitalWalletOutServiceRegisterRequest getOutServiceRequest(ServiceProviderRegisterRepBO serviceProviderRegisterRepBO);

    /**
     * 构建网关请求参数
     * @param outServiceRegisterRequest
     * @return
     */

    Request getRequest(PayDigitalWalletOutServiceRegisterRequest outServiceRegisterRequest);

    /**
     * c网关响应处理
     * @param outServiceRegisterResponse
     * @param serviceProviderBO
     * @return
     */

    ServiceProviderRegisterRspBO handleResponse(PayDigitalWalletOutServiceRegisterResponse outServiceRegisterResponse,ICBCServiceProviderBO serviceProviderBO);
}
