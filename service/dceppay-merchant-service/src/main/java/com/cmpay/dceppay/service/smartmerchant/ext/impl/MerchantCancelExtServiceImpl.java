package com.cmpay.dceppay.service.smartmerchant.ext.impl;

import com.cmpay.channel.data.Request;
import com.cmpay.dceppay.bo.smartmerchant.ICBCMerchantBO;
import com.cmpay.dceppay.bo.smartmerchant.ICBCMerchantUpdateStatusBO;
import com.cmpay.dceppay.bo.smartmerchant.MerchantCancelRspBO;
import com.cmpay.dceppay.channel.IcbcChannelEnum;
import com.cmpay.dceppay.constant.icbc.ICBCConstants;
import com.cmpay.dceppay.dto.icbc.smartpay.MerchantCancelRequest;
import com.cmpay.dceppay.dto.icbc.smartpay.MerchantCancelResponse;
import com.cmpay.dceppay.enums.MsgCodeEnum;
import com.cmpay.dceppay.service.smartmerchant.IICBCMerchantDBService;
import com.cmpay.dceppay.service.smartmerchant.ext.IMerchantCancelExtService;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.utils.LemonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/8/28 15:55
 */
@Service
@Slf4j
public class MerchantCancelExtServiceImpl implements IMerchantCancelExtService {
    @Autowired
    private IICBCMerchantDBService merchantDBService;

    @Override
    public Request getRequest(MerchantCancelRequest merchantCancelRequest) {
        Request request = new Request();
        request.setRequestId(LemonUtils.getRequestId());
        request.setRoute(IcbcChannelEnum.ICBC_MERCHANT);
        request.setBusiType(IcbcChannelEnum.MERCHANT_CANCEL.getName());
        request.setSource(IcbcChannelEnum.ICBC_MERCHANT);
        request.setTarget(merchantCancelRequest);
        return request;
    }

    @Override
    public MerchantCancelRspBO handleResponse(MerchantCancelResponse merchantCancelResponse, ICBCMerchantBO merchantBO,String operateId) {
        int returnCode = merchantCancelResponse.getReturnCode();
        int resultCode = merchantCancelResponse.getResultCode();
        if (resultCode == ICBCConstants.RETURN_CODE_SUCCESS && returnCode == ICBCConstants.RETURN_CODE_SUCCESS) {
            return successDeal(merchantBO,operateId);
        } else {
            return failDeal(merchantCancelResponse, merchantBO, returnCode);
        }
    }

    private MerchantCancelRspBO failDeal(MerchantCancelResponse registerQueryResponse, ICBCMerchantBO merchantBO, int returnCode) {
        String returnMsg = registerQueryResponse.getReturnMsg();
        String errorCode = registerQueryResponse.getErrCode();
        String errorMessage = registerQueryResponse.getErrCodeDes();
        String errorInfo = "服务商进件返回失败，返回码：" + returnCode + "，返回码说明：" + returnMsg + "；错误码：" + errorCode + "，错误码说明：" + errorMessage;
        log.error(errorInfo);
        //更新作废失败
        ICBCMerchantUpdateStatusBO updateStatusBO = new ICBCMerchantUpdateStatusBO();
        updateStatusBO.setMerchantId(merchantBO.getMerchantId());
        updateStatusBO.setUpdateId(merchantBO.getOperatorId());
        int updateNumber = merchantDBService.updateOnboardCancelFail(updateStatusBO);
        if (updateNumber != 1) {
            BusinessException.throwBusinessException(MsgCodeEnum.UPDATE_MERCHANT_STATUS_ERROR);
        }
        BusinessException.throwBusinessException(MsgCodeEnum.MERCHANT_CANCEL_FAIL, errorInfo,"*");
        return new MerchantCancelRspBO();
    }

    private MerchantCancelRspBO successDeal(ICBCMerchantBO merchantBO,String operateId) {
        ICBCMerchantUpdateStatusBO updateStatusBO = new ICBCMerchantUpdateStatusBO();
        updateStatusBO.setMerchantId(merchantBO.getMerchantId());
        updateStatusBO.setUpdateId(operateId);
        int updateNumber = merchantDBService.updateOnboardCancel(updateStatusBO);
        if (updateNumber != 1) {
            BusinessException.throwBusinessException(MsgCodeEnum.UPDATE_MERCHANT_STATUS_ERROR);
        }
        return new MerchantCancelRspBO();

    }
}
