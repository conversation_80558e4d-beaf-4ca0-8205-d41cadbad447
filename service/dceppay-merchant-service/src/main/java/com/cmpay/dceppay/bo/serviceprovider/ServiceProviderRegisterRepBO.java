package com.cmpay.dceppay.bo.serviceprovider;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/8/27 14:27
 * 服务商进件请求参数
 */
@Data
public class ServiceProviderRegisterRepBO {
    /**
     * 服务商编号
     */
    private String outServiceId;
    /**
     * 服务商名称
     */
    private String serviceName;
    /**
     * 证件类型
     */
    private String licenseType;
    /**
     * 证件编号
     */
    private String licenseNo;
    /**
     * 场景ID
     */
    private String sceneId;
    /**
     * 场景描述
     */
    private String sceneDesc;
    /**
     * 备注信息
     */
    private String remark;
    /**
     * 运管操作id
     */
    private String operatorId;
}
