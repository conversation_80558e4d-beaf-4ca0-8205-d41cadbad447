package com.cmpay.dceppay.service.smartmerchant.ext.impl;

import com.cmpay.channel.data.Request;
import com.cmpay.dceppay.bo.smartmerchant.*;
import com.cmpay.dceppay.channel.IcbcChannelEnum;
import com.cmpay.dceppay.config.IcbcConfig;
import com.cmpay.dceppay.constant.icbc.ICBCConstants;
import com.cmpay.dceppay.dto.icbc.smartpay.MerchantRegisterRequest;
import com.cmpay.dceppay.dto.icbc.smartpay.MerchantRegisterResponse;
import com.cmpay.dceppay.enums.MsgCodeEnum;
import com.cmpay.dceppay.enums.common.IdGenKeyEnum;
import com.cmpay.dceppay.enums.icbc.MerchantTypeEnum;
import com.cmpay.dceppay.enums.icbc.WalletTypeEnum;
import com.cmpay.dceppay.service.smartmerchant.IICBCMerchantDBService;
import com.cmpay.dceppay.service.smartmerchant.ext.IMerchantRegisterExtService;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.utils.IdGenUtils;
import com.cmpay.lemon.framework.utils.LemonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/8/28 14:12
 */
@Service
@Slf4j
public class MerchantRegisterExtServiceImpl implements IMerchantRegisterExtService {
    @Autowired
    private IICBCMerchantDBService merchantDBService;
    @Autowired
    private IcbcConfig icbcConfig;

    @Override
    public MerchantRegisterRequest getMerchantRegisterRequest(MerchantRegisterReqBO registerReqBO) {
        MerchantRegisterRequest registerRequest = new MerchantRegisterRequest();
        registerRequest.setProtocolId(icbcConfig.getProtocolId());
        //必输
        registerRequest.setIsServiceMerchant(registerReqBO.getIsServiceMerchant());
        if (registerRequest.getIsServiceMerchant() == ICBCConstants.IS_SERVICE_MERCHANT) {
            registerRequest.setServiceId(registerReqBO.getServiceId());
        }
        registerRequest.setOutMerchantId(registerReqBO.getOutMerchantId());
        registerRequest.setMerchantType(registerReqBO.getMerchantType().getValue());
        registerRequest.setMerchantLicenseType(registerReqBO.getMerchantLicenseType());
        registerRequest.setMerchantLicense(registerReqBO.getMerchantLicense());
        if (MerchantTypeEnum.NORMAL_MERCHANT.getValue() == registerReqBO.getMerchantType().getValue()) {
            registerRequest.setMerchantName(registerReqBO.getMerchantName());
        }
        registerRequest.setMerchantShowName(registerReqBO.getMerchantShowName());

        registerRequest.setMerchantShortName(registerReqBO.getMerchantShortName());

        registerRequest.setMerchantCategory(registerReqBO.getMerchantCategory());
        registerRequest.setServicePhone(registerReqBO.getServicePhone());
        registerRequest.setContactName(registerReqBO.getContactName());
        registerRequest.setContactPhone(registerReqBO.getContactPhone());
        registerRequest.setContactEmail(registerReqBO.getContactEmail());
        registerRequest.setBussType(registerReqBO.getBussType());
        registerRequest.setBussCode(registerReqBO.getBussCode());
        registerRequest.setMerchantRemark(registerReqBO.getMerchantRemark());
        registerRequest.setUnifyEntryFlag(registerReqBO.getUnifyEntryFlag().getValue());
        registerRequest.setAccType(registerReqBO.getAccType().getValue());
        if (WalletTypeEnum.WALLET.getValue() == registerReqBO.getAccType().getValue()) {
            registerRequest.setWalletName(registerReqBO.getWalletName());
        }
        if (WalletTypeEnum.SAME_BANK_ACCOUNT == registerReqBO.getAccType() || WalletTypeEnum.DIFFERENT_BANK_ACCOUNT == registerReqBO.getAccType()) {
            registerRequest.setAccName(registerReqBO.getAccName());
            registerRequest.setAccNo(registerReqBO.getAccNo());
            registerRequest.setAccBankName(registerReqBO.getAccBankName());
            registerRequest.setAccBankCode(registerReqBO.getAccBankCode());
        }
        registerRequest.setSettleCycle(registerReqBO.getSettleCycle());
        return registerRequest;
    }

    @Override
    public Request getRequest(MerchantRegisterRequest registerRequest) {
        Request request = new Request();
        request.setRequestId(LemonUtils.getRequestId());
        request.setRoute(IcbcChannelEnum.ICBC_MERCHANT);
        request.setBusiType(IcbcChannelEnum.MERCHANT_REGISTER.getName());
        request.setSource(IcbcChannelEnum.ICBC_MERCHANT);
        request.setTarget(registerRequest);
        return request;
    }

    @Override
    public MerchantRegisterRspBO handleResponse(MerchantRegisterResponse registerResponse, ICBCMerchantBO icbcMerchantBO) {
        int returnCode = registerResponse.getReturnCode();
        int resultCode = registerResponse.getResultCode();
        if (resultCode == ICBCConstants.RETURN_CODE_SUCCESS && returnCode == ICBCConstants.RETURN_CODE_SUCCESS) {
            return successDeal(registerResponse, icbcMerchantBO);
        } else {
            return failDeal(registerResponse, icbcMerchantBO, returnCode);
        }
    }

    private MerchantRegisterRspBO failDeal(MerchantRegisterResponse registerResponse, ICBCMerchantBO icbcMerchantBO, int returnCode) {
        String returnMsg = registerResponse.getReturnMsg();
        String errorCode = registerResponse.getErrCode();
        String errorMessage = registerResponse.getErrCodeDes();
        String errorInfo = "服务商进件返回失败，返回码：" + returnCode + "，返回码说明：" + returnMsg + "；错误码：" + errorCode + "，错误码说明：" + errorMessage;
        log.error(errorInfo);
        ICBCMerchantUpdateStatusBO updateStatusBO = new ICBCMerchantUpdateStatusBO();
        updateStatusBO.setMerchantId(icbcMerchantBO.getMerchantId());
        updateStatusBO.setUpdateId(icbcMerchantBO.getOperatorId());
        updateStatusBO.setOnboardId(IdGenUtils.generateIdWithDateTime(IdGenKeyEnum.ONBOARD_ID.name(), IdGenKeyEnum.ONBOARD_ID_LENGTH));
        updateStatusBO.setMsgCode(errorCode);
        updateStatusBO.setMsgInfo(errorInfo);
        // 更新状态进件中
        int updateNumber = merchantDBService.updateOnboardFail(updateStatusBO);
        if (updateNumber != 1) {
            BusinessException.throwBusinessException(MsgCodeEnum.UPDATE_MERCHANT_STATUS_ERROR);

        }
        BusinessException.throwBusinessException(MsgCodeEnum.MERCHANT_ONBOARD_FAIL, errorInfo,"*");
        return new MerchantRegisterRspBO();
    }

    private MerchantRegisterRspBO successDeal(MerchantRegisterResponse registerResponse, ICBCMerchantBO icbcMerchantBO) {
        MerchantRegisterRspBO registerRspBO = new MerchantRegisterRspBO();
        String operatorMerchantId = registerRspBO.getMerchantId();
        String umbrDownWalletId = registerResponse.getUmbrDownWalletId();
        String umbrDownWalletName = registerResponse.getUmbrDownWalletName();
        String umbrTopWalletId = registerResponse.getUmbrTopWalletId();
        String umbrTopWalletName = registerResponse.getUmbrTopWalletName();
        boolean checkFlag = checkLocalValue(registerResponse);
        ICBCMerchantUpdateStatusBO updateStatusBO = new ICBCMerchantUpdateStatusBO();
        updateStatusBO.setMerchantId(icbcMerchantBO.getMerchantId());
        updateStatusBO.setUpdateId(icbcMerchantBO.getOperatorId());
        updateStatusBO.setOperatorMerchantId(operatorMerchantId);
        updateStatusBO.setOnboardId(IdGenUtils.generateIdWithDateTime(IdGenKeyEnum.ONBOARD_ID.name(), IdGenKeyEnum.ONBOARD_ID_LENGTH));
        updateStatusBO.setMsgCode("");
        updateStatusBO.setMsgInfo("");
        updateStatusBO.setProtocolWalletId(umbrTopWalletId);
        updateStatusBO.setProtocolWalletName(umbrTopWalletName);
        updateStatusBO.setMerchantWalletId(umbrDownWalletId);
        updateStatusBO.setMerchantWalletName(umbrDownWalletName);
        updateStatusBO.setOperatorMerchantId(registerResponse.getMerchantId());
        if (checkFlag) {
            // 更新状态进件中
            int updateNumber = merchantDBService.updateOnboardSuccess(updateStatusBO);
            if (updateNumber != 1) {
                BusinessException.throwBusinessException(MsgCodeEnum.UPDATE_MERCHANT_STATUS_ERROR);
            }
        } else {
            merchantDBService.updateOnboardFail(updateStatusBO);
            BusinessException.throwBusinessException(MsgCodeEnum.ONBOARD_RESPONSE_DATA_ERROR);
        }
        registerRspBO.setMerchantId(operatorMerchantId);
        registerRspBO.setUmbrDownWalletId(umbrDownWalletId);
        registerRspBO.setUmbrDownWalletName(umbrDownWalletName);
        registerRspBO.setUmbrTopWalletId(umbrTopWalletId);
        registerRspBO.setUmbrTopWalletName(umbrTopWalletName);
        return registerRspBO;
    }

    private boolean checkLocalValue(MerchantRegisterResponse registerResponse) {
        String umbrTopWalletId = registerResponse.getUmbrTopWalletId();
        return JudgeUtils.equals(icbcConfig.getUmbrTopWalletId(), umbrTopWalletId);
    }
}
