package com.cmpay.dceppay.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2024/10/11 17:25
 */
@Configuration
@ConfigurationProperties(prefix = "icbc")
@Data
public class IcbcConfig {
    //伞顶钱包id
    private String umbrTopWalletId;
    //伞顶钱包名称
    private String umbrTopWalletName;
    /**
     * 智能收款协议编号
     */
    private String protocolId;
}
