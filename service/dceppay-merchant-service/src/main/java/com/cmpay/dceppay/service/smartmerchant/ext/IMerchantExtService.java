package com.cmpay.dceppay.service.smartmerchant.ext;

import com.cmpay.dceppay.bo.smartmerchant.ICBCMerchantBO;
import com.cmpay.dceppay.bo.smartmerchant.MerchantRegisterCheckResultBO;
import com.cmpay.dceppay.bo.smartmerchant.MerchantRegisterReqBO;

/**
 * <AUTHOR>
 * @date 2024/8/29 11:15
 */
public interface IMerchantExtService {

    /**
     * 检查并返回商户信息
     * @param outMerchantId
     * @return
     */
    ICBCMerchantBO checkAndGetICBCMerchantInfo(String outMerchantId);

    /**
     * 更新进件状态
     * @param icbcMerchantBO
     * @param operatorId
     */
    void updateOnboardStatus(ICBCMerchantBO icbcMerchantBO, String operatorId);

    /**
     * 更新进件
     * @param merchantBO
     * @param operatorId
     */
    void updateOnboardCanceling(ICBCMerchantBO merchantBO, String operatorId);

    /**
     * 商户进件请求参数校验
     * @param registerReqBO
     * @return
     */
    MerchantRegisterCheckResultBO checkPram(MerchantRegisterReqBO registerReqBO);
}
