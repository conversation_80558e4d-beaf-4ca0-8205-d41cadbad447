package com.cmpay.dceppay.service.serviceprovider.ext.impl;

import com.cmpay.channel.data.Request;
import com.cmpay.dceppay.bo.serviceprovider.ServiceProviderRegisterRepBO;
import com.cmpay.dceppay.bo.serviceprovider.ServiceProviderRegisterRspBO;
import com.cmpay.dceppay.bo.smartmerchant.ICBCServiceProviderBO;
import com.cmpay.dceppay.bo.smartmerchant.ICBCServiceProviderUpdateStatusBO;
import com.cmpay.dceppay.channel.IcbcChannelEnum;
import com.cmpay.dceppay.config.IcbcConfig;
import com.cmpay.dceppay.constant.icbc.ICBCConstants;
import com.cmpay.dceppay.dto.icbc.smartpay.PayDigitalWalletOutServiceRegisterRequest;
import com.cmpay.dceppay.dto.icbc.smartpay.PayDigitalWalletOutServiceRegisterResponse;
import com.cmpay.dceppay.enums.MsgCodeEnum;
import com.cmpay.dceppay.enums.common.IdGenKeyEnum;
import com.cmpay.dceppay.service.serviceprovider.ext.IServiceProviderExtService;
import com.cmpay.dceppay.service.smartmerchant.IICBCServiceProviderDBService;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.utils.IdGenUtils;
import com.cmpay.lemon.framework.utils.LemonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/8/28 15:30
 */
@Service
@Slf4j
public class ServiceProviderExtServiceImpl implements IServiceProviderExtService {

    @Autowired
    private IICBCServiceProviderDBService iicbcServiceProviderDBService;
    @Autowired
    private IcbcConfig icbcConfig;

    @Override
    public ICBCServiceProviderBO checkServiceProviderInfo(String serviceId, String operatorId) {
        //查询服务商信息表中数据是否存在outServiceId
        ICBCServiceProviderBO serviceProviderBO = iicbcServiceProviderDBService.getServiceProviderInfoByServiceID(serviceId);
        if (JudgeUtils.isNull(serviceProviderBO)) {
            BusinessException.throwBusinessException(MsgCodeEnum.SERVICE_INFO_NOT_EXIST);
        }
        ICBCServiceProviderUpdateStatusBO updateStatusBO = new ICBCServiceProviderUpdateStatusBO();
        updateStatusBO.setServiceId(serviceId);
        updateStatusBO.setUpdateId(operatorId);
        // 更新状态进件中
        int updateNumber = iicbcServiceProviderDBService.updateOnboarding(updateStatusBO);
        if (updateNumber != 1) {
            BusinessException.throwBusinessException(MsgCodeEnum.UPDATE_SERVICE_ONBOARD_STATUS_ERROR.getMsgCd());
        }
        serviceProviderBO.setOperatorId(operatorId);
        return serviceProviderBO;
    }

    @Override
    public PayDigitalWalletOutServiceRegisterRequest getOutServiceRequest(ServiceProviderRegisterRepBO serviceProviderRegisterRepBO) {
        PayDigitalWalletOutServiceRegisterRequest outServiceRegisterRequest = new PayDigitalWalletOutServiceRegisterRequest();
        outServiceRegisterRequest.setProtocolId(icbcConfig.getProtocolId());
        outServiceRegisterRequest.setOutServiceId(serviceProviderRegisterRepBO.getOutServiceId());
        outServiceRegisterRequest.setServiceName(serviceProviderRegisterRepBO.getServiceName());
        outServiceRegisterRequest.setLicenseType(serviceProviderRegisterRepBO.getLicenseType());
        outServiceRegisterRequest.setLicenseNo(serviceProviderRegisterRepBO.getLicenseNo());
        //非必输
        outServiceRegisterRequest.setSceneId(serviceProviderRegisterRepBO.getSceneId());
        outServiceRegisterRequest.setSceneDesc(serviceProviderRegisterRepBO.getSceneDesc());
        outServiceRegisterRequest.setRemark(serviceProviderRegisterRepBO.getRemark());
        return outServiceRegisterRequest;
    }

    @Override
    public Request getRequest(PayDigitalWalletOutServiceRegisterRequest outServiceRegisterRequest) {
        Request request = new Request();
        request.setRequestId(LemonUtils.getRequestId());
        request.setRoute(IcbcChannelEnum.ICBC_MERCHANT);
        request.setBusiType(IcbcChannelEnum.OUT_SERVICE_REGISTER.getName());
        request.setSource(IcbcChannelEnum.ICBC_MERCHANT);
        request.setTarget(outServiceRegisterRequest);
        return request;
    }

    @Override
    public ServiceProviderRegisterRspBO handleResponse(PayDigitalWalletOutServiceRegisterResponse outServiceRegisterResponse, ICBCServiceProviderBO serviceProviderBO) {
        int returnCode = outServiceRegisterResponse.getReturnCode();
        int resultCode = outServiceRegisterResponse.getResultCode();
        if (resultCode == ICBCConstants.RETURN_CODE_SUCCESS && returnCode == ICBCConstants.RETURN_CODE_SUCCESS) {
            return successDeal(outServiceRegisterResponse, serviceProviderBO);
        } else {
            return failDeal(outServiceRegisterResponse, serviceProviderBO, returnCode);
        }

    }

    private ServiceProviderRegisterRspBO failDeal(PayDigitalWalletOutServiceRegisterResponse outServiceRegisterResponse, ICBCServiceProviderBO serviceProviderBO, int returnCode) {
        String returnMsg = outServiceRegisterResponse.getReturnMsg();
        String errorCode = outServiceRegisterResponse.getErrCode();
        String errorMessage = outServiceRegisterResponse.getErrCodeDes();
        String errorInfo = "服务商进件返回失败，返回码：" + returnCode + "，返回码说明：" + returnMsg + "；错误码：" + errorCode + "，错误码说明：" + errorMessage;
        log.error(errorInfo);
        ICBCServiceProviderUpdateStatusBO updateStatusBO = new ICBCServiceProviderUpdateStatusBO();
        updateStatusBO.setServiceId(serviceProviderBO.getServiceId());
        updateStatusBO.setUpdateId(serviceProviderBO.getOperatorId());
        updateStatusBO.setOnboardId(IdGenUtils.generateIdWithDateTime(IdGenKeyEnum.ONBOARD_ID.name(), IdGenKeyEnum.ONBOARD_ID_LENGTH));
        updateStatusBO.setMsgCode(errorCode);
        updateStatusBO.setMsgInfo(errorInfo);
        // 更新状态进件中
        int updateNumber = iicbcServiceProviderDBService.updateOnboardFail(updateStatusBO);
        if (updateNumber != 1) {
            BusinessException.throwBusinessException(MsgCodeEnum.UPDATE_SERVICE_ONBOARD_STATUS_ERROR);
        }
        BusinessException.throwBusinessException(MsgCodeEnum.SERVICE_ONBOARD_FAIL);
        return new ServiceProviderRegisterRspBO();
    }

    private ServiceProviderRegisterRspBO successDeal(PayDigitalWalletOutServiceRegisterResponse outServiceRegisterResponse, ICBCServiceProviderBO serviceProviderBO) {
        ServiceProviderRegisterRspBO serviceProviderRegisterRspBO = new ServiceProviderRegisterRspBO();
        String serviceProviderId = outServiceRegisterResponse.getServiceId();
        if (JudgeUtils.isNotNull(serviceProviderId)) {
            ICBCServiceProviderUpdateStatusBO updateStatusBO = new ICBCServiceProviderUpdateStatusBO();
            updateStatusBO.setServiceId(serviceProviderBO.getServiceId());
            updateStatusBO.setUpdateId(serviceProviderBO.getOperatorId());
            updateStatusBO.setServiceProviderId(serviceProviderId);
            updateStatusBO.setOnboardId(IdGenUtils.generateIdWithDateTime(IdGenKeyEnum.ONBOARD_ID.name(), IdGenKeyEnum.ONBOARD_ID_LENGTH));
            updateStatusBO.setMsgCode("");
            updateStatusBO.setMsgInfo("");
            // 更新状态进件中
            int updateNumber = iicbcServiceProviderDBService.updateOnboardSuccess(updateStatusBO);
            if (updateNumber != 1) {
                BusinessException.throwBusinessException(MsgCodeEnum.UPDATE_SERVICE_ONBOARD_STATUS_ERROR);
            }
        } else {
            BusinessException.throwBusinessException(MsgCodeEnum.SERVICE_ONBOARD_RESPONSE_DATA_ERROR);
        }
        serviceProviderRegisterRspBO.setServiceId(serviceProviderId);
        return serviceProviderRegisterRspBO;
    }
}
