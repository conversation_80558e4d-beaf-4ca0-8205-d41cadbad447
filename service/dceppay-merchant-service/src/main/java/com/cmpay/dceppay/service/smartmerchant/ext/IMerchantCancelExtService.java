package com.cmpay.dceppay.service.smartmerchant.ext;

import com.cmpay.channel.data.Request;
import com.cmpay.dceppay.bo.smartmerchant.ICBCMerchantBO;
import com.cmpay.dceppay.bo.smartmerchant.MerchantCancelReqBO;
import com.cmpay.dceppay.bo.smartmerchant.MerchantCancelRspBO;
import com.cmpay.dceppay.dto.icbc.smartpay.MerchantCancelRequest;
import com.cmpay.dceppay.dto.icbc.smartpay.MerchantCancelResponse;

/**
 * <AUTHOR>
 * @date 2024/8/28 15:52
 */
public interface IMerchantCancelExtService {


    Request getRequest(MerchantCancelRequest merchantCancelRequest);

    MerchantCancelRspBO handleResponse(MerchantCancelResponse merchantCancelResponse, ICBCMerchantBO merchantBO,String operateId);
}
