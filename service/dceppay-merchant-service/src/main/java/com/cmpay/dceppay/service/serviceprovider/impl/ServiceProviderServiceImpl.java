package com.cmpay.dceppay.service.serviceprovider.impl;

import com.cmpay.channel.data.Request;
import com.cmpay.channel.data.Response;
import com.cmpay.dceppay.bo.serviceprovider.ServiceProviderRegisterRepBO;
import com.cmpay.dceppay.bo.serviceprovider.ServiceProviderRegisterRspBO;
import com.cmpay.dceppay.bo.smartmerchant.ICBCServiceProviderBO;
import com.cmpay.dceppay.client.DceppayCgwOutClient;
import com.cmpay.dceppay.dto.icbc.smartpay.PayDigitalWalletOutServiceRegisterRequest;
import com.cmpay.dceppay.dto.icbc.smartpay.PayDigitalWalletOutServiceRegisterResponse;
import com.cmpay.dceppay.service.serviceprovider.IServiceProviderService;
import com.cmpay.dceppay.service.serviceprovider.ext.IServiceProviderExtService;
import com.cmpay.framework.data.request.GenericDTO;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/8/27 14:31
 */
@Service
@Slf4j
public class ServiceProviderServiceImpl implements IServiceProviderService {
    @Autowired
    private DceppayCgwOutClient dceppayCgwOutClient;
    @Autowired
    private IServiceProviderExtService serviceProviderExtService;


    @Override
    public ServiceProviderRegisterRspBO register(ServiceProviderRegisterRepBO serviceProviderRegisterRepBO) {
        String serviceId=serviceProviderRegisterRepBO.getOutServiceId();
        String operatorId=serviceProviderRegisterRepBO.getOperatorId();
        ICBCServiceProviderBO serviceProviderBO = serviceProviderExtService.checkServiceProviderInfo(serviceId,operatorId);

        //封装网关请求参数对象
        PayDigitalWalletOutServiceRegisterRequest outServiceRegisterRequest = serviceProviderExtService.getOutServiceRequest(serviceProviderRegisterRepBO);

        Request request = serviceProviderExtService.getRequest(outServiceRegisterRequest);
        //请求网关
        GenericRspDTO<Response> genericRspDTO = dceppayCgwOutClient.request(GenericDTO.newChildInstanceWithBody(GenericDTO.class, request));
        //处理网关响应对象
        if (JudgeUtils.isNotSuccess(genericRspDTO)) {
            BusinessException.throwBusinessException(genericRspDTO);
        }
        PayDigitalWalletOutServiceRegisterResponse outServiceRegisterResponse = Optional.of(genericRspDTO)
                .map(GenericRspDTO::getBody)
                .map(Response::getResult).map(x -> (PayDigitalWalletOutServiceRegisterResponse) x)
                .orElse(new PayDigitalWalletOutServiceRegisterResponse());
        serviceProviderBO.setOperatorId(operatorId);
        ServiceProviderRegisterRspBO serviceProviderRegisterRspBO = serviceProviderExtService.handleResponse(outServiceRegisterResponse,serviceProviderBO);
        //返回
        return serviceProviderRegisterRspBO;
    }


}
