package com.cmpay.dceppay.bo.smartmerchant;

import com.cmpay.dceppay.enums.icbc.MerchantLicenseTypeEnum;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/8/19 16:09
 */
@Data
public class MerchantRegisterQueryRspBO {
    /**
     * 商户进件成功后，运营机构侧系统生成
     */
    private String merchantId;
    /**
     * 服务商号 运营机构侧服务商号，返回0或空表示无服务商
     */
    private String serviceId;
    /**
     * 客户编号。对于合作方类型为分行时必输，对于普通商户需上送对公客户编号，当为个人商户时，需上送入个人客户编号；当合作方来源为行外时可不输。
     */
    private String customerId;
    /**
     * 智能收款协议编号。当合作方来源为其他时必输，当合作方来源为分行时可不输
     */
    private String protocolId;
    /**
     * 合作方商户编号
     */
    private String outMerchantId;
    /**
     * 商户类型。1-普通商户；2-个人商户
     */
    private int merchantType;
    /**
     * 商户证件类型
     */
    private String merchantLicenseType;
    /**
     * 商户名称
     */
    private String merchantName;
    /**
     * 商户证件编号
     */
    private String merchantLicense;
    /**
     * 商户对外经营名称
     */
    private String merchantShowName;
    /**
     * 商户简称
     */
    private String merchantShortName;
    /**
     * 商户经营类目
     */
    private String merchantCategory;
    /**
     * 商户联系人电话
     */
    private String servicePhone;
    /**
     * 商户联系人
     */
    private String contactName;
    /**
     * 联系电话
     */
    private String contactPhone;
    /**
     * 联系邮箱
     */
    private String contactEmail;
    /**
     *
     */
    private String bussType;
    /**
     * 业务类型编
     */
    private String bussCode;
    /**
     * 备注
     */
    private String merchantRemark;
    /**
     * 统一入账标志 1-是，2-否
     * 当智能收款协议中的“是否支持统一入账”为开时，可送1-是，2-否；当
     * 智能收款协议中的“是否支持统一入账”为关时，只能送2-否。
     */
    private int unifyEntryFlag;
    /**
     * 结算账户/钱包类型。1-钱包；2-本行账户；3-他行账户；
     */
    private int accType;
    /**
     * 结算钱包id。当acc_type为钱包时必输，当acc_type为账户时无需输入
     */
    private String walletId;
    /**
     * 结算钱包名称。当acc_type为钱包时必输；当acc_type为账户时无需输入
     */
    private String walletName;
    /**
     * 结算账户名称。当acc_type为本行账户和他行账户时必输；当acc_type为钱包时时无需输入
     */
    private String accName;
    /**
     * 结算账户号。当acc_type为本行账户和他行账户时必输；当acc_type为钱包时时无需输入
     */
    private String accNo;
    /**
     * 开户行名称。填写支行名称，当acc_type为本行账户和他行账户时必输；当acc_type为钱包时时无需输入
     */
    private String accBankName;
    /**
     * 开户行行号。联行号，当acc_type为本行账户和他行账户时必输；当acc_type为钱包时时无需输入。
     */
    private String accBankCode;
    /**
     * 结算周期。 1-实时入账；2-T+1日入账（暂不支持）；3-指令入账
     */
    private int settleCycle;
    /**
     * 商户伞底钱包名称
     */
    private String umbrDownWalletName;
    /**
     * 商户伞底钱包
     */
    private String umbrDownWalletId;
}
