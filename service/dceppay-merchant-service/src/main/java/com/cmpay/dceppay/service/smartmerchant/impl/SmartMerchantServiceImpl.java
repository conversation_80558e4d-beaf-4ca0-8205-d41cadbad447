package com.cmpay.dceppay.service.smartmerchant.impl;

import com.cmpay.channel.data.Request;
import com.cmpay.channel.data.Response;
import com.cmpay.dceppay.bo.smartmerchant.*;
import com.cmpay.dceppay.client.DceppayCgwOutClient;
import com.cmpay.dceppay.dto.icbc.smartpay.*;
import com.cmpay.dceppay.service.smartmerchant.ISmartMerchantService;
import com.cmpay.dceppay.service.smartmerchant.ext.IMerchantCancelExtService;
import com.cmpay.dceppay.service.smartmerchant.ext.IMerchantExtService;
import com.cmpay.dceppay.service.smartmerchant.ext.IMerchantRegisterExtService;
import com.cmpay.dceppay.service.smartmerchant.ext.IMerchantRegisterQueryExtService;
import com.cmpay.framework.data.request.GenericDTO;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/8/19 16:41
 */
@Service
public class SmartMerchantServiceImpl implements ISmartMerchantService {
    @Autowired
    private DceppayCgwOutClient dceppayCgwOutClient;
    @Autowired
    private IMerchantRegisterExtService merchantRegisterExtService;
    @Autowired
    private IMerchantRegisterQueryExtService registerQueryExtService;
    @Autowired
    private IMerchantCancelExtService merchantCancelExtService;
    @Autowired
    private IMerchantExtService merchantExtService;

    @Override
    public MerchantRegisterRspBO register(MerchantRegisterReqBO registerReqBO) {
        MerchantRegisterCheckResultBO paramCheckFlag = merchantExtService.checkPram(registerReqBO);
        if (!paramCheckFlag.isCheckFlag()) {
            BusinessException.throwBusinessException(paramCheckFlag.getMsgCd());
        }
        // 根据商户号merchantId查询服务商进件信息表icbc_merchant，数据不存在，报错退出；
        ICBCMerchantBO icbcMerchantBO = merchantExtService.checkAndGetICBCMerchantInfo(registerReqBO.getOutMerchantId());
        //进件商户数据存在，更新进件状态为“进件中”；
        merchantExtService.updateOnboardStatus(icbcMerchantBO, registerReqBO.getOperatorId());
        //封装网关请求参数对象
        MerchantRegisterRequest registerRequest = merchantRegisterExtService.getMerchantRegisterRequest(registerReqBO);

        Request request = merchantRegisterExtService.getRequest(registerRequest);
        //请求网关
        GenericRspDTO<Response> genericRspDTO = dceppayCgwOutClient.request(GenericDTO.newChildInstanceWithBody(GenericDTO.class, request));
        //处理网关响应对象
        if (JudgeUtils.isNotSuccess(genericRspDTO)) {
            BusinessException.throwBusinessException(genericRspDTO);
        }
        MerchantRegisterResponse registerResponse = Optional.of(genericRspDTO)
                .map(GenericRspDTO::getBody)
                .map(Response::getResult).map(x -> (MerchantRegisterResponse) x)
                .orElse(new MerchantRegisterResponse());
        icbcMerchantBO.setOperatorId(registerReqBO.getOperatorId());
        MerchantRegisterRspBO registerRspBO = merchantRegisterExtService.handleResponse(registerResponse, icbcMerchantBO);
        //返回
        return registerRspBO;
    }


    @Override
    public MerchantRegisterQueryRspBO registerQuery(MerchantRegisterQueryReqBO registerQueryReqBO) {
        // 根据商户号merchantId查询服务商进件信息表icbc_merchant，数据不存在，报错退出；
        ICBCMerchantBO icbcMerchantBO = merchantExtService.checkAndGetICBCMerchantInfo(registerQueryReqBO.getMerchantId());
        //封装网关请求参数对象
        MerchantRegisterQueryRequest registerQueryRequest = new MerchantRegisterQueryRequest();
        registerQueryRequest.setMerchantId(icbcMerchantBO.getOperatorMerchantId());
        Request request = registerQueryExtService.getRequest(registerQueryRequest);
        //请求网关
        GenericRspDTO<Response> genericRspDTO = dceppayCgwOutClient.request(GenericDTO.newChildInstanceWithBody(GenericDTO.class, request));
        //处理网关响应对象
        if (JudgeUtils.isNotSuccess(genericRspDTO)) {
            BusinessException.throwBusinessException(genericRspDTO);
        }
        MerchantRegisterQueryResponse registerQueryResponse = Optional.of(genericRspDTO)
                .map(GenericRspDTO::getBody)
                .map(Response::getResult).map(x -> (MerchantRegisterQueryResponse) x)
                .orElse(new MerchantRegisterQueryResponse());
        MerchantRegisterQueryRspBO registerRspBO = registerQueryExtService.handleResponse(registerQueryResponse);
        //返回
        return registerRspBO;
    }

    @Override
    public MerchantCancelRspBO cancel(MerchantCancelReqBO cancelReqBO) {
        ICBCMerchantBO merchantBO = merchantExtService.checkAndGetICBCMerchantInfo(cancelReqBO.getMerchantId());
        //进件商户数据存在，更新进件状态为“进件中”；
        merchantExtService.updateOnboardCanceling(merchantBO, cancelReqBO.getOperationId());
        //封装网关请求参数对象
        MerchantCancelRequest merchantCancelRequest = new MerchantCancelRequest();
        merchantCancelRequest.setMerchantId(merchantBO.getOperatorMerchantId());
        Request request = merchantCancelExtService.getRequest(merchantCancelRequest);
        //请求网关
        GenericRspDTO<Response> genericRspDTO = dceppayCgwOutClient.request(GenericDTO.newChildInstanceWithBody(GenericDTO.class, request));
        //处理网关响应对象
        if (JudgeUtils.isNotSuccess(genericRspDTO)) {
            BusinessException.throwBusinessException(genericRspDTO);
        }
        MerchantCancelResponse merchantCancelResponse = Optional.of(genericRspDTO)
                .map(GenericRspDTO::getBody)
                .map(Response::getResult).map(x -> (MerchantCancelResponse) x)
                .orElse(new MerchantCancelResponse());
        return merchantCancelExtService.handleResponse(merchantCancelResponse, merchantBO, cancelReqBO.getOperationId());

    }
}
