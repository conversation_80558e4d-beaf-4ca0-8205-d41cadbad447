package com.cmpay.dceppay.service.smartmerchant.ext.impl;

import com.cmpay.dceppay.bo.smartmerchant.ICBCMerchantBO;
import com.cmpay.dceppay.bo.smartmerchant.ICBCMerchantUpdateStatusBO;
import com.cmpay.dceppay.bo.smartmerchant.MerchantRegisterCheckResultBO;
import com.cmpay.dceppay.bo.smartmerchant.MerchantRegisterReqBO;
import com.cmpay.dceppay.constant.icbc.ICBCConstants;
import com.cmpay.dceppay.enums.MsgCodeEnum;
import com.cmpay.dceppay.enums.icbc.WalletTypeEnum;
import com.cmpay.dceppay.service.smartmerchant.IICBCMerchantDBService;
import com.cmpay.dceppay.service.smartmerchant.ext.IMerchantExtService;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/8/29 11:18
 */
@Service
public class IMerchantExtServiceImpl implements IMerchantExtService {
    @Autowired
    private IICBCMerchantDBService merchantDBService;

    @Override
    public ICBCMerchantBO checkAndGetICBCMerchantInfo(String outMerchantId) {
        ICBCMerchantBO merchantBO = merchantDBService.getMerchantByMerchantID(outMerchantId);
        if (JudgeUtils.isNull(merchantBO)) {
            BusinessException.throwBusinessException(MsgCodeEnum.MERCHANT_INFO_NOT_EXIST);
        }
        return merchantBO;
    }

    @Override
    public void updateOnboardStatus(ICBCMerchantBO icbcMerchantBO, String operatorId) {
        ICBCMerchantUpdateStatusBO updateStatusBO = new ICBCMerchantUpdateStatusBO();
        updateStatusBO.setMerchantId(icbcMerchantBO.getMerchantId());
        updateStatusBO.setUpdateId(operatorId);
        // 更新状态进件中
        int updateNumber = merchantDBService.updateOnboarding(updateStatusBO);
        if (updateNumber != 1) {
            BusinessException.throwBusinessException(MsgCodeEnum.UPDATE_MERCHANT_STATUS_ERROR);
        }
    }

    @Override
    public void updateOnboardCanceling(ICBCMerchantBO merchantBO, String operatorId) {
        ICBCMerchantUpdateStatusBO updateStatusBO = new ICBCMerchantUpdateStatusBO();
        updateStatusBO.setMerchantId(merchantBO.getMerchantId());
        updateStatusBO.setUpdateId(operatorId);
        // 更新状态进件中
        int updateNumber = merchantDBService.updateOnboardCanceling(updateStatusBO);
        if (updateNumber != 1) {
            BusinessException.throwBusinessException(MsgCodeEnum.UPDATE_MERCHANT_STATUS_ERROR);
        }
    }

    @Override
    public MerchantRegisterCheckResultBO checkPram(MerchantRegisterReqBO registerReqBO) {
        MerchantRegisterCheckResultBO checkResultBO = new MerchantRegisterCheckResultBO();
        //  当是否服务商商户为1-是时,运营机构服务商编号servicePproviderId必填；
        if (registerReqBO.getIsServiceMerchant() == ICBCConstants.IS_SERVICE_MERCHANT && JudgeUtils.isBlank(registerReqBO.getServiceId())) {
            return createFailureResult(MsgCodeEnum.SERVICE_ID_IS_NULL, checkResultBO);
        }
        //  账户类型：本行账户和他行账户；结算账户名称，银行账号，开户银行(含支行)，开户行联行号必输；
        if (registerReqBO.getAccType() == WalletTypeEnum.SAME_BANK_ACCOUNT || registerReqBO.getAccType() == WalletTypeEnum.DIFFERENT_BANK_ACCOUNT) {
            if (JudgeUtils.isBlank(registerReqBO.getAccNo())) {
                return createFailureResult(MsgCodeEnum.ACC_NO_NOT_VALID, checkResultBO);
            }
            if (JudgeUtils.isBlank(registerReqBO.getAccName())) {
                return createFailureResult(MsgCodeEnum.ACC_NAME_NOT_VALID, checkResultBO);
            }
            if (JudgeUtils.isBlank(registerReqBO.getAccBankCode())) {
                return createFailureResult(MsgCodeEnum.ACC_BANK_CODE_NOT_VALID, checkResultBO);

            }
            if (JudgeUtils.isBlank(registerReqBO.getAccBankName())) {
                return createFailureResult(MsgCodeEnum.ACC_BANK_NAME_NOT_VALID, checkResultBO);

            }

        }
        if (registerReqBO.getAccType() == WalletTypeEnum.WALLET) {
            if (JudgeUtils.isBlank(registerReqBO.getWalletId())) {
                return createFailureResult(MsgCodeEnum.WALLET_ID_NOT_VALID, checkResultBO);
            }
            if (JudgeUtils.isBlank(registerReqBO.getWalletName())) {
                return createFailureResult(MsgCodeEnum.WALLET_NAME_NOT_VALID, checkResultBO);
            }
        }
        checkResultBO.setCheckFlag(true);
        return checkResultBO;
    }

    private MerchantRegisterCheckResultBO createFailureResult(MsgCodeEnum msgCode, MerchantRegisterCheckResultBO checkResultBO) {
        checkResultBO.setCheckFlag(false);
        checkResultBO.setMsgCd(msgCode.getMsgCd());
        return checkResultBO;
    }
}
