package com.cmpay.dceppay.service.smartmerchant;

import com.cmpay.dceppay.bo.smartmerchant.*;

/**
 * <AUTHOR>
 * @date 2024/8/19 16:41
 * 工行智能商户接口
 */
public interface ISmartMerchantService {


    /**
     * 工行智能收款商户进件
     * @param registerReqBO
     * @return MerchantRegisterRspBO
     */
    MerchantRegisterRspBO register(MerchantRegisterReqBO registerReqBO);

    /**
     * 工行智能收款商户进件查询
     * @param registerQueryReqBO
     * @return MerchantRegisterQueryRspBO
     */

    MerchantRegisterQueryRspBO registerQuery(MerchantRegisterQueryReqBO registerQueryReqBO);

    /**
     * 工行智能收款商户作废
     * @param cancelReqBO
     * @return MerchantCancelRspBO
     */

    MerchantCancelRspBO cancel(MerchantCancelReqBO cancelReqBO);
}
