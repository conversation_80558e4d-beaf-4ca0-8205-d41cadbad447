package com.cmpay.dceppay.service.check.reader;

import com.cmpay.dceppay.bo.CheckFileBO;
import com.cmpay.dceppay.constant.check.CheckFileConstant;
import com.cmpay.dceppay.enums.MsgCodeEnum;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.univocity.parsers.csv.CsvParser;
import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.util.ArrayList;
import java.util.List;

/**
 * Created on 2019/4/5
 * 解析文本数据模板类
 *
 * @author: sun_zhh
 */
@Slf4j
public abstract class AbstractDataReaderComponent<T> implements IDataReader, IUnivocity, IBaseHandler<T> {
    @Override
    public void readData(CheckFileBO fileBO) {
        // 创建CSV解析器（将分隔符传入对象）
        CsvParser parser = new CsvParser(parseSetting());

        List insertCheckDetail = new ArrayList<>();
        try (Reader reader = getReader(dataPath)) {
            // 调用beginParsing逐个读取记录，使用迭代器iterator
            parser.beginParsing(reader);
            String[] row;
            Long i = 0L;
            while ((row = parser.parseNext()) != null) {
                if (JudgeUtils.notEquals(CheckFileConstant.LAST_LINE_END, row[0])) {
                    readParse(row, i++, insertCheckDetail,fileBO);
                }
            }
            insertCheckDetail(insertCheckDetail);
        } catch (Exception e) {
            log.error("解析该地址对账文件失败:{},错误信息:{}", dataPath, e.getCause());
            BusinessException.throwBusinessException(MsgCodeEnum.PARSE_CHECK_DETAIL_FILE_ERROR);
        } finally {
            // 在读取结束时自动关闭所有资源
            parser.stopParsing();
        }
    }

    protected InputStreamReader getReader(String path) throws FileNotFoundException {
        try {
            FileInputStream fis = new FileInputStream(path);
            return new InputStreamReader(fis, charset);
        } catch (UnsupportedEncodingException e) {
            throw new IllegalStateException("Unable to read input", e);
        }
    }

    @Override
    public IBaseHandler setNextHandler(IBaseHandler baseHandler) {
        return nextHandler = baseHandler;
    }

    @Override
    public IBaseHandler getNextHandler() {
        return nextHandler;
    }

    /**
     * 子类实现入库方法
     *
     * @param list
     */
    public abstract void insertCheckDetail(List list);

    protected String charset = "UTF-8";
    protected String dataPath;
    protected IBaseHandler nextHandler;

}
