package com.cmpay.dceppay.timer.check;

import com.cmpay.dceppay.service.check.IDcepCheckService;
import com.cmpay.dceppay.util.DateTimeUtil;
import com.cmpay.lemon.framework.schedule.batch.BatchScheduled;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParametersInvalidException;
import org.springframework.batch.core.repository.JobExecutionAlreadyRunningException;
import org.springframework.batch.core.repository.JobInstanceAlreadyCompleteException;
import org.springframework.batch.core.repository.JobRestartException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/9/24 11:29
 */
@Component
@Slf4j
public class CheckTimer {
    @Autowired
    private IDcepCheckService checkService;


    @BatchScheduled(cron = "0 0/15 1-23 * * ?")
    public void execute() throws JobInstanceAlreadyCompleteException, JobExecutionAlreadyRunningException, JobParametersInvalidException, JobRestartException {
        String checkDate = DateTimeUtil.getCheckDate();
        checkService.check(checkDate);
    }
}
