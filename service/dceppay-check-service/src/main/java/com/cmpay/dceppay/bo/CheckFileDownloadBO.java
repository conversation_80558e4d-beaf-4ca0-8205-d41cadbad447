package com.cmpay.dceppay.bo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/23 12:19
 * 对账文件下载BO
 */
@Data
public class CheckFileDownloadBO {
    private String checkFileDate;
    private String institutionCode;
    //总笔数
    private Integer totalCount;
    // 总金额
    private BigDecimal totalAmount;
    //接收通知的文件列表名
    private String fileNameList;
    //   filePath 格式说明：ip:端口/路径/，例如：127.0.0.1:22/运营机构编码/对账日期（YYYYMMDD）
    private String downloadPath;
    private boolean downloadFlag;
    //自定义默认需要下载的文件名（匹配）
    private String fileNamePatter;
    //数字信封
    private String digitalEnv;

}
