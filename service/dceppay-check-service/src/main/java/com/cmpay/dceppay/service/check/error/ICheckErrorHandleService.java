package com.cmpay.dceppay.service.check.error;

import com.cmpay.dceppay.bo.CheckCancelRefundBO;
import com.cmpay.dceppay.bo.CheckErrorAmountUpdateBO;
import com.cmpay.dceppay.bo.CheckErrorCancelBO;

/**
 * <AUTHOR>
 * @date 2024/9/29 10:51
 * 差错处理Service
 */
public interface ICheckErrorHandleService {
    /**
     * 差错取消
     *
     * @param errorCancelBO
     */
    void cancelError(CheckErrorCancelBO errorCancelBO);

    /**
     * 修改金额
     * @param amountUpdateBO
     */
    void updateCheckAmount(CheckErrorAmountUpdateBO amountUpdateBO);

    void cancelRefund(CheckCancelRefundBO checkErrorBO);
}
