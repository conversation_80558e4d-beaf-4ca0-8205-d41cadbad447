package com.cmpay.dceppay.service.check.reconciliation.impl;

import com.cmpay.dceppay.service.check.reconciliation.IReconciliationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.*;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.batch.core.repository.JobExecutionAlreadyRunningException;
import org.springframework.batch.core.repository.JobInstanceAlreadyCompleteException;
import org.springframework.batch.core.repository.JobRestartException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.task.SimpleAsyncTaskExecutor;
import org.springframework.stereotype.Service;
import com.cmpay.dceppay.batch.check.GridPartitioner;

/**
 * <AUTHOR>
 * @date 2024/9/24 14:43
 */
@Service
@Slf4j
public class IReconciliationServiceImpl implements IReconciliationService {
    @Autowired
    @Qualifier("jobBeanLauncher")
    JobLauncher jobBeanLauncher;
    @Autowired
    JobBuilderFactory jobBuilderFactory;
    @Autowired
    StepBuilderFactory stepBuilderFactory;

    @Autowired
    @Qualifier("partitionerStepReconciliation")
    Step partitionerStep;
    @Autowired
    @Qualifier("jobListenerReconciliation")
    JobExecutionListener jobListener;

    @Override
    public void start(int gridSize, String checkDate, String institutionCode) throws JobInstanceAlreadyCompleteException, JobExecutionAlreadyRunningException, JobParametersInvalidException, JobRestartException {
        Job partitionerJob = jobBuilderFactory.get("partitionerJobReconciliation")
                .incrementer(new RunIdIncrementer())
                .start(stepBuilderFactory.get("partitionerStepReconciliation")
                        .partitioner("rangePartitionerReconciliation", new GridPartitioner(gridSize, checkDate, institutionCode))
                        .step(partitionerStep)
                        .gridSize(gridSize)
                        .taskExecutor(new SimpleAsyncTaskExecutor())
                        .build()).listener(jobListener)
                .build();
        JobParameters jobParameters = new JobParametersBuilder()
                .addLong("time", System.currentTimeMillis())
                .addString("checkDate", checkDate)
                .addString("institutionCode", institutionCode)
                .toJobParameters();
        jobBeanLauncher.run(partitionerJob, jobParameters);
    }
}
