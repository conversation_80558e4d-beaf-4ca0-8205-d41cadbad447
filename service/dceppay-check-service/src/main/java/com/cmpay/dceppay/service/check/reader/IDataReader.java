package com.cmpay.dceppay.service.check.reader;

import com.cmpay.dceppay.bo.CheckFileBO;

import java.io.FileNotFoundException;
import java.lang.reflect.InvocationTargetException;
import java.util.List;

/**
 * Created on 2019/4/5
 *  文本数据解析接口定义
 * @author: sun_zhh
 */
public interface IDataReader<T> {
    void readData(CheckFileBO fileBO) throws FileNotFoundException, IllegalAccessException, NoSuchMethodException, InvocationTargetException;


    void readParse(String[]  data, Long rowIndex, List insertCheckDetail,CheckFileBO fileBO) throws IllegalAccessException, NoSuchMethodException, InvocationTargetException;

}
