package com.cmpay.dceppay.service.check.reader;

import com.cmpay.dceppay.bo.pay.PaySettlementBO;
import org.springframework.batch.item.ItemReader;
import org.springframework.batch.item.ParseException;
import org.springframework.batch.item.UnexpectedInputException;

import java.util.List;

/**
 * 结算表数据读取
 */
public class SettlementItemReader implements ItemReader<PaySettlementBO> {
    private final List<PaySettlementBO> items;
    private int currentIndex = 0;

    public SettlementItemReader(List<PaySettlementBO> items) {
        this.items = items;
    }

    @Override
    public PaySettlementBO read() throws Exception, UnexpectedInputException, ParseException {
        if (currentIndex < items.size()) {
            return items.get(currentIndex++);
        } else {
            return null; // 表示没有更多的数据
        }
    }
}