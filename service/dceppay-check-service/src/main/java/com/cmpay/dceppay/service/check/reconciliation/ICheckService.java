package com.cmpay.dceppay.service.check.reconciliation;

import com.cmpay.dceppay.bo.check.CheckFileDetailBO;

/**
 * <AUTHOR>
 * @date 2024/9/23 11:48
 */
public interface ICheckService {
    /**
     * 是否开始对账
     *
     * @param checkDate
     * @return
     */
    boolean getCheckFlag(String checkDate,String  institutionCode);

    /**
     * 对账
     * @param item 对账明细
     * @param checkDate 对账日期
     */
    void check(CheckFileDetailBO item, String checkDate);

    /**
     * 对账是否完成
     *
     * @param checkDate
     * @return
     */
    boolean checkFinishFlag(String checkDate,String  institutionCode);
}
