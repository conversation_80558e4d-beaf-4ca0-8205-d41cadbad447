package com.cmpay.dceppay.timer.check;

import com.cmpay.dceppay.enums.pay.ChannelCodeEnum;
import com.cmpay.dceppay.service.check.writer.IWriterService;
import com.cmpay.dceppay.util.DateTimeUtil;
import com.cmpay.lemon.framework.schedule.batch.BatchScheduled;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/9/18 8:55
 * 对账文件生成
 */
@Service
@Slf4j
public class CheckFileWriterTimer {
    @Autowired
    private IWriterService writerService;


    @BatchScheduled(cron = "0 0/30 1-23 * * ?")
    public void executeGenFile() {
        String accountDate = DateTimeUtil.getCheckDate();
        String channelCode = ChannelCodeEnum.PAY_CENTER.getChannelCode();
        writerService.genCheckFile(accountDate,channelCode,false);
    }
}
