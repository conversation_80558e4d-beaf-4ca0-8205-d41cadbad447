package com.cmpay.dceppay.service.check.download.impl;

import com.cmpay.channel.data.Request;
import com.cmpay.channel.data.Response;
import com.cmpay.dceppay.bo.CheckFileDownloadBO;
import com.cmpay.dceppay.bo.check.CheckFileProcessBO;
import com.cmpay.dceppay.bo.check.CheckFileSummaryBO;
import com.cmpay.dceppay.channel.DcepChannelEnum;
import com.cmpay.dceppay.client.DceppayCgwOutClient;
import com.cmpay.dceppay.constant.check.CheckFileConstant;
import com.cmpay.dceppay.constant.check.SftpConstant;
import com.cmpay.dceppay.dto.dcep.billDownload.BillDownLoadRequest;
import com.cmpay.dceppay.dto.dcep.billDownload.BillDownLoadResponse;
import com.cmpay.dceppay.enums.MsgCodeEnum;
import com.cmpay.dceppay.enums.check.CheckFileProcessEnums;
import com.cmpay.dceppay.properties.DcepBilUpSftpProperties;
import com.cmpay.dceppay.properties.DcepBillDownSftpProperties;
import com.cmpay.dceppay.service.check.ICheckBillSummaryDBService;
import com.cmpay.dceppay.service.check.ICheckFileProcessDBService;
import com.cmpay.dceppay.service.check.download.ICheckFileDownloadService;
import com.cmpay.dceppay.utils.ExceptionHandlerUtil;
import com.cmpay.framework.data.request.GenericDTO;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.file.IFileSystem;
import com.cmpay.lemon.file.entity.SFTPFileEnv;
import com.cmpay.lemon.framework.utils.LemonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/9/23 15:11
 */
@Slf4j
@Service
public class CheckFileDownloadServiceImpl implements ICheckFileDownloadService {
    @Autowired
    private DceppayCgwOutClient dceppayCgwOutClient;
    @Autowired
    private DcepBillDownSftpProperties billDownSftpProperties;
    @Autowired
    private DcepBilUpSftpProperties billUpSftpProperties;
    @Autowired
    private IFileSystem fileSystem;
    @Autowired
    private ICheckFileProcessDBService checkFileProcessDBService;
    @Autowired
    private ICheckBillSummaryDBService checkBillSummaryDBService;


    @Override
    public CheckFileDownloadBO getDownloadFlag(String accountDate, String institutionCode) {
        //查看账单汇总表数据是否存在
        CheckFileSummaryBO checkFileSummaryBO = checkBillSummaryDBService.getByKey(accountDate, institutionCode);
        CheckFileDownloadBO checkFileDownloadBO = new CheckFileDownloadBO();
        checkFileDownloadBO.setDownloadFlag(false);
        if (JudgeUtils.isNotNull(checkFileSummaryBO)) {
            CheckFileProcessBO checkFileProcessBO = checkFileProcessDBService.getProcessBO(accountDate, institutionCode);
            if (JudgeUtils.isNotNull(checkFileProcessBO)
                    && !JudgeUtils.equalsAny(
                    checkFileProcessBO.getCheckStatus(),
                    CheckFileProcessEnums.IMPORTED.name(),
                    CheckFileProcessEnums.CHECKING.name(),
                    CheckFileProcessEnums.CHECK_FINISH.name())
            ) {
                checkFileDownloadBO.setDownloadFlag(true);
                checkFileDownloadBO.setCheckFileDate(accountDate);
                checkFileDownloadBO.setInstitutionCode(checkFileSummaryBO.getInstitutionCode());
                checkFileDownloadBO.setTotalCount(checkFileSummaryBO.getTotalCount());
                checkFileDownloadBO.setTotalAmount(checkFileSummaryBO.getTotalAmount());
                checkFileDownloadBO.setDownloadPath(checkFileSummaryBO.getFilePath());
                checkFileDownloadBO.setFileNameList(checkFileSummaryBO.getFileNameList());
                checkFileDownloadBO.setDigitalEnv(checkFileSummaryBO.getDigitalEnv());
            }
        }
        return checkFileDownloadBO;
    }

    @Override
    public List<String> download(CheckFileDownloadBO downloadBO) {
        checkFileProcessDBService.updateCheckFileDownloadBegin(downloadBO.getCheckFileDate(), downloadBO.getInstitutionCode());
        List<String> detailFileList = downLoadCheckFile(downloadBO);
        checkFileProcessDBService.updateCheckFileDownloadEnd(downloadBO.getCheckFileDate(), downloadBO.getInstitutionCode());
        return detailFileList;
    }

    @Override
    public List<String> downLoadCheckFile(CheckFileDownloadBO downloadBO) {
        // 封装网关请求参数对象
        BillDownLoadRequest downLoadRequest = buildDownloadRequestParam(downloadBO);
        Request request = bulidDownlodRequest(downLoadRequest);
        BillDownLoadResponse billDownLoadResponse;
        List<String> detailFileList = null;
        try {
            // 请求网关
            GenericRspDTO<Response> genericRspDTO = dceppayCgwOutClient.request(GenericDTO.newChildInstanceWithBody(GenericDTO.class, request));
            // 处理网关响应对象
            if (JudgeUtils.isNotSuccess(genericRspDTO)) {
                BusinessException.throwBusinessException(MsgCodeEnum.TRANSACTION_REQUEST_FAIL);
            }
            billDownLoadResponse = Optional.ofNullable(genericRspDTO)
                    .map(GenericRspDTO::getBody)
                    .map(Response::getResult)
                    .map(x -> (BillDownLoadResponse) x)
                    .orElse(new BillDownLoadResponse());
            String responseStatus = billDownLoadResponse.getResponseStatus();
            String responseMsg = billDownLoadResponse.getResponseMessage();
            if (JudgeUtils.equals(CheckFileConstant.FAIL, responseStatus)) {
                log.error("下载互联互通对账文件失败：{}",responseMsg);
                BusinessException.throwBusinessException(MsgCodeEnum.CHECK_FILE_CGW_DOWNLOAD_FAILED);
            }
            detailFileList = billDownLoadResponse.getDetailFileNameList();
        } catch (Exception e) {
            ExceptionHandlerUtil.throwRequestDcepError(e);
        }
        if (!downloadFileList(detailFileList, downloadBO.getCheckFileDate())) {
            BusinessException.throwBusinessException(MsgCodeEnum.CHECK_FILE_DOWNLOAD_FAILED);
        }
        return detailFileList;
    }

    private boolean downloadFileList(List<String> fileList, String date) {
        try {
            String localPath = billUpSftpProperties.getLocalDataPath() + date + File.separator;
            File localDir = new File(localPath);
            if (!localDir.exists()) {
                localDir.mkdirs();
            }
            String remotePath = billUpSftpProperties.getFileRemotePath() + date + File.separator;
            if (JudgeUtils.isNotBlank(billUpSftpProperties.getSftpUserPasswd())) {
                SFTPFileEnv billFile = new SFTPFileEnv(
                        billUpSftpProperties.getSftpUrl(), billUpSftpProperties.getSftpUsername(),
                        billUpSftpProperties.getSftpUserPasswd(), remotePath, localPath, fileList,
                        SftpConstant.SFTP_TIME_OUT,
                        SftpConstant.SFTP_ENCODE, SftpConstant.DOWNLOAD_TYPE);
                return fileSystem.process(billFile);
            } else {
                SFTPFileEnv billFile = new SFTPFileEnv(
                        billUpSftpProperties.getSftpUrl(), billUpSftpProperties.getSftpUsername(), remotePath,
                        localPath, fileList, SftpConstant.SFTP_TIME_OUT,
                        SftpConstant.SFTP_ENCODE, SftpConstant.DOWNLOAD_TYPE, billUpSftpProperties.getIdentityKey(), null);
                return fileSystem.process(billFile);
            }
        } catch (Exception e) {
            log.error("从文件服务器下载对账文件失败：{},{}", fileList, e.getCause());
            return false;
        }
    }

    private Request bulidDownlodRequest(BillDownLoadRequest downLoadRequest) {
        Request request = new Request();
        request.setRequestId(LemonUtils.getRequestId());
        request.setRoute(DcepChannelEnum.DCEP_PAY);
        request.setBusiType(DcepChannelEnum.BILL_DOWNLOAD.getName());
        request.setSource(DcepChannelEnum.DCEP_PAY);
        request.setTarget(downLoadRequest);
        return request;
    }

    private BillDownLoadRequest buildDownloadRequestParam(CheckFileDownloadBO downloadBO) {
        BillDownLoadRequest billDownLoadRequest = new BillDownLoadRequest();
        billDownLoadRequest.setCheckFileDate(downloadBO.getCheckFileDate());
        //filePath 格式说明：ip:端口/路径/，例如：127.0.0.1:22/运营机构编码/对账日期（YYYYMMDD）
        billDownLoadRequest.setDownloadPath(downloadBO.getDownloadPath());
        billDownLoadRequest.setDownloadUserName(billDownSftpProperties.getSftpUsername());
        billDownLoadRequest.setLocalPath(billDownSftpProperties.getLocalDataPath());
        billDownLoadRequest.setDcepIdentityKey(billDownSftpProperties.getDcepIdentityKey());
        billDownLoadRequest.setFileNameList(downloadBO.getFileNameList());
        billDownLoadRequest.setUploadUrl(billUpSftpProperties.getSftpUrl());
        billDownLoadRequest.setUploadUserName(billUpSftpProperties.getSftpUsername());
        billDownLoadRequest.setUploadPath(billUpSftpProperties.getFileRemotePath());
        billDownLoadRequest.setDigitalEev(downloadBO.getDigitalEnv());
        billDownLoadRequest.setPasswd(billUpSftpProperties.getSftpUserPasswd());
        return billDownLoadRequest;
    }


}
