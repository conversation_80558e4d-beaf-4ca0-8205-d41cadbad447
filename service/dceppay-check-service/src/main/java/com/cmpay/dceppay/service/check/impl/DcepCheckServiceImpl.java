package com.cmpay.dceppay.service.check.impl;

import com.cmpay.dceppay.bo.CheckFileDownloadBO;
import com.cmpay.dceppay.config.DceppayConfig;
import com.cmpay.dceppay.constant.check.CheckFileConstant;
import com.cmpay.dceppay.service.check.IDcepCheckService;
import com.cmpay.dceppay.service.check.download.ICheckFileDownloadService;
import com.cmpay.dceppay.service.check.download.ICheckFileImportService;
import com.cmpay.dceppay.service.check.download.ICheckFileVerifyService;
import com.cmpay.dceppay.service.check.reconciliation.IReconciliationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParametersInvalidException;
import org.springframework.batch.core.repository.JobExecutionAlreadyRunningException;
import org.springframework.batch.core.repository.JobInstanceAlreadyCompleteException;
import org.springframework.batch.core.repository.JobRestartException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/5 22:33
 */
@Service
@Slf4j
public class DcepCheckServiceImpl implements IDcepCheckService {
    @Autowired
    private ICheckFileImportService checkFileImportService;
    @Autowired
    private ICheckFileDownloadService checkFileDownloadService;
    @Autowired
    private ICheckFileVerifyService checkFileVerifyService;
    @Autowired
    private DceppayConfig dceppayConfig;
    @Autowired
    private com.cmpay.dceppay.service.check.reconciliation.ICheckService checkService;
    @Autowired
    private IReconciliationService reconciliationService;

    @Override
    public void download(String checkDate) {
        //受理服务机构的对账明细文件由收款运营机构生成（每日生成） 收款运营机构编码
        String institutionCode = dceppayConfig.getCreditorCode();
        CheckFileDownloadBO downloadBO = checkFileDownloadService.getDownloadFlag(checkDate, institutionCode);
        if (!downloadBO.isDownloadFlag()) {
            log.info("互联互通对账文件下载暂未开始或已完成");
            return;
        }
        List<String> detailFileList;
        try {
            detailFileList = checkFileDownloadService.download(downloadBO);
        } catch (Exception e) {
            log.error("互联互通对账文件下载异常：{}", e.getCause());
            return;
        }
        boolean verifyCheckFile = checkFileVerifyService.verifyCheckFile(downloadBO, detailFileList);
        if (verifyCheckFile) {
            try {
                checkFileImportService.importToDB(downloadBO, detailFileList);
            } catch (Exception e) {
                log.error("互联互通对账文件入库异常：{}", e.getCause());
            }
        }
    }

    @Override
    public void check(String checkDate) throws JobInstanceAlreadyCompleteException, JobExecutionAlreadyRunningException, JobParametersInvalidException, JobRestartException {
        String institutionCode = dceppayConfig.getCreditorCode();
        boolean startFlag = checkService.getCheckFlag(checkDate, institutionCode);
        if (!startFlag) {
            log.info("对账文件入库未完成或已完成对账，本次对账退出====");
            return;
        }
        reconciliationService.start(CheckFileConstant.CHECK_GRID_SIZE, checkDate, institutionCode);

    }
}
