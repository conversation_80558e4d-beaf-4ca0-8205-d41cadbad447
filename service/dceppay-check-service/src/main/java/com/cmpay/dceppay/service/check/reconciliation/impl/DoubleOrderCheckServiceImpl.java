package com.cmpay.dceppay.service.check.reconciliation.impl;

import com.cmpay.dceppay.bo.check.CheckErrorBO;
import com.cmpay.dceppay.bo.check.CheckFileDetailBO;
import com.cmpay.dceppay.bo.pay.PaySettlementBO;
import com.cmpay.dceppay.constant.check.CheckFileConstant;
import com.cmpay.dceppay.enums.check.CheckHandleEnum;
import com.cmpay.dceppay.enums.check.CheckStatusEnum;
import com.cmpay.dceppay.enums.check.ErrorTypeEnum;
import com.cmpay.dceppay.enums.pay.OrderTypeEnum;
import com.cmpay.dceppay.service.check.ICheckErrorService;
import com.cmpay.dceppay.service.check.ICheckFileDetailService;
import com.cmpay.dceppay.service.check.reconciliation.IDoubleOrderCheckService;
import com.cmpay.dceppay.service.pay.IPaySettlementDBService;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/24 16:29
 */
@Service
@Slf4j
public class DoubleOrderCheckServiceImpl implements IDoubleOrderCheckService {
    @Autowired
    private ICheckErrorService checkErrorService;
    @Autowired
    private IPaySettlementDBService settlementDBService;
    @Autowired
    private ICheckFileDetailService checkFileDetailService;

    @Override
    public void doubleComplete(String checkDate) {
        //d+2日处理d日订单
        String lastCheckDate = DateTimeUtils.formatLocalDate(DateTimeUtils.parseLocalDate(checkDate).minusDays(1));
        List<CheckErrorBO> errorDoubleList = checkErrorService.findDoubleList(lastCheckDate);
        if (JudgeUtils.isNull(errorDoubleList)) {
            log.info("存疑数据不存在，对账日期:{}", lastCheckDate);
            return;
        }
        for (CheckErrorBO checkErrorBO : errorDoubleList) {
            PaySettlementBO settlementBO = new PaySettlementBO();
            String tradeType = checkErrorBO.getTradeType();
            settlementBO.setOutOrderNo(checkErrorBO.getOutOrderNo());
            settlementBO.setRefundOrderNo(checkErrorBO.getRefundOrderNo());
            settlementBO.setSettlementDate(lastCheckDate);
            settlementBO.setSettlementType(tradeType);
            if (JudgeUtils.equals(tradeType, OrderTypeEnum.PAYMENT.name())) {
                PaySettlementBO paySettlementBO = settlementDBService.getPaymentShortDoubt(settlementBO);
                if (JudgeUtils.isNotNull(paySettlementBO)&&JudgeUtils.equals(CheckStatusEnum.COMPLETE.name(),paySettlementBO.getCheckStatus())) {
                    checkErrorBO.setCheckAmount(paySettlementBO.getOrderAmount());
                    checkErrorBO.setErrorHandleType(CheckHandleEnum.DEAL_SUCCESS.name());
                    checkErrorBO.setErrorStatus(CheckStatusEnum.DOUBT_COMPLETE.name());
                    checkErrorBO.setTradeAmount(settlementBO.getOrderAmount());
                    checkErrorService.updateDoubleComplete(checkErrorBO);
                } else {
                   //查询对账文件表的数据
                    CheckFileDetailBO checkFileDetailBO=new CheckFileDetailBO();
                    checkFileDetailBO.setCheckFileDate(checkDate);
                    checkFileDetailBO.setOutOrderNo(checkErrorBO.getOutOrderNo());
                    checkFileDetailBO.setRefundOrderNo(checkErrorBO.getRefundOrderNo());
                    checkFileDetailBO.setCheckType(tradeType);
                    CheckFileDetailBO checkFileDetailDBBO=checkFileDetailService.getPaymentShortDoubt(checkFileDetailBO);
                    if (JudgeUtils.isNull(checkFileDetailDBBO)){
                        checkErrorBO.setErrorType(ErrorTypeEnum.PAYMENT_SHORT.name());
                        checkErrorBO.setDoubtFlag(CheckFileConstant.DOUBT_FLAG_N);
                        checkErrorService.updatePaymentShort(checkErrorBO);
                        if (JudgeUtils.isNotNull(paySettlementBO)){
                            settlementDBService.updatePaymentShort(paySettlementBO,checkDate) ;
                        }

                    }else{
                        if (JudgeUtils.equals(checkFileDetailBO.getCheckStatus(),ErrorTypeEnum.AMOUNT_ERROR.name())){
                            checkErrorBO.setCheckAmount(checkFileDetailBO.getOrderAmount());
                            checkErrorBO.setErrorType(ErrorTypeEnum.AMOUNT_ERROR.name());
                            checkErrorService.updateAmountError(checkErrorBO);
                            if (JudgeUtils.isNotNull(paySettlementBO)){
                                settlementDBService.updateAmountError(paySettlementBO,checkDate);
                            }
                        }
                    }
                }
            } else if (JudgeUtils.equals(tradeType, OrderTypeEnum.REFUND.name())) {
                PaySettlementBO paySettlementBO = settlementDBService.getRefundLongDoubt(settlementBO);
                if (JudgeUtils.isNotNull(paySettlementBO)&&JudgeUtils.equals(CheckStatusEnum.COMPLETE.name(),paySettlementBO.getCheckStatus())) {
                    checkErrorBO.setCheckAmount(paySettlementBO.getOrderAmount());
                    checkErrorBO.setErrorHandleType(CheckHandleEnum.DEAL_SUCCESS.name());
                    checkErrorBO.setErrorStatus(CheckStatusEnum.DOUBT_COMPLETE.name());
                    checkErrorBO.setTradeAmount(settlementBO.getOrderAmount());
                    checkErrorService.updateDoubleComplete(checkErrorBO);
                } else {
                    //查询对账文件表的数据
                    CheckFileDetailBO checkFileDetailBO=new CheckFileDetailBO();
                    checkFileDetailBO.setCheckFileDate(checkDate);
                    checkFileDetailBO.setOutOrderNo(checkErrorBO.getOutOrderNo());
                    checkFileDetailBO.setRefundOrderNo(checkErrorBO.getRefundOrderNo());
                    checkFileDetailBO.setCheckType(tradeType);
                    CheckFileDetailBO checkFileDetailDBBO=checkFileDetailService.getRefundLogDoubt(checkFileDetailBO);
                    if (JudgeUtils.isNull(checkFileDetailDBBO)){
                        checkErrorBO.setErrorType(ErrorTypeEnum.REFUND_LONG.name());
                        checkErrorBO.setDoubtFlag(CheckFileConstant.DOUBT_FLAG_N);
                        checkErrorService.updateRefundLong(checkErrorBO);
                        if (JudgeUtils.isNotNull(paySettlementBO)){
                            settlementDBService.updateRefundLog(paySettlementBO,checkDate) ;
                        }
                    }else{
                        if (JudgeUtils.equals(checkFileDetailBO.getCheckStatus(),ErrorTypeEnum.AMOUNT_ERROR.name())){
                            checkErrorBO.setCheckAmount(checkFileDetailBO.getOrderAmount());
                            checkErrorBO.setErrorType(ErrorTypeEnum.AMOUNT_ERROR.name());
                            checkErrorService.updateAmountError(checkErrorBO);
                            if (JudgeUtils.isNotNull(paySettlementBO)){
                                settlementDBService.updateAmountError(paySettlementBO,checkDate);
                            }
                        }
                    }
                }
            } else {
                log.error("订单类型错误:{}", tradeType);
            }

        }

    }

    @Override
    public void addDoubleOrder(String checkDate) {
        //D+1日将D日存疑订单登记差错表
        List<PaySettlementBO> settlementList = settlementDBService.findWaitOrderList(checkDate);
        if (settlementList.isEmpty()) {
            log.info("{}结算表数据均完成对账", checkDate);
            return;
        }
        for (PaySettlementBO settlementBO : settlementList) {
            String settlementType = settlementBO.getSettlementType();
            //将当天存疑加入差错表
            CheckErrorBO checkErrorDetail = new CheckErrorBO();
            if (JudgeUtils.equals(settlementType, OrderTypeEnum.PAYMENT.name())) {
                //支付短款存疑
                checkErrorDetail.setErrorType(ErrorTypeEnum.PAYMENT_SHORT_DOUBT.name());
            } else if (JudgeUtils.equals(settlementType, OrderTypeEnum.REFUND.name())) {
                //退款长款存疑
                checkErrorDetail.setErrorType(ErrorTypeEnum.REFUND_LONG_DOUBT.name());
            } else {
                log.error("结算类型错误:{}", settlementType);
                continue;
            }
            initCheckError(checkDate, settlementBO, checkErrorDetail, settlementType);
            checkErrorService.addError(checkErrorDetail);
        }
    }

    private static void initCheckError(String checkDate, PaySettlementBO settlementBO, CheckErrorBO checkErrorDetail, String settlementType) {
        checkErrorDetail.setOutOrderNo(settlementBO.getOutOrderNo());
        checkErrorDetail.setRefundOrderNo(settlementBO.getRefundOrderNo());
        checkErrorDetail.setTradeAmount(settlementBO.getOrderAmount());
        checkErrorDetail.setDoubtFlag(CheckFileConstant.DOUBT_FLAG_Y);
        checkErrorDetail.setErrorHandleType(CheckHandleEnum.DEAL_WAIT.name());
        checkErrorDetail.setTradeType(settlementType);
        //对账文件日期
        checkErrorDetail.setCheckFileDate(checkDate);
        //对账差错时间
        checkErrorDetail.setErrorTime(DateTimeUtils.getCurrentTimeStr());
        checkErrorDetail.setMerchantNo(settlementBO.getMerchantNo());
        checkErrorDetail.setOrgMerchantNo(settlementBO.getOrgMerchantNo());
    }
}
