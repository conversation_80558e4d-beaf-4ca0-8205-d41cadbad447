package com.cmpay.dceppay.batch.check;

import org.springframework.batch.core.partition.support.Partitioner;
import org.springframework.batch.item.ExecutionContext;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/9/18 10:09
 */
public class WriterPartitioner implements Partitioner {

    private String checkDate;
    private String channelCode;

    public WriterPartitioner(String checkDate, String channelCode) {
        this.checkDate = checkDate;
        this.channelCode = channelCode;
    }


    @Override
    public Map<String, ExecutionContext> partition(int gridSize) {
        Map<String, ExecutionContext> result = new HashMap<>(4);
        for (int i = 0; i < gridSize; i++) {
            ExecutionContext value = new ExecutionContext();
            value.putInt("indexId", i);
            value.putLong("gridSize", gridSize - 1);
            value.putString("channelCode", channelCode);
            value.putString("checkDate", checkDate);
            result.put("partition" + i, value);
        }
        return result;
    }
}
