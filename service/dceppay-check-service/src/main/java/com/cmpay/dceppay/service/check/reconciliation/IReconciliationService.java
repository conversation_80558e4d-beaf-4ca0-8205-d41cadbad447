package com.cmpay.dceppay.service.check.reconciliation;

import org.springframework.batch.core.JobParametersInvalidException;
import org.springframework.batch.core.repository.JobExecutionAlreadyRunningException;
import org.springframework.batch.core.repository.JobInstanceAlreadyCompleteException;
import org.springframework.batch.core.repository.JobRestartException;

/**
 * <AUTHOR>
 * @date 2024/9/24 14:42
 */
public interface IReconciliationService {
    /**
     * @param gridSize
     * @param checkDate
     * @param institutionCode
     * @throws JobParametersInvalidException
     * @throws JobExecutionAlreadyRunningException
     * @throws JobRestartException
     * @throws JobInstanceAlreadyCompleteException
     */
    void start(int gridSize, String checkDate, String institutionCode) throws JobParametersInvalidException, JobExecutionAlreadyRunningException, JobRestartException, JobInstanceAlreadyCompleteException;

}
