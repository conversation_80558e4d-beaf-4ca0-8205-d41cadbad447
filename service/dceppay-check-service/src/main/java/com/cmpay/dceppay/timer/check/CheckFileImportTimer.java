package com.cmpay.dceppay.timer.check;

import com.cmpay.dceppay.service.check.IDcepCheckService;
import com.cmpay.dceppay.util.DateTimeUtil;
import com.cmpay.lemon.framework.schedule.batch.BatchScheduled;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/9/23 11:42
 * 对账文件下载入库
 */
@Service
@Slf4j
public class CheckFileImportTimer {

    @Autowired
    private IDcepCheckService checkService;


    @BatchScheduled(cron = "0 0/10 1-23 * * ?")
    public void execute() {
        String checkDate = DateTimeUtil.getCheckDate();
        checkService.download(checkDate);
    }


}
