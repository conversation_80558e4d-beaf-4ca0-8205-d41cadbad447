package com.cmpay.dceppay.service.check.reader;

import com.cmpay.dceppay.constant.common.SeparatorConstants;
import com.univocity.parsers.csv.CsvParserSettings;

/**
 * Created on 2019/4/5
 *  文本数据解析接口定义
 * @author: sun_zhh
 */
public interface IUnivocity {
    default CsvParserSettings parseSetting() {
        // 创建csv解析器settings配置对象
        CsvParserSettings settings = new CsvParserSettings();
        settings.getFormat().setDelimiter('|');
        settings.getFormat().setLineSeparator(SeparatorConstants.NEWLINE);
        return settings;
    }
}