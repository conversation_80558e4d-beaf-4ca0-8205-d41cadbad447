package com.cmpay.dceppay.service.check.error.impl;

import com.cmpay.dceppay.bo.CheckCancelRefundBO;
import com.cmpay.dceppay.bo.CheckErrorAmountUpdateBO;
import com.cmpay.dceppay.bo.CheckErrorCancelBO;
import com.cmpay.dceppay.bo.account.AccountTreatmentResultBO;
import com.cmpay.dceppay.bo.check.CheckErrorBO;
import com.cmpay.dceppay.bo.pay.PayOrderDBBO;
import com.cmpay.dceppay.bo.refund.RefundOrderRepBO;
import com.cmpay.dceppay.constant.pay.PaymentConstants;
import com.cmpay.dceppay.enums.MsgCodeEnum;
import com.cmpay.dceppay.enums.check.CheckHandleEnum;
import com.cmpay.dceppay.enums.check.CheckStatusEnum;
import com.cmpay.dceppay.enums.check.ErrorTypeEnum;
import com.cmpay.dceppay.enums.common.IdGenKeyEnum;
import com.cmpay.dceppay.enums.pay.AccountProcessStatusEnum;
import com.cmpay.dceppay.enums.pay.ChannelCodeEnum;
import com.cmpay.dceppay.enums.pay.RefundCancleEnum;
import com.cmpay.dceppay.service.account.IDcepAccountService;
import com.cmpay.dceppay.service.check.ICheckErrorService;
import com.cmpay.dceppay.service.check.ICheckFileDetailService;
import com.cmpay.dceppay.service.check.error.ICheckErrorHandleService;
import com.cmpay.dceppay.service.pay.IPayOrderDBService;
import com.cmpay.dceppay.service.pay.IPaySettlementDBService;
import com.cmpay.dceppay.service.pay.IPaymentService;
import com.cmpay.dceppay.util.DateTimeUtil;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.utils.IdGenUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/9/29 10:51
 */
@Service
@Slf4j
public class CheckErrorHandleServiceImpl implements ICheckErrorHandleService {

    @Autowired
    private ICheckErrorService checkErrorService;
    @Autowired
    private IPayOrderDBService payOrderDBService;
    @Autowired
    private IDcepAccountService accountService;
    @Autowired
    private IPaymentService paymentService;
    @Autowired
    private IPaySettlementDBService settlementDBService;
    @Autowired
    private ICheckFileDetailService checkFileDetailService;
    @Autowired
    private IPaySettlementDBService paySettlementDBService;

    @Override
    public void cancelError(CheckErrorCancelBO errorCancelBO) {
        checkOperatorIsValid(errorCancelBO.getOutOrderNo(), errorCancelBO.getRefundOrderNo(), null);
        settlementDBService.updateCheckErrorComplete(errorCancelBO.getOutOrderNo(), errorCancelBO.getRefundOrderNo());
        checkFileDetailService.updateCheckErrorComplete(errorCancelBO.getOutOrderNo(), errorCancelBO.getRefundOrderNo());
        CheckErrorBO checkErrorBO = new CheckErrorBO();
        BeanUtils.copyProperties(checkErrorBO, errorCancelBO);
        checkErrorBO.setErrorHandleType(CheckHandleEnum.DEAL_SUCCESS.name());
        checkErrorBO.setErrorStatus(CheckStatusEnum.ERROR_CANCEL.name());
        checkErrorBO.setErrorProcessTime(DateTimeUtil.getCurrentDateTimeStr());
        int updateNumber = checkErrorService.cancelError(checkErrorBO);
        if (updateNumber != 1) {
            BusinessException.throwBusinessException(MsgCodeEnum.CHECK_ERROR_CANCEL_FAIL);
        }

    }


    @Override
    public void updateCheckAmount(CheckErrorAmountUpdateBO amountUpdateBO) {
        checkOperatorIsValid(amountUpdateBO.getOutOrderNo(), amountUpdateBO.getRefundOrderNo(), ErrorTypeEnum.AMOUNT_ERROR.name());
        settlementDBService.updateCheckErrorAmount(amountUpdateBO.getOutOrderNo(), amountUpdateBO.getRefundOrderNo(), amountUpdateBO.getAmount());
        checkFileDetailService.updateCheckErrorAmount(amountUpdateBO.getOutOrderNo(), amountUpdateBO.getRefundOrderNo(), amountUpdateBO.getAmount());
        CheckErrorBO checkErrorBO = new CheckErrorBO();
        BeanUtils.copyProperties(checkErrorBO, amountUpdateBO);
        checkErrorBO.setErrorHandleType(CheckHandleEnum.DEAL_SUCCESS.name());
        checkErrorBO.setErrorStatus(CheckStatusEnum.AMOUNT_UPDATE.name());
        checkErrorBO.setTradeAmount(amountUpdateBO.getAmount());
        checkErrorBO.setCheckAmount(amountUpdateBO.getAmount());
        checkErrorBO.setErrorProcessTime(DateTimeUtil.getCurrentDateTimeStr());
        int updateNumber = checkErrorService.updateAmount(checkErrorBO);
        if (updateNumber != 1) {
            BusinessException.throwBusinessException(MsgCodeEnum.CHECK_ERROR_UPDATE_AMOUNT_FAIL);
        }
    }

    @Override
    public void cancelRefund(CheckCancelRefundBO checkCancelRefundBO) {
        checkOperatorIsValid(checkCancelRefundBO.getOutOrderNo(), checkCancelRefundBO.getRefundOrderNo(),ErrorTypeEnum.PAYMENT_LONG_STS_DIF.name());
        PayOrderDBBO payOrderDBBO = payOrderDBService.getByOutOrderNo(checkCancelRefundBO.getOutOrderNo());
        if (JudgeUtils.isNull(payOrderDBBO)) {
            BusinessException.throwBusinessException(MsgCodeEnum.ORG_ORDER_NOT_EXISTS);
        }
        //todo 改成两个按钮 补单 --调用账务 登记结算表，更新订单表状态
        //todo 改成两个按钮 撤单 --调用退款接口
        AccountTreatmentResultBO accountTreatmentBO = accountService.orderBackFillAccountRegister(payOrderDBBO);
        if (JudgeUtils.notEquals(accountTreatmentBO.getAccountHandleStatus(), AccountProcessStatusEnum.SUCCESS.name())) {
            BusinessException.throwBusinessException(MsgCodeEnum.PAYMENT_ACCOUNT_STATUS_WAIT);
        }
        payOrderDBBO.setOrderCompleteTime(DateTimeUtil.getCurrentDateTimeStr());
        paySettlementDBService.registerPaySettlement(payOrderDBBO);
        payOrderDBService.updateTradeSuccess(payOrderDBBO);
        // 退款
        RefundOrderRepBO refundOrderRepBO = new RefundOrderRepBO();
        refundOrderRepBO.setOutOrderNo(checkCancelRefundBO.getOutOrderNo());
        refundOrderRepBO.setOutRefundNo(IdGenUtils.generateIdWithDateTime(IdGenKeyEnum.REFUND_NO.name(), IdGenKeyEnum.REFUND_NO_LENGTH));
        refundOrderRepBO.setCancelFlag(RefundCancleEnum.Y.name());
        refundOrderRepBO.setRefundReason(PaymentConstants.DEFAULT_REFUND_REASON);
        refundOrderRepBO.setTradeDate(DateTimeUtil.getCurrentDateStr());
        refundOrderRepBO.setRefundAmount(payOrderDBBO.getOrderAmount());
        refundOrderRepBO.setExtra(PaymentConstants.DEFAULT_REFUND_REASON);
        refundOrderRepBO.setChannelCode(ChannelCodeEnum.DCEP_MNG.getChannelCode());
        paymentService.refundOrder(refundOrderRepBO);
        //更新差错表
        CheckErrorBO checkErrorBO = new CheckErrorBO();
        BeanUtils.copyProperties(checkErrorBO, checkCancelRefundBO);
        checkErrorBO.setErrorHandleType(CheckHandleEnum.DEAL_SUCCESS.name());
        checkErrorBO.setErrorStatus(CheckStatusEnum.CANCEL_REFUND.name());
        checkErrorBO.setErrorProcessTime(DateTimeUtil.getCurrentDateTimeStr());
        int updateNumber = checkErrorService.cancelRefund(checkErrorBO);
        if (updateNumber != 1) {
            BusinessException.throwBusinessException(MsgCodeEnum.CHECK_ERROR_CANCEL_REFUND_FAIL);
        }

    }

    private void checkOperatorIsValid(String outOrderNo, String outRefundNo, String errorType) {
        CheckErrorBO checkErrorBO = checkErrorService.getByKey(outOrderNo, outRefundNo);
        if (JudgeUtils.isNull(checkErrorBO)) {
            BusinessException.throwBusinessException(MsgCodeEnum.ERROR_DATA_NOT_EXIST);
        }
        if (JudgeUtils.notEquals(checkErrorBO.getErrorHandleType(), CheckHandleEnum.DEAL_WAIT.name())) {
            BusinessException.throwBusinessException(MsgCodeEnum.ERROR_STATUS_ERROR);
        }
        if (JudgeUtils.isNotNull(errorType)) {
            if (JudgeUtils.notEquals(checkErrorBO.getErrorType(), errorType)) {
                BusinessException.throwBusinessException(MsgCodeEnum.ERROR_NOT_SUPPORT);
            }
        }
    }
}
