package com.cmpay.dceppay.service.check.download.impl;

import com.cmpay.dceppay.bo.CheckDetailFileInfoBO;
import com.cmpay.dceppay.constant.check.CheckFileConstant;
import com.cmpay.dceppay.constant.common.SeparatorConstants;
import com.cmpay.dceppay.service.check.download.IReadCheckDetailFileService;
import com.cmpay.lemon.common.utils.JudgeUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/10/9 11:07
 */
@Service
@Slf4j
public class ReadCheckDetailFileServiceImpl implements IReadCheckDetailFileService {

    @Override
    public boolean isLastLineEnd(String filePath) {
        boolean lastLineIsEnd = false;
        try (BufferedReader reader = new BufferedReader(new FileReader(filePath))) {
            String lastLine = null;
            // 使用try-with-resources确保BufferedReader正确关闭
            // 检查是否还有更多行
            while (reader.ready()) {
                // 直接读取下一行
                lastLine = reader.readLine();
            }
            if (JudgeUtils.equals(CheckFileConstant.LAST_LINE_END, lastLine)) {
                lastLineIsEnd = true;
            }
        } catch (IOException e) {
            log.error("【互联互通对账明细文件读取失败】:{}", e.getCause());
        }
        return lastLineIsEnd;
    }

    @Override
    public CheckDetailFileInfoBO parseDetailFile(String filePath) {
        String firstLine;
        CheckDetailFileInfoBO checkDetailFileInfoBO = new CheckDetailFileInfoBO();
        try (BufferedReader reader = new BufferedReader(new FileReader(filePath))) {
            // 读取第一行
            firstLine = reader.readLine();
            checkDetailFileInfoBO.setDetailCount(Integer.parseInt(firstLine.split(SeparatorConstants.PIPE)[0]));
            checkDetailFileInfoBO.setDetailAmount(new BigDecimal(firstLine.split(SeparatorConstants.PIPE)[1]));
        } catch (IOException e) {
            log.error("【互联互通对账明细文件读取失败】:{}", e.getCause());
        }
        return checkDetailFileInfoBO;

    }
}
