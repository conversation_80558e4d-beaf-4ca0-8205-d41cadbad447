package com.cmpay.dceppay.service.check.writer;

import java.io.IOException;
import java.util.List;

/**
 * Created on 2019/4/5
 *  文本数据解析接口定义
 * @author: sun_zhh
 */
public interface IDataWriter<T ,K> {
    /**
     * 明细文件数据
     * @param checkBO
     * @param channelCode
     * @param checkDate
     * @param indexId
     * @return
     * @throws Exception
     */
    List<String> data(T checkBO,K channelCode,String checkDate, String indexId) throws Exception;

    /**
     * 对账文件创建
     * @param channelCode
     * @param checkDate
     * @param gridSize
     * @throws IOException
     */
    void before(K channelCode,String checkDate, Long gridSize) throws IOException;

    /**
     * 对账文件处理
     * @param channelCode
     * @param checkDate
     * @param gridSize
     * @throws Exception
     */
    void close(String channelCode,String checkDate, Long gridSize)throws Exception;
}
