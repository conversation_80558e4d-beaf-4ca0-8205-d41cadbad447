package com.cmpay.dceppay.service.check.reconciliation.impl;

import com.cmpay.dceppay.bo.check.CheckErrorBO;
import com.cmpay.dceppay.bo.check.CheckFileDetailBO;
import com.cmpay.dceppay.bo.check.CheckFileProcessBO;
import com.cmpay.dceppay.bo.pay.PayOrderDBBO;
import com.cmpay.dceppay.bo.pay.PaySettlementBO;
import com.cmpay.dceppay.bo.pay.RefundOrderDBBO;
import com.cmpay.dceppay.bo.paymentquery.PaymentOrderResultBO;
import com.cmpay.dceppay.bo.refundquery.RefundOrderResultBO;
import com.cmpay.dceppay.constant.check.CheckFileConstant;
import com.cmpay.dceppay.constant.dcep.DcepResponseStatusConstant;
import com.cmpay.dceppay.enums.check.CheckFileProcessEnums;
import com.cmpay.dceppay.enums.check.CheckHandleEnum;
import com.cmpay.dceppay.enums.check.ErrorTypeEnum;
import com.cmpay.dceppay.enums.pay.OrderStatusEnum;
import com.cmpay.dceppay.enums.pay.OrderTypeEnum;
import com.cmpay.dceppay.service.check.ICheckErrorService;
import com.cmpay.dceppay.service.check.ICheckFileDetailService;
import com.cmpay.dceppay.service.check.ICheckFileProcessDBService;
import com.cmpay.dceppay.service.check.reconciliation.ICheckService;
import com.cmpay.dceppay.service.pay.*;
import com.cmpay.dceppay.util.DateTimeUtil;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.lemon.framework.data.interceptor.InitialLemonData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/9/23 11:49
 * 对账差错处理
 */
@Service
@Slf4j
public class CheckServiceImpl implements ICheckService {
    @Autowired
    private ICheckFileProcessDBService checkFileProcessDBService;
    @Autowired
    private IPaySettlementDBService settlementDBService;
    @Autowired
    private ICheckFileDetailService checkFileDetailService;
    @Autowired
    private ICheckErrorService checkErrorService;
    @Autowired
    private IPayOrderDBService orderDBService;
    @Autowired
    private IPayRefundDBService refundDBService;
    @Autowired
    private IPayOrderService payOrderService;
    @Autowired
    private IPayOrderDataService payOrderDataService;
    @Autowired
    private IRefundOrderDataService refundOrderDataService;
    @Autowired
    private IPayRefundService refundService;

    @Override
    public boolean getCheckFlag(String checkDate, String institutionCode) {
        CheckFileProcessBO checkFileProcessBO = checkFileProcessDBService.getProcessBO(checkDate, institutionCode);
        if (JudgeUtils.isNull(checkFileProcessBO)) {
            return false;
        }
        return StringUtils.equalsAny(checkFileProcessBO.getCheckStatus(), CheckFileProcessEnums.IMPORTED.name(), CheckFileProcessEnums.CHECKING.name()) ? true : false;
    }


    @Override
    public boolean checkFinishFlag(String checkDate, String institutionCode) {
        CheckFileProcessBO checkFileProcessBO = checkFileProcessDBService.getProcessBO(checkDate, institutionCode);
        if (JudgeUtils.isNull(checkFileProcessBO) || JudgeUtils.notEquals(checkFileProcessBO.getCheckStatus(), CheckFileProcessEnums.CHECK_FINISH.name())) {
            return false;
        }
        return true;
    }

    @Override
    @InitialLemonData("lemonDataInitializer")
    public void check(CheckFileDetailBO item, String checkDate) {
        if (JudgeUtils.notEquals(item.getOrderStatus(), DcepResponseStatusConstant.PAY_ORDER_SUCCESS)) {
            log.warn("互联互通对账,存在非成功状态的数据：{}", item);
            return;
        }
        String checkType = item.getCheckType();
        OrderTypeEnum orderTypeEnum = OrderTypeEnum.valueOf(checkType);
        if (JudgeUtils.isNull(orderTypeEnum)) {
            log.error("对账类型非法：{}", checkType);
            return;
        }
        switch (orderTypeEnum) {
            case PAYMENT:
                checkPayment(item, checkDate);
                break;
            case REFUND:
                checkRefund(item, checkDate);
                break;
            default:
                log.error("对账类型非法：{}", checkType);
                break;
        }
    }


    public void checkPayment(CheckFileDetailBO checkFileDetailBO, String checkDate) {
        String settlementDate = DateTimeUtils.formatLocalDate(DateTimeUtils.parseLocalDate(checkDate).minusDays(1));
        PaySettlementBO queryPaySettlementBO = new PaySettlementBO();
        queryPaySettlementBO.setOutOrderNo(checkFileDetailBO.getOutOrderNo());
        queryPaySettlementBO.setSettlementDate(settlementDate);
        queryPaySettlementBO.setSettlementType(OrderTypeEnum.PAYMENT.name());
        PaySettlementBO settlementBO = settlementDBService.getPaymentSettlement(queryPaySettlementBO);
        if (JudgeUtils.isNull(settlementBO)) {
            PayOrderDBBO orderDBBO = orderDBService.getByOutOrderNo(checkFileDetailBO.getOutOrderNo());
            if (JudgeUtils.isNull(orderDBBO)) {
                //订单表不存在 充值长款（未订单）
                checkFileDetailService.updatePaymentLongNoOrder(checkFileDetailBO, checkDate);
                checkErrorService.addError(buildCheckError(settlementBO, checkFileDetailBO, checkDate, ErrorTypeEnum.PAYMENT_LONG_NOT_ORD.name()));
            } else {
                //订单表存在 状态不一致
                OrderStatusEnum orderStatusEnum = OrderStatusEnum.valueOf(orderDBBO.getStatus());
                if (JudgeUtils.isNull(orderStatusEnum)) {
                    return;
                }
                switch (orderStatusEnum) {
                    case WAIT_PAY:
                        syncCheckFilePaymentInfo(checkFileDetailBO, orderDBBO);
                        break;
                    case TRADE_FAIL:
                    case TRADE_CLOSED:
                        checkFileDetailService.updatePaymentLongStsError(checkFileDetailBO, checkDate);
                        checkErrorService.addError(buildCheckError(settlementBO, checkFileDetailBO, checkDate, ErrorTypeEnum.PAYMENT_LONG_STS_DIF.name()));
                        break;
                    default:
                        break;
                }
            }
            return;
        }
        if (checkFileDetailBO.getOrderAmount().compareTo(settlementBO.getOrderAmount()) == 0) {
            settlementDBService.updateCheckComplete(settlementBO, checkDate);
            checkFileDetailService.updateCheckComplete(checkFileDetailBO, checkDate);
        } else {
            settlementDBService.updateAmountError(settlementBO, checkDate);
            checkFileDetailService.updateAmountError(checkFileDetailBO, checkDate);
            checkErrorService.addError(buildCheckError(settlementBO, checkFileDetailBO, checkDate, ErrorTypeEnum.AMOUNT_ERROR.name()));
        }
    }

    /**
     * 对账文件成功，订单待支付
     *
     * @param orderDBBO
     */
    private void syncCheckFilePaymentInfo(CheckFileDetailBO checkFileDetailBO, PayOrderDBBO orderDBBO) {
        //补单待下次轮询对账发起后再次对账即可
        PaymentOrderResultBO paymentOrderResultBO = payOrderDataService.buildPayOrderResult(checkFileDetailBO);
        payOrderService.handleSuccessPayOrder(paymentOrderResultBO, orderDBBO);
    }


    public void checkRefund(CheckFileDetailBO checkFileDetailBO, String checkDate) {
        String settlementDate = DateTimeUtils.formatLocalDate(DateTimeUtils.parseLocalDate(checkDate).minusDays(1));
        PaySettlementBO queryPaySettlementBO = new PaySettlementBO();
        queryPaySettlementBO.setRefundOrderNo(checkFileDetailBO.getRefundOrderNo());
        queryPaySettlementBO.setSettlementDate(settlementDate);
        queryPaySettlementBO.setSettlementType(OrderTypeEnum.REFUND.name());
        PaySettlementBO settlementBO = settlementDBService.getRefundSettlement(queryPaySettlementBO);
        if (JudgeUtils.isNull(settlementBO)) {
            RefundOrderDBBO refundOrder = refundDBService.getByOutRefundNo(checkFileDetailBO.getRefundOrderNo());
            if (JudgeUtils.isNull(refundOrder)) {
                //订单表不存在退款短款（未知订单
                checkFileDetailService.updateRefundShortNoOrder(checkFileDetailBO, checkDate);
                checkErrorService.addError(buildCheckError(settlementBO, checkFileDetailBO, checkDate, ErrorTypeEnum.REFUND_SHORT_NOT_ORD.name()));
            } else {
                OrderStatusEnum orderStatusEnum = OrderStatusEnum.valueOf(refundOrder.getStatus());
                if (JudgeUtils.isNull(orderStatusEnum)) {
                    return;
                }
                switch (orderStatusEnum) {
                    case REFUND_WAIT:
                    case REFUND_PEND:
                        syncCheckFileRefundInfo(checkFileDetailBO, refundOrder);
                        break;
                    case REFUND_FAIL:
                        //订单表存在 状态不一致
                        checkFileDetailService.updateRefundShortStsError(checkFileDetailBO, checkDate);
                        checkFileDetailBO.setOutOrderNo(refundOrder.getOutOrderNo());
                        checkErrorService.addError(buildCheckError(settlementBO, checkFileDetailBO, checkDate, ErrorTypeEnum.REFUND_SHORT_STS_DIF.name()));
                        break;
                    default:
                        break;
                }
            }
            return;
        }
        if (checkFileDetailBO.getOrderAmount().compareTo(settlementBO.getOrderAmount()) == 0) {
            settlementDBService.updateCheckComplete(settlementBO, checkDate);
            checkFileDetailService.updateCheckComplete(checkFileDetailBO, checkDate);
        } else {
            settlementDBService.updateAmountError(settlementBO, checkDate);
            checkFileDetailService.updateAmountError(checkFileDetailBO, checkDate);
            checkFileDetailBO.setOutOrderNo(settlementBO.getOutOrderNo());
            checkErrorService.addError(buildCheckError(settlementBO, checkFileDetailBO, checkDate, ErrorTypeEnum.AMOUNT_ERROR.name()));
        }
    }

    private void syncCheckFileRefundInfo(CheckFileDetailBO checkFileDetailBO, RefundOrderDBBO refundOrder) {
        RefundOrderResultBO refundOrderResultBO = refundOrderDataService.buildRefundResult(checkFileDetailBO);
        refundService.handleSuccessRefundOrder(refundOrderResultBO, refundOrder);
    }


    private CheckErrorBO buildCheckError(PaySettlementBO settlementBO, CheckFileDetailBO checkFileDetailBO, String checkDate, String errorType) {
        CheckErrorBO checkErrorBO = new CheckErrorBO();
        checkErrorBO.setOutOrderNo(checkFileDetailBO.getOutOrderNo());
        checkErrorBO.setRefundOrderNo(checkFileDetailBO.getRefundOrderNo());
        checkErrorBO.setCheckFileDate(checkDate);
        checkErrorBO.setErrorTime(DateTimeUtil.getCurrentDateTimeStr());
        checkErrorBO.setErrorHandleType(CheckHandleEnum.DEAL_WAIT.name());
        checkErrorBO.setCheckAmount(checkFileDetailBO.getOrderAmount());
        checkErrorBO.setTradeType(checkFileDetailBO.getCheckType());
        checkErrorBO.setErrorType(errorType);
        checkErrorBO.setDoubtFlag(CheckFileConstant.DOUBT_FLAG_N);
        if(JudgeUtils.equalsAny(errorType,ErrorTypeEnum.PAYMENT_LONG_NOT_ORD.name(),ErrorTypeEnum.PAYMENT_SHORT_DOUBT.name(),
                ErrorTypeEnum.REFUND_SHORT_NOT_ORD.name(),ErrorTypeEnum.REFUND_LONG_DOUBT.name())){
            checkErrorBO.setDoubtFlag(CheckFileConstant.DOUBT_FLAG_Y);
        }
        if (JudgeUtils.isNotNull(settlementBO)) {
            checkErrorBO.setTradeAmount(settlementBO.getOrderAmount());
            checkErrorBO.setMerchantNo(settlementBO.getMerchantNo());
            checkErrorBO.setOrgMerchantNo(settlementBO.getOrgMerchantNo());
        }
        return checkErrorBO;
    }

}
