package com.cmpay.dceppay.timer.check;

import com.cmpay.dceppay.bo.account.PayOrderAccountBO;
import com.cmpay.dceppay.bo.check.CheckFileSummaryBO;
import com.cmpay.dceppay.bo.mng.TaskStatusBO;
import com.cmpay.dceppay.config.DceppayConfig;
import com.cmpay.dceppay.enums.common.IdGenKeyEnum;
import com.cmpay.dceppay.enums.mng.TaskKeyEnum;
import com.cmpay.dceppay.service.account.IDcepAccountService;
import com.cmpay.dceppay.service.check.ICheckBillSummaryDBService;
import com.cmpay.dceppay.service.check.reconciliation.ICheckService;
import com.cmpay.dceppay.service.mng.ITaskStatusService;
import com.cmpay.dceppay.util.DateTimeUtil;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.schedule.batch.BatchScheduled;
import com.cmpay.lemon.framework.utils.IdGenUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/9/26 10:36
 * 对账文件账务处理
 */
@Service
@Slf4j
public class CheckFileAccountTimer {
    @Autowired
    private ICheckService checkService;
    @Autowired
    private IDcepAccountService accountService;
    @Autowired
    private ICheckBillSummaryDBService checkBillSummaryDBService;
    @Autowired
    private ITaskStatusService taskStatusService;
    @Autowired
    private DceppayConfig dceppayConfig;


    @BatchScheduled(cron = "0 0/20 1-23 * * ?")
    public void checkFileAccountDeal() {
        String checkDate = DateTimeUtil.getCheckDate();
        String institutionCode = dceppayConfig.getCreditorCode();
        if (!isCheckFinished(checkDate, institutionCode)) {
            log.info("对账未完成直接退出:{}", checkDate);
            return;
        }
        if (isTaskProcessed(checkDate)) {
            log.info("对账账务已处理, 日期：{}", checkDate);
            return;
        }
        CheckFileSummaryBO fileSummaryBO = getFileSummaryBO(checkDate, institutionCode);
        if (fileSummaryBO == null) {
            log.error("对账账务处理失败：文件汇总信息不存在, 日期：{}", checkDate);
            return;
        }
        try {
            if (fileSummaryBO.getPaymentSuccessAmount().compareTo(new BigDecimal(0)) > 0) {
                processPayments(checkDate, fileSummaryBO);
            }
            if (fileSummaryBO.getRefundSuccessAmount().compareTo(new BigDecimal(0)) > 0) {
                processRefunds(checkDate, fileSummaryBO);
            }

            taskStatusService.addCheckAccountTaskInfo(checkDate);
        } catch (Exception e) {
            log.error("对账账务处理失败，日期：{}；错误信息：{}", checkDate, e.getCause());
        }
    }

    private boolean isCheckFinished(String checkDate, String institutionCode) {
        return checkService.checkFinishFlag(checkDate, institutionCode);
    }

    private boolean isTaskProcessed(String checkDate) {
        TaskStatusBO taskStatusBO = taskStatusService.getByKey(checkDate, TaskKeyEnum.CHECK_ACC_TASK.name());
        return JudgeUtils.isNotNull(taskStatusBO);
    }

    private CheckFileSummaryBO getFileSummaryBO(String checkDate, String institutionCode) {
        return checkBillSummaryDBService.getByKey(checkDate, institutionCode);
    }

    private void processPayments(String checkDate, CheckFileSummaryBO fileSummaryBO) {
        PayOrderAccountBO payOrderAccountBO = createPayOrderAccountBO(checkDate, fileSummaryBO.getPaymentSuccessAmount());
        accountService.paymentCheckAccountRegister(payOrderAccountBO);
    }

    private void processRefunds(String checkDate, CheckFileSummaryBO fileSummaryBO) {
        PayOrderAccountBO payOrderAccountBO = createPayOrderAccountBO(checkDate, fileSummaryBO.getRefundSuccessAmount());
        accountService.refundCheckAccountRegister(payOrderAccountBO);
    }

    private PayOrderAccountBO createPayOrderAccountBO(String checkDate, BigDecimal amount) {
        PayOrderAccountBO payOrderAccountBO = new PayOrderAccountBO();
        payOrderAccountBO.setOrderNo(IdGenUtils.generateIdWithDateTime(IdGenKeyEnum.ACCOUNT_BUS_JRN_NO.name(), IdGenKeyEnum.ACCOUNT_BUS_JRN_NO_LENGTH));
        payOrderAccountBO.setOrderDate(checkDate);
        payOrderAccountBO.setOrderTime(DateTimeUtil.getCurrentTimeStr());
        payOrderAccountBO.setAmount(amount);
        return payOrderAccountBO;
    }


}



