package com.cmpay.dceppay.service.check.reader.impl;

import com.cmpay.dceppay.bo.CheckFileBO;
import com.cmpay.dceppay.bo.check.CheckFileDetailBO;
import com.cmpay.dceppay.constant.check.CheckFileConstant;
import com.cmpay.dceppay.service.check.ICheckFileDetailService;
import com.cmpay.dceppay.service.check.reader.AbstractDataReaderComponent;
import com.cmpay.dceppay.service.check.reader.IDcepDataParseService;
import com.cmpay.dceppay.service.check.reader.IReadExecute;
import com.cmpay.lemon.common.utils.JudgeUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/23 17:19
 */
@Service
@Slf4j
public class DcepCheckFileReaderServiceImpl extends AbstractDataReaderComponent<CheckFileBO> implements IReadExecute<CheckFileBO> {
    @Autowired
    private ICheckFileDetailService checkFileDetailService;
    @Autowired
    private IDcepDataParseService dataParseService;

    @Override
    public void execute(CheckFileBO fileBO)  {
        charset = "GBK";
        dataPath = fileBO.getCheckFilePath();
        this.readData(fileBO);
        this.handlerProcess(fileBO);
    }

    @Override
    public void insertCheckDetail(List list) {
        if (JudgeUtils.isNotNull(list) && list.size() != 0) {
            checkFileDetailService.addCheckFileCount(list);
            list.clear();
        }
    }

    @Override
    public void readParse(String[] data, Long rowIndex, List insertCheckDetail,CheckFileBO fileBO) {
        if (rowIndex != 0) {
            CheckFileDetailBO checkFileDetailBO = dataParseService.parseCheckFileContent(data,fileBO);
            insertCheckDetail.add(checkFileDetailBO);
            if (insertCheckDetail.size() >= CheckFileConstant.DETAIL_INSERT_SIZE) {
                checkFileDetailService.addCheckFileCount(insertCheckDetail);
                insertCheckDetail.clear();
            }
        }
    }
}
