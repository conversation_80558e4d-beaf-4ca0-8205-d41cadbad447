package com.cmpay.dceppay.service.check.download.impl;

import com.cmpay.dceppay.bo.CheckFileBO;
import com.cmpay.dceppay.bo.CheckFileDownloadBO;
import com.cmpay.dceppay.properties.DcepBilUpSftpProperties;
import com.cmpay.dceppay.service.check.ICheckBillSummaryDBService;
import com.cmpay.dceppay.service.check.ICheckFileProcessDBService;
import com.cmpay.dceppay.service.check.download.ICheckFileImportService;
import com.cmpay.dceppay.service.check.reader.IReadExecute;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/24 13:24
 */
@Service
public class CheckFileImportServiceImpl implements ICheckFileImportService {
    @Autowired
    private ICheckFileProcessDBService checkFileProcessDBService;
    @Autowired
    private ICheckBillSummaryDBService billSummaryDBService;
    @Autowired
    private IReadExecute readExecute;
    @Autowired
    private DcepBilUpSftpProperties bilUpSftpProperties;

    @Override
    public void importToDB(CheckFileDownloadBO downloadBO, List<String> detailFileList) {
        checkFileProcessDBService.updateImportBegin(downloadBO.getCheckFileDate(), downloadBO.getInstitutionCode());
        for (String fileName : detailFileList) {
            CheckFileBO checkFileBO = new CheckFileBO();
            checkFileBO.setCheckFileDate(downloadBO.getCheckFileDate());
            checkFileBO.setInstitutionCode(downloadBO.getInstitutionCode());
            checkFileBO.setCheckFilePath(bilUpSftpProperties.getLocalDataPath() + downloadBO.getCheckFileDate() + File.separator + fileName);
            readExecute.execute(checkFileBO);
        }
        checkFileProcessDBService.updateImportEnd(downloadBO.getCheckFileDate(), downloadBO.getInstitutionCode());
        billSummaryDBService.updateSummaryInfo(downloadBO.getCheckFileDate(), downloadBO.getInstitutionCode());
    }
}
