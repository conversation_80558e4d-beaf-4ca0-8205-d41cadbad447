package com.cmpay.dceppay.service.check.writer;

/**
 * <AUTHOR>
 * @date 2024/9/18 9:00
 */
public interface IWriterService {

    /**
     * 生成指定账期的对账文件
     * @param accountDate 会计日期
     * @param channelCode 渠道编号
     */
    void genCheckFile(String accountDate,String channelCode,boolean reGenFlag);


    /**
     * 推送文件到支付中台
     * @param date
     */
    void pushFileToPayCenter(String date);
}
