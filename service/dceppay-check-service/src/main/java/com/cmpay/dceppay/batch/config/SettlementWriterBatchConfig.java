package com.cmpay.dceppay.batch.config;

import com.cmpay.dceppay.bo.pay.PaySettlementBO;
import com.cmpay.dceppay.constant.check.CheckFileConstant;
import com.cmpay.dceppay.service.check.writer.NoOpItemWriter;
import com.cmpay.dceppay.service.check.writer.impl.SettlementDataWriter;
import com.cmpay.dceppay.utils.ExceptionHandlerUtil;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.JobExecutionListener;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.batch.item.ItemReader;
import org.springframework.batch.item.database.JdbcPagingItemReader;
import org.springframework.batch.item.database.support.SqlPagingQueryProviderFactoryBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;

/**
 * 通用中台对账文件生成
 */
@Configuration
@Slf4j
public class SettlementWriterBatchConfig {
    @Autowired
    StepBuilderFactory stepBuilderFactory;

    @Autowired
    @Qualifier("pagingItemReaderWriter")
    ItemReader<PaySettlementBO> pagingItemReaderWriter;
    @Autowired
    @Qualifier("itemProcessorWriter")
    ItemProcessor<PaySettlementBO, String> itemProcessorWriter;
    @Autowired
    DataSource dataSource;

    @Autowired
    NoOpItemWriter<String> noOpItemWriter;
    @Autowired
    private SettlementDataWriter settlementDataWriter;

    @Bean
    public Step partitionerStepWriter() {
        return stepBuilderFactory.get("partitionerStepWriter")
                .<PaySettlementBO, String>chunk(1000)
                .reader(pagingItemReaderWriter)
                .processor(itemProcessorWriter)
                .writer(noOpItemWriter)
                .build();
    }


    @Bean
    @StepScope
    public ItemProcessor<PaySettlementBO, String> itemProcessorWriter(@Value("#{stepExecutionContext['checkDate']}") String checkDate,
                                                                      @Value("#{stepExecutionContext['channelCode']}") String channelCode,
                                                                      @Value("#{stepExecutionContext['indexId']}") String indexId) {
        log.info("生成支付中台对账文件写数据开始=====");
        return item -> {
            try {
                settlementDataWriter.data(item, channelCode, checkDate, indexId);
            } catch (Exception e) {
                log.error("生成支付中台对账文件后处理异常,processItem:{}", item);
                ExceptionHandlerUtil.throwRequestDcepError(e);
            }
            return "";
        };
    }


    @Bean
    @StepScope
    public ItemReader<PaySettlementBO> pagingItemReaderWriter(@Value("#{stepExecutionContext['checkDate']}") String checkDate,
                                                              @Value("#{stepExecutionContext['channelCode']}") String channelCode
    ) throws Exception {
        log.info("生成支付中台对账文件读取数据开始=====");
        JdbcPagingItemReader<PaySettlementBO> reader = new JdbcPagingItemReader<>();
        reader.setDataSource(dataSource);
        String checkDateOne = DateTimeUtils.formatLocalDate(DateTimeUtils.parseLocalDate(checkDate).plusDays(1));
        SqlPagingQueryProviderFactoryBean queryProvider = new SqlPagingQueryProviderFactoryBean();
        queryProvider.setDataSource(dataSource);
        queryProvider.setSelectClause("select trade_jrn_no, out_order_no, refund_order_no, order_amount,settlement_type,bank_order_no,   pay_way, scene, bus_type, account_date, order_complete_time");
        queryProvider.setFromClause("from pay_settlement");
        queryProvider.setWhereClause("where  settlement_date >=:checkDate and settlement_date <=:checkDateOne and channel_code =:channelCode and account_date =:checkDate");
        queryProvider.setSortKey("out_order_no");
        reader.setQueryProvider(queryProvider.getObject());
        Map<String, Object> parameterValues = new HashMap<>(8);
        parameterValues.put("channelCode", channelCode);
        parameterValues.put("checkDate", checkDate);
        parameterValues.put("checkDateOne", checkDateOne);
        reader.setParameterValues(parameterValues);
        reader.setPageSize(CheckFileConstant.SETTLEMENT_READ_SIZE);
        reader.setRowMapper((rs, number) -> {
            PaySettlementBO settlementBO = new PaySettlementBO();
            settlementBO.setTradeJrnNo(rs.getString("trade_jrn_no"));
            settlementBO.setOutOrderNo(rs.getString("out_order_no"));
            settlementBO.setRefundOrderNo(rs.getString("refund_order_no"));
            settlementBO.setOrderAmount(rs.getBigDecimal("order_amount"));
            settlementBO.setSettlementType(rs.getString("settlement_type"));
            settlementBO.setPayWay(rs.getString("pay_way"));
            settlementBO.setScene(rs.getString("scene"));
            settlementBO.setBusType(rs.getString("bus_type"));
            settlementBO.setAccountDate(rs.getString("account_date"));
            settlementBO.setOrderCompleteTime(rs.getString("order_complete_time"));
            settlementBO.setBankOrderNo(rs.getString("bank_order_no"));
            return settlementBO;
        });
        return reader;
    }

    @Bean
    public JobExecutionListener jobListenerWriter() {
        return new JobExecutionListener() {
            private long startTime;

            @Override
            public void beforeJob(JobExecution jobExecution) {
                startTime = System.currentTimeMillis();
                log.info("生成支付中台对账文件任务处理开始=====");
                String channelCode = jobExecution.getJobParameters().getString("channelCode");
                String checkDate = jobExecution.getJobParameters().getString("checkDate");
                Long gridSize = jobExecution.getJobParameters().getLong("gridSize");
                try {
                    settlementDataWriter.before(channelCode, checkDate, gridSize);
                } catch (Exception e) {
                    log.error("生成支付中台对账文件前处理异常:{}", e.getCause());
                    ExceptionHandlerUtil.throwRequestDcepError(e);
                }
            }

            @Override
            public void afterJob(JobExecution jobExecution) {
                long endTime = System.currentTimeMillis();
                log.info("生成支付中台对账文件任务处理结,耗时：{}", (endTime - startTime) + "ms");
                String channelCode = jobExecution.getJobParameters().getString("channelCode");
                String checkDate = jobExecution.getJobParameters().getString("checkDate");
                Long gridSize = jobExecution.getJobParameters().getLong("gridSize");
                try {
                    settlementDataWriter.close(channelCode, checkDate, gridSize);
                } catch (Exception e) {
                    log.error("生成支付中台对账文件后处理异常:{}", e.getCause());
                    ExceptionHandlerUtil.throwRequestDcepError(e);
                }
            }
        };
    }

}

