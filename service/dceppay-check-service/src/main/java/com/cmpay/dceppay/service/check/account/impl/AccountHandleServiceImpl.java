package com.cmpay.dceppay.service.check.account.impl;

import com.cmpay.dceppay.bo.account.AccountTreatmentCancelBO;
import com.cmpay.dceppay.bo.account.AccountTreatmentReverseBO;
import com.cmpay.dceppay.bo.pay.PayAccountRecordBO;
import com.cmpay.dceppay.enums.MsgCodeEnum;
import com.cmpay.dceppay.enums.pay.AccountProcessStatusEnum;
import com.cmpay.dceppay.outclient.igw.IAccountClientService;
import com.cmpay.dceppay.service.check.account.IAccountHandleService;
import com.cmpay.dceppay.service.pay.IPayAccountRecordDBService;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 2024/9/30 10:09
 */
@Service
public class AccountHandleServiceImpl implements IAccountHandleService {
    @Autowired
    private IAccountClientService accountClientService;
    @Autowired
    private IPayAccountRecordDBService payAccountRecordDBService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void accountCancel(AccountTreatmentCancelBO cancelBO) {
        processAccount(cancelBO.getJrnNo(), AccountProcessStatusEnum.SUCCESS,
                () -> accountClientService.accountCancel(cancelBO),
                MsgCodeEnum.ACCOUNT_CANCEL_ERROR,
                MsgCodeEnum.ACCOUNT_CANCEL_STATUS_ERROR);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void accountReserve(AccountTreatmentReverseBO reverseBO) {
        processAccount(reverseBO.getJrnNo(), AccountProcessStatusEnum.SUCCESS,
                () -> accountClientService.accountReverse(reverseBO),
                MsgCodeEnum.ACCOUNT_REVERSE_ERROR,
                MsgCodeEnum.ACCOUNT_RESERVE_STATUS_ERROR);
    }

    private void processAccount(String jrnNo, AccountProcessStatusEnum expectedStatus,
                                Runnable action, MsgCodeEnum actionError, MsgCodeEnum statusError) {
        PayAccountRecordBO accountRecordBO = payAccountRecordDBService.getByJrnNo(jrnNo);
        String handleStatus = accountRecordBO.getHandleStatus();
        if (JudgeUtils.notEquals(handleStatus, expectedStatus.name())) {
            BusinessException.throwBusinessException(statusError);
        }
        try {
            action.run();
            payAccountRecordDBService.updateWaitDeal(jrnNo);
        } catch (Exception e) {
            BusinessException.throwBusinessException(actionError);
        }
    }
}
