package com.cmpay.dceppay.service.check.writer;

import com.cmpay.dceppay.Utils.CheckFileNameUtils;
import com.cmpay.dceppay.constant.check.CheckFileConstant;
import com.univocity.parsers.csv.CsvWriter;
import com.univocity.parsers.csv.CsvWriterSettings;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * <AUTHOR>
 * @date 2024/9/21 15:28
 */
@Data
@Slf4j
public class WriterBO {
    public WriterBO(String channelCode, CsvWriterSettings settings, String checkDate, int gridSize) throws IOException {
        this.channelCode = channelCode;
        this.settings = settings;
        this.checkDate = checkDate;

        this.detailFileName = CheckFileNameUtils.getDetailIndexFileName(checkDate, gridSize);
        this.writerDetailPath = CheckFileConstant.CHECK_FILE_LOCAL_PATH + checkDate + File.separator + detailFileName;
        this.writerDetail = instanceWriter(writerDetailPath);

        this.totalPayNumber = 0;
        this.totalPayAmount = new BigDecimal("0");
        this.totalRefundNumber = 0;
        this.totalRefundAmount = new BigDecimal("0");
    }

    public CsvWriter instanceWriter(String path) throws IOException {
        // 使用Paths.get来创建Path对象
        Path directory = Paths.get(path);
        // 使用Files.createDirectories来创建路径，如果路径已经存在则不执行任何操作
        // createDirectories会创建多级目录，如果中间的某个目录不存在，它也会被创建
        Files.createDirectories(directory.getParent());
        return new CsvWriter(new FileWriter(path), settings);
    }

    //渠道编码
    private String channelCode;
    //账期
    private String checkDate;
    //文件格式
    private CsvWriterSettings settings;
    //对账单明细文件
    private CsvWriter writerDetail;
    //对账单index文件
    private CsvWriter writerIndex;
    //对账单汇总文件
    private CsvWriter writerSummary;
    //明细文件路径
    private String writerDetailPath;
    //index 文件路径
    private String writerIndexPath;
    //汇总文件路径
    private String writerSummaryPath;
    //明细文件名
    private String detailFileName;
    //index 文件名
    private String indexFileName;
    //汇总文件名
    private String summaryFileName;
    //支付总笔数
    private int totalPayNumber;
    //支付总金额
    private BigDecimal totalPayAmount;
    //退款总笔数
    private int totalRefundNumber;
    //退款总金额
    private BigDecimal totalRefundAmount;

    public void writerSummaryFile() throws IOException {
        summaryFileName = CheckFileNameUtils.getSummaryFileName(checkDate);
        writerSummaryPath = CheckFileConstant.CHECK_FILE_LOCAL_PATH + checkDate + File.separator + summaryFileName;
        writerSummary = instanceWriter(writerSummaryPath);
    }

    public void writerIndexFile() throws IOException {
        indexFileName = CheckFileNameUtils.getIndexFileName(checkDate);
        writerIndexPath = CheckFileConstant.CHECK_FILE_LOCAL_PATH + checkDate + File.separator + indexFileName;
        writerIndex = instanceWriter(writerIndexPath);
    }
}
