package com.cmpay.dceppay.batch.config;

import com.cmpay.dceppay.bo.check.CheckFileDetailBO;
import com.cmpay.dceppay.bo.pay.PaySettlementBO;
import com.cmpay.dceppay.constant.check.CheckFileConstant;
import com.cmpay.dceppay.service.check.ICheckFileDetailService;
import com.cmpay.dceppay.service.check.ICheckFileProcessDBService;
import com.cmpay.dceppay.service.check.reconciliation.ICheckService;
import com.cmpay.dceppay.service.check.reconciliation.IDoubleOrderCheckService;
import com.cmpay.dceppay.service.check.writer.NoOpItemWriter;
import com.cmpay.lemon.common.utils.JudgeUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.JobExecutionListener;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.batch.item.ItemReader;
import org.springframework.batch.item.database.JdbcPagingItemReader;
import org.springframework.batch.item.database.support.SqlPagingQueryProviderFactoryBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/9/24 15:25
 */
@Configuration
@Slf4j
public class ReconciliationBatchConfig {
    private static int flag = 0;


    @Autowired
    private StepBuilderFactory stepBuilderFactory;
    @Autowired
    private DataSource dataSource;
    @Autowired
    private NoOpItemWriter<PaySettlementBO> noOpItemWriter;

    @Autowired
    @Qualifier("pagingItemReaderReconciliation")
    ItemReader<CheckFileDetailBO> pagingItemReaderReconciliation;
    @Autowired
    @Qualifier("itemProcessorReconciliation")
    ItemProcessor<CheckFileDetailBO, PaySettlementBO> itemProcessorReconciliation;
    @Autowired
    private ICheckService checkService;
    @Autowired
    private ICheckFileProcessDBService checkFileProcessService;
    @Autowired
    private ICheckFileDetailService checkFileDetailService;
    @Autowired
    private IDoubleOrderCheckService doubleOrderCheckService;

    @Bean
    public Step partitionerStepReconciliation() {
        return stepBuilderFactory.get("partitionerStepReconciliation")
                .<CheckFileDetailBO, PaySettlementBO>chunk(1000)
                .reader(pagingItemReaderReconciliation)
                .processor(itemProcessorReconciliation)
                .writer(noOpItemWriter)
                .build();
    }

    @Bean
    @Primary
    @StepScope
    public ItemProcessor<CheckFileDetailBO, PaySettlementBO> itemProcessorReconciliation(@Value("#{stepExecutionContext[checkDate]}") String checkDate) {
        return item -> {
            PaySettlementBO settlementBO = new PaySettlementBO();
            try {
                checkService.check(item, checkDate);
            } catch (Exception e) {
                flag = 1;
                log.error("item check error:{}", e.getCause());
            }
            return settlementBO;
        };
    }


    @Bean
    @Primary
    @StepScope
    public ItemReader<CheckFileDetailBO> pagingItemReaderReconciliation(@Value("#{stepExecutionContext['checkDate']}") String checkDate
    ) throws Exception {
        JdbcPagingItemReader<CheckFileDetailBO> reader = new JdbcPagingItemReader<>();
        reader.setDataSource(dataSource);
        SqlPagingQueryProviderFactoryBean queryProvider = new SqlPagingQueryProviderFactoryBean();
        queryProvider.setDataSource(dataSource);
        queryProvider.setSelectClause("select out_order_no, refund_order_no, check_file_date , order_amount, check_type, bank_order_no, bank_refund_no, order_status,order_complete_time");
        queryProvider.setFromClause("from check_file_detail");
        queryProvider.setWhereClause("where check_file_date = :checkDate and check_status='wait'");
        queryProvider.setSortKey("out_order_no");
        reader.setQueryProvider(Objects.requireNonNull(queryProvider.getObject()));
        Map<String, Object> parameterValues = new HashMap<>(16);
        parameterValues.put("checkDate", checkDate);
        reader.setParameterValues(parameterValues);
        reader.setPageSize(CheckFileConstant.DETAIL_READ_SIZE);
        reader.setRowMapper((rs, number) -> {
            CheckFileDetailBO checkFileDetailBO = new CheckFileDetailBO();
            checkFileDetailBO.setOutOrderNo(rs.getString("out_order_no"));
            checkFileDetailBO.setRefundOrderNo(rs.getString("refund_order_no"));
            checkFileDetailBO.setCheckFileDate(rs.getString("check_file_date"));
            checkFileDetailBO.setOrderAmount(rs.getBigDecimal("order_amount"));
            checkFileDetailBO.setCheckType(rs.getString("check_type"));
            checkFileDetailBO.setBankOrderNo(rs.getString("bank_order_no"));
            checkFileDetailBO.setBankRefundNo(rs.getString("bank_refund_no"));
            checkFileDetailBO.setOrderStatus(rs.getString("order_status"));
            checkFileDetailBO.setOrderCompleteTime(rs.getString("order_complete_time"));
            return checkFileDetailBO;
        });

        return reader;
    }

    @Bean
    @Primary
    public JobExecutionListener jobListenerReconciliation() {
        return new JobExecutionListener() {
            private long startTime;

            @Override
            public void beforeJob(JobExecution jobExecution) {
                flag = 0;
                startTime = System.currentTimeMillis();
                log.info("对账任务处理开始");
                String checkDate = jobExecution.getJobParameters().getString("checkDate");
                String institutionCode = jobExecution.getJobParameters().getString("institutionCode");
                checkFileProcessService.updateCheckBegin(checkDate, institutionCode);
            }

            @Override
            public void afterJob(JobExecution jobExecution) {
                long endTime = System.currentTimeMillis();
                log.info("对账任务处理结束,耗时：{}ms", (endTime - startTime));
                if (flag == 0) {
                    String checkDate = jobExecution.getJobParameters().getString("checkDate");
                    String institutionCode = jobExecution.getJobParameters().getString("institutionCode");
                    //查询当前批次对账文件表中是否存在未对账数据
                    List<CheckFileDetailBO> checkFileDetailBOList = checkFileDetailService.listWaitDetailBO(checkDate);
                    if (JudgeUtils.isNotNull(checkFileDetailBOList)) {
                        log.info("对账文件明细表中存在等待对账的数据，对账未完成");
                        return;
                    }
                    checkFileProcessService.updateCheckEnd(checkDate, institutionCode);
                    try {
                        //存疑订单对平
                        doubleOrderCheckService.doubleComplete(checkDate);
                        //将当日存疑加入差错表
                        doubleOrderCheckService.addDoubleOrder(checkDate);
                    } catch (Exception e) {
                        log.error("insert double-table error", e.getCause());
                    }
                }
            }
        };
    }

}
