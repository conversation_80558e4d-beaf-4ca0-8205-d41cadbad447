package com.cmpay.dceppay.batch.check;

import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.core.partition.support.Partitioner;
import org.springframework.batch.item.ExecutionContext;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/9/18 10:09
 */
@Slf4j
public class GridPartitioner implements Partitioner {
    private int pageSize;
    private String checkDate;
    private String institutionCode;

    public GridPartitioner(int pageSize, String checkDate, String institutionCode) {
        this.pageSize = pageSize;
        this.checkDate = checkDate;
        this.institutionCode = institutionCode;
    }

    @Override
    public Map<String, ExecutionContext> partition(int gridSize) {
        Map<String, ExecutionContext> result = new HashMap<>(8);
        for (int i = 0; i < gridSize; i++) {
            ExecutionContext value = new ExecutionContext();
            log.info("Starting : Thread {}", i);
            value.putInt("indexId", i);
            value.putInt("pageSize", pageSize);
            value.putString("checkDate", checkDate);
            value.putString("institutionCode", institutionCode);
            result.put("partition" + i, value);
        }
        return result;

    }
}
