package com.cmpay.dceppay.service.check.account;

import com.cmpay.dceppay.bo.account.AccountTreatmentCancelBO;
import com.cmpay.dceppay.bo.account.AccountTreatmentReverseBO;

/**
 * <AUTHOR>
 * @date 2024/9/30 10:07
 */
public interface IAccountHandleService {
    /**
     * 账务冲正
     * @param cancelBO
     */
    void accountCancel(AccountTreatmentCancelBO cancelBO);

    /**
     * 账务撤销
     * @param reverseBO
     */
    void accountReserve(AccountTreatmentReverseBO reverseBO);
}
