package com.cmpay.dceppay.service.check.download;

import com.cmpay.dceppay.bo.CheckFileDownloadBO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/23 11:47
 */
public interface ICheckFileDownloadService {
    /**
     * 获取对账文件下载标志
     *
     * @param accountDate
     * @return
     */
    CheckFileDownloadBO getDownloadFlag(String accountDate,String institutionCode);

    /**
     * 获取对账文件
     *
     * @param downloadBO
     * @return 对账明细文件列表
     */
    List<String> download(CheckFileDownloadBO downloadBO);

    List<String> downLoadCheckFile(CheckFileDownloadBO downloadBO);
}
