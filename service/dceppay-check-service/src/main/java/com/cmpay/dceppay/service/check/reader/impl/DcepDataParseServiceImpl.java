package com.cmpay.dceppay.service.check.reader.impl;

import com.cmpay.dceppay.bo.CheckFileBO;
import com.cmpay.dceppay.bo.check.CheckFileDetailBO;
import com.cmpay.dceppay.service.check.reader.IDcepDataParseService;
import com.cmpay.dceppay.util.DateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/9/23 18:59
 */
@Service
@Slf4j
public class DcepDataParseServiceImpl implements IDcepDataParseService {
    @Override
    public CheckFileDetailBO parseCheckFileContent(String[] data, CheckFileBO fileBO) {
        CheckFileDetailBO checkFileDetailBO = new CheckFileDetailBO();
        //交易完成时间
        checkFileDetailBO.setOrderCompleteTime(DateTimeUtil.changeISODateTime(data[0]));
        //报文编号
        checkFileDetailBO.setMessageNumber(data[1]);
        //报文标识号
        checkFileDetailBO.setMessageIdentifier(data[2]);
        //受理服务机构编码
        checkFileDetailBO.setAcceptInstitutionCode(data[3]);
        //付款运营机构编码
        checkFileDetailBO.setPayInstitutionCode(data[4]);
        //收款运营机构编码
        checkFileDetailBO.setReceiveInstitutionCode(data[5]);
        //货币代码
        checkFileDetailBO.setCurrencyCode(data[6]);
        //金额
        checkFileDetailBO.setOrderAmount(new BigDecimal(data[7]));
        //受理订单号
        checkFileDetailBO.setOutOrderNo(data[8]);
        //订单号
        checkFileDetailBO.setBankOrderNo(data[9]);
        //受理退款订单号
        checkFileDetailBO.setRefundOrderNo(data[10]);
        //退款订单号
        checkFileDetailBO.setBankRefundNo(data[11]);
        //业务状态
        checkFileDetailBO.setOrderStatus(data[12]);
        if (data.length == 14) {
            //交易描述信息
            checkFileDetailBO.setTradeDesc(data[13]);
        }
        checkFileDetailBO.setCheckFileDate(fileBO.getCheckFileDate());
        return checkFileDetailBO;
    }
}
