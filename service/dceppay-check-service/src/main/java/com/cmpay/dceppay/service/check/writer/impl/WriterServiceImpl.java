package com.cmpay.dceppay.service.check.writer.impl;

import com.cmpay.dceppay.Utils.CheckFileNameUtils;
import com.cmpay.dceppay.batch.check.WriterPartitioner;
import com.cmpay.dceppay.constant.check.CheckFileConstant;
import com.cmpay.dceppay.constant.check.SftpConstant;
import com.cmpay.dceppay.enums.MsgCodeEnum;
import com.cmpay.dceppay.properties.CmpaySFTProperties;
import com.cmpay.dceppay.service.check.ext.ICheckGenFileStatusService;
import com.cmpay.dceppay.service.check.writer.IWriterService;
import com.cmpay.dceppay.util.DateTimeUtil;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.file.IFileSystem;
import com.cmpay.lemon.file.entity.SFTPFileEnv;
import com.cmpay.lemon.framework.lock.DistributedLocked;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.*;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.batch.core.repository.JobExecutionAlreadyRunningException;
import org.springframework.batch.core.repository.JobInstanceAlreadyCompleteException;
import org.springframework.batch.core.repository.JobRestartException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.task.SimpleAsyncTaskExecutor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/18 9:03
 */
@Service
@Slf4j
public class WriterServiceImpl implements IWriterService {
    @Autowired
    private ICheckGenFileStatusService checkGenFileStatusService;
    @Autowired
    private CmpaySFTProperties sftProperties;
    @Autowired
    private IFileSystem fileSystem;


    @Autowired
    @Qualifier("jobBeanLauncher")
    private JobLauncher jobBeanLauncher;
    @Autowired
    private JobBuilderFactory jobBuilderFactory;
    @Autowired
    private StepBuilderFactory stepBuilderFactory;
    @Autowired
    @Qualifier("partitionerStepWriter")
    private Step partitionerStep;
    @Autowired
    @Qualifier("jobListenerWriter")
    private JobExecutionListener jobListener;

    @SneakyThrows
    @Override
    @DistributedLocked(lockName = "'genPayCenterFile'", leaseTime = 21600, waitTime = 10)
    public void genCheckFile(String accountDate, String channelCode, boolean reGenFile) {
        log.info("genPayCenterFileStartTime:{}", DateTimeUtil.getCurrentDateTimeStr());
        if (!reGenFile && checkGenFileStatusService.fileSendSuccess(accountDate, channelCode)) {
            log.info("genPayCenterFileAlreadyFinish");
            return;
        }
        try {
            checkGenFileStatusService.addCheckGenFileStatus(accountDate, channelCode);
            //开始任务
            Job partitionerJob = jobBuilderFactory.get("partitionerJobWriter")
                    .incrementer(new RunIdIncrementer())
                    .start(stepBuilderFactory.get("partitionerStepWriter")
                            .partitioner("rangePartitionerWriter", new WriterPartitioner(accountDate, channelCode))
                            .step(partitionerStep)
                            .gridSize(CheckFileConstant.CHECK_FILE_GRID_SIZE)
                            .taskExecutor(new SimpleAsyncTaskExecutor())
                            .build()).listener(jobListener)
                    .build();
            JobParameters jobParameters = new JobParametersBuilder()
                    .addLong("time", System.currentTimeMillis())
                    .addString("checkDate", accountDate)
                    .addString("channelCode", channelCode)
                    .addLong("gridSize", (long) CheckFileConstant.CHECK_FILE_GRID_SIZE)
                    .toJobParameters();
            jobBeanLauncher.run(partitionerJob, jobParameters);
            log.info("genPayCenterFileEndTime:{}", DateTimeUtil.getCurrentDateTimeStr());
        } catch (JobExecutionAlreadyRunningException e) {
            log.error("genPayCenterFile任务正常在运行：{}", e.getCause());
        } catch (JobRestartException e) {
            log.error("genPayCenterFile任务重新运行：{}", e.getCause());
        } catch (JobInstanceAlreadyCompleteException e) {
            log.error("genPayCenterFile任务已经完成：{}", e.getCause());
        } catch (JobParametersInvalidException e) {
            log.error("genPayCenterFile任务参数非法：{}", e.getCause());
        }
    }

    @Override
    public void pushFileToPayCenter(String date) {
        List<String> fileNameList = new ArrayList<>();
        fileNameList.add(CheckFileNameUtils.getDetailFileName(date));
        fileNameList.add(CheckFileNameUtils.getIndexFileName(date));
        fileNameList.add(CheckFileNameUtils.getSummaryFileName(date));
        boolean upFlag;
        if (JudgeUtils.isNotBlank(sftProperties.getSftpUserPasswd())) {
            SFTPFileEnv indexEnv = new SFTPFileEnv(sftProperties.getSftpUrl(), sftProperties.getSftpUsername(), sftProperties.getSftpUserPasswd(),
                    sftProperties.getFileRemotePath(), sftProperties.getLocalDataPath() + date, fileNameList,
                    SftpConstant.SFTP_TIME_OUT, SftpConstant.SFTP_ENCODE, SftpConstant.UPLOAD_TYPE);
            upFlag = fileSystem.process(indexEnv);
        } else {
            SFTPFileEnv billFile = new SFTPFileEnv(
                    sftProperties.getSftpUrl(), sftProperties.getSftpUsername(), sftProperties.getFileRemotePath(),
                    sftProperties.getLocalDataPath() + date, fileNameList, SftpConstant.SFTP_TIME_OUT,
                    SftpConstant.SFTP_ENCODE, SftpConstant.UPLOAD_TYPE, sftProperties.getIdentityKey(), null);
            upFlag = fileSystem.process(billFile);
        }
        if (!upFlag) {
            BusinessException.throwBusinessException(MsgCodeEnum.CHECK_FILE_UPLOAD_FAILED);
        }
    }

}
