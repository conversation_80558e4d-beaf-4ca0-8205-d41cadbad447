package com.cmpay.dceppay.service.check.writer.impl;

import com.cmpay.dceppay.enums.MsgCodeEnum;
import com.cmpay.dceppay.service.check.writer.CheckFileRePuhService;
import com.cmpay.dceppay.service.check.writer.IWriterService;
import com.cmpay.lemon.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/10/23 16:20
 */
@Slf4j
@Service
public class CheckFileRePuhServiceImpl implements CheckFileRePuhService {
    @Autowired
    private IWriterService writerService;

    @Override
    public void reGenAndPush(String checkDate, String channelCode) {
        writerService.genCheckFile(checkDate, channelCode, true);
    }

    @Override
    public void onlyRePush(String checkDate) {
        writerService.pushFileToPayCenter(checkDate);
    }

    @Override
    public void genCheckFile(String checkDate, String channelCode, boolean reGenFile) {
        try {
            if (reGenFile) {
                reGenAndPush(checkDate, channelCode);
            } else {
                onlyRePush(checkDate);
            }
        } catch (Exception e) {
            log.error("生成对账文件失败：{}",e.getCause());
            BusinessException.throwBusinessException(MsgCodeEnum.CHECK_FILE_RE_PUSH_FAILED);
        }
    }
}
