package com.cmpay.dceppay.Utils;

import com.cmpay.dceppay.constant.check.CheckFileConstant;
import com.cmpay.dceppay.constant.common.SeparatorConstants;
import com.cmpay.lemon.common.utils.StringUtils;

/**
 * <AUTHOR>
 * @date 2024/10/23 16:04
 */
public class CheckFileNameUtils {
    public static String getDetailIndexFileName(String checkDate, int gridSize) {
        return CheckFileConstant.PAY_CENTER_CHECK_FILE_PREFIX + SeparatorConstants.UNDER_LINE + checkDate + SeparatorConstants.UNDER_LINE
                + StringUtils.leftPad(String.valueOf(gridSize + 1), 2, "0") + CheckFileConstant.CHECK_DETAIL_FILE_SUFFIX;
    }

    public static String getSummaryFileName(String checkDate) {
        return CheckFileConstant.PAY_CENTER_CHECK_FILE_PREFIX + SeparatorConstants.UNDER_LINE + checkDate + CheckFileConstant.CHECK_SUMMARY_FILE_SUFFIX;
    }

    public static String getIndexFileName(String checkDate) {
        return CheckFileConstant.PAY_CENTER_CHECK_FILE_PREFIX + SeparatorConstants.UNDER_LINE + checkDate + CheckFileConstant.CHECK_INDEX_FILE_SUFFIX;
    }

    public static String getDetailFileName(String checkDate) {
        return CheckFileConstant.PAY_CENTER_CHECK_FILE_PREFIX + SeparatorConstants.UNDER_LINE + checkDate + CheckFileConstant.CHECK_DETAIL_FILE_SUFFIX;
    }
}
