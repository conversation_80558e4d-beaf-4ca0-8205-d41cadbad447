package com.cmpay.dceppay.service.check.writer.impl;

import com.cmpay.dceppay.bo.check.CheckGenFileStatusBO;
import com.cmpay.dceppay.bo.pay.PaySettlementBO;
import com.cmpay.dceppay.constant.check.CheckFileConstant;
import com.cmpay.dceppay.constant.check.SftpConstant;
import com.cmpay.dceppay.constant.common.SeparatorConstants;
import com.cmpay.dceppay.enums.MsgCodeEnum;
import com.cmpay.dceppay.enums.pay.OrderTypeEnum;
import com.cmpay.dceppay.properties.CmpaySFTProperties;
import com.cmpay.dceppay.service.check.ICheckGenFileStatusDBService;
import com.cmpay.dceppay.service.check.writer.IDataWriter;
import com.cmpay.dceppay.service.check.writer.WriterBO;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.file.IFileSystem;
import com.cmpay.lemon.file.entity.SFTPFileEnv;
import com.univocity.parsers.csv.CsvWriterSettings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.channels.FileChannel;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2024/9/21 13:59
 */
@Component
@Slf4j
public class SettlementDataWriter implements IDataWriter<PaySettlementBO, String> {
    private static final Map<String, List<WriterBO>> writers = new ConcurrentHashMap<>();
    @Autowired
    private ICheckGenFileStatusDBService checkGenFileStatusService;
    @Autowired
    private CmpaySFTProperties sftProperties;
    @Autowired
    private IFileSystem fileSystem;

    @Override
    public List<String> data(PaySettlementBO settlementBO, String channelCode, String checkDate, String indexId) {
        try {
            WriterBO writer = writers.get(channelCode).get(Integer.parseInt(indexId));
            List<String> data = getDetailInfo(settlementBO, checkDate);
            //写入一行明细
            writer.getWriterDetail().writeRow(data);
            //计数
            summaryCheckAmount(settlementBO, writer);
            return data;
        } catch (NumberFormatException e) {
            log.error("生成对账文件异常，数字转换失败");
            BusinessException.throwBusinessException(MsgCodeEnum.WRITE_CHECK_FILE_ERROR);
        }
        return null;
    }


    @Override
    public void before(String channelCode, String checkDate, Long gridSize) {
        try {
            writers.remove(channelCode);
            CsvWriterSettings settings = new CsvWriterSettings();
            List<WriterBO> writerList = new ArrayList<>();
            for (int i = 0; i < gridSize; i++) {
                writerList.add(new WriterBO(channelCode, settings, checkDate, i));
            }
            writers.put(channelCode, writerList);
        } catch (IOException e) {
            BusinessException.throwBusinessException(MsgCodeEnum.WRITE_CHECK_FILE_BEFORE_ERROR);
        }
    }

    @Override
    public void close(String channelCode, String date, Long gridSize) {
        try {
            List<WriterBO> writerList = writers.get(channelCode);
            WriterBO writerResult = genResultFile(gridSize, writerList);
            genSummaryAndIndexFile(writerResult);
            writers.remove(channelCode);
            checkGenFileStatusService.updateFileGenFinish(buildGenFileStatusBO(date, channelCode, writerResult));
            uploadSftp(writerResult, channelCode, date);
        } catch (Exception e) {
            BusinessException.throwBusinessException(MsgCodeEnum.WRITE_CHECK_FILE_AFTER_ERROR);
        }
    }

    private void genSummaryAndIndexFile(WriterBO writerResult) throws Exception {
        String detailFilePath = writerResult.getWriterDetailPath();
        Path path = Paths.get(detailFilePath);
        String appendContent = CheckFileConstant.PAY_CENTER_CHECK_FILE_LAST_LINE;
        Files.write(path, appendContent.getBytes(), StandardOpenOption.APPEND);
        File file = new File(detailFilePath);
        String newDetailFilePath = detailFilePath.replace("_01", "");
        File newFile = new File(newDetailFilePath);

        // 检查目标文件是否存在
        if (newFile.exists()) {
            // 删除目标文件
            boolean success = newFile.delete();
            if (!success) {
                log.error("生成对账文件失败：删除目标文件失败");
                BusinessException.throwBusinessException(MsgCodeEnum.WRITE_CHECK_FILE_ERROR);
            }
        }
        if (!file.renameTo(newFile)) {
            log.error("生成对账文件失败：重命名文件失败");
            BusinessException.throwBusinessException(MsgCodeEnum.WRITE_CHECK_FILE_ERROR);
        }
        writerResult.setWriterDetailPath(newDetailFilePath);
        writerResult.setDetailFileName(newFile.getName());
        summaryFile(writerResult);
        indexFile(writerResult);
    }

    private static WriterBO genResultFile(Long gridSize, List<WriterBO> writerList) throws IOException {
        WriterBO writerResult = writerList.get(0);
        writerResult.getWriterDetail().flush();
        for (int i = 1; i < gridSize; i++) {
            WriterBO writer = writerList.get(i);
            writer.getWriterDetail().flush();
            writer.getWriterDetail().close();
            FileChannel resultFileChannel = new FileOutputStream(writerResult.getWriterDetailPath(), true).getChannel();
            FileChannel inFileChannel = new FileInputStream(writer.getWriterDetailPath()).getChannel();
            resultFileChannel.transferFrom(inFileChannel, resultFileChannel.size(), inFileChannel.size());
            inFileChannel.close();
            resultFileChannel.close();
            writerResult.setTotalPayNumber(writerResult.getTotalPayNumber() + writer.getTotalPayNumber());
            writerResult.setTotalPayAmount(writerResult.getTotalPayAmount().add(writer.getTotalPayAmount()));
            writerResult.setTotalRefundNumber(writerResult.getTotalRefundNumber() + writer.getTotalRefundNumber());
            writerResult.setTotalRefundAmount(writerResult.getTotalRefundAmount().add(writer.getTotalRefundAmount()));
            File file = new File(writer.getWriterDetailPath());
            file.delete();
        }
        writerResult.getWriterDetail().close();
        return writerResult;
    }


    private static List<String> getDetailInfo(PaySettlementBO settlementBO, String checkDate) {
        List<String> data = new ArrayList<>();
        if (JudgeUtils.equals(settlementBO.getSettlementType(), OrderTypeEnum.PAYMENT.name())) {
            //交易订单号
            data.add(settlementBO.getTradeJrnNo());
        } else if (JudgeUtils.equals(settlementBO.getSettlementType(), OrderTypeEnum.REFUND.name())) {
            //退款订单号
            data.add(settlementBO.getRefundOrderNo());
        }
        // 账单日期
        data.add(checkDate);
        data.add(settlementBO.getOutOrderNo());
        if (JudgeUtils.equals(settlementBO.getSettlementType(), OrderTypeEnum.PAYMENT.name())) {
            //交易订单号
            data.add(settlementBO.getBankOrderNo());
        } else if (JudgeUtils.equals(settlementBO.getSettlementType(), OrderTypeEnum.REFUND.name())) {
            //退款订单号
            data.add(settlementBO.getRefundOrderNo());
        }
        //订单总金额
        data.add(settlementBO.getOrderAmount().toString());
        data.add(CheckFileConstant.SETTLEMENT_SUCCESS);
        return data;
    }

    private static void summaryCheckAmount(PaySettlementBO settlementBO, WriterBO writer) {
        if (StringUtils.equalsIgnoreCase(settlementBO.getSettlementType(), OrderTypeEnum.PAYMENT.name())) {
            writer.setTotalPayNumber(writer.getTotalPayNumber() + 1);
            writer.setTotalPayAmount(settlementBO.getOrderAmount() == null ? new BigDecimal("0") : settlementBO.getOrderAmount().add(writer.getTotalPayAmount()));
        } else if (StringUtils.equalsIgnoreCase(settlementBO.getSettlementType(), OrderTypeEnum.PAYMENT.name())) {
            writer.setTotalRefundNumber(writer.getTotalRefundNumber() + 1);
            writer.setTotalRefundAmount(settlementBO.getOrderAmount() == null ? new BigDecimal("0") : settlementBO.getOrderAmount().add(writer.getTotalRefundAmount()));
        }
    }

    protected void summaryFile(WriterBO writer) throws IOException {
        writer.writerSummaryFile();
        List<String> summaryFile = new ArrayList<>();
        int totalNumber = writer.getTotalPayNumber() + writer.getTotalRefundNumber();
        summaryFile.add(String.valueOf(totalNumber));
        summaryFile.add(String.valueOf(totalNumber));
        summaryFile.add(writer.getTotalPayAmount().add(writer.getTotalRefundAmount()).toString());
        writer.getWriterSummary().writeRow(summaryFile);
        summaryFile.clear();
        if (totalNumber != 0) {
            summaryFile.add(writer.getDetailFileName());
            summaryFile.add(String.valueOf(totalNumber));
            summaryFile.add(writer.getTotalPayAmount().add(writer.getTotalRefundAmount()).toString());
            writer.getWriterSummary().writeRow(summaryFile);
        }
        writer.getWriterSummary().close();
    }

    protected void indexFile(WriterBO writer) throws IOException {
        writer.writerIndexFile();
        writer.getWriterIndex().close();
    }

    protected void uploadSftp(WriterBO writer, String channelCode, String date) {
        if (JudgeUtils.isNotNull(channelCode)) {
            List<String> fileNameList = new ArrayList<>();
            fileNameList.add(writer.getIndexFileName());
            fileNameList.add(writer.getDetailFileName());
            fileNameList.add(writer.getSummaryFileName());
            boolean upFlag;
            if (JudgeUtils.isNotBlank(sftProperties.getSftpUserPasswd())) {
                SFTPFileEnv indexEnv = new SFTPFileEnv(sftProperties.getSftpUrl(), sftProperties.getSftpUsername(), sftProperties.getSftpUserPasswd(),
                        sftProperties.getFileRemotePath(), sftProperties.getLocalDataPath() + date, fileNameList,
                        SftpConstant.SFTP_TIME_OUT, SftpConstant.SFTP_ENCODE, SftpConstant.UPLOAD_TYPE);
                upFlag = fileSystem.process(indexEnv);
            } else {
                SFTPFileEnv billFile = new SFTPFileEnv(
                        sftProperties.getSftpUrl(), sftProperties.getSftpUsername(), sftProperties.getFileRemotePath(),
                        sftProperties.getLocalDataPath() + date, fileNameList, SftpConstant.SFTP_TIME_OUT,
                        SftpConstant.SFTP_ENCODE, SftpConstant.UPLOAD_TYPE, sftProperties.getIdentityKey(), null);
                upFlag = fileSystem.process(billFile);
            }
            if (!upFlag) {
                checkGenFileStatusService.updateFileSendFail(date, channelCode);
                BusinessException.throwBusinessException(MsgCodeEnum.CHECK_FILE_UPLOAD_FAILED);
            } else {
                checkGenFileStatusService.updateFileSendFinish(date, channelCode);
            }
        } else {
            checkGenFileStatusService.updateFileSendFail(date, channelCode);
        }

    }

    private CheckGenFileStatusBO buildGenFileStatusBO(String checkDate, String channelCode, WriterBO writerResult) {
        CheckGenFileStatusBO checkGenFileStatusBO = new CheckGenFileStatusBO();
        checkGenFileStatusBO.setChannelCode(channelCode);
        checkGenFileStatusBO.setCheckFileDate(checkDate);
        checkGenFileStatusBO.setTotalCount(writerResult.getTotalPayNumber() + writerResult.getTotalRefundNumber());
        checkGenFileStatusBO.setTotalAmount(writerResult.getTotalPayAmount().add(writerResult.getTotalRefundAmount()));
        checkGenFileStatusBO.setPaymentCount(writerResult.getTotalPayNumber());
        checkGenFileStatusBO.setPaymentAmount(writerResult.getTotalPayAmount());
        checkGenFileStatusBO.setRefundCount(writerResult.getTotalRefundNumber());
        checkGenFileStatusBO.setRefundAmount(writerResult.getTotalRefundAmount());
        return checkGenFileStatusBO;

    }
}
