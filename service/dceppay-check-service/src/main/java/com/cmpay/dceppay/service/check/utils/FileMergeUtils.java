package com.cmpay.dceppay.service.check.utils;

import org.apache.commons.io.FileUtils;

import java.io.*;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/21 15:58
 */
public class FileMergeUtils {
    public static List<String> extractMessage(String msg) {
        List<String> list = new ArrayList<String>();
        int start = 0;
        int startFlag = 0;
        int endFlag = 0;
        for (int i = 0; i < msg.length(); i++) {
            if (msg.charAt(i) == '[') {
                startFlag++;
                if (startFlag == endFlag + 1) {
                    start = i;
                }
            } else if (msg.charAt(i) == ']') {
                endFlag++;
                if (endFlag == startFlag) {
                    list.add(msg.substring(start + 1, i));
                }
            }
        }
        return list;
    }


    public static String getDate(String str) {
        String str2 = str.replace("-", "");
        return str2.substring(0, str2.indexOf(" "));
    }

    public static void insertStringInFile(File inFile, int lineno, String lineToBeInserted)
            throws Exception {
        // 临时文件
        File outFile = File.createTempFile(inFile.getName(), ".tmp");
        PrintWriter out = null;
        BufferedReader in = null;
        FileInputStream fis = null;
        FileOutputStream fos = null;
        try {
            // 输入
            fis = new FileInputStream(inFile);
            in = new BufferedReader(new InputStreamReader(fis));
            // 输出
            fos = new FileOutputStream(outFile);
            out = new PrintWriter(fos);
            // 保存一行数据
            String thisLine;
            // 行号从1开始
            int i = 1;
            if (i == lineno) {
                out.println(lineToBeInserted);
            }
            int totalLines = Integer.parseInt(lineToBeInserted.split("\\|")[0]);
            while ((thisLine = in.readLine()) != null) {
                // 如果行号等于目标行，则输出要插入的数据
                if (i == lineno && i != 1) {
                    out.println(lineToBeInserted);
                }

                if (i == totalLines) {
                    out.print(thisLine);
                } else {
                    out.println(thisLine);
                }

                // 行号增加
                i++;
            }
            out.flush();
        } finally {
            if (null != out) {
                out.close();
            }
            if (null != in) {
                try {
                    in.close();
                } catch (IOException e) {
                }
            }
            if (null != fis) {
                try {
                    fis.close();
                } catch (IOException e) {
                }
            }
            if (null != fos) {
                try {
                    fos.close();
                } catch (IOException e) {
                }
            }
            // 删除原始文件
            inFile.delete();
            // 把临时文件改名为原文件名
            FileUtils.moveFile(outFile, inFile);
        }
    }
}
