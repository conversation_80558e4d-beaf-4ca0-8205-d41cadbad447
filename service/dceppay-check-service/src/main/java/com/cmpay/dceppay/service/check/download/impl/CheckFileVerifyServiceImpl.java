package com.cmpay.dceppay.service.check.download.impl;

import com.cmpay.dceppay.bo.CheckDetailFileInfoBO;
import com.cmpay.dceppay.bo.CheckFileDownloadBO;
import com.cmpay.dceppay.bo.check.CheckFileDetailBO;
import com.cmpay.dceppay.bo.check.CheckFileVerifyResultBO;
import com.cmpay.dceppay.enums.check.CheckFileHandleEnum;
import com.cmpay.dceppay.properties.DcepBilUpSftpProperties;
import com.cmpay.dceppay.service.check.ICheckFileProcessDBService;
import com.cmpay.dceppay.service.check.ICheckFileVerifyDBService;
import com.cmpay.dceppay.service.check.download.ICheckFileVerifyService;
import com.cmpay.dceppay.service.check.download.IReadCheckDetailFileService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/24 9:59
 * 对账文件合法性校验业务处理类
 */
@Service
@Slf4j
public class CheckFileVerifyServiceImpl implements ICheckFileVerifyService {
    @Autowired
    private ICheckFileProcessDBService checkFileProcessDBService;

    @Autowired
    private ICheckFileVerifyDBService checkFileVerifyDBService;
    @Autowired
    private DcepBilUpSftpProperties bilUpSftpProperties;
    @Autowired
    private IReadCheckDetailFileService readCheckDetailFileService;


    @Override
    public boolean verifyCheckFile(CheckFileDownloadBO downloadBO, List<String> detailFileList) {
        CheckFileVerifyResultBO verifyResultBO = checkFile(downloadBO, detailFileList);
        if (!verifyResultBO.isVerifyPassFlag()) {
            checkFileProcessDBService.updateWaitConfirmInfo(downloadBO.getCheckFileDate(), downloadBO.getInstitutionCode());
            String ignoreDiffCount = checkFileVerifyDBService.queryCheckDiffFlag(downloadBO.getCheckFileDate(), downloadBO.getInstitutionCode());
            CheckFileHandleEnum fileHandleEnum = CheckFileHandleEnum.valueOf(ignoreDiffCount);
            switch (fileHandleEnum) {
                case DO_NOTHING:
                    return false;
                case RE_GET:
                    checkFileVerifyDBService.resetHandleFlag(downloadBO.getCheckFileDate(), downloadBO.getInstitutionCode());
                    return false;
                case IGNORE:
                    checkFileVerifyDBService.resetHandleFlag(downloadBO.getCheckFileDate(), downloadBO.getInstitutionCode());
                    return true;
                default:
                    break;
            }
        }
        return true;
    }

    private CheckFileVerifyResultBO checkFile(CheckFileDownloadBO downloadBO, List<String> detailFileList) {
        BigDecimal detailAmount = new BigDecimal(0);
        int detailCount = 0;
        CheckFileVerifyResultBO checkFileVerifyResultBO = new CheckFileVerifyResultBO();
        for (String detailFileName : detailFileList) {
            // 获取明细文件名
            String filePath = bilUpSftpProperties.getLocalDataPath() + downloadBO.getCheckFileDate() + File.separator + detailFileName;
            File detailFile = new File(filePath);
            if (!detailFile.exists()) {
                log.warn("【互联互通对账文件校验不通过】：对账文件不存在；对账日期：{},文件路径：{}",
                        downloadBO.getCheckFileDate(), filePath);
                checkFileVerifyResultBO.setVerifyPassFlag(true);
                return checkFileVerifyResultBO;
            }
            if (!readCheckDetailFileService.isLastLineEnd(filePath)) {
                log.error("【互联互通对账文件校验不通过】：对账明细文件末行不是<end>；对账日期：{},文件路径：{}",
                        downloadBO.getCheckFileDate(), detailFileName);
                checkFileVerifyResultBO.setVerifyPassFlag(false);
                return checkFileVerifyResultBO;
            }
            CheckDetailFileInfoBO detailFileInfoBO = readCheckDetailFileService.parseDetailFile(filePath);
            detailAmount = detailAmount.add(detailFileInfoBO.getDetailAmount());
            detailCount = detailCount + detailFileInfoBO.getDetailCount();
        }
        BigDecimal totalAmount = downloadBO.getTotalAmount();
        int totalCount = downloadBO.getTotalCount();
        if (totalAmount.compareTo(detailAmount) != 0 || totalCount != detailCount) {
            log.info("对账汇总核对通知总笔数：{}，对账汇总核对通知总金额：{}，明细文件总笔数：{}，明细文件总金额：{}", totalCount, totalAmount, detailCount, detailAmount);
            log.error("【互联互通对账文件校验不通过】：对账汇总核对通知总笔数总金额跟明细文件总笔数总金额不一致；对账日期：{}",
                    downloadBO.getCheckFileDate());
            checkFileVerifyResultBO.setVerifyPassFlag(false);
            return checkFileVerifyResultBO;
        }
        checkFileVerifyResultBO.setVerifyPassFlag(true);
        return checkFileVerifyResultBO;
    }
}
