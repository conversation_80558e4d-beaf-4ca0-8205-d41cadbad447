<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "dtd/mybatis-generator-config_1_0.dtd">
<generatorConfiguration>
    <context id="mysql" targetRuntime="MyBatis3" defaultModelType="lemonflat">
        <property name="javaFileEncoding" value="UTF-8"/>
        <property name="beginningDelimiter" value="`"/>
        <property name="endingDelimiter" value="`"/>

        <plugin type="org.mybatis.generator.lemon.LemonPlugin">
            <property name="mappers" value="com.cmpay.lemon.framework.dao.BaseDao"/>
            <property name="ignoreModelFields" value="tmSmp"/>
        </plugin>

        <commentGenerator>
            <property name="suppressDate" value="true"/>
            <property name="suppressAllComments" value="false"/>
        </commentGenerator>

<!--        <jdbcConnection connectionURL="*******************************************,10.176.52.241:6446,10.176.52.242:6446/dcep_db"-->
<!--                        driverClass="com.mysql.jdbc.Driver"-->
<!--                        password="dcep#123"-->
<!--                        userId="dcepadm"/>-->
        <jdbcConnection connectionURL="*******************************************,10.176.52.241:6446,10.176.52.242:6446/dcepmng_db"
                        driverClass="com.mysql.jdbc.Driver"
                        password="dcepmng#123"
                        userId="dcepmng"/>

        <javaTypeResolver>
            <property name="forceBigDecimals" value="false"/>
        </javaTypeResolver>

        <javaModelGenerator targetPackage="com.cmpay.dceppay.entity.line" targetProject="src/main/java">
            <property name="enableSubPackages" value="false"/>
            <property name="trimStrings" value="false"/>
            <property name="rootClass" value="com.cmpay.framework.data.BaseDO"/>
        </javaModelGenerator>
        <sqlMapGenerator targetPackage="com.cmpay.dceppay.mapper.line" targetProject="src/main/resources">
            <property name="enableSubPackages" value="false"/>
        </sqlMapGenerator>
        <javaClientGenerator type="XMLMAPPER" targetPackage="com.cmpay.dceppay.dao.line" targetProject="src/main/java">
            <property name="enableSubPackages" value="false"/>
        </javaClientGenerator>
<!--        <table tableName="icbc_merchant" domainObjectName="ICBCMerchantDO" enableCountByExample="false"-->
<!--               enableUpdateByExample="false"-->
<!--               enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false">-->
<!--        </table>-->
<!--        <table tableName="icbc_service_provider" domainObjectName="ICBCServiceProviderDO" enableCountByExample="false"-->
<!--               enableUpdateByExample="false"-->
<!--               enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false">-->
<!--        </table>-->

<!--        <table tableName="pay_order" domainObjectName="PayOrderDO" enableCountByExample="false"-->
<!--               enableUpdateByExample="false"-->
<!--               enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false">-->
<!--        </table>-->
<!--        <table tableName="pay_refund" domainObjectName="PayRefundDO" enableCountByExample="false"-->
<!--               enableUpdateByExample="false"-->
<!--               enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false">-->
<!--        </table>-->
<!--        <table tableName="pay_settlement" domainObjectName="PaySettlementDO" enableCountByExample="false"-->
<!--               enableUpdateByExample="false"-->
<!--               enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false">-->
<!--        </table>-->
<!--        <table tableName="pay_notify" domainObjectName="PayNotifyDO" enableCountByExample="false"-->
<!--               enableUpdateByExample="false"-->
<!--               enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false">-->
<!--        </table>-->
<!--        <table tableName="pay_merchant_route" domainObjectName="PayMerchantRouteDO" enableCountByExample="false"-->
<!--               enableUpdateByExample="false"-->
<!--               enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false">-->
<!--        </table>-->
<!--        <table tableName="pay_account_record" domainObjectName="PayAccountRecordDO" enableCountByExample="false"-->
<!--               enableUpdateByExample="false"-->
<!--               enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false">-->
<!--        </table>-->
<!--        <table tableName="pay_account_detail" domainObjectName="PayAccountDetailDO" enableCountByExample="false"-->
<!--               enableUpdateByExample="false"-->
<!--               enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false">-->
<!--        </table>-->
<!--        <table tableName="check_file_detail" domainObjectName="CheckFileDetailDO" enableCountByExample="false"-->
<!--               enableUpdateByExample="false"-->
<!--               enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false">-->
<!--        </table>-->
<!--        <table tableName="check_bill_summary" domainObjectName="CheckBillSummaryDO" enableCountByExample="false"-->
<!--               enableUpdateByExample="false"-->
<!--               enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false">-->
<!--        </table>-->
<!--        <table tableName="check_file_process" domainObjectName="CheckFileProcessDO" enableCountByExample="false"-->
<!--               enableUpdateByExample="false"-->
<!--               enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false">-->
<!--        </table>-->
<!--                <table tableName="check_file_verify" domainObjectName="CheckFileVerifyDO" enableCountByExample="false"-->
<!--                       enableUpdateByExample="false"-->
<!--                       enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false">-->
<!--                </table>-->
<!--        <table tableName="check_error" domainObjectName="CheckErrorDetailDO" enableCountByExample="false"-->
<!--               enableUpdateByExample="false"-->
<!--               enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false">-->
<!--        </table>-->
<!--        <table tableName="check_gen_file_status" domainObjectName="CheckGenFileStatusDO" enableCountByExample="false"-->
<!--               enableUpdateByExample="false"-->
<!--               enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false">-->
<!--        </table>-->
<!--        <table tableName="task_status" domainObjectName="TaskStatusDO" enableCountByExample="false"-->
<!--               enableUpdateByExample="false"-->
<!--               enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false">-->
<!--        </table>-->
<!--                <table tableName="bus_param" domainObjectName="BusParamDO" enableCountByExample="false"-->
<!--                       enableUpdateByExample="false"-->
<!--                       enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false">-->
<!--                </table>-->
<!--                        <table tableName="bus_message" domainObjectName="BusMessageDO" enableCountByExample="false"-->
<!--                               enableUpdateByExample="false"-->
<!--                               enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false">-->
<!--                        </table>-->
        <table tableName="line_info" domainObjectName="LineInfoDO" enableCountByExample="false"
               enableUpdateByExample="false"
               enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false">
        </table>
        <table tableName="line_health_record" domainObjectName="LineHealthRecordDO" enableCountByExample="false"
               enableUpdateByExample="false"
               enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false">
        </table>
    </context>
</generatorConfiguration>
