<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmpay.dceppay.dao.line.ILineInfoExtDao">

    <resultMap id="BaseResultMap" type="com.cmpay.dceppay.entity.line.LineInfoDO" >
        <id column="line_id" property="lineId" jdbcType="VARCHAR" />
        <result column="line_mapping_address" property="lineMappingAddress" jdbcType="VARCHAR" />
        <result column="line_name" property="lineName" jdbcType="VARCHAR" />
        <result column="institution_line_address" property="institutionLineAddress" jdbcType="VARCHAR" />
        <result column="pboc_line_address" property="pbocLineAddress" jdbcType="VARCHAR" />
        <result column="pboc_idc_code" property="pbocIdcCode" jdbcType="VARCHAR" />
        <result column="pboc_real_address" property="pbocRealAddress" jdbcType="VARCHAR" />
        <result column="business_type" property="businessType" jdbcType="VARCHAR" />
        <result column="is_default" property="isDefault" jdbcType="CHAR" />
        <result column="health_check_enabled" property="healthCheckEnabled" jdbcType="CHAR" />
        <result column="network_status" property="networkStatus" jdbcType="VARCHAR" />
        <result column="service_status" property="serviceStatus" jdbcType="VARCHAR" />
        <result column="probe_result" property="probeResult" jdbcType="VARCHAR" />
        <result column="last_check_time" property="lastCheckTime" jdbcType="VARCHAR" />
        <result column="fail_start_time" property="failStartTime" jdbcType="VARCHAR" />
        <result column="fail_count" property="failCount" jdbcType="INTEGER" />
        <result column="rsp_time_avg" property="rspTimeAvg" jdbcType="BIGINT" />
        <result column="del_flag" property="delFlag" jdbcType="CHAR" />
        <result column="remark" property="remark" jdbcType="VARCHAR" />
        <result column="create_user_no" property="createUserNo" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="VARCHAR" />
        <result column="update_user_no" property="updateUserNo" jdbcType="VARCHAR" />
        <result column="update_time" property="updateTime" jdbcType="VARCHAR" />
        <result column="tm_smp" property="tmSmp" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        line_id, line_mapping_address, line_name, institution_line_address, pboc_line_address,
        pboc_idc_code, pboc_real_address, business_type, is_default, health_check_enabled,
        network_status, service_status, probe_result, last_check_time, fail_start_time, fail_count,
        rsp_time_avg, del_flag, remark, create_user_no, create_time, update_user_no, update_time,
        tm_smp
    </sql>

    <select id="listCheckLine" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM line_info
        WHERE health_check_enabled = 'Y'
        and del_flag='N'
        and  business_type = 'API'
    </select>



    <update id="updateLineNetInfo" parameterType="com.cmpay.dceppay.entity.line.LineInfoDO">
       // todo
    </update>

</mapper>
