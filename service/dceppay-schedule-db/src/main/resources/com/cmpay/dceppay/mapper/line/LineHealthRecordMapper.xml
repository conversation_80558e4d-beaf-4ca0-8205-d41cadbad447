<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.dceppay.dao.line.ILineHealthRecordDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.dceppay.entity.line.LineHealthRecordDO" >
        <id column="record_id" property="recordId" jdbcType="VARCHAR" />
        <result column="line_id" property="lineId" jdbcType="VARCHAR" />
        <result column="check_date" property="checkDate" jdbcType="VARCHAR" />
        <result column="check_time" property="checkTime" jdbcType="VARCHAR" />
        <result column="check_type" property="checkType" jdbcType="VARCHAR" />
        <result column="response_time" property="responseTime" jdbcType="BIGINT" />
        <result column="check_result" property="checkResult" jdbcType="VARCHAR" />
        <result column="error_message" property="errorMessage" jdbcType="VARCHAR" />
        <result column="tm_smp" property="tmSmp" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        record_id, line_id, check_date, check_time, check_type, response_time, check_result, 
        error_message, tm_smp
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from line_health_record
        where record_id = #{recordId,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from line_health_record
        where record_id = #{recordId,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.cmpay.dceppay.entity.line.LineHealthRecordDO" >
        insert into line_health_record
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="recordId != null" >
                record_id,
            </if>
            <if test="lineId != null" >
                line_id,
            </if>
            <if test="checkDate != null" >
                check_date,
            </if>
            <if test="checkTime != null" >
                check_time,
            </if>
            <if test="checkType != null" >
                check_type,
            </if>
            <if test="responseTime != null" >
                response_time,
            </if>
            <if test="checkResult != null" >
                check_result,
            </if>
            <if test="errorMessage != null" >
                error_message,
            </if>
            <if test="tmSmp != null" >
                tm_smp,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="recordId != null" >
                #{recordId,jdbcType=VARCHAR},
            </if>
            <if test="lineId != null" >
                #{lineId,jdbcType=VARCHAR},
            </if>
            <if test="checkDate != null" >
                #{checkDate,jdbcType=VARCHAR},
            </if>
            <if test="checkTime != null" >
                #{checkTime,jdbcType=VARCHAR},
            </if>
            <if test="checkType != null" >
                #{checkType,jdbcType=VARCHAR},
            </if>
            <if test="responseTime != null" >
                #{responseTime,jdbcType=BIGINT},
            </if>
            <if test="checkResult != null" >
                #{checkResult,jdbcType=VARCHAR},
            </if>
            <if test="errorMessage != null" >
                #{errorMessage,jdbcType=VARCHAR},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.cmpay.dceppay.entity.line.LineHealthRecordDO" >
        update line_health_record
        <set >
            <if test="lineId != null" >
                line_id = #{lineId,jdbcType=VARCHAR},
            </if>
            <if test="checkDate != null" >
                check_date = #{checkDate,jdbcType=VARCHAR},
            </if>
            <if test="checkTime != null" >
                check_time = #{checkTime,jdbcType=VARCHAR},
            </if>
            <if test="checkType != null" >
                check_type = #{checkType,jdbcType=VARCHAR},
            </if>
            <if test="responseTime != null" >
                response_time = #{responseTime,jdbcType=BIGINT},
            </if>
            <if test="checkResult != null" >
                check_result = #{checkResult,jdbcType=VARCHAR},
            </if>
            <if test="errorMessage != null" >
                error_message = #{errorMessage,jdbcType=VARCHAR},
            </if>
            <if test="tmSmp != null" >
                tm_smp = #{tmSmp,jdbcType=VARCHAR},
            </if>
        </set>
        where record_id = #{recordId,jdbcType=VARCHAR}
    </update>

    <select id="find" resultMap="BaseResultMap" parameterType="com.cmpay.dceppay.entity.line.LineHealthRecordDO" >
        select 
        <include refid="Base_Column_List" />
        from line_health_record
        <where >
            <if test="recordId != null" >
                and record_id = #{recordId,jdbcType=VARCHAR}
            </if>
            <if test="lineId != null" >
                and line_id = #{lineId,jdbcType=VARCHAR}
            </if>
            <if test="checkDate != null" >
                and check_date = #{checkDate,jdbcType=VARCHAR}
            </if>
            <if test="checkTime != null" >
                and check_time = #{checkTime,jdbcType=VARCHAR}
            </if>
            <if test="checkType != null" >
                and check_type = #{checkType,jdbcType=VARCHAR}
            </if>
            <if test="responseTime != null" >
                and response_time = #{responseTime,jdbcType=BIGINT}
            </if>
            <if test="checkResult != null" >
                and check_result = #{checkResult,jdbcType=VARCHAR}
            </if>
            <if test="errorMessage != null" >
                and error_message = #{errorMessage,jdbcType=VARCHAR}
            </if>
            <if test="tmSmp != null" >
                and tm_smp = #{tmSmp,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
</mapper>