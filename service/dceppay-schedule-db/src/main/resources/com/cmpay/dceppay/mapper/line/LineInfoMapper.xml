<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.dceppay.dao.line.ILineInfoDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.dceppay.entity.line.LineInfoDO" >
        <id column="line_id" property="lineId" jdbcType="VARCHAR" />
        <result column="line_mapping_address" property="lineMappingAddress" jdbcType="VARCHAR" />
        <result column="line_name" property="lineName" jdbcType="VARCHAR" />
        <result column="institution_line_address" property="institutionLineAddress" jdbcType="VARCHAR" />
        <result column="pboc_line_address" property="pbocLineAddress" jdbcType="VARCHAR" />
        <result column="pboc_idc_code" property="pbocIdcCode" jdbcType="VARCHAR" />
        <result column="pboc_real_address" property="pbocRealAddress" jdbcType="VARCHAR" />
        <result column="business_type" property="businessType" jdbcType="VARCHAR" />
        <result column="is_default" property="isDefault" jdbcType="CHAR" />
        <result column="health_check_enabled" property="healthCheckEnabled" jdbcType="CHAR" />
        <result column="network_status" property="networkStatus" jdbcType="VARCHAR" />
        <result column="service_status" property="serviceStatus" jdbcType="VARCHAR" />
        <result column="probe_result" property="probeResult" jdbcType="VARCHAR" />
        <result column="last_check_time" property="lastCheckTime" jdbcType="VARCHAR" />
        <result column="fail_start_time" property="failStartTime" jdbcType="VARCHAR" />
        <result column="fail_count" property="failCount" jdbcType="INTEGER" />
        <result column="rsp_time_avg" property="rspTimeAvg" jdbcType="BIGINT" />
        <result column="del_flag" property="delFlag" jdbcType="CHAR" />
        <result column="remark" property="remark" jdbcType="VARCHAR" />
        <result column="create_user_no" property="createUserNo" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="VARCHAR" />
        <result column="update_user_no" property="updateUserNo" jdbcType="VARCHAR" />
        <result column="update_time" property="updateTime" jdbcType="VARCHAR" />
        <result column="tm_smp" property="tmSmp" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        line_id, line_mapping_address, line_name, institution_line_address, pboc_line_address, 
        pboc_idc_code, pboc_real_address, business_type, is_default, health_check_enabled, 
        network_status, service_status, probe_result, last_check_time, fail_start_time, fail_count, 
        rsp_time_avg, del_flag, remark, create_user_no, create_time, update_user_no, update_time, 
        tm_smp
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from line_info
        where line_id = #{lineId,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from line_info
        where line_id = #{lineId,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.cmpay.dceppay.entity.line.LineInfoDO" >
        insert into line_info
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="lineId != null" >
                line_id,
            </if>
            <if test="lineMappingAddress != null" >
                line_mapping_address,
            </if>
            <if test="lineName != null" >
                line_name,
            </if>
            <if test="institutionLineAddress != null" >
                institution_line_address,
            </if>
            <if test="pbocLineAddress != null" >
                pboc_line_address,
            </if>
            <if test="pbocIdcCode != null" >
                pboc_idc_code,
            </if>
            <if test="pbocRealAddress != null" >
                pboc_real_address,
            </if>
            <if test="businessType != null" >
                business_type,
            </if>
            <if test="isDefault != null" >
                is_default,
            </if>
            <if test="healthCheckEnabled != null" >
                health_check_enabled,
            </if>
            <if test="networkStatus != null" >
                network_status,
            </if>
            <if test="serviceStatus != null" >
                service_status,
            </if>
            <if test="probeResult != null" >
                probe_result,
            </if>
            <if test="lastCheckTime != null" >
                last_check_time,
            </if>
            <if test="failStartTime != null" >
                fail_start_time,
            </if>
            <if test="failCount != null" >
                fail_count,
            </if>
            <if test="rspTimeAvg != null" >
                rsp_time_avg,
            </if>
            <if test="delFlag != null" >
                del_flag,
            </if>
            <if test="remark != null" >
                remark,
            </if>
            <if test="createUserNo != null" >
                create_user_no,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="updateUserNo != null" >
                update_user_no,
            </if>
            <if test="updateTime != null" >
                update_time,
            </if>
            <if test="tmSmp != null" >
                tm_smp,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="lineId != null" >
                #{lineId,jdbcType=VARCHAR},
            </if>
            <if test="lineMappingAddress != null" >
                #{lineMappingAddress,jdbcType=VARCHAR},
            </if>
            <if test="lineName != null" >
                #{lineName,jdbcType=VARCHAR},
            </if>
            <if test="institutionLineAddress != null" >
                #{institutionLineAddress,jdbcType=VARCHAR},
            </if>
            <if test="pbocLineAddress != null" >
                #{pbocLineAddress,jdbcType=VARCHAR},
            </if>
            <if test="pbocIdcCode != null" >
                #{pbocIdcCode,jdbcType=VARCHAR},
            </if>
            <if test="pbocRealAddress != null" >
                #{pbocRealAddress,jdbcType=VARCHAR},
            </if>
            <if test="businessType != null" >
                #{businessType,jdbcType=VARCHAR},
            </if>
            <if test="isDefault != null" >
                #{isDefault,jdbcType=CHAR},
            </if>
            <if test="healthCheckEnabled != null" >
                #{healthCheckEnabled,jdbcType=CHAR},
            </if>
            <if test="networkStatus != null" >
                #{networkStatus,jdbcType=VARCHAR},
            </if>
            <if test="serviceStatus != null" >
                #{serviceStatus,jdbcType=VARCHAR},
            </if>
            <if test="probeResult != null" >
                #{probeResult,jdbcType=VARCHAR},
            </if>
            <if test="lastCheckTime != null" >
                #{lastCheckTime,jdbcType=VARCHAR},
            </if>
            <if test="failStartTime != null" >
                #{failStartTime,jdbcType=VARCHAR},
            </if>
            <if test="failCount != null" >
                #{failCount,jdbcType=INTEGER},
            </if>
            <if test="rspTimeAvg != null" >
                #{rspTimeAvg,jdbcType=BIGINT},
            </if>
            <if test="delFlag != null" >
                #{delFlag,jdbcType=CHAR},
            </if>
            <if test="remark != null" >
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createUserNo != null" >
                #{createUserNo,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=VARCHAR},
            </if>
            <if test="updateUserNo != null" >
                #{updateUserNo,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null" >
                #{updateTime,jdbcType=VARCHAR},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.cmpay.dceppay.entity.line.LineInfoDO" >
        update line_info
        <set >
            <if test="lineMappingAddress != null" >
                line_mapping_address = #{lineMappingAddress,jdbcType=VARCHAR},
            </if>
            <if test="lineName != null" >
                line_name = #{lineName,jdbcType=VARCHAR},
            </if>
            <if test="institutionLineAddress != null" >
                institution_line_address = #{institutionLineAddress,jdbcType=VARCHAR},
            </if>
            <if test="pbocLineAddress != null" >
                pboc_line_address = #{pbocLineAddress,jdbcType=VARCHAR},
            </if>
            <if test="pbocIdcCode != null" >
                pboc_idc_code = #{pbocIdcCode,jdbcType=VARCHAR},
            </if>
            <if test="pbocRealAddress != null" >
                pboc_real_address = #{pbocRealAddress,jdbcType=VARCHAR},
            </if>
            <if test="businessType != null" >
                business_type = #{businessType,jdbcType=VARCHAR},
            </if>
            <if test="isDefault != null" >
                is_default = #{isDefault,jdbcType=CHAR},
            </if>
            <if test="healthCheckEnabled != null" >
                health_check_enabled = #{healthCheckEnabled,jdbcType=CHAR},
            </if>
            <if test="networkStatus != null" >
                network_status = #{networkStatus,jdbcType=VARCHAR},
            </if>
            <if test="serviceStatus != null" >
                service_status = #{serviceStatus,jdbcType=VARCHAR},
            </if>
            <if test="probeResult != null" >
                probe_result = #{probeResult,jdbcType=VARCHAR},
            </if>
            <if test="lastCheckTime != null" >
                last_check_time = #{lastCheckTime,jdbcType=VARCHAR},
            </if>
            <if test="failStartTime != null" >
                fail_start_time = #{failStartTime,jdbcType=VARCHAR},
            </if>
            <if test="failCount != null" >
                fail_count = #{failCount,jdbcType=INTEGER},
            </if>
            <if test="rspTimeAvg != null" >
                rsp_time_avg = #{rspTimeAvg,jdbcType=BIGINT},
            </if>
            <if test="delFlag != null" >
                del_flag = #{delFlag,jdbcType=CHAR},
            </if>
            <if test="remark != null" >
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createUserNo != null" >
                create_user_no = #{createUserNo,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=VARCHAR},
            </if>
            <if test="updateUserNo != null" >
                update_user_no = #{updateUserNo,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null" >
                update_time = #{updateTime,jdbcType=VARCHAR},
            </if>
            <if test="tmSmp != null" >
                tm_smp = #{tmSmp,jdbcType=VARCHAR},
            </if>
        </set>
        where line_id = #{lineId,jdbcType=VARCHAR}
    </update>

    <select id="find" resultMap="BaseResultMap" parameterType="com.cmpay.dceppay.entity.line.LineInfoDO" >
        select 
        <include refid="Base_Column_List" />
        from line_info
        <where >
            <if test="lineId != null" >
                and line_id = #{lineId,jdbcType=VARCHAR}
            </if>
            <if test="lineMappingAddress != null" >
                and line_mapping_address = #{lineMappingAddress,jdbcType=VARCHAR}
            </if>
            <if test="lineName != null" >
                and line_name = #{lineName,jdbcType=VARCHAR}
            </if>
            <if test="institutionLineAddress != null" >
                and institution_line_address = #{institutionLineAddress,jdbcType=VARCHAR}
            </if>
            <if test="pbocLineAddress != null" >
                and pboc_line_address = #{pbocLineAddress,jdbcType=VARCHAR}
            </if>
            <if test="pbocIdcCode != null" >
                and pboc_idc_code = #{pbocIdcCode,jdbcType=VARCHAR}
            </if>
            <if test="pbocRealAddress != null" >
                and pboc_real_address = #{pbocRealAddress,jdbcType=VARCHAR}
            </if>
            <if test="businessType != null" >
                and business_type = #{businessType,jdbcType=VARCHAR}
            </if>
            <if test="isDefault != null" >
                and is_default = #{isDefault,jdbcType=CHAR}
            </if>
            <if test="healthCheckEnabled != null" >
                and health_check_enabled = #{healthCheckEnabled,jdbcType=CHAR}
            </if>
            <if test="networkStatus != null" >
                and network_status = #{networkStatus,jdbcType=VARCHAR}
            </if>
            <if test="serviceStatus != null" >
                and service_status = #{serviceStatus,jdbcType=VARCHAR}
            </if>
            <if test="probeResult != null" >
                and probe_result = #{probeResult,jdbcType=VARCHAR}
            </if>
            <if test="lastCheckTime != null" >
                and last_check_time = #{lastCheckTime,jdbcType=VARCHAR}
            </if>
            <if test="failStartTime != null" >
                and fail_start_time = #{failStartTime,jdbcType=VARCHAR}
            </if>
            <if test="failCount != null" >
                and fail_count = #{failCount,jdbcType=INTEGER}
            </if>
            <if test="rspTimeAvg != null" >
                and rsp_time_avg = #{rspTimeAvg,jdbcType=BIGINT}
            </if>
            <if test="delFlag != null" >
                and del_flag = #{delFlag,jdbcType=CHAR}
            </if>
            <if test="remark != null" >
                and remark = #{remark,jdbcType=VARCHAR}
            </if>
            <if test="createUserNo != null" >
                and create_user_no = #{createUserNo,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null" >
                and create_time = #{createTime,jdbcType=VARCHAR}
            </if>
            <if test="updateUserNo != null" >
                and update_user_no = #{updateUserNo,jdbcType=VARCHAR}
            </if>
            <if test="updateTime != null" >
                and update_time = #{updateTime,jdbcType=VARCHAR}
            </if>
            <if test="tmSmp != null" >
                and tm_smp = #{tmSmp,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
</mapper>