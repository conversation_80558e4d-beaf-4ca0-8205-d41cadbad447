/*
 * @ClassName LineHealthRecordDO
 * @Description 
 * @version 1.0
 * @Date 2025-09-08 15:49:24
 */
package com.cmpay.dceppay.entity.line;

import com.cmpay.framework.data.BaseDO;
import com.cmpay.lemon.framework.annotation.DataObject;

@DataObject
public class LineHealthRecordDO extends BaseDO {
    /**
     * @Fields recordId 记录编号
     */
    private String recordId;
    /**
     * @Fields lineId 专线编号
     */
    private String lineId;
    /**
     * @Fields checkDate 探活日期
     */
    private String checkDate;
    /**
     * @Fields checkTime 探活时间
     */
    private String checkTime;
    /**
     * @Fields checkType 探活类型，网络：NET/接口：APT
     */
    private String checkType;
    /**
     * @Fields responseTime 响应时间
     */
    private Long responseTime;
    /**
     * @Fields checkResult 探活结果，SUCCESS-成功/FAIL-失败/TIMEOUT-超时
     */
    private String checkResult;
    /**
     * @Fields errorMessage 错误信息
     */
    private String errorMessage;

    public String getRecordId() {
        return recordId;
    }

    public void setRecordId(String recordId) {
        this.recordId = recordId;
    }

    public String getLineId() {
        return lineId;
    }

    public void setLineId(String lineId) {
        this.lineId = lineId;
    }

    public String getCheckDate() {
        return checkDate;
    }

    public void setCheckDate(String checkDate) {
        this.checkDate = checkDate;
    }

    public String getCheckTime() {
        return checkTime;
    }

    public void setCheckTime(String checkTime) {
        this.checkTime = checkTime;
    }

    public String getCheckType() {
        return checkType;
    }

    public void setCheckType(String checkType) {
        this.checkType = checkType;
    }

    public Long getResponseTime() {
        return responseTime;
    }

    public void setResponseTime(Long responseTime) {
        this.responseTime = responseTime;
    }

    public String getCheckResult() {
        return checkResult;
    }

    public void setCheckResult(String checkResult) {
        this.checkResult = checkResult;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
}