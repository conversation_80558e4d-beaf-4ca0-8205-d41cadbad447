package com.cmpay.dceppay.bo.line;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/9/29 13:33
 */
@Data
public class LineHealthRecordBO {
    /**
     * @Fields lineId 专线编号
     */
    private String lineId;
    /**
     * @Fields checkDate 探活日期
     */
    private String checkDate;
    /**
     * @Fields checkTime 探活时间
     */
    private String checkTime;
    /**
     * @Fields checkType 探活类型，网络：NET/接口：APT
     */
    private String checkType;
    /**
     * @Fields responseTime 响应时间
     */
    private Long responseTime;
    /**
     * @Fields checkResult 探活结果，SUCCESS-成功/FAIL-失败/TIMEOUT-超时
     */
    private String checkResult;
    /**
     * @Fields errorMessage 错误信息
     */
    private String errorMessage;
}
