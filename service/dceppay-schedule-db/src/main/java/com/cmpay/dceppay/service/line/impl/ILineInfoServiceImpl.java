package com.cmpay.dceppay.service.line.impl;

import com.cmpay.dceppay.bo.line.LineInfoBO;
import com.cmpay.dceppay.dao.line.ILineInfoExtDao;
import com.cmpay.dceppay.entity.line.LineInfoDO;
import com.cmpay.dceppay.service.ILineInfoService;
import com.cmpay.dceppay.util.BeanConvertUtils;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/9/29 12:21
 */
@Service
public class ILineInfoServiceImpl implements ILineInfoService {
    @Autowired
    private ILineInfoExtDao lineInfoExtDao;

    @Override
    public List<LineInfoBO> listCheckLine() {
        List<LineInfoDO> lineInfoDOList = lineInfoExtDao.listCheckLine();
        if (JudgeUtils.isNull(lineInfoDOList)) {
            return new ArrayList<>();
        }
        return BeanConvertUtils.convertList(lineInfoDOList, LineInfoBO.class);
    }

    @Override
    public int updateLineNetInfo(LineInfoBO lineInfoBO) {
        //todo
        //lineId	专线编号	String
        //network_status	网络状态	String
        //last_check_time	最近一次探测时间	String
        //fail_start_time	探测异常开始时间	String
        //fail_count	连续失败次数	int
        //rsp_time_avg	平均响应时长	bigint
        LineInfoDO lineInfoDO=new LineInfoDO();
        BeanUtils.copyProperties(lineInfoDO,lineInfoBO);
        return lineInfoExtDao.updateLineNetInfo(lineInfoDO);
    }
}
