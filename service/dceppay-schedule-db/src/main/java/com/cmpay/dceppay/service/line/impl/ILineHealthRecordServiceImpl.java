package com.cmpay.dceppay.service.line.impl;

import com.cmpay.dceppay.bo.line.LineHealthRecordBO;
import com.cmpay.dceppay.dao.line.ILineHealthRecordExtDao;
import com.cmpay.dceppay.entity.line.LineHealthRecordDO;
import com.cmpay.dceppay.service.ILineHealthRecordService;
import com.cmpay.dceppay.util.DateTimeUtil;
import com.cmpay.lemon.common.utils.BeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/9/29 13:40
 */
@Service
@Slf4j
public class ILineHealthRecordServiceImpl implements ILineHealthRecordService {
    @Autowired
    private ILineHealthRecordExtDao lineHealthRecordExtDao;

    @Override
    public int addLineHealthRecord(LineHealthRecordBO healthRecordBO) {
        LineHealthRecordDO lineHealthRecordDO = BeanUtils.copyPropertiesReturnDest(new LineHealthRecordDO(), healthRecordBO);
        lineHealthRecordDO.setTmSmp(DateTimeUtil.getCurrentDateTimeStr());
        try {
            return lineHealthRecordExtDao.insert(lineHealthRecordDO);
        } catch (Exception e) {
            log.error("addLineHealthRecordError: {}", e.getMessage(), e);
            return 0;
        }
    }
}
