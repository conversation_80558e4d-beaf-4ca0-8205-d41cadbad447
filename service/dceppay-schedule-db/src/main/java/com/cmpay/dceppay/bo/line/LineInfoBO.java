package com.cmpay.dceppay.bo.line;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/9/29 12:18
 */
@Data
public class LineInfoBO {
    /**
     * @Fields lineId 专线编号
     */
    private String lineId;
    /**
     * @Fields lineMappingAddress 专线映射地址
     */
    private String lineMappingAddress;
    /**
     * @Fields lineName 专线名称
     */
    private String lineName;
    /**
     * @Fields institutionLineAddress 机构专线地址
     */
    private String institutionLineAddress;
    /**
     * @Fields pbocLineAddress 央行专线地址
     */
    private String pbocLineAddress;
    /**
     * @Fields pbocIdcCode 央行IDC标识
     */
    private String pbocIdcCode;
    /**
     * @Fields pbocRealAddress 央行真实地址
     */
    private String pbocRealAddress;
    /**
     * @Fields businessType 业务类型
     */
    private String businessType;
    /**
     * @Fields isDefault 默认标识
     */
    private String isDefault;
    /**
     * @Fields healthCheckEnabled 探活开关
     */
    private String healthCheckEnabled;
    /**
     * @Fields networkStatus 网络状态，NORMAL-正常/WARNING-告警/FAULT-故障
     */
    private String networkStatus;
    /**
     * @Fields serviceStatus 服务状态，NORMAL-正常/ABNORMAL-异常
     */
    private String serviceStatus;
    /**
     * @Fields probeResult 探测结果
     */
    private String probeResult;
    /**
     * @Fields lastCheckTime 最近一次探测时间
     */
    private String lastCheckTime;
    /**
     * @Fields failStartTime 探测异常开始时间
     */
    private String failStartTime;
    /**
     * @Fields failCount 连续失败次数
     */
    private Integer failCount;
    /**
     * @Fields rspTimeAvg 平均响应时长
     */
    private Long rspTimeAvg;
    /**
     * @Fields delFlag 删除标识，默认N，删除时置为Y
     */
    private String delFlag;
    /**
     * @Fields remark 备注
     */
    private String remark;
    /**
     * @Fields createUserNo 创建者
     */
    private String createUserNo;
    /**
     * @Fields createTime 创建时间
     */
    private String createTime;
    /**
     * @Fields updateUserNo 更新者
     */
    private String updateUserNo;
    /**
     * @Fields updateTime 更新时间
     */
    private String updateTime;
}
