package com.cmpay.dceppay.service.line.impl;

import com.cmpay.dceppay.bo.line.LineHealthRecordBO;
import com.cmpay.dceppay.bo.line.LineInfoBO;
import com.cmpay.dceppay.constant.mng.LineManagementConstants;
import com.cmpay.dceppay.service.cache.ILineCheckRecordCacheService;
import com.cmpay.dceppay.service.line.ILineHealthCheckService;
import com.cmpay.dceppay.service.ILineHealthRecordService;
import com.cmpay.dceppay.service.ILineInfoService;
import com.cmpay.dceppay.service.line.ILineNetCheckService;
import com.cmpay.dceppay.util.NetworkConnectivityUtil;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/23 10:00
 * 专线探活服务实现类
 */
@Service
@Slf4j
public class LineHealthCheckServiceImpl implements ILineHealthCheckService {

    @Autowired
    private ILineInfoService lineInfoService;

    @Autowired
    private ILineHealthRecordService healthRecordService;
    @Autowired
    private ILineCheckRecordCacheService checkRecordCacheService;
    @Autowired
    private ILineNetCheckService lineNetCheckService;


    @Override
    public void executeHealthCheck() {
        log.info("开始执行专线探活任务");
        // 查询所有启用探活的专线
        List<LineInfoBO> lines = lineInfoService.listCheckLine();
        if (lines == null || lines.isEmpty()) {
            log.warn("executeHealthCheckEnd :No wait check Lines");
            return;
        }
        // 对每个专线进行探活
        for (LineInfoBO line : lines) {
            try {
                checkSingleLine(line);
            } catch (Exception e) {
                log.error("checkSingleLineException:专线ID: {}, 错误: {}", line.getLineId(), e.getMessage(), e);
            }
        }
        log.info("专线探活任务执行完成，共检查 {} 条专线", lines.size());
    }

    @Override
    public void checkSingleLine(LineInfoBO line) {
        String lineId = line.getLineId();
        String address = line.getLineMappingAddress();
        log.info("checkSingleLine:名称 {}, 地址: {}", line.getLineName(), address);
        //todo 调用IlineCheckRecordCacheService.queryCeckCache查询专线累计探测次数，若累计次数>=5，直接跳出本次循环，进入下一条专线探测任务；
        checkRecordCacheService.queryCheckCache(lineId);
        //todo 网关处理 使用新的连通性检测工具
        NetworkConnectivityUtil.ConnectivityResult result = null;
        String checkResult = null;
        long responseTime = 0;
        String errorMessage = null;
        try {
            result = NetworkConnectivityUtil.checkConnectivity(address);
        } catch (Exception e) {
            //todo 如果是超时  HealthCheckResultEnum
            checkResult = HealthCheckResultEnum.CHECK_RESULT_TIMEOUT;
        }
        //todo 失败则调用IlineNetCheckService.dealLineFailInfo进行专线探测失败信息处理；若为成功则调用IlineNetCheckService.dealLineSuccessInfo进行专线探测成功信息处理；
        if (JudgeUtils.isNotNull(result)) {
            boolean isSuccess = result.isPingSuccess() && result.isTelnetSuccess();
            checkResult = isSuccess ? HealthCheckResultEnum.CHECK_RESULT_SUCCESS : HealthCheckResultEnum.CHECK_RESULT_FAIL;
            responseTime = result.getTotalTime();
            errorMessage = result.getErrorMessage();
            if (isSuccess) {
                log.debug("专线探活成功: {}, ping: {}ms, telnet: {}ms, 总耗时: {}ms",
                        address, result.getPingTime(), result.getTelnetTime(), responseTime);
                lineNetCheckService.dealLineSuccessInfo()

            }
        }else {
            log.warn("专线探活失败: {}, 错误: {}, ping成功: {}, telnet成功: {}, 总耗时: {}ms",
                    address, errorMessage, result.isPingSuccess(), result.isTelnetSuccess(), responseTime);
            lineNetCheckService.dealLineFailInfo()
        }
        // 记录探活结果到数据库
        String checkType = line.getBusinessType();
        recordHealthCheck(lineId, checkType, responseTime, checkResult, errorMessage);
        //todo  更新缓存中的探活结果
        lineNetCheckService.handleCheckCache();
    }


    /**
     * 记录探活结果
     */
    private void recordHealthCheck(String lineId, String checkType, long responseTime,
                                   String checkResult, String errorMessage) {
        LineHealthRecordBO healthRecordBO = new LineHealthRecordBO();
        healthRecordBO.setLineId(lineId);
        healthRecordBO.setCheckDate(DateTimeUtils.getCurrentDateStr());
        healthRecordBO.setCheckTime(DateTimeUtils.getCurrentDateTimeStr());
        healthRecordBO.setCheckType(checkType);
        healthRecordBO.setCheckResult(checkResult);
        healthRecordBO.setResponseTime(responseTime);
        healthRecordBO.setErrorMessage(errorMessage);
        healthRecordService.addLineHealthRecord(healthRecordBO);
    }


}
