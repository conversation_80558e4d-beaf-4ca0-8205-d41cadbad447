package com.cmpay.dceppay.xxl.jobhandler;

import com.cmpay.dceppay.service.INetworkScanService;
import com.cmpay.dceppay.util.DateTimeUtil;
import com.cmpay.lemon.framework.data.interceptor.InitialLemonData;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/10/8 14:20
 * 机房探测
 */
@Component
@Slf4j
public class NetworkScanTask {
    @Autowired
    private INetworkScanService networkScanService;


    //每30s触发一次
    @XxlJob("networkScanTask")
    @InitialLemonData("lemonDataInitializer")
    public void networkScanTask() {
        log.info("networkScanTask startTime:{}", DateTimeUtil.getCurrentDateTimeStr());
        networkScanService.scan();
    }
}
