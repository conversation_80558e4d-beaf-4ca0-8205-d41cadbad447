package com.cmpay.dceppay.service;

import com.cmpay.dceppay.entity.mng.DcepLineInfoDO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/23 10:00
 * 专线管理服务接口
 */
public interface ILineManagementService {
    
    /**
     * 获取所有启用探活的专线
     * @return 专线列表
     */
    List<DcepLineInfoDO> getEnabledHealthCheckLines();

    /**
     * 根据业务类型获取默认专线
     * @param businessType 业务类型
     * @return 默认专线
     */
    DcepLineInfoDO getDefaultLine(String businessType);

    /**
     * 根据业务类型获取最优专线（响应最快且状态正常）
     * @param businessType 业务类型
     * @return 最优专线
     */
    DcepLineInfoDO getBestLine(String businessType);

    /**
     * 新增专线
     * @param line 专线信息
     * @return 新增结果
     */
    boolean addLine(DcepLineInfoDO line);

    /**
     * 更新专线信息
     * @param line 专线信息
     * @return 更新结果
     */
    boolean updateLine(DcepLineInfoDO line);
    
    /**
     * 删除专线（逻辑删除）
     * @param lineId 专线ID
     * @return 删除结果
     */
    boolean deleteLine(String lineId);
    
    /**
     * 启用/禁用专线探活
     * @param lineId 专线ID
     * @param enabled 是否启用
     * @return 操作结果
     */
    boolean toggleHealthCheck(String lineId, boolean enabled);
}
