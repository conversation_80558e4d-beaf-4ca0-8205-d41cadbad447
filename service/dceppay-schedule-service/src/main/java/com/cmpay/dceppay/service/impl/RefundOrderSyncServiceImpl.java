package com.cmpay.dceppay.service.impl;

import com.cmpay.dceppay.bo.pay.PayOrderDBBO;
import com.cmpay.dceppay.bo.pay.PayOrderQueryBO;
import com.cmpay.dceppay.bo.pay.RefundOrderDBBO;
import com.cmpay.dceppay.bo.refundquery.RefundOrderQueryBO;
import com.cmpay.dceppay.bo.refundquery.RefundOrderResultBO;
import com.cmpay.dceppay.enums.pay.OrderStatusEnum;
import com.cmpay.dceppay.service.IRefundOrderSyncService;
import com.cmpay.dceppay.service.channel.IPaymentChannelService;
import com.cmpay.dceppay.service.pay.IPayOrderDBService;
import com.cmpay.dceppay.service.pay.IPayRefundDBService;
import com.cmpay.dceppay.service.pay.IPayRefundService;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.lock.DistributedLocked;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/17 10:15
 */
@Service
@Slf4j
public class RefundOrderSyncServiceImpl implements IRefundOrderSyncService {
    @Autowired
    private IPayRefundDBService refundDBService;
    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private IPayOrderDBService payOrderDBService;
    @Autowired
    private IPayRefundService refundService;

    @Override
    public void syncRefundOrder(PayOrderQueryBO refundOrderQueryBO) {
        List<RefundOrderDBBO> refundOrderDBBOList = refundDBService.findWaitRefundList(refundOrderQueryBO);
        if (JudgeUtils.isEmpty(refundOrderDBBOList)) {
            log.info("shardIndex: {}, 等待退款列表为空!!", refundOrderQueryBO.getShardIndex());
            return;
        }
        refundOrderDBBOList.forEach(this::syncSingleRefundOrder);
    }

    @DistributedLocked(lockName = "'syncSingleRefundOrder:'+#refundOrderDBBO.getRefundOrderNo()", leaseTime = 40, waitTime = 10)
    private void syncSingleRefundOrder(RefundOrderDBBO refundOrderDBBO) {
        PayOrderDBBO payOrderDBBO = payOrderDBService.getByOutOrderNo(refundOrderDBBO.getOutOrderNo());
        if (JudgeUtils.isNull(payOrderDBBO)) {
            log.info("syncRefundOrder order  is null!! refundOrderNO:{},outOrderNo:{}", refundOrderDBBO.getRefundOrderNo(), refundOrderDBBO.getOutOrderNo());
            return;
        }
        try {
            String channelName = payOrderDBBO.getPayWay() + payOrderDBBO.getScene();
            IPaymentChannelService paymentChannelService = (IPaymentChannelService) applicationContext.getBean(channelName);
            RefundOrderQueryBO refundOrderQueryBO = buildRefundOrderQueryBO(refundOrderDBBO);
            RefundOrderResultBO refundOrderResultBO = paymentChannelService.refundQuery(refundOrderQueryBO);
            if (JudgeUtils.isNotNull(refundOrderResultBO.getRefundStatus())) {
                OrderStatusEnum orderStatusEnum = OrderStatusEnum.valueOf(refundOrderResultBO.getRefundStatus());
                if (refundOrderResultBO.getRefundAmount().equals(refundOrderDBBO.getRefundAmount())) {
                    handleOrderStatus(orderStatusEnum, refundOrderResultBO, refundOrderDBBO);
                }
            }
        } catch (BeansException e) {
            log.error("syncSingleRefundOrder失败：未找到可用的退款查询接口");
        } catch (Exception e) {
            log.error("syncSingleRefundOrder执行失败：{}", e.getCause());
        }
    }

    private void handleOrderStatus(OrderStatusEnum orderStatusEnum, RefundOrderResultBO refundOrderResultBO, RefundOrderDBBO refundOrderDBBO) {
        switch (orderStatusEnum) {
            case REFUND_SUCCESS:
                refundService.handleSuccessRefundOrder(refundOrderResultBO, refundOrderDBBO);
                break;
            case REFUND_FAIL:
                refundService.handleFailRefundOrder(refundOrderResultBO, refundOrderDBBO);
                break;
            default:
                break;
        }
    }

    private RefundOrderQueryBO buildRefundOrderQueryBO(RefundOrderDBBO refundOrderDBBO) {
        RefundOrderQueryBO refundOrderQueryBO = new RefundOrderQueryBO();
        refundOrderQueryBO.setOutRefundNo(refundOrderDBBO.getRefundOrderNo());
        refundOrderQueryBO.setOriginalMessageIdentification(refundOrderDBBO.getMessageIdentification());
        refundOrderQueryBO.setMerchantId(refundOrderDBBO.getOrgMerchantNo());
        refundOrderQueryBO.setBankRefundNo(refundOrderDBBO.getBankRefundNo());
        refundOrderQueryBO.setWalletId(refundOrderDBBO.getWalletId());
        return refundOrderQueryBO;
    }
}
