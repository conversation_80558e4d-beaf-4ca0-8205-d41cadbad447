package com.cmpay.dceppay.service.impl;

import com.cmpay.dceppay.bo.account.AccountTreatmentQueryResponseBO;
import com.cmpay.dceppay.bo.pay.AccountQueryBO;
import com.cmpay.dceppay.bo.pay.PayAccountRecordBO;
import com.cmpay.dceppay.bo.pay.PayOrderDBBO;
import com.cmpay.dceppay.bo.pay.RefundOrderDBBO;
import com.cmpay.dceppay.stream.client.DceppayAsyncClient;
import com.cmpay.dceppay.enums.pay.AccountOrderTypeEnum;
import com.cmpay.dceppay.enums.pay.AccountProcessStatusEnum;
import com.cmpay.dceppay.enums.pay.AccountTradeCodeEnum;
import com.cmpay.dceppay.enums.pay.OrderStatusEnum;
import com.cmpay.dceppay.service.IAccountSyncDealService;
import com.cmpay.dceppay.service.account.IDcepAccountService;
import com.cmpay.dceppay.service.pay.IPayAccountRecordDBService;
import com.cmpay.dceppay.service.pay.IPayOrderDBService;
import com.cmpay.dceppay.service.pay.IPayRefundDBService;
import com.cmpay.dceppay.service.pay.IPaySettlementDBService;
import com.cmpay.lemon.common.utils.JudgeUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/29 12:34
 */
@Service
@Slf4j
public class AccountSyncDealServiceImpl implements IAccountSyncDealService {
    @Autowired
    private IPayAccountRecordDBService accountRecordDBService;
    @Autowired
    private IDcepAccountService accountService;
    @Autowired
    private IPayOrderDBService payOrderDBService;
    @Autowired
    private IPayRefundDBService refundDBService;
    @Autowired
    private IPaySettlementDBService settlementDBService;
    @Autowired
    private DceppayAsyncClient asyncClient;

    @Override
    public void queryAccount(AccountQueryBO accountQueryBO) {
        List<PayAccountRecordBO> accountRecordBOList = accountRecordDBService.queryWaitHandleAccount(accountQueryBO);
        if (JudgeUtils.isEmpty(accountRecordBOList)) {
            log.info("shardIndex: {}, 等待处理账务列表为空!!", accountQueryBO.getShardIndex());
            return;
        }
        accountRecordBOList.forEach(this::queryAccountStatus);
    }
    @Override
    public void queryAccountStatus(PayAccountRecordBO accountRecordBO) {
        try {
            AccountTreatmentQueryResponseBO queryResponseBO = accountService.queryAccountTreatment(accountRecordBO);
            accountRecordBO.setAccountDate(queryResponseBO.getAccountDate());
            if (JudgeUtils.equals(queryResponseBO.getAccountHandleStatus(), AccountProcessStatusEnum.SUCCESS.name())) {
                String orderNo = accountRecordBO.getOrderNo();
                String orderType = accountRecordBO.getOrderType();
                if (JudgeUtils.equals(orderType, AccountOrderTypeEnum.PAYMENT.getValue())) {
                    PayOrderDBBO orderDBBO = payOrderDBService.getByOutOrderNo(orderNo);
                    orderDBBO.setAccountDate(queryResponseBO.getAccountDate());
                    orderDBBO.setStatus(OrderStatusEnum.TRADE_SUCCESS.name());
                    settlementDBService.registerPaySettlement(orderDBBO);
                    payOrderDBService.updateTradeSuccess(orderDBBO);
                    asyncClient.asyncNotify(orderDBBO);
                } else if (JudgeUtils.equals(orderType, AccountOrderTypeEnum.REFUND.getValue())) {
                    RefundOrderDBBO refundOrderDBBO = refundDBService.getByOutRefundNo(orderNo);
                    refundOrderDBBO.setAccountDate(queryResponseBO.getAccountDate());
                    if (JudgeUtils.equals(accountRecordBO.getBusCode(), AccountTradeCodeEnum.DCEP0006.name())) {
                        //数币退款申请账务处理成功，更新退款表账期，登记结算表
                        refundOrderDBBO.setAccountDate(queryResponseBO.getAccountDate());
                        settlementDBService.registerRefundSettlement(refundOrderDBBO);
                        refundDBService.updateAccountDate(refundOrderDBBO);

                    }
                }
                accountRecordDBService.updateAccountSuccess(accountRecordBO);
            } else if (JudgeUtils.equals(queryResponseBO.getAccountHandleStatus(), AccountProcessStatusEnum.REVERSE.name())) {
                accountRecordDBService.updateAccountCancelReserve(accountRecordBO);
            } else if (JudgeUtils.equals(queryResponseBO.getAccountHandleStatus(), AccountProcessStatusEnum.NORECORD.name())) {
                accountRecordDBService.updateNoRecordAccount(accountRecordBO);
            }
        } catch (Exception e) {
            log.error("账务查询处理失败，处理对象：{},失败原因：{}", accountRecordBO, e.getCause());
        }
    }

}
