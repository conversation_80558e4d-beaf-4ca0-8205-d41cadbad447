package com.cmpay.dceppay.constants;

/**
 * <AUTHOR>
 * @date 2024/9/10 15:25
 *分布式任务每次处理默认记录数常量
 */
public class ScheduleHandleNumberConstant {
    /**
     * 支付订单状态同步笔数
     */
    public static  final int PAY_ORDER_SYNC_NUMBER=100;

    /**
     * 退款订单状态同步笔数
     */
    public static  final int REFUND_ORDER_SYNC_NUMBER=100;

    /**
     * 支付订单成功通知同步笔数
     */
    public static  final int PAY_ORDER_NOTIFY_SYNC_NUMBER=100;

    /**
     * 账务查询一次处理数据量
     */
    public static  final int ACCOUNT_STATUS_SYNC_NUMBER=100;

    /**
     * 退款订单异步发起笔数
     */
    public static  final int REFUND_NUMBER=100;

}
