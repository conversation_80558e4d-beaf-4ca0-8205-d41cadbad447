package com.cmpay.dceppay.service.line;

import com.cmpay.dceppay.bo.LineCheckRecordCacheBO;

/**
 * <AUTHOR>
 * @date 2025/9/29 17:11
 */
public interface ILineNetCheckService {
    boolean dealLineFailInfo(LineCheckRecordCacheBO lineCheckRecordCacheBO);
    boolean dealLineSuccessInfo(LineCheckRecordCacheBO lineCheckRecordCacheBO);
    boolean handleCheckCache(LineCheckRecordCacheBO lineCheckRecordCacheBO);

}
