package com.cmpay.dceppay.xxl.jobhandler;

import com.cmpay.dceppay.service.line.ILineHealthCheckService;
import com.cmpay.dceppay.util.DateTimeUtil;
import com.cmpay.lemon.framework.data.interceptor.InitialLemonData;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/12/23 10:00
 * 专线探活定时任务
 */
@Component
@Slf4j
public class LineHealthCheckTask {
    
    @Autowired
    private ILineHealthCheckService lineHealthCheckService;
    
    /**
     * 专线探活任务
     * 每5秒执行一次
     */
    @XxlJob("lineHealthCheckTask")
    @InitialLemonData("lemonDataInitializer")
    public void lineHealthCheckTask() {
        log.info("专线探活任务开始执行，时间: {}", DateTimeUtil.getCurrentDateTimeStr());
        
        try {
            lineHealthCheckService.executeHealthCheck();
            log.info("专线探活任务执行成功");
        } catch (Exception e) {
            log.error("专线探活任务执行失败: {}", e.getMessage(), e);
            // 重新抛出异常，让XXL-Job记录失败状态
            throw e;
        }
    }
}
