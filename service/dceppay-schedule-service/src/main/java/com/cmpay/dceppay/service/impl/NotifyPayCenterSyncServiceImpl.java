package com.cmpay.dceppay.service.impl;

import com.cmpay.dceppay.bo.notify.NotifySyncQueryBO;
import com.cmpay.dceppay.bo.pay.PayNotifyBO;
import com.cmpay.dceppay.bo.pay.PayOrderDBBO;
import com.cmpay.dceppay.stream.client.DceppayAsyncClient;
import com.cmpay.dceppay.service.INotifyPayCenterSyncService;
import com.cmpay.dceppay.service.mng.IBusParamService;
import com.cmpay.dceppay.service.pay.IPayNotifyDBService;
import com.cmpay.dceppay.service.pay.IPayOrderDBService;
import com.cmpay.lemon.common.utils.JudgeUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/29 12:35
 */
@Service
@Slf4j
public class NotifyPayCenterSyncServiceImpl implements INotifyPayCenterSyncService {
    @Autowired
    private IPayNotifyDBService notifyDBService;
    @Autowired
    private DceppayAsyncClient dceppayAsyncClient;
    @Autowired
    private IPayOrderDBService payOrderDBService;
    @Autowired
    private IBusParamService paramService;

    @Override
    public void notifyOrder(NotifySyncQueryBO notifySyncQueryBO) {
        notifySyncQueryBO.setNotifyCount(paramService.getMaxNotifyCount());
        List<PayNotifyBO> paymentNotifyBOList = notifyDBService.queryWaitNotifyRecord(notifySyncQueryBO);
        if (JudgeUtils.isEmpty(paymentNotifyBOList)) {
            log.info("shardIndex: {}, 等待通知列表为空!!", notifySyncQueryBO.getShardIndex());
            return;
        }
        paymentNotifyBOList.forEach(this::notifyPaymentSuccessOrder);
    }

    public void notifyPaymentSuccessOrder(PayNotifyBO notifyBO) {
        PayOrderDBBO payOrderDBBO = payOrderDBService.getByOutOrderNo(notifyBO.getOutOrderNo());
        if (JudgeUtils.isNull(payOrderDBBO)) {
            log.info("notifyPaymentSuccessOrder order  is null!! notifyBO:{}", notifyBO);
            return;
        }
        try {
            dceppayAsyncClient.asyncNotify(payOrderDBBO);
        } catch (Exception e) {
            log.error("通知支付中台失败，失败原因：{}", e.getCause());
        }
    }
}
