package com.cmpay.dceppay.service.impl;

import com.cmpay.channel.data.Request;
import com.cmpay.channel.data.Response;
import com.cmpay.dceppay.bo.mng.BusParamBO;
import com.cmpay.dceppay.client.DceppayCgwOutClient;
import com.cmpay.dceppay.constant.mng.BusParamTypeConstants;
import com.cmpay.dceppay.dto.dcep.faut.Fault;
import com.cmpay.dceppay.dto.dcep.faut.FaultSoapEnvelopResponse;
import com.cmpay.dceppay.dto.dcep.networkscan.NetWorkScanRsp;
import com.cmpay.dceppay.dto.dcep.networkscan.request.NetworkScanRequest;
import com.cmpay.dceppay.dto.dcep.networkscan.response.CityZone;
import com.cmpay.dceppay.dto.dcep.networkscan.response.DCEPProcessResponse;
import com.cmpay.dceppay.dto.dcep.networkscan.response.NetworkScanResponse;
import com.cmpay.dceppay.service.INetworkScanService;
import com.cmpay.dceppay.service.cache.IdcInformationCacheService;
import com.cmpay.dceppay.service.channel.app.ext.INetworkScanExtService;
import com.cmpay.dceppay.service.mng.IBusParamService;
import com.cmpay.dceppay.utils.ExceptionHandlerUtil;
import com.cmpay.framework.data.request.GenericDTO;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/10/8 14:23
 */
@Service
@Slf4j
public class NetworkScanServiceImpl implements INetworkScanService {
    @Autowired
    private DceppayCgwOutClient dceppayCgwOutClient;
    @Autowired
    private INetworkScanExtService networkScanExtService;


    @Override
    public void scan() {
        NetworkScanRequest networkScanRequest = networkScanExtService.buildNetworkScanRequestParam();
        Request request = networkScanExtService.bulidNetworkScanRequest(networkScanRequest);
        GenericRspDTO<Response> genericRspDTO;
        NetWorkScanRsp networkScanResponse = new NetWorkScanRsp();
        try {
            genericRspDTO = dceppayCgwOutClient.request(GenericDTO.newChildInstanceWithBody(GenericDTO.class, request));
            //处理网关响应对象
            if (JudgeUtils.isNotSuccess(genericRspDTO)) {
                BusinessException.throwBusinessException(genericRspDTO);
            }
            networkScanResponse = Optional.of(genericRspDTO)
                    .map(GenericRspDTO::getBody)
                    .map(Response::getResult).map(x -> (NetWorkScanRsp) x)
                    .orElse(new NetWorkScanRsp());
        } catch (Exception e) {
            ExceptionHandlerUtil.throwRequestDcepError(e);
        }
        networkScanExtService.handleResponse(networkScanResponse);
    }


}
