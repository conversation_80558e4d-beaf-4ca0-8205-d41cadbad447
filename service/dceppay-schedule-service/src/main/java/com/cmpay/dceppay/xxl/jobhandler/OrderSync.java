package com.cmpay.dceppay.xxl.jobhandler;

import com.cmpay.dceppay.constants.ScheduleHandleNumberConstant;
import com.cmpay.dceppay.bo.pay.PayOrderQueryBO;
import com.cmpay.dceppay.service.IPayOrderSyncService;
import com.cmpay.dceppay.service.IRefundOrderSyncService;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.lemon.framework.data.interceptor.InitialLemonData;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.executor.XxlJobExecutor;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/9/10 14:05
 * 支付订单状态同步
 */
@Component
@Slf4j
public class OrderSync extends XxlJobExecutor {
    @Autowired
    private IPayOrderSyncService payOrderSyncService;
    @Autowired
    private IRefundOrderSyncService refundOrderSyncService;

    @XxlJob("payOrderSyn")
    @InitialLemonData("lemonDataInitializer")
    public void payOrderSyn() {
        // 获取任务参数
        String param = XxlJobHelper.getJobParam();
        int handleNumber = StringUtils.isNotBlank(param) ? Integer.parseInt(param) : ScheduleHandleNumberConstant.PAY_ORDER_SYNC_NUMBER;
        //当前分片参数
        int shardIndex = XxlJobHelper.getShardIndex();
        //总分片数
        int shardTotal = XxlJobHelper.getShardTotal();
        PayOrderQueryBO payOrderQueryBO = new PayOrderQueryBO();
        payOrderQueryBO.setShardIndex(shardIndex);
        payOrderQueryBO.setShardTotal(shardTotal);
        payOrderQueryBO.setTotal(handleNumber);
        //todo 同步范围以及同步频次确认 同步当前时间1天之前到当前时间10s订单状态
        LocalDate localDate = DateTimeUtils.parseLocalDate(DateTimeUtils.getCurrentDateStr());
        LocalDateTime localDateTime = DateTimeUtils.parseLocalDateTime(DateTimeUtils.getCurrentDateTimeStr());
        payOrderQueryBO.setOrderTimeBegin(DateTimeUtils.formatLocalDateTime(localDateTime.minusDays(1)));
        payOrderQueryBO.setOrderTimeEnd(DateTimeUtils.formatLocalDateTime(localDateTime.minusSeconds(10)));
        payOrderQueryBO.setRequestDate(DateTimeUtils.formatLocalDate(localDate.minusDays(1)));
        payOrderSyncService.syncPayOrder(payOrderQueryBO);
    }

    @XxlJob("refundOrderSyn")
    @InitialLemonData("lemonDataInitializer")
    public void refundOrderSyn() {
        // 获取任务参数
        String param = XxlJobHelper.getJobParam();
        int handleNumber = StringUtils.isNotBlank(param) ? Integer.parseInt(param) : ScheduleHandleNumberConstant.REFUND_ORDER_SYNC_NUMBER;
        //当前分片参数
        int shardIndex = XxlJobHelper.getShardIndex();
        //总分片数
        int shardTotal = XxlJobHelper.getShardTotal();
        PayOrderQueryBO payOrderQueryBO = new PayOrderQueryBO();
        payOrderQueryBO.setShardIndex(shardIndex);
        payOrderQueryBO.setShardTotal(shardTotal);
        payOrderQueryBO.setTotal(handleNumber);
        //todo 同步范围以及同步频次确认 同步当前时间1天之前到当前时间2min订单状态
        LocalDate localDate = DateTimeUtils.parseLocalDate(DateTimeUtils.getCurrentDateStr());
        LocalDateTime localDateTime = DateTimeUtils.parseLocalDateTime(DateTimeUtils.getCurrentDateTimeStr());
        payOrderQueryBO.setOrderTimeBegin(DateTimeUtils.formatLocalDateTime(localDateTime.minusDays(1)));
        payOrderQueryBO.setOrderTimeEnd(DateTimeUtils.formatLocalDateTime(localDateTime.minusMinutes(2)));
        payOrderQueryBO.setRequestDate(DateTimeUtils.formatLocalDate(localDate.minusDays(1)));
        refundOrderSyncService.syncRefundOrder(payOrderQueryBO);
    }
}
