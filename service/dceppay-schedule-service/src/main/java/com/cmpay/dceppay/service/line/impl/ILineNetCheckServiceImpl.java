package com.cmpay.dceppay.service.line.impl;

import com.cmpay.dceppay.bo.LineCheckRecordCacheBO;
import com.cmpay.dceppay.bo.line.LineInfoBO;
import com.cmpay.dceppay.service.ILineInfoService;
import com.cmpay.dceppay.service.cache.ILineCheckRecordCacheService;
import com.cmpay.dceppay.service.line.ILineNetCheckService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/9/29 17:14
 */
@Service
@Slf4j
public class ILineNetCheckServiceImpl implements ILineNetCheckService {
    @Autowired
    private ILineCheckRecordCacheService lineCheckRecordCacheService;
    @Autowired
    private ILineInfoService lineInfoService;

    @Override
    public boolean dealLineFailInfo(LineCheckRecordCacheBO lineCheckRecordCacheBO) {
        //todo
        // 1、	调用IlineCheckRecordCacheService.queryCeckCache	查询专线探测记录缓存信息；
        //2、	缓存不存在：
        //checkCount（累计探测次数）：1
        //checkStartTime（最近探测开始时间）：当前时间
        //checkExceptionTime（异常开始时间）：当前时间
        //failCount	连续失败次数：1
        //avgDuration（平均耗时）：探测耗时
        //networkStatus	网络状态：失败
        //3、	缓存信息存在：
        //checkCount（累计探测次数）：+1
        //checkStartTime（最近探测开始时间）：当前时间
        //checkExceptionTime（异常开始时间）：不变
        //failCount	连续失败次数：1
        //avgDuration（平均耗时）：（查询到的平均耗时+探测耗时）/累计探测次数
        //networkStatus	网络状态：失败
        //4、	结束。
        return false;
    }

    @Override
    public boolean dealLineSuccessInfo(LineCheckRecordCacheBO lineCheckRecordCacheBO) {
        //todo
        // 1、	调用IlineCheckRecordCacheService.queryCeckCache	查询专线探测记录缓存信息；
        //2、	缓存不存在：
        //checkCount（累计探测次数）：1
        //checkStartTime（最近探测开始时间）：当前时间
        //checkExceptionTime（异常开始时间）：为空
        //failCount	连续失败次数：0
        //avgDuration（平均耗时）：探测耗时
        //networkStatus	网络状态：正常
        //3、	缓存信息存在：
        //checkCount（累计探测次数）：+1
        //checkStartTime（最近探测开始时间）：当前时间
        //checkExceptionTime（异常开始时间）：为空
        //failCount	连续失败次数：0
        //avgDuration（平均耗时）：（查询到的平均耗时+探测耗时）/累计探测次数
        //networkStatus	网络状态：正常
        //4、	结束。
        return false;
    }

    @Override
    public boolean handleCheckCache(LineCheckRecordCacheBO lineCheckRecordCacheBO) {
        //todo
        // 1、	调用IlineCheckRecordCacheService.queryCeckCache	查询专线探测记录缓存信息；
        //2、	缓存不存在，调用IlineCheckRecordCacheService.addCheckCache添加专线探测记录缓存；
        //3、	缓存存在，调用IlineCheckRecordCacheService.updateCheckCache更新专线探测记录缓存；
        //4、	1）连续失败次数=3：
        //打印错误日志：【专线网络状态告警】 (专线编号，专线IDC,专线映射地址); LineNetworkStatusEnum
        //       调用  lineInfoService.updateLineNetInfo(LineInfoBO);更新专线网络状态：告警。
        //2）连续失败次数=5：
        //打印错误日志：【专线网络状态故障】 (专线编号，专线IDC,专线映射地址);
        //       调用  lineInfoService.updateLineNetInfo(LineInfoBO);更新专线网络状态“故障”、探测开关置为“禁用”。
        //5、	checkCount累计探测次数=5：
        //调用llineInfoService.updateLineNetInfo更新专线网络探测信息； LineNetworkStatusEnum
        lineInfoService.updateLineNetInfo(LineInfoBO);
        //调用lineCheckRecordCacheService.deteleCeckCache删除专线探测记录缓存；
        //6、	结束。
        return false;
    }
}
