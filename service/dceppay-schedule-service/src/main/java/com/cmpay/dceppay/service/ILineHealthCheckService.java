package com.cmpay.dceppay.service;

import com.cmpay.dceppay.entity.mng.DcepLineManagementDO;

/**
 * <AUTHOR>
 * @date 2024/12/23 10:00
 * 专线探活服务接口
 */
public interface ILineHealthCheckService {
    
    /**
     * 执行专线探活任务
     */
    void executeHealthCheck();
    
    /**
     * 对单个专线进行探活
     * @param line 专线信息
     */
    void checkSingleLine(DcepLineManagementDO line);
    
    /**
     * 更新专线缓存（选择响应最快的专线）
     */
    void updateLineCache();
    
    /**
     * 获取指定业务类型的最优专线
     * @param businessType 业务类型
     * @return 最优专线
     */
    DcepLineManagementDO getBestLine(String businessType);
}
