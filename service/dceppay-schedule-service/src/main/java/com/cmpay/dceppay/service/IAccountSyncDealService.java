package com.cmpay.dceppay.service;

import com.cmpay.dceppay.bo.pay.AccountQueryBO;
import com.cmpay.dceppay.bo.pay.PayAccountRecordBO;

/**
 * <AUTHOR>
 * @date 2024/9/29 12:34
 */
public interface IAccountSyncDealService {
    /**
     * 账务查询
     * @param accountQueryBO
     */
    void queryAccount(AccountQueryBO accountQueryBO);

    void queryAccountStatus(PayAccountRecordBO accountRecordBO);
}
