package com.cmpay.dceppay.xxl.jobhandler;

import com.cmpay.dceppay.bo.notify.NotifySyncQueryBO;
import com.cmpay.dceppay.constants.ScheduleHandleNumberConstant;
import com.cmpay.dceppay.service.INotifyPayCenterSyncService;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.lemon.framework.data.interceptor.InitialLemonData;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/9/26 14:41
 * 通知补偿
 */
@Component
@Slf4j
public class OrderNotifySync {
    @Autowired
    private INotifyPayCenterSyncService notifyPayCenterSyncService;

    @XxlJob("orderNotifySync")
    @InitialLemonData("lemonDataInitializer")
    public void orderNotifySync() {
        // 获取任务参数
        String param = XxlJobHelper.getJobParam();
        int handleNumber = StringUtils.isNotBlank(param) ? Integer.parseInt(param) : ScheduleHandleNumberConstant.PAY_ORDER_NOTIFY_SYNC_NUMBER;
        //当前分片参数
        int shardIndex = XxlJobHelper.getShardIndex();
        //总分片数
        int shardTotal = XxlJobHelper.getShardTotal();
        NotifySyncQueryBO notifySyncQueryBO = new NotifySyncQueryBO();
        notifySyncQueryBO.setShardIndex(shardIndex);
        notifySyncQueryBO.setShardTotal(shardTotal);
        notifySyncQueryBO.setTotal(handleNumber);
        //todo 同步范围以及同步频次确认 同步当前时间1天之前到当前时间10s订单状态
        LocalDate localDate = DateTimeUtils.parseLocalDate(DateTimeUtils.getCurrentDateStr());
        LocalDateTime localDateTime = DateTimeUtils.parseLocalDateTime(DateTimeUtils.getCurrentDateTimeStr());
        notifySyncQueryBO.setOrderTimeBegin(DateTimeUtils.formatLocalDateTime(localDateTime.minusDays(1)));
        notifySyncQueryBO.setOrderTimeEnd(DateTimeUtils.formatLocalDateTime(localDateTime.minusSeconds(10)));
        notifySyncQueryBO.setRequestDate(DateTimeUtils.formatLocalDate(localDate.minusDays(1)));
        notifyPayCenterSyncService.notifyOrder(notifySyncQueryBO);
    }

}
