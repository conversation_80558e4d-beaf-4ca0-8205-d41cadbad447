package com.cmpay.dceppay.service.impl;

import com.cmpay.dceppay.bo.pay.PayOrderDBBO;
import com.cmpay.dceppay.bo.pay.PayOrderQueryBO;
import com.cmpay.dceppay.bo.pay.RefundOrderDBBO;
import com.cmpay.dceppay.service.IAsyncRefundService;
import com.cmpay.dceppay.service.channel.IPaymentChannelService;
import com.cmpay.dceppay.service.pay.IPayOrderDBService;
import com.cmpay.dceppay.service.pay.IPayRefundDBService;
import com.cmpay.lemon.common.utils.JudgeUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/25 15:34
 */
@Service
@Slf4j
public class AsyncRefundServiceImpl implements IAsyncRefundService {
    @Autowired
    private IPayRefundDBService refundDBService;
    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private IPayOrderDBService payOrderDBService;

    @Override
    public void asyncRefundOrder(PayOrderQueryBO payOrderQueryBO) {
        List<RefundOrderDBBO> refundOrderDBBOList = refundDBService.findWaitPendList(payOrderQueryBO);
        if (JudgeUtils.isEmpty(refundOrderDBBOList)) {
            log.info("shardIndex: {}, 等待退款发起列表为空!!", payOrderQueryBO.getShardIndex());
            return;
        }
        refundOrderDBBOList.forEach(this::refundOrder);

    }

    private void refundOrder(RefundOrderDBBO refundOrderDBBO) {
        PayOrderDBBO payOrderDBBO = payOrderDBService.getByOutOrderNo(refundOrderDBBO.getOutOrderNo());
        if (JudgeUtils.isNull(payOrderDBBO)) {
            log.info("refundOrder order  is null!! refundOrderNO:{},outOrderNo:{}", refundOrderDBBO.getRefundOrderNo(), refundOrderDBBO.getOutOrderNo());
            return;
        }
        try {
            String channelName = payOrderDBBO.getPayWay() + payOrderDBBO.getScene();
            IPaymentChannelService paymentChannelService = (IPaymentChannelService) applicationContext.getBean(channelName);
            refundOrderDBBO.setOriginalMessageIdentification(payOrderDBBO.getMessageIdentification());
            paymentChannelService.refundAsync(refundOrderDBBO);
        } catch (BeansException e) {
            log.error("异步发起退款失败：未找到可用的退款查询接口");
        } catch (Exception e) {
            log.error("异步发起退款失败：{}", e.getCause());
        }
    }
}
