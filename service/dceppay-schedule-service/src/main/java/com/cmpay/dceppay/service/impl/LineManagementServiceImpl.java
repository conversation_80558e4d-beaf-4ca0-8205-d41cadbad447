package com.cmpay.dceppay.service.impl;

import com.cmpay.dceppay.dao.mng.IDcepLineInfoDao;
import com.cmpay.dceppay.entity.mng.DcepLineInfoDO;
import com.cmpay.dceppay.service.ILineManagementService;
import com.cmpay.lemon.framework.utils.IdGenUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/23 10:00
 * 专线管理服务实现类
 */
@Service
@Slf4j
public class LineManagementServiceImpl implements ILineManagementService {
    
    @Autowired
    private IDcepLineInfoDao lineInfoDao;
    
    @Override
    public List<DcepLineInfoDO> getEnabledHealthCheckLines() {
        return lineInfoDao.selectEnabledHealthCheckLines();
    }

    @Override
    public DcepLineInfoDO getDefaultLine(String businessType) {
        return lineInfoDao.selectDefaultLineByBusinessType(businessType);
    }

    @Override
    public DcepLineInfoDO getBestLine(String businessType) {
        return lineInfoDao.selectFastestLineByBusinessType(businessType);
    }
    
    @Override
    public boolean addLine(DcepLineInfoDO line) {
        try {
            line.setId(IdGenUtils.generateId());
            line.setCreateTime(new Date());
            line.setUpdateTime(new Date());

            // 如果设置为默认专线，需要先取消同业务类型的其他默认专线
            if ("Y".equals(line.getIsDefault())) {
                resetDefaultLine(line.getBusinessType());
            }

            int result = lineInfoDao.insert(line);
            return result > 0;
        } catch (Exception e) {
            log.error("新增专线失败: {}", e.getMessage(), e);
            return false;
        }
    }
    
    @Override
    public boolean updateLine(DcepLineInfoDO line) {
        try {
            line.setUpdateTime(new Date());

            // 如果设置为默认专线，需要先取消同业务类型的其他默认专线
            if ("Y".equals(line.getIsDefault())) {
                resetDefaultLine(line.getBusinessType());
            }

            int result = lineInfoDao.updateByPrimaryKey(line);
            return result > 0;
        } catch (Exception e) {
            log.error("更新专线失败: {}", e.getMessage(), e);
            return false;
        }
    }
    
    @Override
    public boolean deleteLine(String lineId) {
        try {
            DcepLineInfoDO line = lineInfoDao.selectByPrimaryKey(lineId);
            if (line == null) {
                log.warn("专线不存在: {}", lineId);
                return false;
            }

            // 物理删除，因为新表结构没有status字段
            int result = lineInfoDao.deleteByPrimaryKey(lineId);
            return result > 0;
        } catch (Exception e) {
            log.error("删除专线失败: {}", e.getMessage(), e);
            return false;
        }
    }
    
    @Override
    public boolean toggleHealthCheck(String lineId, boolean enabled) {
        try {
            DcepLineInfoDO line = lineInfoDao.selectByPrimaryKey(lineId);
            if (line == null) {
                log.warn("专线不存在: {}", lineId);
                return false;
            }

            line.setHealthCheckEnabled(enabled ? "Y" : "N");
            line.setUpdateTime(new Date());

            int result = lineInfoDao.updateByPrimaryKey(line);
            return result > 0;
        } catch (Exception e) {
            log.error("切换专线探活状态失败: {}", e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 重置指定业务类型的默认专线
     */
    private void resetDefaultLine(String businessType) {
        // 这里需要实现将同业务类型的其他专线的is_default设置为'N'
        // 由于BaseDao没有提供批量更新方法，这里可以通过自定义SQL实现
        log.info("重置业务类型 {} 的默认专线", businessType);
    }
}
