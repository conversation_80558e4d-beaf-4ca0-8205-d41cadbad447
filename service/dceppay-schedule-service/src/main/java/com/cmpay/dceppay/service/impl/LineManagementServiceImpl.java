package com.cmpay.dceppay.service.impl;

import com.cmpay.dceppay.dao.mng.IDcepLineManagementDao;
import com.cmpay.dceppay.entity.mng.DcepLineManagementDO;
import com.cmpay.dceppay.service.ILineManagementService;
import com.cmpay.lemon.framework.utils.IdGenUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/23 10:00
 * 专线管理服务实现类
 */
@Service
@Slf4j
public class LineManagementServiceImpl implements ILineManagementService {
    
    @Autowired
    private IDcepLineManagementDao lineManagementDao;
    
    @Override
    public List<DcepLineManagementDO> getEnabledHealthCheckLines() {
        return lineManagementDao.selectEnabledHealthCheckLines();
    }
    
    @Override
    public DcepLineManagementDO getDefaultLine(String businessType) {
        return lineManagementDao.selectDefaultLineByBusinessType(businessType);
    }
    
    @Override
    public DcepLineManagementDO getBestLine(String businessType) {
        return lineManagementDao.selectFastestLineByBusinessType(businessType);
    }
    
    @Override
    public boolean addLine(DcepLineManagementDO line) {
        try {
            line.setId(IdGenUtils.generateId());
            line.setCreateTime(new Date());
            line.setUpdateTime(new Date());
            line.setStatus("A"); // 默认有效状态
            
            // 如果设置为默认专线，需要先取消同业务类型的其他默认专线
            if ("Y".equals(line.getIsDefault())) {
                resetDefaultLine(line.getBusinessType());
            }
            
            int result = lineManagementDao.insert(line);
            return result > 0;
        } catch (Exception e) {
            log.error("新增专线失败: {}", e.getMessage(), e);
            return false;
        }
    }
    
    @Override
    public boolean updateLine(DcepLineManagementDO line) {
        try {
            line.setUpdateTime(new Date());
            
            // 如果设置为默认专线，需要先取消同业务类型的其他默认专线
            if ("Y".equals(line.getIsDefault())) {
                resetDefaultLine(line.getBusinessType());
            }
            
            int result = lineManagementDao.updateByPrimaryKey(line);
            return result > 0;
        } catch (Exception e) {
            log.error("更新专线失败: {}", e.getMessage(), e);
            return false;
        }
    }
    
    @Override
    public boolean deleteLine(String lineId) {
        try {
            DcepLineManagementDO line = lineManagementDao.selectByPrimaryKey(lineId);
            if (line == null) {
                log.warn("专线不存在: {}", lineId);
                return false;
            }
            
            line.setStatus("I"); // 逻辑删除
            line.setUpdateTime(new Date());
            
            int result = lineManagementDao.updateByPrimaryKey(line);
            return result > 0;
        } catch (Exception e) {
            log.error("删除专线失败: {}", e.getMessage(), e);
            return false;
        }
    }
    
    @Override
    public boolean toggleHealthCheck(String lineId, boolean enabled) {
        try {
            DcepLineManagementDO line = lineManagementDao.selectByPrimaryKey(lineId);
            if (line == null) {
                log.warn("专线不存在: {}", lineId);
                return false;
            }
            
            line.setHealthCheckEnabled(enabled ? "Y" : "N");
            line.setUpdateTime(new Date());
            
            int result = lineManagementDao.updateByPrimaryKey(line);
            return result > 0;
        } catch (Exception e) {
            log.error("切换专线探活状态失败: {}", e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 重置指定业务类型的默认专线
     */
    private void resetDefaultLine(String businessType) {
        // 这里需要实现将同业务类型的其他专线的is_default设置为'N'
        // 由于BaseDao没有提供批量更新方法，这里可以通过自定义SQL实现
        log.info("重置业务类型 {} 的默认专线", businessType);
    }
}
