package com.cmpay.dceppay.xxl.jobhandler;

import com.cmpay.dceppay.bo.mng.BusParamBO;
import com.cmpay.dceppay.enums.dcep.FinancialStatusEnum;
import com.cmpay.dceppay.service.mng.IDcepLoginService;
import com.cmpay.dceppay.service.mng.IFinancialBusParamService;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.data.interceptor.InitialLemonData;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/11/5 16:37
 * 本地离线状态，定时登录
 */
@Component
@Slf4j
public class LoginDcepSchedule {
    //本地为离线状态，系统应尝试自动登录，登录失败后每15min重试一次，重试三次仍失败需告警处理，由运营人员人工登录。

    @Autowired
    private IDcepLoginService loginService;
    @Autowired
    private IFinancialBusParamService paramService;


    //每30s触发一次
    @XxlJob("loginDcepSchedule")
    @InitialLemonData("lemonDataInitializer")
    public void loginDcepSchedule() {
        BusParamBO busParamBO = paramService.getFinancialStatus();
        if (JudgeUtils.equals(busParamBO.getParamCode(), FinancialStatusEnum.OFF.name())) {
            try {
                loginService.login(null);
            } catch (Exception e) {
                log.error("【自动登录互联互通平台失败】"+e.getCause());
            }
        }

    }
}
