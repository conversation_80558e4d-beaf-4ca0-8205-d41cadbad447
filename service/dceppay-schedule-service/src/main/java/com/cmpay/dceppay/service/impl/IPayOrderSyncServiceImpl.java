package com.cmpay.dceppay.service.impl;

import com.cmpay.dceppay.bo.pay.PayOrderDBBO;
import com.cmpay.dceppay.bo.pay.PayOrderQueryBO;
import com.cmpay.dceppay.bo.paymentquery.PaymentOrderQueryBO;
import com.cmpay.dceppay.bo.paymentquery.PaymentOrderResultBO;
import com.cmpay.dceppay.enums.pay.OrderStatusEnum;
import com.cmpay.dceppay.service.IPayOrderSyncService;
import com.cmpay.dceppay.service.channel.IPaymentChannelService;
import com.cmpay.dceppay.service.pay.IPayOrderDBService;
import com.cmpay.dceppay.service.pay.IPayOrderService;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.data.interceptor.InitialLemonData;
import com.cmpay.lemon.framework.lock.DistributedLocked;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/10 15:03
 */
@Service
@Slf4j
public class IPayOrderSyncServiceImpl implements IPayOrderSyncService {
    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private IPayOrderDBService payOrderDBService;
    @Autowired
    private IPayOrderService payOrderService;


    @Override
    public void syncPayOrder(PayOrderQueryBO payOrderQueryBO) {
        List<PayOrderDBBO> payOrderDBBOList = payOrderDBService.findWaitPayList(payOrderQueryBO);
        if (JudgeUtils.isEmpty(payOrderDBBOList)) {
            log.info("shardIndex: {}, 等待支付列表为空!!", payOrderQueryBO.getShardIndex());
            return;
        }
        payOrderDBBOList.forEach(this::syncSinglePayOrder);
    }


    @DistributedLocked(lockName = "'syncSinglePayOrder:'+#payOrderDBBO.getOutOrderNo()", leaseTime = 40, waitTime = 10)
    private void syncSinglePayOrder(PayOrderDBBO payOrderDBBO) {
        try {
            String channelName = payOrderDBBO.getPayWay() + payOrderDBBO.getScene();
            IPaymentChannelService paymentChannelService = (IPaymentChannelService) applicationContext.getBean(channelName);
            PaymentOrderQueryBO orderQueryBO = buildPaymentOrderQueryBO(payOrderDBBO);
            PaymentOrderResultBO paymentOrderResultBO = paymentChannelService.paymentQuery(orderQueryBO);
            if (JudgeUtils.isNotNull(paymentOrderResultBO.getOrderStatus())) {
                OrderStatusEnum orderStatusEnum = OrderStatusEnum.valueOf(paymentOrderResultBO.getOrderStatus());
                if (paymentOrderResultBO.getTotalAmount().equals(payOrderDBBO.getOrderAmount())) {
                    handleOrderStatus(orderStatusEnum, paymentOrderResultBO, payOrderDBBO);
                }
            }
        } catch (BeansException e) {
            log.error("syncSinglePayOrder执行失败：未找到可用的支付查询接口");
        } catch (Exception e) {
            log.error("syncSinglePayOrder执行失败：{}", e.getCause());
        }
    }

    private PaymentOrderQueryBO buildPaymentOrderQueryBO(PayOrderDBBO payOrderDBBO) {
        PaymentOrderQueryBO orderQueryBO = new PaymentOrderQueryBO();
        orderQueryBO.setOutOrderNo(payOrderDBBO.getOutOrderNo());
        orderQueryBO.setOriginalMessageIdentification(payOrderDBBO.getMessageIdentification());
        orderQueryBO.setMerchantId(payOrderDBBO.getOrgMerchantNo());
        orderQueryBO.setBankOrderNo(payOrderDBBO.getBankOrderNo());
        orderQueryBO.setWalletId(payOrderDBBO.getWalletId());
        return orderQueryBO;
    }


    private void handleOrderStatus(OrderStatusEnum orderStatusEnum, PaymentOrderResultBO paymentOrderResultBO, PayOrderDBBO payOrderDBBO) {
        switch (orderStatusEnum) {
            case TRADE_SUCCESS:
                payOrderService.handleSuccessPayOrder(paymentOrderResultBO, payOrderDBBO);
                break;
            case TRADE_FAIL:
                payOrderService.handleFailPayOrder(paymentOrderResultBO, payOrderDBBO);
                break;
            default:
                break;
        }
    }

}
