package com.cmpay.dceppay.xxl.jobhandler;

import com.cmpay.dceppay.bo.pay.AccountQueryBO;
import com.cmpay.dceppay.constants.ScheduleHandleNumberConstant;
import com.cmpay.dceppay.service.IAccountSyncDealService;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.lemon.framework.data.interceptor.InitialLemonData;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/9/26 14:40
 * 账务处理查询
 */
@Component
@Slf4j
public class AccountSync {
    @Autowired
    private IAccountSyncDealService accountSyncDealService;

    @XxlJob("accountSync")
    @InitialLemonData("lemonDataInitializer")
    public void queryAccountResult() {
        // 获取任务参数
        String param = XxlJobHelper.getJobParam();
        int handleNumber = StringUtils.isNotBlank(param) ? Integer.parseInt(param) : ScheduleHandleNumberConstant.ACCOUNT_STATUS_SYNC_NUMBER;
        //当前分片参数
        int shardIndex = XxlJobHelper.getShardIndex();
        //总分片数
        int shardTotal = XxlJobHelper.getShardTotal();
        AccountQueryBO accountQueryBO = new AccountQueryBO();
        accountQueryBO.setShardIndex(shardIndex);
        accountQueryBO.setShardTotal(shardTotal);
        accountQueryBO.setTotal(handleNumber);
        LocalDate localDate = DateTimeUtils.parseLocalDate(DateTimeUtils.getCurrentDateStr());
        LocalDateTime localDateTime = DateTimeUtils.parseLocalDateTime(DateTimeUtils.getCurrentDateTimeStr());
        accountQueryBO.setTimeBegin(DateTimeUtils.formatLocalDateTime(localDateTime.minusDays(3)));
        accountQueryBO.setTimeEnd(DateTimeUtils.formatLocalDateTime(localDateTime.minusSeconds(10)));
        accountQueryBO.setRequestDate(DateTimeUtils.formatLocalDate(localDate.minusDays(3)));
        accountSyncDealService.queryAccount(accountQueryBO);
    }


}
