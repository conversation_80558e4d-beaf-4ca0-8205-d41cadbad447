package com.cmpay.dceppay.service.impl;

import com.cmpay.dceppay.constant.mng.LineManagementConstants;
import com.cmpay.dceppay.dao.mng.IDcepLineHealthRecordDao;
import com.cmpay.dceppay.dao.mng.IDcepLineManagementDao;
import com.cmpay.dceppay.entity.mng.DcepLineHealthRecordDO;
import com.cmpay.dceppay.entity.mng.DcepLineManagementDO;
import com.cmpay.dceppay.service.ILineHealthCheckService;
import com.cmpay.lemon.framework.utils.IdGenUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.Socket;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024/12/23 10:00
 * 专线探活服务实现类
 */
@Service
@Slf4j
public class LineHealthCheckServiceImpl implements ILineHealthCheckService {
    
    @Autowired
    private IDcepLineManagementDao lineManagementDao;
    
    @Autowired
    private IDcepLineHealthRecordDao healthRecordDao;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    private static final String CACHE_KEY_PREFIX = LineManagementConstants.CACHE_KEY_PREFIX;
    private static final int PING_TIMEOUT = LineManagementConstants.PING_TIMEOUT_MS;
    private static final int WARNING_FAIL_COUNT = LineManagementConstants.WARNING_FAIL_COUNT;
    private static final int FAULT_FAIL_COUNT = LineManagementConstants.FAULT_FAIL_COUNT;
    private static final int AVG_CALC_COUNT = LineManagementConstants.AVG_CALC_COUNT;
    
    @Override
    public void executeHealthCheck() {
        log.info("开始执行专线探活任务");
        
        // 查询所有启用探活的专线
        List<DcepLineManagementDO> lines = lineManagementDao.selectEnabledHealthCheckLines();
        
        if (lines == null || lines.isEmpty()) {
            log.warn("没有找到启用探活的专线");
            return;
        }
        
        // 对每个专线进行探活
        for (DcepLineManagementDO line : lines) {
            try {
                checkSingleLine(line);
            } catch (Exception e) {
                log.error("专线探活异常，专线ID: {}, 错误: {}", line.getId(), e.getMessage(), e);
            }
        }
        
        // 更新专线缓存
        updateLineCache();
        
        log.info("专线探活任务执行完成，共检查 {} 条专线", lines.size());
    }
    
    @Override
    public void checkSingleLine(DcepLineManagementDO line) {
        String lineId = line.getId();
        String address = line.getLineMappingAddress();
        
        log.debug("开始探活专线: {}, 地址: {}", line.getLineName(), address);
        
        // 解析IP和端口
        String[] parts = address.split(":");
        if (parts.length != 2) {
            log.error("专线地址格式错误: {}", address);
            return;
        }
        
        String ip = parts[0];
        int port;
        try {
            port = Integer.parseInt(parts[1]);
        } catch (NumberFormatException e) {
            log.error("专线端口格式错误: {}", parts[1]);
            return;
        }
        
        // 执行探活
        long startTime = System.currentTimeMillis();
        boolean isSuccess = false;
        String errorMessage = null;

        try {
            isSuccess = telnetCheck(ip, port);
        } catch (Exception e) {
            errorMessage = e.getMessage();
            log.warn("专线探活失败: {}, 错误: {}", address, errorMessage);
        }

        long responseTime = System.currentTimeMillis() - startTime;

        // 记录探活结果
        recordHealthCheck(lineId, LineManagementConstants.CHECK_TYPE_TELNET, responseTime, isSuccess, errorMessage);
        
        // 更新专线状态
        updateLineStatus(line, isSuccess, responseTime);
    }
    
    /**
     * Telnet连接检查
     */
    private boolean telnetCheck(String ip, int port) throws IOException {
        try (Socket socket = new Socket()) {
            socket.connect(new InetSocketAddress(ip, port), PING_TIMEOUT);
            return true;
        }
    }
    
    /**
     * 记录探活结果
     */
    private void recordHealthCheck(String lineId, String checkType, long responseTime, 
                                 boolean isSuccess, String errorMessage) {
        DcepLineHealthRecordDO record = new DcepLineHealthRecordDO();
        record.setId(IdGenUtils.generateId());
        record.setLineId(lineId);
        record.setCheckTime(new Date());
        record.setCheckType(checkType);
        record.setResponseTime(responseTime);
        record.setCheckResult(isSuccess ? LineManagementConstants.CHECK_RESULT_SUCCESS : LineManagementConstants.CHECK_RESULT_FAIL);
        record.setErrorMessage(errorMessage);
        record.setCreateTime(new Date());
        
        healthRecordDao.insert(record);
    }
    
    /**
     * 更新专线状态
     */
    private void updateLineStatus(DcepLineManagementDO line, boolean isSuccess, long responseTime) {
        String lineId = line.getId();
        Integer currentFailCount = line.getConsecutiveFailCount() != null ? line.getConsecutiveFailCount() : 0;
        String currentStatus = line.getNetworkStatus();
        
        String newStatus;
        Integer newFailCount;
        Date failStartTime = line.getHealthCheckFailStartTime();
        
        if (isSuccess) {
            // 探活成功，重置失败计数和状态
            newStatus = LineManagementConstants.NETWORK_STATUS_NORMAL;
            newFailCount = 0;
            failStartTime = null;

            if (!LineManagementConstants.NETWORK_STATUS_NORMAL.equals(currentStatus)) {
                log.info("专线恢复正常: {}, 原状态: {}", line.getLineName(), currentStatus);
            }
        } else {
            // 探活失败，增加失败计数
            newFailCount = currentFailCount + 1;

            if (newFailCount >= FAULT_FAIL_COUNT) {
                newStatus = LineManagementConstants.NETWORK_STATUS_FAULT;
                if (failStartTime == null) {
                    failStartTime = new Date();
                }

                if (!LineManagementConstants.NETWORK_STATUS_FAULT.equals(currentStatus)) {
                    log.error("专线故障告警: {}, 连续失败次数: {}", line.getLineName(), newFailCount);
                }
            } else if (newFailCount >= WARNING_FAIL_COUNT) {
                newStatus = LineManagementConstants.NETWORK_STATUS_WARNING;
                if (failStartTime == null) {
                    failStartTime = new Date();
                }

                if (!LineManagementConstants.NETWORK_STATUS_WARNING.equals(currentStatus) &&
                    !LineManagementConstants.NETWORK_STATUS_FAULT.equals(currentStatus)) {
                    log.warn("专线告警: {}, 连续失败次数: {}", line.getLineName(), newFailCount);
                }
            } else {
                newStatus = currentStatus != null ? currentStatus : LineManagementConstants.NETWORK_STATUS_NORMAL;
            }
        }
        
        // 计算平均响应时间
        Long avgResponseTime = calculateAvgResponseTime(lineId);
        
        // 更新数据库
        lineManagementDao.updateNetworkStatus(lineId, newStatus, newFailCount, failStartTime, avgResponseTime);
    }
    
    /**
     * 计算平均响应时间
     */
    private Long calculateAvgResponseTime(String lineId) {
        return healthRecordDao.calculateAvgResponseTime(lineId, AVG_CALC_COUNT);
    }
    
    @Override
    public void updateLineCache() {
        log.debug("开始更新专线缓存");
        
        // 更新API业务类型的最优专线
        DcepLineManagementDO apiBestLine = lineManagementDao.selectFastestLineByBusinessType(LineManagementConstants.BUSINESS_TYPE_API);
        if (apiBestLine != null) {
            redisTemplate.opsForValue().set(CACHE_KEY_PREFIX + LineManagementConstants.BUSINESS_TYPE_API,
                    apiBestLine, LineManagementConstants.CACHE_EXPIRE_MINUTES, TimeUnit.MINUTES);
            log.info("更新API最优专线缓存: {}, 平均响应时间: {}ms",
                    apiBestLine.getLineName(), apiBestLine.getResponseTimeAvg());
        }

        // 更新SFTP业务类型的最优专线
        DcepLineManagementDO sftpBestLine = lineManagementDao.selectFastestLineByBusinessType(LineManagementConstants.BUSINESS_TYPE_SFTP);
        if (sftpBestLine != null) {
            redisTemplate.opsForValue().set(CACHE_KEY_PREFIX + LineManagementConstants.BUSINESS_TYPE_SFTP,
                    sftpBestLine, LineManagementConstants.CACHE_EXPIRE_MINUTES, TimeUnit.MINUTES);
            log.info("更新SFTP最优专线缓存: {}, 平均响应时间: {}ms",
                    sftpBestLine.getLineName(), sftpBestLine.getResponseTimeAvg());
        }
    }
    
    @Override
    public DcepLineManagementDO getBestLine(String businessType) {
        // 先从缓存获取
        DcepLineManagementDO cachedLine = (DcepLineManagementDO) redisTemplate.opsForValue()
                .get(CACHE_KEY_PREFIX + businessType);
        
        if (cachedLine != null) {
            return cachedLine;
        }
        
        // 缓存未命中，从数据库获取
        DcepLineManagementDO bestLine = lineManagementDao.selectFastestLineByBusinessType(businessType);
        
        if (bestLine != null) {
            // 更新缓存
            redisTemplate.opsForValue().set(CACHE_KEY_PREFIX + businessType, bestLine,
                    LineManagementConstants.CACHE_EXPIRE_MINUTES, TimeUnit.MINUTES);
        }
        
        return bestLine;
    }
}
