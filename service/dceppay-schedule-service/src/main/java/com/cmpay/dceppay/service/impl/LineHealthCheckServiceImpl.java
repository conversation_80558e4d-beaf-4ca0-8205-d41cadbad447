package com.cmpay.dceppay.service.impl;

import com.cmpay.dceppay.constant.mng.LineManagementConstants;
import com.cmpay.dceppay.dao.mng.IDcepLineHealthRecordDao;
import com.cmpay.dceppay.dao.mng.IDcepLineInfoDao;
import com.cmpay.dceppay.entity.mng.DcepLineHealthRecordDO;
import com.cmpay.dceppay.entity.mng.DcepLineInfoDO;
import com.cmpay.dceppay.service.ILineHealthCheckService;
import com.cmpay.dceppay.util.NetworkConnectivityUtil;
import com.cmpay.lemon.framework.utils.IdGenUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.Serializable;
import java.net.InetSocketAddress;
import java.net.Socket;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024/12/23 10:00
 * 专线探活服务实现类
 */
@Service
@Slf4j
public class LineHealthCheckServiceImpl implements ILineHealthCheckService {

    /**
     * 探活缓存数据结构
     */
    private static class HealthCheckCache implements Serializable {
        private static final long serialVersionUID = 1L;
        private List<Long> responseTimes = new ArrayList<>();
        private List<Boolean> results = new ArrayList<>();
        private int checkCount = 0;

        public void addResult(long responseTime, boolean success) {
            responseTimes.add(responseTime);
            results.add(success);
            checkCount++;
        }

        public boolean shouldUpdateStatus() {
            return checkCount >= BATCH_CHECK_COUNT;
        }

        public double getAverageResponseTime() {
            return responseTimes.stream()
                    .filter(time -> time > 0)
                    .mapToLong(Long::longValue)
                    .average()
                    .orElse(0.0);
        }

        public int getSuccessCount() {
            return (int) results.stream().mapToInt(b -> b ? 1 : 0).sum();
        }

        public int getFailCount() {
            return results.size() - getSuccessCount();
        }

        public void reset() {
            responseTimes.clear();
            results.clear();
            checkCount = 0;
        }
    }
    
    @Autowired
    private IDcepLineInfoDao lineInfoDao;
    
    @Autowired
    private IDcepLineHealthRecordDao healthRecordDao;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    private static final String CACHE_KEY_PREFIX = LineManagementConstants.CACHE_KEY_PREFIX;
    private static final String HEALTH_CHECK_CACHE_PREFIX = "dcep:line:health:";
    private static final int PING_TIMEOUT = LineManagementConstants.PING_TIMEOUT_MS;
    private static final int WARNING_FAIL_COUNT = LineManagementConstants.WARNING_FAIL_COUNT;
    private static final int FAULT_FAIL_COUNT = LineManagementConstants.FAULT_FAIL_COUNT;
    private static final int AVG_CALC_COUNT = LineManagementConstants.AVG_CALC_COUNT;
    private static final int BATCH_CHECK_COUNT = 5; // 每5次探测后更新状态
    
    @Override
    public void executeHealthCheck() {
        log.info("开始执行专线探活任务");
        
        // 查询所有启用探活的专线
        List<DcepLineInfoDO> lines = lineInfoDao.selectEnabledHealthCheckLines();
        
        if (lines == null || lines.isEmpty()) {
            log.warn("没有找到启用探活的专线");
            return;
        }
        
        // 对每个专线进行探活
        for (DcepLineInfoDO line : lines) {
            try {
                checkSingleLine(line);
            } catch (Exception e) {
                log.error("专线探活异常，专线ID: {}, 错误: {}", line.getId(), e.getMessage(), e);
            }
        }
        
        // 更新专线缓存
        updateLineCache();
        
        log.info("专线探活任务执行完成，共检查 {} 条专线", lines.size());
    }
    
    @Override
    public void checkSingleLine(DcepLineInfoDO line) {
        String lineId = line.getId();
        String address = line.getLineMappingAddress();

        log.debug("开始探活专线: {}, 地址: {}", line.getLineName(), address);

        // 使用新的连通性检测工具
        NetworkConnectivityUtil.ConnectivityResult result = NetworkConnectivityUtil.checkConnectivity(address);

        boolean isSuccess = result.isPingSuccess() && result.isTelnetSuccess();
        long responseTime = result.getTotalTime();
        String errorMessage = result.getErrorMessage();

        if (isSuccess) {
            log.debug("专线探活成功: {}, ping: {}ms, telnet: {}ms, 总耗时: {}ms",
                    address, result.getPingTime(), result.getTelnetTime(), responseTime);
        } else {
            log.warn("专线探活失败: {}, 错误: {}, ping成功: {}, telnet成功: {}, 总耗时: {}ms",
                    address, errorMessage, result.isPingSuccess(), result.isTelnetSuccess(), responseTime);
        }

        // 记录探活结果到数据库
        String checkType = result.isPingSuccess() ?
                (result.isTelnetSuccess() ? "PING_TELNET_SUCCESS" : "PING_SUCCESS_TELNET_FAIL") :
                "PING_FAIL";
        recordHealthCheck(lineId, checkType, responseTime, isSuccess, errorMessage);

        // 更新缓存中的探活结果
        updateHealthCheckCache(lineId, responseTime, isSuccess);

        // 检查是否需要更新专线状态（每5次探测后）
        if (shouldUpdateLineStatus(lineId)) {
            updateLineStatusFromCache(line);
        }
    }
    


    /**
     * 更新探活缓存
     */
    private void updateHealthCheckCache(String lineId, long responseTime, boolean isSuccess) {
        String cacheKey = HEALTH_CHECK_CACHE_PREFIX + lineId;
        HealthCheckCache cache = (HealthCheckCache) redisTemplate.opsForValue().get(cacheKey);

        if (cache == null) {
            cache = new HealthCheckCache();
        }

        cache.addResult(responseTime, isSuccess);

        // 缓存30分钟
        redisTemplate.opsForValue().set(cacheKey, cache, 30, TimeUnit.MINUTES);

        log.debug("更新专线探活缓存: {}, 当前次数: {}, 成功: {}, 响应时间: {}ms",
                lineId, cache.checkCount, isSuccess, responseTime);
    }

    /**
     * 检查是否应该更新专线状态
     */
    private boolean shouldUpdateLineStatus(String lineId) {
        String cacheKey = HEALTH_CHECK_CACHE_PREFIX + lineId;
        HealthCheckCache cache = (HealthCheckCache) redisTemplate.opsForValue().get(cacheKey);

        return cache != null && cache.shouldUpdateStatus();
    }

    /**
     * 从缓存更新专线状态
     */
    private void updateLineStatusFromCache(DcepLineInfoDO line) {
        String lineId = line.getId();
        String cacheKey = HEALTH_CHECK_CACHE_PREFIX + lineId;
        HealthCheckCache cache = (HealthCheckCache) redisTemplate.opsForValue().get(cacheKey);

        if (cache == null) {
            log.warn("专线探活缓存不存在: {}", lineId);
            return;
        }

        int successCount = cache.getSuccessCount();
        int failCount = cache.getFailCount();
        double avgResponseTime = cache.getAverageResponseTime();

        log.info("专线 {} 最近{}次探活结果: 成功{}次, 失败{}次, 平均响应时间: {:.2f}ms",
                line.getLineName(), BATCH_CHECK_COUNT, successCount, failCount, avgResponseTime);

        // 判断网络状态
        String newStatus;
        Integer currentFailCount = line.getConsecutiveFailCount() != null ? line.getConsecutiveFailCount() : 0;
        Date failStartTime = line.getHealthCheckFailStartTime();

        if (successCount > failCount) {
            // 成功次数多于失败次数，认为网络正常
            newStatus = LineManagementConstants.NETWORK_STATUS_NORMAL;
            currentFailCount = 0;
            failStartTime = null;

            if (!LineManagementConstants.NETWORK_STATUS_NORMAL.equals(line.getNetworkStatus())) {
                log.info("专线恢复正常: {}, 原状态: {}", line.getLineName(), line.getNetworkStatus());
            }
        } else {
            // 失败次数多于成功次数，累计失败次数
            currentFailCount += failCount;

            if (currentFailCount >= FAULT_FAIL_COUNT) {
                newStatus = LineManagementConstants.NETWORK_STATUS_FAULT;
                if (failStartTime == null) {
                    failStartTime = new Date();
                }

                if (!LineManagementConstants.NETWORK_STATUS_FAULT.equals(line.getNetworkStatus())) {
                    log.error("专线故障告警: {}, 累计失败次数: {}", line.getLineName(), currentFailCount);
                }
            } else if (currentFailCount >= WARNING_FAIL_COUNT) {
                newStatus = LineManagementConstants.NETWORK_STATUS_WARNING;
                if (failStartTime == null) {
                    failStartTime = new Date();
                }

                if (!LineManagementConstants.NETWORK_STATUS_WARNING.equals(line.getNetworkStatus()) &&
                    !LineManagementConstants.NETWORK_STATUS_FAULT.equals(line.getNetworkStatus())) {
                    log.warn("专线告警: {}, 累计失败次数: {}", line.getLineName(), currentFailCount);
                }
            } else {
                newStatus = line.getNetworkStatus() != null ? line.getNetworkStatus() : LineManagementConstants.NETWORK_STATUS_NORMAL;
            }
        }

        // 更新数据库
        lineInfoDao.updateNetworkStatus(lineId, newStatus, failStartTime);

        // 重置缓存
        cache.reset();
        redisTemplate.opsForValue().set(cacheKey, cache, 30, TimeUnit.MINUTES);
    }
    
    /**
     * 记录探活结果
     */
    private void recordHealthCheck(String lineId, String checkType, long responseTime, 
                                 boolean isSuccess, String errorMessage) {
        DcepLineHealthRecordDO record = new DcepLineHealthRecordDO();
        record.setId(IdGenUtils.generateId());
        record.setLineId(lineId);
        record.setCheckTime(new Date());
        record.setCheckType(checkType);
        record.setResponseTime(responseTime);
        record.setCheckResult(isSuccess ? LineManagementConstants.CHECK_RESULT_SUCCESS : LineManagementConstants.CHECK_RESULT_FAIL);
        record.setErrorMessage(errorMessage);
        record.setCreateTime(new Date());
        
        healthRecordDao.insert(record);
    }
    

    
    /**
     * 计算平均响应时间
     */
    private Long calculateAvgResponseTime(String lineId) {
        return healthRecordDao.calculateAvgResponseTime(lineId, AVG_CALC_COUNT);
    }
    
    @Override
    public void updateLineCache() {
        // 检查是否有专线完成了5次探测，如果有则更新缓存
        List<DcepLineInfoDO> lines = lineInfoDao.selectEnabledHealthCheckLines();
        boolean shouldUpdate = false;

        for (DcepLineInfoDO line : lines) {
            if (shouldUpdateLineStatus(line.getId())) {
                shouldUpdate = true;
                break;
            }
        }

        if (!shouldUpdate) {
            log.debug("没有专线完成5次探测，跳过缓存更新");
            return;
        }

        log.info("开始更新专线缓存");

        // 更新API业务类型的最优专线
        DcepLineInfoDO apiBestLine = lineInfoDao.selectFastestLineByBusinessType(LineManagementConstants.BUSINESS_TYPE_API);
        if (apiBestLine != null) {
            redisTemplate.opsForValue().set(CACHE_KEY_PREFIX + LineManagementConstants.BUSINESS_TYPE_API,
                    apiBestLine, LineManagementConstants.CACHE_EXPIRE_MINUTES, TimeUnit.MINUTES);
            log.info("更新API最优专线缓存: {}", apiBestLine.getLineName());
        }

        // 更新SFTP业务类型的最优专线
        DcepLineInfoDO sftpBestLine = lineInfoDao.selectFastestLineByBusinessType(LineManagementConstants.BUSINESS_TYPE_SFTP);
        if (sftpBestLine != null) {
            redisTemplate.opsForValue().set(CACHE_KEY_PREFIX + LineManagementConstants.BUSINESS_TYPE_SFTP,
                    sftpBestLine, LineManagementConstants.CACHE_EXPIRE_MINUTES, TimeUnit.MINUTES);
            log.info("更新SFTP最优专线缓存: {}", sftpBestLine.getLineName());
        }
    }
    
    @Override
    public DcepLineInfoDO getBestLine(String businessType) {
        // 先从缓存获取
        DcepLineInfoDO cachedLine = (DcepLineInfoDO) redisTemplate.opsForValue()
                .get(CACHE_KEY_PREFIX + businessType);

        if (cachedLine != null) {
            return cachedLine;
        }

        // 缓存未命中，从数据库获取
        DcepLineInfoDO bestLine = lineInfoDao.selectFastestLineByBusinessType(businessType);
        
        if (bestLine != null) {
            // 更新缓存
            redisTemplate.opsForValue().set(CACHE_KEY_PREFIX + businessType, bestLine,
                    LineManagementConstants.CACHE_EXPIRE_MINUTES, TimeUnit.MINUTES);
        }
        
        return bestLine;
    }
}
