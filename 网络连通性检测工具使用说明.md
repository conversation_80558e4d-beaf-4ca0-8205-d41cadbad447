# 网络连通性检测工具使用说明

## 概述

`NetworkConnectivityUtil` 是一个用于检测IP:端口连通性的工具类，支持ping和telnet两种检测方式，超时时间为200ms。

## 功能特性

1. **先ping后telnet**: 只有ping通后才会进行telnet检测
2. **快速响应**: 超时时间200ms，适合高频检测
3. **详细结果**: 返回ping时间、telnet时间、总耗时等详细信息
4. **异常处理**: 完善的异常处理和错误信息返回
5. **灵活调用**: 支持字符串地址和分离的IP、端口参数

## 使用方法

### 1. 基本用法

```java
import com.cmpay.dceppay.util.NetworkConnectivityUtil;
import com.cmpay.dceppay.util.NetworkConnectivityUtil.ConnectivityResult;

// 检测字符串格式地址
ConnectivityResult result = NetworkConnectivityUtil.checkConnectivity("*************:8080");

// 检测分离的IP和端口
ConnectivityResult result = NetworkConnectivityUtil.checkConnectivity("*************", 8080);
```

### 2. 结果解析

```java
ConnectivityResult result = NetworkConnectivityUtil.checkConnectivity("www.baidu.com:80");

// 检查ping是否成功
if (result.isPingSuccess()) {
    System.out.println("Ping成功，耗时: " + result.getPingTime() + "ms");
}

// 检查telnet是否成功
if (result.isTelnetSuccess()) {
    System.out.println("Telnet成功，耗时: " + result.getTelnetTime() + "ms");
}

// 检查整体是否成功（ping和telnet都成功）
if (result.isPingSuccess() && result.isTelnetSuccess()) {
    System.out.println("连通性检测成功，总耗时: " + result.getTotalTime() + "ms");
} else {
    System.out.println("连通性检测失败: " + result.getErrorMessage());
}
```

### 3. 在专线探活中的应用

```java
@Override
public void checkSingleLine(DcepLineManagementDO line) {
    String address = line.getLineMappingAddress();
    
    // 使用连通性检测工具
    ConnectivityResult result = NetworkConnectivityUtil.checkConnectivity(address);
    
    // 判断是否成功（ping和telnet都成功才算成功）
    boolean isSuccess = result.isPingSuccess() && result.isTelnetSuccess();
    long responseTime = result.getTotalTime();
    
    // 记录详细的检测类型
    String checkType;
    if (result.isPingSuccess()) {
        checkType = result.isTelnetSuccess() ? "PING_TELNET_SUCCESS" : "PING_SUCCESS_TELNET_FAIL";
    } else {
        checkType = "PING_FAIL";
    }
    
    // 后续处理...
}
```

## ConnectivityResult 结果对象

### 属性说明

| 属性 | 类型 | 说明 |
|------|------|------|
| pingSuccess | boolean | ping是否成功 |
| telnetSuccess | boolean | telnet是否成功 |
| pingTime | long | ping耗时（毫秒） |
| telnetTime | long | telnet耗时（毫秒） |
| totalTime | long | 总耗时（毫秒） |
| errorMessage | String | 错误信息 |

### 方法说明

```java
// 判断整体是否成功
boolean isOverallSuccess = result.isPingSuccess() && result.isTelnetSuccess();

// 获取格式化的结果字符串
String resultString = result.toString();
// 输出示例: ConnectivityResult{ping=true(15ms), telnet=true(25ms), total=45ms, error='null'}
```

## 检测流程

1. **地址解析**: 解析IP:端口格式的地址
2. **参数验证**: 验证IP格式和端口范围(1-65535)
3. **Ping检测**: 使用 `InetAddress.isReachable()` 进行ping检测
4. **Telnet检测**: 如果ping成功，使用Socket连接进行telnet检测
5. **结果汇总**: 统计各阶段耗时和最终结果

## 性能特点

- **超时时间**: 200ms（ping和telnet各200ms）
- **最大耗时**: 约400ms（ping失败200ms + telnet失败200ms）
- **最小耗时**: 约几毫秒（本地地址或缓存命中）
- **内存占用**: 极小，无状态工具类

## 错误处理

### 常见错误类型

1. **地址为空**: "地址为空"
2. **格式错误**: "地址格式错误，应为IP:端口"
3. **端口超范围**: "端口号超出范围(1-65535)"
4. **端口格式错误**: "端口号格式错误"
5. **Ping失败**: "Ping失败"
6. **Telnet失败**: "Telnet失败"
7. **检测异常**: "检测异常: {具体异常信息}"

### 异常处理示例

```java
ConnectivityResult result = NetworkConnectivityUtil.checkConnectivity("invalid-address");

if (!result.isPingSuccess() && !result.isTelnetSuccess()) {
    String error = result.getErrorMessage();
    if (error.contains("格式错误")) {
        // 处理地址格式错误
    } else if (error.contains("Ping失败")) {
        // 处理网络不通
    } else if (error.contains("Telnet失败")) {
        // 处理端口不通
    }
}
```

## 测试用例

### 1. 正常情况测试

```java
// 测试本地地址
ConnectivityResult result1 = NetworkConnectivityUtil.checkConnectivity("127.0.0.1:8080");

// 测试公网地址
ConnectivityResult result2 = NetworkConnectivityUtil.checkConnectivity("www.baidu.com:80");

// 测试DNS服务
ConnectivityResult result3 = NetworkConnectivityUtil.checkConnectivity("*******:53");
```

### 2. 异常情况测试

```java
// 测试不存在的地址
ConnectivityResult result1 = NetworkConnectivityUtil.checkConnectivity("192.168.1.999:8080");

// 测试格式错误
ConnectivityResult result2 = NetworkConnectivityUtil.checkConnectivity("invalid-address");

// 测试端口超范围
ConnectivityResult result3 = NetworkConnectivityUtil.checkConnectivity("127.0.0.1:99999");
```

### 3. 性能测试

```java
// 批量测试
String address = "www.baidu.com:80";
long totalTime = 0;
int testCount = 10;

for (int i = 0; i < testCount; i++) {
    long start = System.currentTimeMillis();
    ConnectivityResult result = NetworkConnectivityUtil.checkConnectivity(address);
    long end = System.currentTimeMillis();
    totalTime += (end - start);
}

System.out.println("平均耗时: " + (totalTime / testCount) + "ms");
```

## 注意事项

1. **网络环境**: 某些网络环境可能禁用ping，导致ping失败但telnet成功
2. **防火墙**: 防火墙可能阻止ping或特定端口的连接
3. **超时设置**: 200ms超时适合内网环境，公网环境可能需要更长时间
4. **并发使用**: 工具类是线程安全的，支持并发调用
5. **资源释放**: Socket连接会自动关闭，无需手动管理

## 与原有telnetCheck的对比

| 特性 | 原telnetCheck | NetworkConnectivityUtil |
|------|---------------|------------------------|
| 检测方式 | 仅telnet | ping + telnet |
| 超时时间 | 5000ms | 200ms |
| 错误信息 | 异常信息 | 详细分类错误 |
| 性能统计 | 仅总耗时 | ping时间、telnet时间、总时间 |
| 地址解析 | 外部处理 | 内置解析和验证 |
| 异常处理 | 抛出异常 | 返回结果对象 |

## 集成建议

1. **替换现有检测**: 可以完全替换原有的telnetCheck方法
2. **日志记录**: 建议记录详细的检测类型和时间信息
3. **监控指标**: 可以基于ping和telnet的成功率建立监控指标
4. **告警策略**: 可以根据ping和telnet的不同失败情况制定不同的告警策略
