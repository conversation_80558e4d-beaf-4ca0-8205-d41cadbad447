package com.cmpay.dceppay.controller.serviceprovider;

import com.cmpay.dceppay.bo.smartmerchant.*;
import com.cmpay.dceppay.dto.smartmerchant.*;
import com.cmpay.dceppay.enums.MsgCodeEnum;
import com.cmpay.dceppay.service.smartmerchant.ISmartMerchantService;
import com.cmpay.framework.data.request.GenericDTO;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.utils.BeanUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.net.HttpURLConnection;

/**
 * <AUTHOR>
 * @date 2024/8/19 15:44
 * 工行智能收款商户业务处理类
 */
@RestController
@RequestMapping("/v1/smartmerchant")
@Api(tags = "工行智能收款商户")
public class SmartMerchantController {
    @InitBinder
    public void populateCustomerRequest(WebDataBinder binder) {
        binder.setDisallowedFields();
    }
    @Autowired
    private ISmartMerchantService smartMerchantService;

    @ApiOperation(value = "进件", nickname = "进件", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = HttpURLConnection.HTTP_OK, message = "进件", response = GenericDTO.class)
    @PostMapping("/register")
    public MerchantRegisterRspDTO merchantRegister(@RequestBody @Valid MerchantRegisterReqDTO registerReqDTO) {
        MerchantRegisterReqBO registerReqBO = new MerchantRegisterReqBO();
        BeanUtils.copyProperties(registerReqBO, registerReqDTO);
        MerchantRegisterRspBO responseBO = smartMerchantService.register(registerReqBO);
        return GenericRspDTO.newChildInstance(MsgCodeEnum.SUCCESS.getMsgCd(), MerchantRegisterRspDTO.class, responseBO);
    }

    @ApiOperation(value = "进件查询", nickname = "进件查询", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = HttpURLConnection.HTTP_OK, message = "成功", response = GenericDTO.class)
    @PostMapping("/registerQuery")
    public MerchantRegisterQueryRspDTO merchantRegisterQuery(@RequestBody @Valid MerchantRegisterQueryReqDTO queryReqDTO) {
        MerchantRegisterQueryReqBO registerQueryReqBO = new MerchantRegisterQueryReqBO();
        BeanUtils.copyProperties(registerQueryReqBO, queryReqDTO);
        MerchantRegisterQueryRspBO responseBO = smartMerchantService.registerQuery(registerQueryReqBO);
        return GenericRspDTO.newChildInstance(MsgCodeEnum.SUCCESS.getMsgCd(), MerchantRegisterQueryRspDTO.class, responseBO);
    }

    @ApiOperation(value = "作废", nickname = "作废", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = HttpURLConnection.HTTP_OK, message = "作废", response = GenericDTO.class)
    @PostMapping("/registerCancel")
    public MerchantCancelRspDTO merchantCancel(@RequestBody @Valid MerchantCancelReqDTO registerReqDTO) {
        MerchantCancelReqBO cancelReqBO = new MerchantCancelReqBO();
        BeanUtils.copyProperties(cancelReqBO, registerReqDTO);
        MerchantCancelRspBO responseBO = smartMerchantService.cancel(cancelReqBO);
        return GenericRspDTO.newChildInstance(MsgCodeEnum.SUCCESS.getMsgCd(), MerchantCancelRspDTO.class, responseBO);
    }


}
