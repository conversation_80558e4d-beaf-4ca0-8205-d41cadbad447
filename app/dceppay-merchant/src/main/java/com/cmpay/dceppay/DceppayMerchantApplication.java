package com.cmpay.dceppay;


import com.cmpay.lemon.common.LemonFramework;
import com.cmpay.lemon.framework.LemonBootApplication;
import com.cmpay.lemon.framework.stream.client.EnableStreamClients;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.autoconfigure.amqp.RabbitAutoConfiguration;
import org.springframework.boot.autoconfigure.data.redis.RedisReactiveAutoConfiguration;
import org.springframework.boot.autoconfigure.data.redis.RedisRepositoriesAutoConfiguration;
import org.springframework.context.annotation.ComponentScan;

/**
 * <AUTHOR>
 * @date 2024/8/13 9:57
 * 商户服务应用启动类
 * -Dlemon.logger.level=DEBUG
 * --spring.profiles.active=dev
 */
@LemonBootApplication(value = {"com.cmpay.dceppay", "com.cmpay.lemon.gw"},
        exclude = {
                RabbitAutoConfiguration.class,
                RedisRepositoriesAutoConfiguration.class,
                RedisReactiveAutoConfiguration.class})
@ComponentScan(basePackages = {"com.cmpay.dceppay"})
@MapperScan(basePackages = {"com.cmpay.dceppay.dao"})
@EnableStreamClients(basePackages = "com.cmpay.dceppay")
public class DceppayMerchantApplication {
    public static void main(String[] args) {
        LemonFramework.run(DceppayMerchantApplication.class, args);
    }
}
