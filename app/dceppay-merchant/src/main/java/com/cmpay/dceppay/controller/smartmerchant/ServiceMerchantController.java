package com.cmpay.dceppay.controller.smartmerchant;

import com.cmpay.dceppay.bo.serviceprovider.ServiceProviderRegisterRepBO;
import com.cmpay.dceppay.bo.serviceprovider.ServiceProviderRegisterRspBO;
import com.cmpay.dceppay.dto.serviceprovider.ServiceProviderRegisterRepDTO;
import com.cmpay.dceppay.dto.serviceprovider.ServiceProviderRegisterRspDTO;
import com.cmpay.dceppay.enums.MsgCodeEnum;
import com.cmpay.dceppay.service.serviceprovider.IServiceProviderService;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.utils.BeanUtils;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @date 2024/8/27 14:24
 * 工行服务商
 */
@RestController
@RequestMapping("/v1/outmerchant")
@Api(tags = "工行服务商")
public class ServiceMerchantController {

    @InitBinder
    public void populateCustomerRequest(WebDataBinder binder) {
        binder.setDisallowedFields();
    }
    @Autowired
    private IServiceProviderService serviceProviderService;

    @PostMapping("/register")
    public ServiceProviderRegisterRspDTO merchantRegister(@RequestBody @Valid ServiceProviderRegisterRepDTO registerReqDTO) {
        ServiceProviderRegisterRepBO serviceProviderRegisterRepBO = new ServiceProviderRegisterRepBO();
        BeanUtils.copyProperties(serviceProviderRegisterRepBO, registerReqDTO);
        serviceProviderRegisterRepBO.setLicenseType(String.valueOf(registerReqDTO.getLicenseType().getValue()));
        ServiceProviderRegisterRspBO responseBO = serviceProviderService.register(serviceProviderRegisterRepBO);
        return GenericRspDTO.newChildInstance(MsgCodeEnum.SUCCESS.getMsgCd(), ServiceProviderRegisterRspDTO.class, responseBO);
    }


}
