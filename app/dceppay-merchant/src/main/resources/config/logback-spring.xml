<configuration>
    <springProperty scope="context" name="appName" source="spring.application.name" defaultValue="LMN"/>
    <springProperty scope="context" name="rootLevel" source="lemon.logger.level" defaultValue="INFO"/>
    <springProperty scope="context" name="sqlLevel" source="lemon.sql.level" defaultValue="INFO"/>

    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${lemon.log.path}/lemon-${appName}.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${lemon.log.path}/lemon-${appName}-%d{yyyyMMdd}.%i.log.gz</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>20MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <encoder>
            <pattern>%d %-5level [%-18.18thread] %-36logger{36} - [%X{requestId:-SYSTEM}]: %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <!-- 该Filter 不要配置到生产，仅仅用于调试环境，用于禁止eureka订阅服务日志干扰调试
        <filter class="com.hisun.lemon.framework.log.logback.TestFilter" />
        -->
        <encoder>
            <pattern>%d %-5level [%-18.18thread] %-36logger{36} - [%X{requestId:-SYSTEM}]: %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="ERROR-FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${lemon.log.path}/error-${appName}.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${lemon.log.path}/error-${appName}-%d{yyyyMMdd}.%i.log.gz</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>20MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <encoder>
            <pattern>%d %-5level [%-18.18thread] %-36logger{36} - [%X{requestId:-SYSTEM}]: %msg%n</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <appender name="FILE-CLIENT" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${lemon.log.path}/client-${appName}.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${lemon.log.path}/client-${appName}-%d{yyyyMMdd}.%i.log.gz</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>20MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <encoder>
            <pattern>%d %-5level [%-18.18thread] - [%X{requestId:-SYSTEM}]: %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="FILE-ACCESS" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${lemon.log.path}/access-${appName}.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${lemon.log.path}/access-${appName}-%d{yyyyMMdd}.%i.log.gz</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>20MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <encoder>
            <pattern>%d %-5level [%-18.18thread] - [%X{requestId:-SYSTEM}]: %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="FILE-STREAM-CONSUMER" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${lemon.log.path}/stream-consumer-${appName}.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${lemon.log.path}/stream-consumer-${appName}-%d{yyyyMMdd}.%i.log</fileNamePattern>
            <maxFileSize>20MB</maxFileSize>
        </rollingPolicy>
        <encoder>
            <pattern>%d %-5level [%-18.18thread] - [%X{requestId:-SYSTEM}]: %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="FILE-STREAM-PRODUCER" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${lemon.log.path}/stream-producer-${appName}.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${lemon.log.path}/stream-producer-${appName}-%d{yyyyMMdd}.%i.log</fileNamePattern>
            <maxFileSize>20MB</maxFileSize>
        </rollingPolicy>
        <encoder>
            <pattern>%d %-5level [%-18.18thread] - [%X{requestId:-SYSTEM}]: %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="FILE-PXNPLTE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${lemon.log.path}/txnPlte-${appName}.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${lemon.log.path}/txnPlte-${appName}-%d{yyyyMMdd}.%i.log</fileNamePattern>
            <maxFileSize>20MB</maxFileSize>
        </rollingPolicy>
        <encoder>
            <pattern>%d{MM-dd HH:mm:ss.SSS} %-5level - %msg%n</pattern>
        </encoder>
    </appender>

    <logger name="com.cmpay.lemon.framework.springcloud.fegin" level="INFO" additivity="false">
        <springProfile name="ci, sit, uat, str, pre, prd">
            <appender-ref ref="FILE-CLIENT"/>
        </springProfile>
        <springProfile name="dev">
            <appender-ref ref="STDOUT"/>
        </springProfile>
    </logger>

    <logger name="com.netflix.loadbalancer.LoadBalancerContext" level="DEBUG" additivity="false">
        <springProfile name="ci, sit, uat, str, pre, prd">
            <appender-ref ref="FILE-CLIENT"/>
        </springProfile>
        <springProfile name="dev">
            <appender-ref ref="STDOUT"/>
        </springProfile>
    </logger>

    <logger name="webRequestAccessLogger" level="INFO" additivity="false">
        <springProfile name="ci, sit, uat, str, pre, prd">
            <appender-ref ref="FILE-ACCESS"/>
        </springProfile>
        <springProfile name="dev">
            <appender-ref ref="STDOUT"/>
        </springProfile>
    </logger>

    <logger name="org.springframework.amqp.rabbit.listener.BlockingQueueConsumer" level="INFO" additivity="false">
        <springProfile name="ci, sit, uat, str, pre, prd">
            <appender-ref ref="FILE-STREAM-CONSUMER"/>
        </springProfile>
        <springProfile name="dev">
            <appender-ref ref="STDOUT"/>
        </springProfile>
    </logger>

    <logger name="com.cmpay.lemon.framework.stream.consumer.DefaultInputConsumer" level="INFO" additivity="false">
        <springProfile name="ci, sit, uat, str, pre, prd">
            <appender-ref ref="FILE-STREAM-CONSUMER"/>
        </springProfile>
        <springProfile name="dev">
            <appender-ref ref="STDOUT"/>
        </springProfile>
    </logger>

    <logger name="com.cmpay.lemon.framework.stream.logging.SimpleOutputLogger" level="INFO" additivity="false">
        <springProfile name="ci, sit, uat, str, pre, prd">
            <appender-ref ref="FILE-STREAM-PRODUCER"/>
        </springProfile>
        <springProfile name="dev">
            <appender-ref ref="STDOUT"/>
        </springProfile>
    </logger>

    <logger name="pxnPlteLogger" level="INFO" additivity="false">
        <springProfile name="ci, sit, uat, str, pre, prd">
            <appender-ref ref="FILE-PXNPLTE"/>
        </springProfile>
        <springProfile name="dev">
            <appender-ref ref="STDOUT"/>
        </springProfile>
    </logger>
    <root level="${rootLevel}">
        <springProfile name="ci, sit, uat, str, pre, prd">
            <appender-ref ref="FILE"/>
            <appender-ref ref="ERROR-FILE"/>
        </springProfile>
        <springProfile name="dev">
            <appender-ref ref="STDOUT"/>
        </springProfile>
    </root>
</configuration>
