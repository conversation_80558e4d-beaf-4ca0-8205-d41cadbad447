eureka:
  instance:
    metadata-map:
      zone: zone1,zone2
  client:
    register-with-eureka: true
    availability-zones:
      css: zone1,zone2
    region: css
    serviceUrl:
      zone1: http://${EurekaUser}:${EurekaPassword}@***********44:9002/eureka/

spring:
  redis:
    password:
    cluster:
      nodes: ***********34:2000,***********35:2000,***********36:2000,***********34:2001,***********35:2001,***********36:2001
    lettuce:
      pool:
        max-active: 8
        max-wait: 10000
        max-idle: 8
        min-idle: 1
    timeout: 5000
  cloud:
    stream:
      binders:
        rabbit-lemon:
          environment:
            spring:
              rabbitmq:
                addresses: ***********40:5672,***********41:5672,***********42:5672
                password:
                username:
        rabbit:
          environment:
            spring:
              rabbitmq:
                addresses: ***********40:5672,***********41:5672,***********42:5672
                password:
                username:

lemon:
  batch:
    enabled: true
  alerting:
    spring:
      redis:
        password:
        cluster:
          nodes: ***********51:2000,***********52:2000,***********53:2000,***********51:2001,***********52:2001,***********53:2001
        lettuce:
          pool:
            max-active: 50
            max-wait: 5000
            max-idle: 10
            min-idle: 1
        timeout: 10000
  dataSources:
    primary:
      url: *******************************************,*************:6446,*************:6446/dcep_db
      username:
      password:
    dcepmng:
      url: *******************************************,*************:6446,*************:6446/dcepmng_db
      username:
      password:
  idgen:
    spring:
      redis:
        password:
        cluster:
          nodes: ***********51:2000,***********52:2000,***********53:2000,***********51:2001,***********52:2001,***********53:2001
        lettuce:
          pool:
            max-active: 50
            max-wait: 5000
            max-idle: 10
            min-idle: 1
        timeout : 10000

  cache:
    redis:
      spring:
        redis:
          password:
          cluster:
            nodes: ***********37:2000,***********38:2000,***********39:2000,***********37:2001,***********38:2001,***********39:2001
          lettuce:
            pool:
              max-active: 50
              max-wait: 5000
              max-idle: 10
              min-idle: 1
          timeout : 10000