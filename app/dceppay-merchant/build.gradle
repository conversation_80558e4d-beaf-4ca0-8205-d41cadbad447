apply plugin: 'org.springframework.boot'

dependencies {
    api project(':interface:dceppay-merchant-interface')
    api project(':service:dceppay-merchant-service')


    implementation("com.cmpay:lemon-framework-starter-cloud")
    implementation('org.springframework.cloud:spring-cloud-starter-bootstrap')
    implementation("org.springframework.cloud:spring-cloud-starter-config")
    implementation('com.cmpay:lemon-swagger-starter')
    implementation("com.cmpay:alerting-starter")
    implementation('com.cmpay:lemon-framework-starter-actuator-security')
    runtimeOnly("mysql:mysql-connector-java")


}

springBoot {
    mainClass = 'com.cmpay.dceppay.DceppayMerchantApplication'
}
archivesBaseName = "dceppay-merchant-app"
