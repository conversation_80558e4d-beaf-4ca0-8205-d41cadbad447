apply plugin: 'org.springframework.boot'

dependencies {
    api project(':interface:dceppay-interface')
    api project(':service:dceppay-payment-service')
    implementation("com.cmpay:lemon-framework-starter-cloud")
    implementation('org.springframework.cloud:spring-cloud-starter-bootstrap')
    implementation("org.springframework.cloud:spring-cloud-starter-config")
    implementation('com.cmpay:lemon-swagger-starter')
    implementation("com.cmpay:alerting-starter")
    implementation("com.cmpay.gw:lemon-gateway-api-client-starter:2.2.7")
    implementation("com.cmpay:lemon-gateway-igw-client-starter")
    implementation('com.cmpay:lemon-framework-starter-actuator-security')
    runtimeOnly("mysql:mysql-connector-java")

}

springBoot {
    mainClass = 'com.cmpay.dceppay.DceppayApplication'
}
archivesBaseName = "dceppay-app"

