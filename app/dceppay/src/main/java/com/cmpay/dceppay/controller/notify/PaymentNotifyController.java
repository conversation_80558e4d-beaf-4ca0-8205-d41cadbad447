package com.cmpay.dceppay.controller.notify;

import com.cmpay.dceppay.bo.notify.PaymentNotifyBO;
import com.cmpay.dceppay.bo.notify.PaymentNotifyRspBO;
import com.cmpay.dceppay.bo.notify.RefundNotifyBO;
import com.cmpay.dceppay.bo.notify.RefundNotifyRspBO;
import com.cmpay.dceppay.dto.notify.NotifyBaseRspDTO;
import com.cmpay.dceppay.dto.notify.PaymentNotifyReqDTO;
import com.cmpay.dceppay.dto.notify.RefundNotifyRepDTO;
import com.cmpay.dceppay.enums.MsgCodeEnum;
import com.cmpay.dceppay.service.notify.INotifyReceiveService;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.gw.codec.annotation.GatewayApi;
import com.cmpay.lemon.common.utils.BeanUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.net.HttpURLConnection;

/**
 * <AUTHOR>
 * @date 2024/8/30 14:09
 */
@RestController
@RequestMapping("/v1")
@Api(tags = "数币收单通知")
public class PaymentNotifyController {
    @InitBinder
    public void populateCustomerRequest(WebDataBinder binder) {
        binder.setDisallowedFields();
    }

    @Autowired
    private INotifyReceiveService notifyReceiveService;

    @ApiOperation(value = "支付通知", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = HttpURLConnection.HTTP_OK, message = "支付通知", response = GenericRspDTO.class)
    @PostMapping(value = "/paymentNotify")
    @GatewayApi(
            accessGateway = "dceppay-cgw-in",
            apiName = "/dceppay-cgw-in/v1/dcepNotify",
            apiDesc = "支付通知",
            sysCnl = "dceppay", busCnl = "dceppay",
            messageType = "xml2json",
            authAccountType = "dcep")
    @ResponseBody
    public NotifyBaseRspDTO paymentNotify(@Valid @RequestBody PaymentNotifyReqDTO paymentNotifyReqDTO) {
        PaymentNotifyBO paymentNotifyBO = new PaymentNotifyBO();
        BeanUtils.copyProperties(paymentNotifyBO, paymentNotifyReqDTO);
        PaymentNotifyRspBO paymentNotifyRspBO = notifyReceiveService.paymentNotify(paymentNotifyBO);
        NotifyBaseRspDTO baseRspDTO=BeanUtils.copyPropertiesReturnDest(new NotifyBaseRspDTO(), paymentNotifyRspBO);
        baseRspDTO.setMsgCd(MsgCodeEnum.SUCCESS.getMsgCd());
        return baseRspDTO;
    }

    @ApiOperation(value = "退款通知", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = HttpURLConnection.HTTP_OK, message = "退款通知", response = GenericRspDTO.class)
    @PostMapping(value = "/refundNotify")
    @GatewayApi(
            accessGateway = "dceppay-cgw-in",
            apiName = "/dceppay-cgw-in/v1/dcepNotify",
            apiDesc = "退款通知",
            sysCnl = "dceppay", busCnl = "dceppay",
            messageType = "xml2json",
            authAccountType = "dcep")
    @ResponseBody
    public NotifyBaseRspDTO refundNotify(@Valid @RequestBody RefundNotifyRepDTO refundNotifyRepDTO) {
        RefundNotifyBO refundNotifyBO = new RefundNotifyBO();
        BeanUtils.copyProperties(refundNotifyBO, refundNotifyRepDTO);
        RefundNotifyRspBO refundOrderRspBO = notifyReceiveService.refundNotify(refundNotifyBO);
        NotifyBaseRspDTO baseRspDTO=BeanUtils.copyPropertiesReturnDest(new NotifyBaseRspDTO(), refundOrderRspBO);
        baseRspDTO.setMsgCd(MsgCodeEnum.SUCCESS.getMsgCd());
        return baseRspDTO;
    }


}
