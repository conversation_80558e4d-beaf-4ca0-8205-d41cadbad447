package com.cmpay.dceppay.controller.mng;

import com.cmpay.dceppay.bo.mng.DcepLoginBO;
import com.cmpay.dceppay.dto.mng.DcepLoginReqDTO;
import com.cmpay.dceppay.service.mng.IDcepLoginService;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.framework.data.NoBody;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.net.HttpURLConnection;

/**
 * <AUTHOR>
 * @date 2024/11/1 8:52
 */
@RestController
@RequestMapping("/v1/dcep")
@Api(tags = "数币收单-互联互通平台登陆|登出")
public class DepLoginController {
    @InitBinder
    public void populateCustomerRequest(WebDataBinder binder) {
        binder.setDisallowedFields();
    }

    @Autowired
    private IDcepLoginService dcepLoginService;

    @ApiOperation(value = "登录", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = HttpURLConnection.HTTP_OK, message = "登录", response = GenericRspDTO.class)
    @PostMapping(value = "/login")
    public GenericRspDTO<NoBody> login(@Valid @RequestBody DcepLoginReqDTO dcepLoginReqDTO) {
        DcepLoginBO dcepLoginBO=new DcepLoginBO();
        dcepLoginBO.setOperateId(dcepLoginReqDTO.getOperateId());
        dcepLoginService.login(dcepLoginBO);
        return GenericRspDTO.newSuccessInstance();
    }


    @ApiOperation(value = "登出", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = HttpURLConnection.HTTP_OK, message = "登出", response = GenericRspDTO.class)
    @PostMapping(value = "/logout")
    public GenericRspDTO<NoBody> loginOut(@Valid @RequestBody DcepLoginReqDTO dcepLoginReqDTO) {
        DcepLoginBO dcepLoginBO=new DcepLoginBO();
        dcepLoginBO.setOperateId(dcepLoginReqDTO.getOperateId());
        dcepLoginService.loginOut(dcepLoginBO);
        return GenericRspDTO.newSuccessInstance();
    }
}
