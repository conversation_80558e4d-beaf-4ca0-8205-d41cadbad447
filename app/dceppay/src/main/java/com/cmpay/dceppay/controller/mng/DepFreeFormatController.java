package com.cmpay.dceppay.controller.mng;

import com.cmpay.dceppay.bo.mng.FreeFormatBO;
import com.cmpay.dceppay.dto.mng.FreeFormatReqDTO;
import com.cmpay.dceppay.service.mng.IFreeFormatService;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.framework.data.NoBody;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.net.HttpURLConnection;

/**
 * <AUTHOR>
 * @date 2024/11/1 8:52
 */
@RestController
@RequestMapping("/v1/dcep")
@Api(tags = "数币收单-自由消息")
public class DepFreeFormatController {
    @InitBinder
    public void populateCustomerRequest(WebDataBinder binder) {
        binder.setDisallowedFields();
    }

    @Autowired
    private IFreeFormatService freeFormatService;

    @ApiOperation(value = "发送消息", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = HttpURLConnection.HTTP_OK, message = "发送消息", response = GenericRspDTO.class)
    @PostMapping(value = "/freeFormatSend")
    public GenericRspDTO<NoBody> freeFormatSend(@Valid @RequestBody FreeFormatReqDTO freeFormatReqDTO) {
        FreeFormatBO freeFormatBO=new FreeFormatBO();
        BeanUtils.copyProperties(freeFormatBO,freeFormatReqDTO);
        freeFormatService.sendFreeFormat(freeFormatBO);
        return GenericRspDTO.newSuccessInstance();
    }



}
