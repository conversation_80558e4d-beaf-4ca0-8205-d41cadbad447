package com.cmpay.dceppay.controller.pay;

import com.cmpay.dceppay.bo.closeorder.CloseOrderBO;
import com.cmpay.dceppay.bo.closeorder.CloseOrderRspBO;
import com.cmpay.dceppay.bo.payment.UnifiedOrderReqBO;
import com.cmpay.dceppay.bo.payment.UnifiedOrderRspBO;
import com.cmpay.dceppay.bo.refund.RefundOrderCheckBO;
import com.cmpay.dceppay.bo.refund.RefundOrderRepBO;
import com.cmpay.dceppay.bo.refund.RefundOrderRspBO;
import com.cmpay.dceppay.dto.closeorder.CloseOrderReqDTO;
import com.cmpay.dceppay.dto.closeorder.CloseOrderRspDTO;
import com.cmpay.dceppay.dto.payment.UnifiedOrderReqDTO;
import com.cmpay.dceppay.dto.payment.UnifiedOrderRsqDTO;
import com.cmpay.dceppay.dto.refund.RefundOrderCheckReqDTO;
import com.cmpay.dceppay.dto.refund.RefundOrderRepDTO;
import com.cmpay.dceppay.dto.refund.RefundOrderRspDTO;
import com.cmpay.dceppay.enums.MsgCodeEnum;
import com.cmpay.dceppay.service.pay.IPaymentService;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.gw.codec.annotation.GatewayApi;
import com.cmpay.gw.codec.annotation.GatewayAuthAccountType;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.framework.data.NoBody;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.net.HttpURLConnection;

/**
 * <AUTHOR>
 * @date 2024/8/30 14:09
 */
@RestController
@RequestMapping("/v1")
@Api(tags = "数币收单")
public class PaymentController {
    @InitBinder
    public void populateCustomerRequest(WebDataBinder binder) {
        binder.setDisallowedFields();
    }
    @Autowired
    private IPaymentService paymentService;

    @ApiOperation(value = "统一下单", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = HttpURLConnection.HTTP_OK, message = "统一下单", response = GenericRspDTO.class)
    @PostMapping("/unifiedOrder")
    @GatewayApi(
            accessGateway = "dceppay-igw",
            apiName = "/dceppay-igw/v1/unifiedOrder",
            apiDesc = "统一下单",
            sysCnl = "dceppay", busCnl = "dceppay",
            authAccountType = GatewayAuthAccountType.ACCESS_IGW_APP)
    public UnifiedOrderRsqDTO unifiedOrder(@Valid @RequestBody UnifiedOrderReqDTO unifiedOrderReqDTO) {
        UnifiedOrderReqBO orderReqBO = new UnifiedOrderReqBO();
        BeanUtils.copyProperties(orderReqBO, unifiedOrderReqDTO);
        UnifiedOrderRspBO orderRspBO = paymentService.unifiedOrder(orderReqBO);
        return GenericRspDTO.newChildInstance(MsgCodeEnum.SUCCESS.getMsgCd(), UnifiedOrderRsqDTO.class, orderRspBO);
    }

    @ApiOperation(value = "订单退款", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = HttpURLConnection.HTTP_OK, message = "订单退款", response = GenericRspDTO.class)
    @PostMapping("/refundOrder")
    @GatewayApi(
            accessGateway = "dceppay-igw",
            apiName = "/dceppay-igw/v1/refundOrder",
            apiDesc = "订单退款",
            sysCnl = "dceppay", busCnl = "dceppay",
            authAccountType = GatewayAuthAccountType.ACCESS_IGW_APP)
    public RefundOrderRspDTO refundOrder(@Valid @RequestBody RefundOrderRepDTO refundOrderRepDTO) {
        RefundOrderRepBO refundOrderRepBO = new RefundOrderRepBO();
        BeanUtils.copyProperties(refundOrderRepBO, refundOrderRepDTO);
        RefundOrderRspBO refundOrderRspBO = paymentService.refundOrder(refundOrderRepBO);
        return GenericRspDTO.newChildInstance(MsgCodeEnum.SUCCESS.getMsgCd(), RefundOrderRspDTO.class, refundOrderRspBO);
    }

    @ApiOperation(value = "订单关闭", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = HttpURLConnection.HTTP_OK, message = "订单关闭", response = GenericRspDTO.class)
    @PostMapping("/closeOrder")
    @GatewayApi(
            accessGateway = "dceppay-igw",
            apiName = "/dceppay-igw/v1/closeOrder",
            apiDesc = "订单关闭",
            sysCnl = "dceppay", busCnl = "dceppay",
            authAccountType = GatewayAuthAccountType.ACCESS_IGW_APP)
    public CloseOrderRspDTO closeOrder(@Valid @RequestBody CloseOrderReqDTO closeOrderReqDTO) {
        CloseOrderBO closeOrderBO = new CloseOrderBO();
        BeanUtils.copyProperties(closeOrderBO, closeOrderReqDTO);
        CloseOrderRspBO closeOrderRspBO = paymentService.closeOrder(closeOrderBO);
        return GenericRspDTO.newChildInstance(MsgCodeEnum.SUCCESS.getMsgCd(), CloseOrderRspDTO.class, closeOrderRspBO);
    }

    @ApiOperation(value = "订单退款预检查", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = HttpURLConnection.HTTP_OK, message = "订单退款预检查", response = GenericRspDTO.class)
    @PostMapping("/refundOrderCheck")
    @GatewayApi(
            accessGateway = "dceppay-igw",
            apiName = "/dceppay-igw/v1/refundOrderCheck",
            apiDesc = "订单退款预检查",
            sysCnl = "dceppay", busCnl = "dceppay",
            authAccountType = GatewayAuthAccountType.ACCESS_IGW_APP)
    public GenericRspDTO<NoBody>  refundOrderCheck(@Valid @RequestBody RefundOrderCheckReqDTO refundOrderCheckReqDTO) {
        RefundOrderCheckBO refundOrderCheckBO = new RefundOrderCheckBO();
        BeanUtils.copyProperties(refundOrderCheckBO, refundOrderCheckReqDTO);
        paymentService.refundOrderCheck(refundOrderCheckBO);
        return GenericRspDTO.newSuccessInstance();
    }

}
