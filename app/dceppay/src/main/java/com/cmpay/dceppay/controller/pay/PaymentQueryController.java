package com.cmpay.dceppay.controller.pay;

import com.cmpay.dceppay.bo.paymentquery.PaymentOrderQueryBO;
import com.cmpay.dceppay.bo.paymentquery.PaymentOrderResultBO;
import com.cmpay.dceppay.bo.refundquery.RefundOrderQueryBO;
import com.cmpay.dceppay.bo.refundquery.RefundOrderResultBO;
import com.cmpay.dceppay.dto.paymentquery.PaymentOrderQueryReqDTO;
import com.cmpay.dceppay.dto.paymentquery.PaymentOrderQueryRspDTO;
import com.cmpay.dceppay.dto.refundquery.RefundOrderQueryRspDTO;
import com.cmpay.dceppay.enums.MsgCodeEnum;
import com.cmpay.dceppay.service.pay.IOrderQueryService;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.gw.codec.annotation.GatewayApi;
import com.cmpay.gw.codec.annotation.GatewayAuthAccountType;
import com.cmpay.lemon.common.utils.BeanUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.net.HttpURLConnection;

/**
 * <AUTHOR>
 * @date 2024/8/30 14:09
 * 订单查询
 */
@RestController
@RequestMapping("/v1")
@Api(tags = "数币收单订单查询")
public class PaymentQueryController {
    @InitBinder
    public void populateCustomerRequest(WebDataBinder binder) {
        binder.setDisallowedFields();
    }
    @Autowired
    private IOrderQueryService orderQueryService;

    @ApiOperation(value = "支付订单查询", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = HttpURLConnection.HTTP_OK, message = "支付订单查询", response = GenericRspDTO.class)
    @PostMapping("/paymentOrderQuery")
    @GatewayApi(
            accessGateway = "dceppay-igw",
            apiName = "/dceppay-igw/v1/paymentOrderQuery",
            apiDesc = "支付订单查询",
            sysCnl = "dceppay", busCnl = "dceppay",
            authAccountType = GatewayAuthAccountType.ACCESS_IGW_APP)
    public PaymentOrderQueryRspDTO paymentOrderQuery(@Valid @RequestBody PaymentOrderQueryReqDTO orderQueryReqDTO) {
        PaymentOrderQueryBO paymentOrderQueryBO = new PaymentOrderQueryBO();
        paymentOrderQueryBO.setOutOrderNo(orderQueryReqDTO.getOutOrderNo());
        PaymentOrderResultBO paymentOrderResultBO = orderQueryService.paymentQuery(paymentOrderQueryBO);
        return GenericRspDTO.newChildInstance(MsgCodeEnum.SUCCESS.getMsgCd(), PaymentOrderQueryRspDTO.class, paymentOrderResultBO);
    }

    @ApiOperation(value = "退款订单查询", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = HttpURLConnection.HTTP_OK, message = "退款订单查询", response = GenericRspDTO.class)
    @PostMapping("/refundOrderQuery")
    @GatewayApi(
            accessGateway = "dceppay-igw",
            apiName = "/dceppay-igw/v1/refundOrderQuery",
            apiDesc = "退款订单查询",
            sysCnl = "dceppay", busCnl = "dceppay",
            authAccountType = GatewayAuthAccountType.ACCESS_IGW_APP)
    public RefundOrderQueryRspDTO refundOrderQuery(@Valid @RequestBody RefundOrderQueryRspDTO refundOrderQueryRspDTO) {
        RefundOrderQueryBO refundOrderQueryBO = new RefundOrderQueryBO();
        BeanUtils.copyProperties(refundOrderQueryBO, refundOrderQueryRspDTO);
        RefundOrderResultBO refundOrderResultBO = orderQueryService.refundQuery(refundOrderQueryBO);
        return GenericRspDTO.newChildInstance(MsgCodeEnum.SUCCESS.getMsgCd(), RefundOrderQueryRspDTO.class, refundOrderResultBO);
    }


}
