package com.cmpay.dceppay.controller.notify;

import com.cmpay.dceppay.bo.notify.CheckFileNotifyBO;
import com.cmpay.dceppay.bo.notify.CheckFileNotifyRspBO;
import com.cmpay.dceppay.dto.notify.CheckFileNotifyReqDTO;
import com.cmpay.dceppay.dto.notify.NotifyBaseRspDTO;
import com.cmpay.dceppay.enums.MsgCodeEnum;
import com.cmpay.dceppay.service.notify.ICheckFileNotifyReceiveService;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.gw.codec.annotation.GatewayApi;
import com.cmpay.gw.codec.annotation.GatewayAuthAccountType;
import com.cmpay.lemon.common.utils.BeanUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.net.HttpURLConnection;

/**
 * <AUTHOR>
 * @date 2024/9/23 8:59
 * 接收数币的对账文件获取通知
 */
@RestController
@RequestMapping("/v1/check")
@Api(tags = "数币收单-对账服务")
public class CheckFileNotifyController {
    @InitBinder
    public void populateCustomerRequest(WebDataBinder binder) {
        binder.setDisallowedFields();
    }
    @Autowired
    private ICheckFileNotifyReceiveService notifyReceiveService;

    @ApiOperation(value = "对账汇总核对通知", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = HttpURLConnection.HTTP_OK, message = "对账汇总核对通知", response = GenericRspDTO.class)
    @PostMapping(value = "/checkFileNotify")
    @GatewayApi(
            accessGateway = "dceppay-cgw-in",
            apiName = "/dceppay-cgw-in/v1/dcepNotify",
            apiDesc = "对账汇总核对通知",
            sysCnl = "dceppay", busCnl = "dceppay",
            authAccountType = GatewayAuthAccountType.MERCHANT_ID)
    public NotifyBaseRspDTO checkFileNotify(@Valid @RequestBody CheckFileNotifyReqDTO checkFileNotifyReqDTO) {
        CheckFileNotifyBO checkFileNotifyBO = new CheckFileNotifyBO();
        BeanUtils.copyProperties(checkFileNotifyBO, checkFileNotifyReqDTO);
        CheckFileNotifyRspBO checkFileNotifyRspBO = notifyReceiveService.checkFileNotify(checkFileNotifyBO);
        NotifyBaseRspDTO baseRspDTO=BeanUtils.copyPropertiesReturnDest(new NotifyBaseRspDTO(), checkFileNotifyRspBO);
        baseRspDTO.setMsgCd(MsgCodeEnum.SUCCESS.getMsgCd());
        return baseRspDTO;
    }
}
