package com.cmpay.dceppay.controller.notify;

import com.cmpay.dceppay.bo.notify.busmessage.BusMessageNotifyBO;
import com.cmpay.dceppay.bo.notify.busmessage.BusMessageNotifyRspBO;
import com.cmpay.dceppay.bo.notify.busmessage.FinancialStatusChangeNotifyBO;
import com.cmpay.dceppay.dto.notify.NotifyBaseRspDTO;
import com.cmpay.dceppay.dto.notify.busmessage.BusMessageNotifyReqDTO;
import com.cmpay.dceppay.dto.notify.busmessage.FinancialStatusChangeNotifyReqDTO;
import com.cmpay.dceppay.enums.MsgCodeEnum;
import com.cmpay.dceppay.service.notify.busmessage.IBusMessageService;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.gw.codec.annotation.GatewayApi;
import com.cmpay.gw.codec.annotation.GatewayAuthAccountType;
import com.cmpay.lemon.common.utils.BeanUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.net.HttpURLConnection;

/**
 * <AUTHOR>
 * @date 2024/10/30 16:13
 */
@RestController
@RequestMapping("/v1/busMessageNotify")
@Api(tags = "数币收单-互联互通平台业务消息通知")
public class BusMessageNotifyController {
    @InitBinder
    public void populateCustomerRequest(WebDataBinder binder) {
        binder.setDisallowedFields();
    }

    @Autowired
    private IBusMessageService busMessageService;

    @ApiOperation(value = "业务权限变更通知", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = HttpURLConnection.HTTP_OK, message = "业务权限变更通知", response = GenericRspDTO.class)
    @PostMapping(value = "/businessAuthorityNotify")
    @GatewayApi(
            accessGateway = "dceppay-cgw-in",
            apiName = "/dceppay-cgw-in/v1/dcepNotify",
            apiDesc = "业务权限变更通知",
            sysCnl = "dceppay", busCnl = "dceppay",
            authAccountType = GatewayAuthAccountType.MERCHANT_ID)
    public NotifyBaseRspDTO businessAuthorityNotify(@Valid @RequestBody BusMessageNotifyReqDTO messageNotifyReqDTO) {
        BusMessageNotifyBO notifyBO = new BusMessageNotifyBO();
        BeanUtils.copyProperties(notifyBO, messageNotifyReqDTO);
        BusMessageNotifyRspBO messageNotifyRspBO = busMessageService.businessAuthority(notifyBO);
        NotifyBaseRspDTO baseRspDTO=BeanUtils.copyPropertiesReturnDest(new NotifyBaseRspDTO(), messageNotifyRspBO);
        baseRspDTO.setMsgCd(MsgCodeEnum.SUCCESS.getMsgCd());
        return baseRspDTO;
    }

    @ApiOperation(value = "机构变更通知", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = HttpURLConnection.HTTP_OK, message = "机构变更通知", response = GenericRspDTO.class)
    @PostMapping(value = "/financialCodeChangeNotify")
    @GatewayApi(
            accessGateway = "dceppay-cgw-in",
            apiName = "/dceppay-cgw-in/v1/dcepNotify",
            apiDesc = "机构变更通知",
            sysCnl = "dceppay", busCnl = "dceppay",
            authAccountType = GatewayAuthAccountType.MERCHANT_ID)
    public NotifyBaseRspDTO financialCodeChangeNotify(@Valid @RequestBody BusMessageNotifyReqDTO messageNotifyReqDTO) {
        BusMessageNotifyBO notifyBO = new BusMessageNotifyBO();
        BeanUtils.copyProperties(notifyBO, messageNotifyReqDTO);
        BusMessageNotifyRspBO messageNotifyRspBO = busMessageService.financialCodeChange(notifyBO);
        NotifyBaseRspDTO baseRspDTO=BeanUtils.copyPropertiesReturnDest(new NotifyBaseRspDTO(), messageNotifyRspBO);
        baseRspDTO.setMsgCd(MsgCodeEnum.SUCCESS.getMsgCd());
        return baseRspDTO;
    }

    @ApiOperation(value = "机构状态变更通知", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = HttpURLConnection.HTTP_OK, message = "机构状态变更通知", response = GenericRspDTO.class)
    @PostMapping(value = "/financialStatusChangeNotify")
    @GatewayApi(
            accessGateway = "dceppay-cgw-in",
            apiName = "/dceppay-cgw-in/v1/dcepNotify",
            apiDesc = "机构状态变更通知",
            sysCnl = "dceppay", busCnl = "dceppay",
            authAccountType = GatewayAuthAccountType.MERCHANT_ID)
    public NotifyBaseRspDTO financialStatusChangeNotify(@Valid @RequestBody FinancialStatusChangeNotifyReqDTO statusChangeNotifyReqDTO) {
        FinancialStatusChangeNotifyBO notifyBO = new FinancialStatusChangeNotifyBO();
        BeanUtils.copyProperties(notifyBO, statusChangeNotifyReqDTO);
        BusMessageNotifyRspBO messageNotifyRspBO = busMessageService.financialStatusChange(notifyBO);
        NotifyBaseRspDTO baseRspDTO=BeanUtils.copyPropertiesReturnDest(new NotifyBaseRspDTO(), messageNotifyRspBO);
        baseRspDTO.setMsgCd(MsgCodeEnum.SUCCESS.getMsgCd());
        return baseRspDTO;
    }

    @ApiOperation(value = "自由格式通知", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = HttpURLConnection.HTTP_OK, message = "自由格式通知", response = GenericRspDTO.class)
    @PostMapping(value = "/freeFormatNotify")
    @GatewayApi(
            accessGateway = "dceppay-cgw-in",
            apiName = "/dceppay-cgw-in/v1/dcepNotify",
            apiDesc = "自由格式通知",
            sysCnl = "dceppay", busCnl = "dceppay",
            authAccountType = GatewayAuthAccountType.MERCHANT_ID)
    public NotifyBaseRspDTO freeFormatNotify(@Valid @RequestBody BusMessageNotifyReqDTO messageNotifyReqDTO) {
        BusMessageNotifyBO notifyBO = new BusMessageNotifyBO();
        BeanUtils.copyProperties(notifyBO, messageNotifyReqDTO);
        BusMessageNotifyRspBO messageNotifyRspBO = busMessageService.freeFormatNotify(notifyBO);
        NotifyBaseRspDTO baseRspDTO=BeanUtils.copyPropertiesReturnDest(new NotifyBaseRspDTO(), messageNotifyRspBO);
        baseRspDTO.setMsgCd(MsgCodeEnum.SUCCESS.getMsgCd());
        return baseRspDTO;
    }

}
