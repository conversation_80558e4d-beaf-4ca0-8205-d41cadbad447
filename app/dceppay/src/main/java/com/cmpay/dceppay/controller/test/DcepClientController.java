package com.cmpay.dceppay.controller.test;

import com.cmpay.channel.data.Request;
import com.cmpay.channel.data.Response;
import com.cmpay.dceppay.channel.DcepChannelEnum;
import com.cmpay.dceppay.client.DceppayCgwOutClient;
import com.cmpay.dceppay.dto.dcep.common.PushSubWalletInformation;
import com.cmpay.dceppay.dto.dcep.common.RequestGroupHeader;
import com.cmpay.dceppay.dto.dcep.unifiedorder.request.*;
import com.cmpay.dceppay.dto.dcep.unifiedorder.response.UnifiedOrderSoapEnvelopResponse;
import com.cmpay.framework.data.request.GenericDTO;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.data.DefaultRspDTO;
import com.cmpay.lemon.framework.utils.LemonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Optional;

/**
 * <tr>
 *
 * @author: Dev.Yi.Zeng
 * @date: 2021/11/19 10:41
 * @since: v1.0
 */
@RestController
@RequestMapping("/dcep-channel-client")
public class DcepClientController {
    private static final Logger LOGGER = LoggerFactory.getLogger(DcepClientController.class);
    @Autowired
    private DceppayCgwOutClient studyChannelClient;

    @GetMapping("/dcep-json-nest")
    public DefaultRspDTO<UnifiedOrderSoapEnvelopResponse> getJsonNest() {
        UnifiedOrderSoapEnvelopRequest soapEnvelop = new UnifiedOrderSoapEnvelopRequest();
        soapEnvelop.setRequestUrl("http://127.0.0.1:6066/outer/dcep-test");
        soapEnvelop.setVersion("1.0");
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss");
        LocalDateTime now = LocalDateTime.now();
        String isoDateTime = now.format(formatter);
        soapEnvelop.setSendDateTm(isoDateTime);

        UnifiedOrderBody unifiedOrderBody = new UnifiedOrderBody();
        unifiedOrderBody.setCallbackUrl("www.tets.com");
//        unifiedOrderBody.setMessageRoot("CreateOrderRequest");
        RequestGroupHeader requestGroupHeader = new RequestGroupHeader();
        requestGroupHeader.setRemark("我是备注");
        requestGroupHeader.setInstructingDirectParty("发起机构");
        requestGroupHeader.setInstructedDirectParty("接收机构");
        unifiedOrderBody.setRequestGroupHeader(requestGroupHeader);

        //交易信息
        TransactionInformation transactionInformation = new TransactionInformation();
        transactionInformation.setTransactionType("交易类型");
        transactionInformation.setTransactionAmount(new BigDecimal(0));
        unifiedOrderBody.setTransactionInformation(transactionInformation);

//        //用户信息
//        UserInformation userInformation = new UserInformation();
//        userInformation.setUserUniqueId("用户唯一标识");
//        unifiedOrderBody.setUserInformation(userInformation);
        //受理方机构信息
        AcquiringAgentInformation acquiringAgentInformation = new AcquiringAgentInformation();
        acquiringAgentInformation.setAcquiringAgentInstitutionIdentification("受理服务机构金融编码");
        unifiedOrderBody.setAcquiringAgentInformation(acquiringAgentInformation);
        //CreditorInformation 收款运营机构信息
        CreditorInformation creditorInformation = new CreditorInformation();
        creditorInformation.setCreditorInstitutionIdentification("商户所属运营机构");
        unifiedOrderBody.setCreditorInformation(creditorInformation);

//        SubMrchntInformation subMrchntInformation = new SubMrchntInformation();
//        subMrchntInformation.setSubMrchntNo("二级商户编码");
////        unifiedOrderBody.setSubMrchntInformation(subMrchntInformation);

        MerchantTerminalInformation merchantTerminalInformation = new MerchantTerminalInformation();
        unifiedOrderBody.setMerchantTerminalInformation(merchantTerminalInformation);
        OrderInformation orderInformation = new OrderInformation();
        unifiedOrderBody.setOrderInformation(orderInformation);


//        PushSubWalletInformation pushSubWalletInformation = new PushSubWalletInformation();
//        unifiedOrderBody.setPushSubWalletInformation(pushSubWalletInformation);
        soapEnvelop.setSoapBody(unifiedOrderBody);

        Request request = new Request();
        request.setRequestId(LemonUtils.getRequestId());
        request.setRoute(DcepChannelEnum.DCEP_PAY);
        request.setBusiType("unifiedOrderTest");
        request.setSource(DcepChannelEnum.DCEP_PAY);
        request.setTarget(soapEnvelop);


        GenericDTO<Request> genericDTO = new GenericDTO<>();
        genericDTO.setBody(request);

        GenericRspDTO<Response> rspDTO = studyChannelClient.request(genericDTO);
        if (JudgeUtils.isNotSuccess(rspDTO)) {
            BusinessException.throwBusinessException(rspDTO);
        }

        UnifiedOrderSoapEnvelopResponse studyRspDTO = Optional.ofNullable(rspDTO.getBody().getResult())
                .map(x -> (UnifiedOrderSoapEnvelopResponse) x)
                .orElse(new UnifiedOrderSoapEnvelopResponse());
        LOGGER.info("channel-client处理-->{}", request);
        return DefaultRspDTO.newSuccessInstance(studyRspDTO);
    }
}
