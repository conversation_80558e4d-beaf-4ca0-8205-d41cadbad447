eureka:
  instance:
    preferIpAddress: true
    metadata-map:
      zone: zone-csse2x
    lease-renewal-interval-in-seconds: 10
    lease-expiration-duration-in-seconds: 30
  client:
    region: css
    availability-zones:
      css: zone-csse2x
    serviceUrl:
      zone-csse2x: http://127.0.0.1:9002/eureka/

server:
  port: 8527
  tomcat:
    accept-count: 10
    max-connections: 5000
    max-threads: 500
  servlet:
    encoding:
      force: true

spring :
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  data:
    redis:
      repositories:
        enabled: false
  application:
    name: dceppay-batch
  mvc:
    throw-exception-if-no-handler-found: true
  messages:
    basename: i18n/messages
  jackson:
    serialization:
      write_dates_as_timestamps: false
  cache:
    jcache:
      config: classpath:config/ehcache3.xml
      provider: org.ehcache.jsr107.EhcacheCachingProvider
  cloud:
    config:
      username :
      password :
      discovery:
        enabled: true
        failFast : false
        serviceId : config-server
    stream:
      default:
        binder: rabbit-lemon
        #content-type : application/json
      defaultBinder: rabbit
      binders:
        rabbit-lemon:
          type: rabbit
          environment:
            spring:
              rabbitmq:
                addresses:  127.0.0.1:5672
                virtualHost: /lemon
                username: cmpayRabbit
                password: '*****'
                requestedHeartbeat: 10
                publisherConfirms: false
                publisherReturns: false
                connectionTimeout: 10000
                cache:
                  channel:
                    size: 20
      bindings:
        input:
          destination: ${spring.application.name}
          group: ${spring.application.name}
          consumer:
            enabled: true
            batchEnabled: true
            concurrency: 5
            maxAttempts: 1
      rabbit:
        bindings:
          input:
            consumer:
              prefix: mirror.
              maxConcurrency: 50
              prefetch: 20
              txSize: 20
#logging configuration
logging:
  config: classpath:config/logback-spring.xml
  level:
    com.hisun.lemon.framework.dao: ${lemon.sql.level:DEBUG}
    com.cmpay.mercmanager: DEBUG

ribbon :
  #retry next Server times
  MaxAutoRetriesNextServer : 0
  #retry same Server times
  MaxAutoRetries: 0
  ReadTimeout: 10000
  ConnectTimeout: 5000

feign:
  # feign compression support
  compression :
    request :
      enabled : true
      mime-types : application/json
      min-request-size : 2048
    response:
      enabled : true

management :
  server:
    port: 9527


lemon:
  batch:
    enabled: true
  alerting:
    prefix : DCP
    source: redis
    internal: true #对内或对外服务，相当于以前的gatewayEnable
    spring:
      redis:
        lettuce:
          pool:
            #连接池最大连接数（使用负值表示没有限制）
            max-active: 200
            #连接池最大阻塞等待时间（使用负值表示没有限制）
            max-wait: 5000
            # 连接池中的最大空闲连接
            max-idle: 50
            # 连接池中的最小空闲连接
            min-idle: 10
        #连接超时时间（毫秒）
        timeout: 5000
  idgen:
    id-infos:
      G_SEQ_JRN_NO:
        maxValue: 669999999999999999
        delta: 1000
        minValue: 640000000000000000
    spring:
      redis:
        lettuce:
          pool:
            #连接池最大连接数（使用负值表示没有限制）
            max-active: 200
            #连接池最大阻塞等待时间（使用负值表示没有限制）
            max-wait: 5000
            # 连接池中的最大空闲连接
            max-idle: 50
            # 连接池中的最小空闲连接
            min-idle: 10
        #连接超时时间（毫秒）
        timeout: 5000
      spring:
        cache:
          redis:
            time-to-live: 600s
        redis:
          lettuce:
            pool:
              #连接池最大连接数（使用负值表示没有限制）
              max-active: 200
              #连接池最大阻塞等待时间（使用负值表示没有限制）
              max-wait: 5000
              # 连接池中的最大空闲连接
              max-idle: 50
              # 连接池中的最小空闲连接
              min-idle: 10
          #连接超时时间（毫秒）
          timeout: 5000
  gateway:
    register: true
    config:
      discovery:
        # 开始API配置远程发现
        enabled: true
        # API配置服务实例名称,默认为lemon-gateway-metadata
        serviceId: "lemon-gateway-metadata"
    refresh:
      watch:
        enabled: true
        initialDelay: 3000
        delay: 30000
      rabbitmq:
        enabled: true
  #Multiple dataSources
  dataSources:
    primary:
      type: com.alibaba.druid.pool.DruidDataSource
      driverClassName: com.mysql.cj.jdbc.Driver
    dcepmng:
      type: com.alibaba.druid.pool.DruidDataSource
      driverClassName: com.mysql.cj.jdbc.Driver
  #dynamic datasource
  dynamicDataSource:
    enabled: true
    defaultDataSource: primary

  pagehelper:
    defaultPageNum: 1
    defaultPageSize: 8
  feign:
    # feign client validation
    validation:
      enabled: true
  management:
    endpoints:
      web:
        security:
          user:
            crypto-mode: sha256

ics:
  acc:
    enabled: false

#解析mapper文件 classpath:com/cmpay/dceppay/mapper/**/*.xml
mybatis:
  mapper-locations: classpath:com/cmpay/dceppay/mapper/**/*.xml