package com.cmpay.dceppay.controller;

import com.cmpay.dceppay.bo.CheckCancelRefundBO;
import com.cmpay.dceppay.bo.CheckErrorAmountUpdateBO;
import com.cmpay.dceppay.bo.CheckErrorCancelBO;
import com.cmpay.dceppay.dto.checkerror.CheckAmountUpdateDTO;
import com.cmpay.dceppay.dto.checkerror.CheckErrorCancelDTO;
import com.cmpay.dceppay.dto.checkerror.CheckErrorHandelBaseDTO;
import com.cmpay.dceppay.service.check.error.ICheckErrorHandleService;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.framework.data.NoBody;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.net.HttpURLConnection;

/**
 * <AUTHOR>
 * @date 2024/9/26 16:49
 * 对账差错处理
 */
@RestController
@RequestMapping("/v1/checkErrorHandle")
@Api(tags = "数币收单-对账服务-差错处理")
public class CheckErrorHandelController {
    @InitBinder
    public void populateCustomerRequest(WebDataBinder binder) {
        binder.setDisallowedFields();
    }

    @Autowired
    private ICheckErrorHandleService checkErrorHandleService;

    //差错取消  充值短款  充值长款（未知订单）
    @ApiOperation(value = "取消差错", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = HttpURLConnection.HTTP_OK, message = "取消差错", response = GenericRspDTO.class)
    @PostMapping("/cancelError")
    public GenericRspDTO<NoBody> cancelError(@Valid @RequestBody CheckErrorCancelDTO checkErrorCancelDTO) {
        CheckErrorCancelBO checkErrorCancelBO = new CheckErrorCancelBO();
        BeanUtils.copyProperties(checkErrorCancelBO, checkErrorCancelDTO);
        checkErrorHandleService.cancelError(checkErrorCancelBO);
        return GenericRspDTO.newSuccessInstance();
    }


    @ApiOperation(value = "金额修改", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = HttpURLConnection.HTTP_OK, message = "金额修改", response = GenericRspDTO.class)
    @PostMapping("/updateAmount")
    public GenericRspDTO<NoBody> updateAmount(@Valid @RequestBody CheckAmountUpdateDTO checkAmountUpdateDTO) {
        CheckErrorAmountUpdateBO amountUpdateBO = new CheckErrorAmountUpdateBO();
        BeanUtils.copyProperties(amountUpdateBO, checkAmountUpdateDTO);
        checkErrorHandleService.updateCheckAmount(amountUpdateBO);
        return GenericRspDTO.newSuccessInstance();
    }

    @ApiOperation(value = "补单退款", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = HttpURLConnection.HTTP_OK, message = "补单退款", response = GenericRspDTO.class)
    @PostMapping("/cancelRefund")
    public GenericRspDTO<NoBody> cancelRefund(@Valid @RequestBody CheckErrorHandelBaseDTO checkErrorHandelBaseDTO) {
        CheckCancelRefundBO checkErrorBO = new CheckCancelRefundBO();
        BeanUtils.copyProperties(checkErrorBO, checkErrorHandelBaseDTO);
        checkErrorHandleService.cancelRefund(checkErrorBO);
        return GenericRspDTO.newSuccessInstance();
    }
}
