package com.cmpay.dceppay.controller;

import com.cmpay.dceppay.dto.checkfile.CheckFileImportReqDTO;
import com.cmpay.dceppay.service.check.IDcepCheckService;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.framework.data.NoBody;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import org.springframework.batch.core.JobParametersInvalidException;
import org.springframework.batch.core.repository.JobExecutionAlreadyRunningException;
import org.springframework.batch.core.repository.JobInstanceAlreadyCompleteException;
import org.springframework.batch.core.repository.JobRestartException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.net.HttpURLConnection;

/**
 * <AUTHOR>
 * @date 2024/12/4 13:43
 */
@RestController
@RequestMapping("/v1/checkFile")
@Api(tags = "数币收单-对账服务-对账")
public class CheckController {

    @InitBinder
    public void populateCustomerRequest(WebDataBinder binder) {
        binder.setDisallowedFields();
    }

    @Autowired
    private IDcepCheckService checkService;

    @ApiOperation(value = "对账文件下载入库", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = HttpURLConnection.HTTP_OK, message = "对账文件下载入库", response = GenericRspDTO.class)
    @PostMapping("/download")
    public GenericRspDTO<NoBody> checkFileDownload(@Valid @RequestBody CheckFileImportReqDTO importReqDTO) {
        checkService.download(importReqDTO.getCheckDate());
        return GenericRspDTO.newSuccessInstance();
    }

    @ApiOperation(value = "对账", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = HttpURLConnection.HTTP_OK, message = "对账", response = GenericRspDTO.class)
    @PostMapping("/check")
    public GenericRspDTO<NoBody> check(@Valid @RequestBody CheckFileImportReqDTO importReqDTO) throws Exception {
        checkService.check(importReqDTO.getCheckDate());
        return GenericRspDTO.newSuccessInstance();
    }
}
