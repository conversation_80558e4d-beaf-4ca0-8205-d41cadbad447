package com.cmpay.dceppay.controller;

import com.cmpay.dceppay.bo.account.AccountTreatmentCancelBO;
import com.cmpay.dceppay.bo.account.AccountTreatmentReverseBO;
import com.cmpay.dceppay.dto.account.AccountHandleReqDTO;
import com.cmpay.dceppay.service.check.account.IAccountHandleService;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.framework.data.NoBody;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.net.HttpURLConnection;

/**
 * <AUTHOR>
 * @date 2024/9/30 9:35
 */
@RestController
@RequestMapping("/v1/accountHandle")
@Api(tags = "数币收单-对账服务-账务处理")
public class AccountHandleController {
    @InitBinder
    public void populateCustomerRequest(WebDataBinder binder) {
        binder.setDisallowedFields();
    }
    @Autowired
    private IAccountHandleService accountHandleService;

    @ApiOperation(value = "冲正", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = HttpURLConnection.HTTP_OK, message = "冲正", response = GenericRspDTO.class)
    @PostMapping("/cancel")
    public GenericRspDTO<NoBody> cancel(@Valid @RequestBody AccountHandleReqDTO accountHandleReqDTO) {
        AccountTreatmentCancelBO cancelBO = new AccountTreatmentCancelBO();
        BeanUtils.copyProperties(cancelBO, accountHandleReqDTO);
        accountHandleService.accountCancel(cancelBO);
        return GenericRspDTO.newSuccessInstance();
    }


    @ApiOperation(value = "撤销", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = HttpURLConnection.HTTP_OK, message = "撤销", response = GenericRspDTO.class)
    @PostMapping("/reverse")
    public GenericRspDTO<NoBody> reverse(@Valid @RequestBody AccountHandleReqDTO handleReqDTO) {
        AccountTreatmentReverseBO reverseBO = new AccountTreatmentReverseBO();
        BeanUtils.copyProperties(reverseBO, handleReqDTO);
        accountHandleService.accountReserve(reverseBO);
        return GenericRspDTO.newSuccessInstance();
    }


}
