package com.cmpay.dceppay.controller;

import com.cmpay.dceppay.dto.checkfile.CheckFileGenReqDTO;
import com.cmpay.dceppay.service.check.writer.CheckFileRePuhService;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.framework.data.NoBody;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.net.HttpURLConnection;

/**
 * <AUTHOR>
 * @date 2024/9/29 9:24
 */
@RestController
@RequestMapping("/v1/checkFile")
@Api(tags = "数币收单-对账服务-对账文件管理")
public class CheckFileGenController {
    @InitBinder
    public void populateCustomerRequest(WebDataBinder binder) {
        binder.setDisallowedFields();
    }
    @Autowired
    private CheckFileRePuhService checkFileRePuhService;

    @ApiOperation(value = "对账文件生成", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = HttpURLConnection.HTTP_OK, message = "对账文件生成", response = GenericRspDTO.class)
    @PostMapping("/genPayCenterFile")
    public GenericRspDTO<NoBody> checkFileNotify(@Valid @RequestBody CheckFileGenReqDTO checkFileNotifyReqDTO) {
        checkFileRePuhService.genCheckFile(checkFileNotifyReqDTO.getCheckDate(), checkFileNotifyReqDTO.getChannelCode(),checkFileNotifyReqDTO.isReGenFile());
        return GenericRspDTO.newSuccessInstance();
    }
}
