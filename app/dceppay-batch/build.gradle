apply plugin: 'org.springframework.boot'

dependencies {
    api project(':interface:dceppay-batch-interface')
    api project(':service:dceppay-check-service')

    implementation("com.cmpay:lemon-framework-starter-cloud")
    implementation("org.springframework.cloud:spring-cloud-starter-config")
    implementation('org.springframework.cloud:spring-cloud-starter-bootstrap')
    implementation("org.springframework.cloud:spring-cloud-starter-config")
    implementation("com.cmpay.gw:lemon-gateway-api-client-starter:2.2.7")
    implementation("com.cmpay:lemon-gateway-igw-client-starter")
    implementation('com.cmpay:lemon-swagger-starter')
    runtimeOnly("mysql:mysql-connector-java")


}

springBoot {
    mainClass = 'com.cmpay.dceppay.DceppayBatchApplication'
}

archivesBaseName = "dceppay-batch-app"
