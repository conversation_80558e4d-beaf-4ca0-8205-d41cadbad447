eureka:
  instance:
    metadata-map:
      zone: zone1,zone2
  client:
    register-with-eureka: true
    availability-zones:
      css: zone1,zone2
    region: css
    serviceUrl:
      zone1: http://${EurekaUser}:${EurekaPassword}@pre.eureka.payscene.pay:9002/eureka/

spring:
  redis:
    password:
    cluster:
      nodes: alerting01.payscene.pay:2000,alerting02.payscene.pay:2000,alerting03.payscene.pay:2000,alerting04.payscene.pay:2000,alerting05.payscene.pay:2000,alerting06.payscene.pay:2000
  cloud:
    stream:
      binders:
        rabbit-lemon:
          environment:
            spring:
              rabbitmq:
                addresses:  pre.rabbitmq.payscene.pay:5672
                username: "rabbitmq"
                password:
        rabbit:
          environment:
            spring:
              rabbitmq:
                addresses:  pre.rabbitmq.payscene.pay:5672
                username: "rabbitmq"
                password:

lemon:
  alerting:
    spring:
      redis:
        password:
        cluster:
          nodes: alerting01.payscene.pay:2000,alerting02.payscene.pay:2000,alerting03.payscene.pay:2000,alerting04.payscene.pay:2000,alerting05.payscene.pay:2000,alerting06.payscene.pay:2000
  dataSources:
    primary:
      url: *******************************************,dcep02.db.pay:6446,dcep03.db.pay:6446/dcep_db
      username: "dcepadm"
      password:
      initialSize: 5
      maxActive: 50
      minIdle: 5
    dcepmng:
      url: *******************************************,dcep02.db.pay:6446,dcep03.db.pay:6446/dcepmng_db
      username: "dcepmng"
      password:
      initialSize: 5
      maxActive: 50
      minIdle: 5
  idgen:
    spring:
      redis:
        password:
        cluster:
          nodes: idgen01.payscene.pay:2000,idgen02.payscene.pay:2000,idgen03.payscene.pay:2000,idgen04.payscene.pay:2000,idgen05.payscene.pay:2000,idgen06.payscene.pay:2000
  cache:
    redis:
      spring:
        redis:
          password:
          cluster:
            nodes: cache01.payscene.pay:2000,cache02.payscene.pay:2000,cache03.payscene.pay:2000,cache04.payscene.pay:2000,cache05.payscene.pay:2000,cache06.payscene.pay:2000
    #分布式锁
  redisson :
    mode: cluster
    password:
    node-address: redis://idgen01.payscene.pay:2000,redis://idgen02.payscene.pay:2000,redis://idgen03.payscene.pay:2000,redis://idgen04.payscene.pay:2000,redis://idgen05.payscene.pay:2000,redis://idgen06.payscene.pay:2000
