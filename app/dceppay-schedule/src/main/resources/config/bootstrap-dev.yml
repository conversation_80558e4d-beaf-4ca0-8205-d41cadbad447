eureka:
  instance:
    metadata-map:
      zone: zone1,zone2
  client:
    register-with-eureka: false
    availability-zones:
      css: zone1,zone2
    region: css
    serviceUrl:
      zone1: http://${EurekaUser}:${EurekaPassword}@*************:9002/eureka/

spring:
  redis:
    password:
    cluster:
      nodes: *************:2000,*************:2000,*************:2000,*************:2001,*************:2001,*************:2001
    lettuce:
      pool:
        max-active: 8
        max-wait: 10000
        max-idle: 8
        min-idle: 1
    timeout: 5000
  cloud:
    stream:
      binders:
        rabbit-lemon:
          environment:
            spring:
              rabbitmq:
                addresses: *************:5672,*************:5672,*************:5672
                password:
                username:
        rabbit:
          environment:
            spring:
              rabbitmq:
                addresses: *************:5672,*************:5672,*************:5672
                password:
                username:

lemon:
  alerting:
    spring:
      redis:
        password:
        cluster:
          nodes: *************:2000,*************:2000,*************:2000,*************:2001,*************:2001,*************:2001
        lettuce:
          pool:
            max-active: 50
            max-wait: 5000
            max-idle: 10
            min-idle: 1
        timeout: 10000
  dataSources:
    primary:
      url: *******************************************,*************:6446,*************:6446/dcep_db
      username:
      password:
    dcepmng:
      url: *******************************************,*************:6446,*************:6446/dcepmng_db
      username:
      password:
  idgen:
    spring:
      redis:
        password:
        cluster:
          nodes: *************:2000,*************:2000,*************:2000,*************:2001,*************:2001,*************:2001
        lettuce:
          pool:
            max-active: 50
            max-wait: 5000
            max-idle: 10
            min-idle: 1
        timeout : 10000
  cache:
    redis:
      spring:
        redis:
          password:
          cluster:
            nodes: 10.176.25.137:2000,10.176.25.138:2000,10.176.25.139:2000,10.176.25.137:2001,10.176.25.138:2001,10.176.25.139:2001
          lettuce:
            pool:
              max-active: 50
              max-wait: 5000
              max-idle: 10
              min-idle: 1
          timeout : 10000