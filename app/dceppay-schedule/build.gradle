apply plugin: 'org.springframework.boot'

dependencies {
    api project(":service:dceppay-schedule-service")
    implementation("com.cmpay:lemon-framework-starter-cloud")
    implementation('org.springframework.cloud:spring-cloud-starter-bootstrap')
    implementation("org.springframework.cloud:spring-cloud-starter-config")
    implementation('com.cmpay:lemon-swagger-starter')
    implementation("com.cmpay:alerting-starter")
    implementation("com.cmpay:lemon-gateway-igw-client-starter")
    runtimeOnly("mysql:mysql-connector-java")
}

springBoot {
    mainClass = 'com.cmpay.dceppay.DceppayScheduleApplication'
}

archivesBaseName = "dceppay-schedule-app"