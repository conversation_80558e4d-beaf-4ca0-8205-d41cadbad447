-- 专线管理表
CREATE TABLE `dcep_line_info` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `line_mapping_address` varchar(100) NOT NULL COMMENT '专线映射地址，格式：IP:端口',
  `line_name` varchar(100) NOT NULL COMMENT '专线名称',
  `institution_line_address` varchar(100) DEFAULT NULL COMMENT '机构专线地址，仅展示信息',
  `pboc_line_address` varchar(100) DEFAULT NULL COMMENT '央行专线地址',
  `pboc_idc_code` varchar(50) DEFAULT NULL COMMENT '央行IDC标识，如bj-1',
  `pboc_real_address` varchar(100) DEFAULT NULL COMMENT '央行真实地址，用于关联专线地址进一步请求央行，主要适用于SFTP业务',
  `is_default` char(1) DEFAULT 'N' COMMENT '默认标识，Y-是/N-否，默认专线仅适用于API业务，有且仅有一条',
  `business_type` varchar(20) NOT NULL COMMENT '业务类型，API/SFTP',
  `health_check_enabled` char(1) DEFAULT 'Y' COMMENT '探活开关，Y-启用/N-禁用当前专线的探活',
  `network_status` varchar(20) DEFAULT 'NORMAL' COMMENT '网络状态，NORMAL-正常/WARNING-告警/FAULT-故障',
  `service_status` varchar(20) DEFAULT 'NORMAL' COMMENT '服务状态，NORMAL-正常/ABNORMAL-异常',
  `probe_result` varchar(200) DEFAULT NULL COMMENT '探测结果，最近一次接口探测返回的机房信息',
  `last_health_check_time` datetime DEFAULT NULL COMMENT '最近一次探活时间',
  `health_check_fail_start_time` datetime DEFAULT NULL COMMENT '探活异常开始时间，当探活失败时登记，探活成功后清空',
  `create_user_no` varchar(50) DEFAULT NULL COMMENT '创建人账号',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_user_no` varchar(50) DEFAULT NULL COMMENT '更新人账号',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `tm_smp` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '时间戳',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_line_mapping_address` (`line_mapping_address`),
  KEY `idx_business_type` (`business_type`),
  KEY `idx_health_check_enabled` (`health_check_enabled`),
  KEY `idx_network_status` (`network_status`),
  KEY `idx_is_default` (`is_default`),
  KEY `idx_last_health_check_time` (`last_health_check_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='专线管理表';

-- 专线探活记录表（用于记录每次探活的详细信息）
CREATE TABLE `dcep_line_health_record` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `line_id` varchar(32) NOT NULL COMMENT '专线ID，关联dcep_line_info.id',
  `check_time` datetime NOT NULL COMMENT '探活时间',
  `check_type` varchar(20) NOT NULL COMMENT '探活类型，PING/TELNET/API',
  `response_time` bigint DEFAULT NULL COMMENT '响应时间（毫秒）',
  `check_result` varchar(20) NOT NULL COMMENT '探活结果，SUCCESS-成功/FAIL-失败/TIMEOUT-超时',
  `error_message` varchar(500) DEFAULT NULL COMMENT '错误信息',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_line_id` (`line_id`),
  KEY `idx_check_time` (`check_time`),
  KEY `idx_check_result` (`check_result`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='专线探活记录表';

-- 插入示例数据
INSERT INTO `dcep_line_info` (
  `id`, `line_mapping_address`, `line_name`, `institution_line_address`,
  `pboc_line_address`, `pboc_idc_code`, `pboc_real_address`,
  `is_default`, `business_type`, `health_check_enabled`,
  `network_status`, `service_status`, `create_user_no`
) VALUES
('1', '*************:8080', '主专线-API', '*************:8080', '**********:8080', 'bj-1', '**********:8080', 'Y', 'API', 'Y', 'NORMAL', 'NORMAL', 'system'),
('2', '*************:22', '备用专线-SFTP', '*************:22', '**********:22', 'bj-2', '**********:22', 'N', 'SFTP', 'Y', 'NORMAL', 'NORMAL', 'system'),
('3', '*************:8080', '备用专线-API', '*************:8080', '**********:8080', 'sh-1', '**********:8080', 'N', 'API', 'Y', 'NORMAL', 'NORMAL', 'system');
