-- 专线管理表
CREATE TABLE `dcep_line_management` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `line_code` varchar(50) NOT NULL COMMENT '专线编码，唯一标识',
  `line_name` varchar(100) NOT NULL COMMENT '专线名称',
  `line_mapping_address` varchar(100) NOT NULL COMMENT '专线映射地址，格式：IP:端口',
  `line_mapping_ip` varchar(45) NOT NULL COMMENT '专线映射IP地址',
  `line_mapping_port` int NOT NULL COMMENT '专线映射端口',
  `institution_line_address` varchar(100) DEFAULT NULL COMMENT '机构专线地址，仅展示信息',
  `pboc_line_address` varchar(100) DEFAULT NULL COMMENT '央行专线地址',
  `pboc_idc_code` varchar(50) DEFAULT NULL COMMENT '央行IDC标识，如bj-1',
  `pboc_real_address` varchar(100) DEFAULT NULL COMMENT '央行真实地址，用于关联专线地址进一步请求央行，主要适用于SFTP业务',
  `is_default` char(1) DEFAULT 'N' COMMENT '默认标识，Y-启用/N-禁用，默认专线仅适用于API业务，有且仅有一条',
  `business_type` varchar(20) NOT NULL COMMENT '业务类型，API/SFTP',
  `line_priority` tinyint DEFAULT 5 COMMENT '专线优先级，1-9，数字越小优先级越高',
  `health_check_enabled` char(1) DEFAULT 'Y' COMMENT '探活开关，Y-启用/N-禁用当前专线的探活',
  `health_check_interval` int DEFAULT 5 COMMENT '探活间隔（秒），默认5秒',
  `timeout_config` varchar(50) DEFAULT 'NORMAL' COMMENT '超时配置，FAST/NORMAL/SLOW/INTRANET/INTERNET',
  `network_status` varchar(20) DEFAULT 'NORMAL' COMMENT '网络状态，NORMAL-正常/WARNING-告警/FAULT-故障',
  `service_status` varchar(20) DEFAULT 'NORMAL' COMMENT '服务状态，NORMAL-正常/ABNORMAL-异常',
  `probe_result` varchar(500) DEFAULT NULL COMMENT '探测结果，最近一次接口探测返回的机房信息',
  `last_health_check_time` datetime DEFAULT NULL COMMENT '最近一次探活时间',
  `health_check_fail_start_time` datetime DEFAULT NULL COMMENT '探活异常开始时间，当探活失败时登记，探活成功后清空',
  `consecutive_fail_count` int DEFAULT 0 COMMENT '连续失败次数',
  `consecutive_success_count` int DEFAULT 0 COMMENT '连续成功次数',
  `total_check_count` bigint DEFAULT 0 COMMENT '总探活次数',
  `success_check_count` bigint DEFAULT 0 COMMENT '成功探活次数',
  `response_time_avg` bigint DEFAULT NULL COMMENT '平均响应时间（毫秒）',
  `response_time_min` bigint DEFAULT NULL COMMENT '最小响应时间（毫秒）',
  `response_time_max` bigint DEFAULT NULL COMMENT '最大响应时间（毫秒）',
  `last_success_time` datetime DEFAULT NULL COMMENT '最近一次成功时间',
  `last_fail_time` datetime DEFAULT NULL COMMENT '最近一次失败时间',
  `status` char(1) DEFAULT 'A' COMMENT '记录状态，A-有效/I-无效',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_user_no` varchar(50) DEFAULT NULL COMMENT '创建人账号',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_user_no` varchar(50) DEFAULT NULL COMMENT '更新人账号',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `tm_smp` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '时间戳',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_line_code` (`line_code`),
  UNIQUE KEY `uk_line_mapping_address` (`line_mapping_address`),
  UNIQUE KEY `uk_business_default` (`business_type`, `is_default`),
  KEY `idx_business_type` (`business_type`),
  KEY `idx_health_check_enabled` (`health_check_enabled`),
  KEY `idx_network_status` (`network_status`),
  KEY `idx_is_default` (`is_default`),
  KEY `idx_line_priority` (`line_priority`),
  KEY `idx_response_time_avg` (`response_time_avg`),
  KEY `idx_last_health_check_time` (`last_health_check_time`),
  KEY `idx_create_time` (`create_time`),
  CONSTRAINT `chk_line_priority` CHECK (`line_priority` >= 1 AND `line_priority` <= 9),
  CONSTRAINT `chk_line_mapping_port` CHECK (`line_mapping_port` >= 1 AND `line_mapping_port` <= 65535),
  CONSTRAINT `chk_health_check_interval` CHECK (`health_check_interval` >= 1 AND `health_check_interval` <= 3600),
  CONSTRAINT `chk_is_default` CHECK (`is_default` IN ('Y', 'N')),
  CONSTRAINT `chk_health_check_enabled` CHECK (`health_check_enabled` IN ('Y', 'N')),
  CONSTRAINT `chk_status` CHECK (`status` IN ('A', 'I')),
  CONSTRAINT `chk_business_type` CHECK (`business_type` IN ('API', 'SFTP')),
  CONSTRAINT `chk_network_status` CHECK (`network_status` IN ('NORMAL', 'WARNING', 'FAULT')),
  CONSTRAINT `chk_service_status` CHECK (`service_status` IN ('NORMAL', 'ABNORMAL')),
  CONSTRAINT `chk_timeout_config` CHECK (`timeout_config` IN ('FAST', 'NORMAL', 'SLOW', 'INTRANET', 'INTERNET'))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='专线管理表';

-- 专线探活记录表（用于记录每次探活的详细信息）
CREATE TABLE `dcep_line_health_record` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `line_id` varchar(32) NOT NULL COMMENT '专线ID，关联dcep_line_management.id',
  `line_code` varchar(50) NOT NULL COMMENT '专线编码，冗余字段便于查询',
  `check_time` datetime NOT NULL COMMENT '探活时间',
  `check_date` date NOT NULL COMMENT '探活日期，便于分区和查询',
  `check_type` varchar(30) NOT NULL COMMENT '探活类型，PING_TELNET_SUCCESS/PING_SUCCESS_TELNET_FAIL/PING_FAIL等',
  `ping_success` char(1) DEFAULT 'N' COMMENT 'Ping是否成功，Y/N',
  `ping_time` bigint DEFAULT NULL COMMENT 'Ping响应时间（毫秒）',
  `telnet_success` char(1) DEFAULT 'N' COMMENT 'Telnet是否成功，Y/N',
  `telnet_time` bigint DEFAULT NULL COMMENT 'Telnet响应时间（毫秒）',
  `total_time` bigint DEFAULT NULL COMMENT '总响应时间（毫秒）',
  `check_result` varchar(20) NOT NULL COMMENT '探活结果，SUCCESS-成功/FAIL-失败/TIMEOUT-超时',
  `error_message` varchar(500) DEFAULT NULL COMMENT '错误信息',
  `timeout_config` varchar(50) DEFAULT NULL COMMENT '使用的超时配置',
  `network_status_before` varchar(20) DEFAULT NULL COMMENT '探活前的网络状态',
  `network_status_after` varchar(20) DEFAULT NULL COMMENT '探活后的网络状态',
  `is_status_changed` char(1) DEFAULT 'N' COMMENT '状态是否发生变化，Y/N',
  `batch_id` varchar(32) DEFAULT NULL COMMENT '批次ID，同一轮探活的记录具有相同批次ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_line_id_check_time` (`line_id`, `check_time`),
  KEY `idx_line_code_check_date` (`line_code`, `check_date`),
  KEY `idx_check_time` (`check_time`),
  KEY `idx_check_date` (`check_date`),
  KEY `idx_check_result` (`check_result`),
  KEY `idx_batch_id` (`batch_id`),
  KEY `idx_is_status_changed` (`is_status_changed`),
  CONSTRAINT `fk_health_record_line` FOREIGN KEY (`line_id`) REFERENCES `dcep_line_management` (`id`) ON DELETE CASCADE,
  CONSTRAINT `chk_ping_success` CHECK (`ping_success` IN ('Y', 'N')),
  CONSTRAINT `chk_telnet_success` CHECK (`telnet_success` IN ('Y', 'N')),
  CONSTRAINT `chk_is_status_changed` CHECK (`is_status_changed` IN ('Y', 'N')),
  CONSTRAINT `chk_check_result` CHECK (`check_result` IN ('SUCCESS', 'FAIL', 'TIMEOUT'))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='专线探活记录表'
PARTITION BY RANGE (TO_DAYS(check_date)) (
  PARTITION p_202412 VALUES LESS THAN (TO_DAYS('2025-01-01')),
  PARTITION p_202501 VALUES LESS THAN (TO_DAYS('2025-02-01')),
  PARTITION p_202502 VALUES LESS THAN (TO_DAYS('2025-03-01')),
  PARTITION p_202503 VALUES LESS THAN (TO_DAYS('2025-04-01')),
  PARTITION p_202504 VALUES LESS THAN (TO_DAYS('2025-05-01')),
  PARTITION p_202505 VALUES LESS THAN (TO_DAYS('2025-06-01')),
  PARTITION p_202506 VALUES LESS THAN (TO_DAYS('2025-07-01')),
  PARTITION p_future VALUES LESS THAN MAXVALUE
);

-- 插入示例数据
INSERT INTO `dcep_line_management` (
  `id`, `line_mapping_address`, `line_name`, `institution_line_address`, 
  `pboc_line_address`, `pboc_idc_code`, `pboc_real_address`, 
  `is_default`, `business_type`, `health_check_enabled`, 
  `network_status`, `service_status`, `create_user_no`
) VALUES 
('1', '*************:8080', '主专线-API', '*************:8080', '**********:8080', 'bj-1', '**********:8080', 'Y', 'API', 'Y', 'NORMAL', 'NORMAL', 'system'),
('2', '*************:22', '备用专线-SFTP', '*************:22', '**********:22', 'bj-2', '**********:22', 'N', 'SFTP', 'Y', 'NORMAL', 'NORMAL', 'system'),
('3', '*************:8080', '备用专线-API', '*************:8080', '**********:8080', 'sh-1', '**********:8080', 'N', 'API', 'Y', 'NORMAL', 'NORMAL', 'system');
