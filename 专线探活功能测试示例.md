# 专线探活功能测试示例

## 测试场景说明

本文档提供了专线探活功能的测试场景和验证方法。

## 1. 基础功能测试

### 1.1 数据准备

```sql
-- 插入测试专线数据
INSERT INTO `dcep_line_management` (
  `id`, `line_mapping_address`, `line_name`, `institution_line_address`, 
  `pboc_line_address`, `pboc_idc_code`, `pboc_real_address`, 
  `is_default`, `business_type`, `health_check_enabled`, 
  `network_status`, `service_status`, `create_user_no`
) VALUES 
('test001', '127.0.0.1:8080', '测试专线1-API', '127.0.0.1:8080', '**********:8080', 'test-1', '**********:8080', 'Y', 'API', 'Y', 'NORMAL', 'NORMAL', 'test'),
('test002', '127.0.0.1:8081', '测试专线2-API', '127.0.0.1:8081', '**********:8081', 'test-2', '**********:8081', 'N', 'API', 'Y', 'NORMAL', 'NORMAL', 'test'),
('test003', '192.168.1.999:22', '测试专线3-SFTP(故障)', '192.168.1.999:22', '**********:22', 'test-3', '**********:22', 'N', 'SFTP', 'Y', 'NORMAL', 'NORMAL', 'test');
```

### 1.2 XXL-Job任务配置

在XXL-Job管理界面配置：
- **任务名称**: lineHealthCheckTask
- **Cron表达式**: `0/5 * * * * ?`
- **执行器**: dceppay-schedule
- **任务参数**: 无

## 2. 测试场景

### 2.1 正常探活测试

**目标**: 验证正常专线的探活功能

**步骤**:
1. 启动本地服务监听8080和8081端口
2. 启动定时任务
3. 观察日志输出

**预期结果**:
```
专线探活任务开始执行，时间: 2024-12-23 10:00:00
开始探活专线: 测试专线1-API, 地址: 127.0.0.1:8080
更新专线探活缓存: test001, 当前次数: 1, 成功: true, 响应时间: 5ms
开始探活专线: 测试专线2-API, 地址: 127.0.0.1:8081
更新专线探活缓存: test002, 当前次数: 1, 成功: true, 响应时间: 8ms
```

### 2.2 故障专线测试

**目标**: 验证故障专线的告警机制

**步骤**:
1. 使用不存在的IP地址（如192.168.1.999）
2. 观察5次探测后的状态变化

**预期结果**:
```
专线探活失败: 192.168.1.999:22, 错误: Connection timed out
更新专线探活缓存: test003, 当前次数: 5, 成功: false, 响应时间: 5000ms
专线 测试专线3-SFTP(故障) 最近5次探活结果: 成功0次, 失败5次, 平均响应时间: 5000.00ms
专线故障告警: 测试专线3-SFTP(故障), 累计失败次数: 5
```

### 2.3 状态恢复测试

**目标**: 验证专线从故障状态恢复正常

**步骤**:
1. 先让专线进入故障状态
2. 修复专线地址或启动对应服务
3. 观察恢复过程

**预期结果**:
```
专线 测试专线1-API 最近5次探活结果: 成功4次, 失败1次, 平均响应时间: 10.25ms
专线恢复正常: 测试专线1-API, 原状态: FAULT
```

### 2.4 最优专线缓存测试

**目标**: 验证最优专线选择和缓存机制

**步骤**:
1. 配置多个API类型专线，响应时间不同
2. 等待5次探测完成
3. 检查Redis缓存

**Redis查询命令**:
```bash
# 查看最优专线缓存
redis-cli get "dcep:line:best:API"
redis-cli get "dcep:line:best:SFTP"

# 查看探活结果缓存
redis-cli keys "dcep:line:health:*"
redis-cli get "dcep:line:health:test001"
```

**预期结果**:
```
更新API最优专线缓存: 测试专线1-API, 平均响应时间: 5ms
更新SFTP最优专线缓存: 测试专线2-SFTP, 平均响应时间: 15ms
```

## 3. 性能测试

### 3.1 并发探活测试

**目标**: 验证多专线并发探活的性能

**配置**: 创建10个专线，同时进行探活

**监控指标**:
- 单次探活任务执行时间
- 内存使用情况
- Redis连接数

### 3.2 长期运行测试

**目标**: 验证长期运行的稳定性

**配置**: 连续运行24小时

**监控指标**:
- 内存泄漏检查
- 数据库连接池状态
- 探活记录表数据量增长

## 4. 异常场景测试

### 4.1 Redis连接异常

**模拟**: 停止Redis服务

**预期**: 探活继续进行，但缓存功能失效，直接更新数据库

### 4.2 数据库连接异常

**模拟**: 停止数据库服务

**预期**: 探活任务报错，但不影响应用其他功能

### 4.3 网络超时测试

**模拟**: 配置超长响应时间的专线

**预期**: 5秒后超时，记录为失败

## 5. 数据验证

### 5.1 数据库记录验证

```sql
-- 查看专线状态
SELECT id, line_name, network_status, consecutive_fail_count, 
       response_time_avg, last_health_check_time 
FROM dcep_line_management 
WHERE health_check_enabled = 'Y';

-- 查看探活记录
SELECT line_id, check_time, check_type, response_time, check_result 
FROM dcep_line_health_record 
WHERE line_id = 'test001' 
ORDER BY check_time DESC 
LIMIT 10;

-- 统计探活成功率
SELECT line_id, 
       COUNT(*) as total_checks,
       SUM(CASE WHEN check_result = 'SUCCESS' THEN 1 ELSE 0 END) as success_count,
       ROUND(SUM(CASE WHEN check_result = 'SUCCESS' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) as success_rate
FROM dcep_line_health_record 
WHERE check_time >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
GROUP BY line_id;
```

### 5.2 缓存数据验证

```bash
# 查看所有专线相关缓存
redis-cli keys "*dcep:line*"

# 查看缓存内容
redis-cli --raw get "dcep:line:best:API"

# 查看缓存TTL
redis-cli ttl "dcep:line:best:API"
```

## 6. 故障排查

### 6.1 常见问题

**问题1**: 探活任务不执行
- 检查XXL-Job配置
- 检查应用是否注册到XXL-Job
- 查看XXL-Job执行日志

**问题2**: 专线状态不更新
- 检查Redis连接
- 查看探活缓存是否正常
- 检查数据库连接

**问题3**: 最优专线选择不准确
- 检查专线的network_status字段
- 验证response_time_avg计算逻辑
- 查看缓存更新日志

### 6.2 日志关键字

监控以下关键日志：
- "专线探活任务开始执行"
- "专线恢复正常"
- "专线告警"
- "专线故障告警"
- "更新API最优专线缓存"
- "更新SFTP最优专线缓存"

## 7. 性能优化建议

1. **批量处理**: 当专线数量较多时，考虑分批处理
2. **异步执行**: 对于响应较慢的专线，使用异步方式探活
3. **缓存优化**: 根据实际情况调整缓存过期时间
4. **数据清理**: 定期清理历史探活记录
5. **监控告警**: 集成监控系统，及时发现异常
