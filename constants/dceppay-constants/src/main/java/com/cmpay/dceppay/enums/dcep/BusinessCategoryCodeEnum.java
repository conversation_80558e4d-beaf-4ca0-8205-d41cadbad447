package com.cmpay.dceppay.enums.dcep;

/**
 * <AUTHOR>
 * @date 2024/10/8 17:01
 * 业务类型编码
 */
public enum BusinessCategoryCodeEnum {


    //100	消费（301报文）
    PAYMENT("100", "消费"),
    //204	退款（309报文）
    REFUND("204", "退款");

    private final String code;
    private final String description;

    BusinessCategoryCodeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
