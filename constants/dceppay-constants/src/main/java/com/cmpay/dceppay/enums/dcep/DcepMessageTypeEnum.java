package com.cmpay.dceppay.enums.dcep;

import com.cmpay.dceppay.enums.icbc.MerchantLicenseTypeEnum;

/**
 * <AUTHOR>
 * @date 2024/9/6 15:26
 * 互联互通报文编号常量类
 */
public enum DcepMessageTypeEnum {

    /**
     * 探测请求报文
     */
    DETECTION_REQUEST("dcep.991.001.01", "991","CZone"),

    /**
     * 探测应答报文
     */
    DETECTION_RESPONSE("dcep.992.001.01", "992","CZone"),

    /**
     * 统一下单请求报文
     */
    UNIFIED_ORDER_REQUEST("dcep.301.001.01", "301","CZone"),

    /**
     * 统一下单应答报文
     */
    UNIFIED_ORDER_RESPONSE("dcep.302.001.01", "302","CZone"),

    /**
     * 报文丢弃通知报文
     */
    MESSAGE_DISCARD_NOTIFICATION("dcep.911.001.01", "911",""),

    /**
     * 支付结果通知报文
     */
    PAYMENT_RESULT_NOTIFICATION("dcep.369.001.01", "369","CZone"),

    /**
     * 通信级确认报文
     */
    COMMUNICATION_CONFIRMATION("dcep.902.001.01", "902",""),

    /**
     * 支付结果查询请求报文
     */
    PAYMENT_RESULT_QUERY_REQUEST("dcep.311.001.01", "311","CZone"),

    /**
     * 支付结果查询应答报文
     */
    PAYMENT_RESULT_QUERY_RESPONSE("dcep.312.001.01", "312","CZone"),

    /**
     * 关闭订单请求报文
     */
    ORDER_CLOSE_REQUEST("dcep.315.001.01", "315","CZone"),

    /**
     * 关闭订单应答报文
     */
    ORDER_CLOSE_RESPONSE("dcep.316.001.01", "316","CZone"),

    /**
     * 订单退款请求报文
     */
    ORDER_REFUND_REQUEST("dcep.309.001.01", "309","CZone"),

    /**
     * 订单退款响应报文
     */
    ORDER_REFUND_RESPONSE("dcep.310.001.01", "310","CZone"),

    /**
     * 退款结果通知报文
     */
    REFUND_RESULT_NOTIFICATION("dcep.367.001.01", "367","CZone"),

    /**
     * 退款结果查询请求报文
     */
    REFUND_RESULT_QUERY_REQUEST("dcep.313.001.01", "313","CZone"),

    /**
     * 退款结果查询应答报文
     */
    REFUND_RESULT_QUERY_RESPONSE("dcep.314.001.01", "314","CZone"),

    /**
     * 对账汇总核对通知报文
     */
    RECONCILIATION_SUMMARY_NOTIFICATION("dcep.371.001.01", "371","CZone"),
    //登录/退出请求报文
    LOGIN_REQUEST("dcep.933.001.01", "933","GZone"),
    //登录/退出应答报文
    LOGIN_RESPONSE("dcep.934.001.01", "934","GZone"),
    //机构变更通知报文
    FINANCIAL_CODE_CHANGE_REQUEST("dcep.917.001.01", "917",""),
    //机构状态变更通知”
    FINANCIAL_STATUS_CHANGE_REQUEST("dcep.931.001.01", "931",""),
    //业务权限变更通知报文
    BUSINESS_AUTHORITY_REQUEST("dcep.915.001.01", "915",""),
    //自由格式报文
    FREE_FORMAT_REQUEST("dcep.401.001.01", "401","CZone");


    public static final String FINANCIAL_CODE_CHANGE = "dcep.917.001";
    //机构状态变更通知”
    public static final String FINANCIAL_STATUS_CHANGE = "dcep.931.001";
    //业务权限变更通知报文
    public static final String BUSINESS_AUTHORITY = "dcep.915.001";

    //自由格式报文
    public static final String FREE_FORMAT="dcep.401.001";

    private String messageType;
    private String value;

    //业务单元-机房
    private String idcInfo;

    DcepMessageTypeEnum(String messageType, String value,String idcInfo) {
        this.messageType = messageType;
        this.value = value;
        this.idcInfo=idcInfo;
    }

    public String getMessageType() {
        return messageType;
    }

    public void setMessageType(String messageType) {
        this.messageType = messageType;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }


    public String getIdcInfo() {
        return idcInfo;
    }

    public void setIdcInfo(String idcInfo) {
        this.idcInfo = idcInfo;
    }

    public static DcepMessageTypeEnum getByMessageType(String  messageType) {
        for (DcepMessageTypeEnum messageTypeEnum : values()) {
            if (messageTypeEnum.getMessageType() == messageType) {
                return messageTypeEnum;
            }
        }
        return null;
    }
}
