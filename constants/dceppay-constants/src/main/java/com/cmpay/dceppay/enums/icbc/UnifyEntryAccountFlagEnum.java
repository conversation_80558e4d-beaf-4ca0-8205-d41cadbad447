package com.cmpay.dceppay.enums.icbc;

/**
 * <AUTHOR>
 * @date 2024/8/29 16:13
 * 统一入账标示枚举类
 */
public enum UnifyEntryAccountFlagEnum {
    /**
     * "是"
     */
    YES(1),
    /**
     * "否"
     */
    NO(2);
    private final int value;

    UnifyEntryAccountFlagEnum(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static UnifyEntryAccountFlagEnum fromValue(int value) {
        for (UnifyEntryAccountFlagEnum flag : values()) {
            if (flag.getValue() == value) {
                return flag;
            }
        }
        return null;
    }
}
