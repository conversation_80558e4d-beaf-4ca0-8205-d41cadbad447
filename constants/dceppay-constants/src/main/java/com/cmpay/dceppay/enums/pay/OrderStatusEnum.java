package com.cmpay.dceppay.enums.pay;

/**
 * <AUTHOR>
 * @date 2024/9/5 11:48
 */
public enum OrderStatusEnum {
    /**
     * 交易创建，等待付款
     */
    WAIT_PAY("等待付款"),
    /**
     * 未付款交易超时关闭
     */
    TRADE_CLOSED("超时关闭"),
    /**
     * 支付完成后部分退款
     */
    REFUND_PART("部分退款"),
    /**
     * 交易支付成功
     */
    TRADE_SUCCESS("支付成功"),

    /**
     * 交易结束支付失败
     */
    TRADE_FAIL("支付失败"),
    /**
     * 退款成功
     */
    REFUND_SUCCESS("退款成功"),
    /**
     * 退款中
     */
    REFUND_WAIT("退款中"),
    /**
     * 退款失败
     */
    REFUND_FAIL("退款失败"),
    /**
     * 退款失败
     */
    REFUND_ACCEPT_FAIL("退款受理失败"),
    /**
     * 关单失败
     */
    CLOSE_FAIL("关单失败"),
    /**
     * 订单不存在
     */
    TRADE_NOT("订单不存在"),
    /**
     * 退款待处理
     */
    REFUND_PEND("退款待处理");

    private String desc;

    OrderStatusEnum(String desc) {
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }
}
