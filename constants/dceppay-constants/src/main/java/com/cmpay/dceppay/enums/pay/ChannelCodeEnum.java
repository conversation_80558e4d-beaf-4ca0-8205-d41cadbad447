package com.cmpay.dceppay.enums.pay;

import com.cmpay.lemon.common.utils.JudgeUtils;

/**
 * <AUTHOR>
 * @date 2024/9/29 14:41
 */
public enum ChannelCodeEnum {
    PAY_CENTER("BC0001", "支付中心"),
    DCEP_MNG("BC0002", "数币收单运管服务"),

    DCEP_SIMULATOR("BC0003", "数币收单模拟器");

    ChannelCodeEnum(String channelCode, String channelDesc) {
        this.channelDesc = channelDesc;
        this.channelCode = channelCode;
    }
    public static ChannelCodeEnum getChannelCodeEnum(String value) {
        for (ChannelCodeEnum m : ChannelCodeEnum.values()) {
            if (JudgeUtils.equals(m.getChannelCode(), value)) {
                return m;
            }
        }
        return null;
    }
    private String channelCode;
    private String channelDesc;


    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }

    public String getChannelDesc() {
        return channelDesc;
    }

    public void setChannelDesc(String channelDesc) {
        this.channelDesc = channelDesc;
    }
}
