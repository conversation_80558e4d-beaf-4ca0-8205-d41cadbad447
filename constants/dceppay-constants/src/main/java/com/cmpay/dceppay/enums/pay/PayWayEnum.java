package com.cmpay.dceppay.enums.pay;

import com.cmpay.lemon.common.utils.JudgeUtils;

/**
 * <AUTHOR>
 * @date 2024/9/4 11:16
 * 支付方式枚举
 */
public enum PayWayEnum {
    DCEP_APP("dcepapp", "数字人民币APP拉起支付");

    private String value;
    private String desc;

    PayWayEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static PayWayEnum getPayWayEnum(String value) {
        for (PayWayEnum m : PayWayEnum.values()) {
            if (JudgeUtils.equals(m.getValue(), value)) {
                return m;
            }
        }
        return null;
    }

    public String getValue() {
        return this.value;
    }

    public String getDesc() {
        return this.desc;
    }
}
