package com.cmpay.dceppay.constant.check;

/**
 * <AUTHOR>
 * @date 2024/9/21 14:39
 */
public class CheckFileConstant {
    /**
     * 生成文件进程数
     */
    public static final int CHECK_FILE_GRID_SIZE = 1;
    /**
     * 每次读取结算表的记录数
     */
    public static final int SETTLEMENT_READ_SIZE = 1000;
    /**
     * 文件存放本地路径
     */
    public static final String CHECK_FILE_LOCAL_PATH = "/app/apprun/data/checkfile/pay/";
    /**
     * 中台对账文件名前缀
     */
    public static final String PAY_CENTER_CHECK_FILE_PREFIX ="DCPPAY";

    public static final String PAY_CENTER_CHECK_FILE_LAST_LINE ="END";
    public static final String CHECK_DETAIL_FILE_SUFFIX = ".csv";
    public static final String CHECK_SUMMARY_FILE_SUFFIX = "_summary.csv";

    public static final String CHECK_INDEX_FILE_SUFFIX = "_index.csv";


    public static final String LAST_LINE_END = "<end>";
    /**
     * 批量插入对账文件表记录数
     */
    public static final int DETAIL_INSERT_SIZE = 1000;
    /**
     * 下载成功
     */
    public static final String SUCCESS = "success";

    /**
     * 下载失败
     */
    public static final String FAIL = "fail";

    /**
     * 对账进程数
     */
    public static final int CHECK_GRID_SIZE = 1;

    /**
     * 批量读取对账文件表记录数
     */
    public static final int DETAIL_READ_SIZE = 1000;

    /**
     * 存疑
     */
    public static final String DOUBT_FLAG_Y = "Y";
    /**
     * 非存疑
     */
    public static final String DOUBT_FLAG_N= "N";
    /**
     * 对账文件订单状态S
     */
    public static final String SETTLEMENT_SUCCESS= "S";



}
