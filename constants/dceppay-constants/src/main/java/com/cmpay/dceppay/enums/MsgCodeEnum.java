package com.cmpay.dceppay.enums;


import com.cmpay.lemon.common.AlertCapable;

/**
 * <AUTHOR>
 * @date 2024/8/27 14:42
 * 消息码枚举类
 */
public enum MsgCodeEnum implements AlertCapable {

    /**
     * 服务商进件 0-50
     */
    SERVICE_ID_IS_NULL("DCP00001", "服务商编号不能为空"),
    SERVICE_NAME_IS_NULL("DCP00002", "服务商名称不能为空"),
    LICENSE_TYPE_NOT_VALID("DCP00003", "证件类型不合法"),
    LICENSE_NO_IS_NULL("DCP00004", "证件编号不能为空"),
    SERVICE_INFO_NOT_EXIST("DCP00005", "服务商信息不存在"),
    SERVICE_ONBOARD_FAIL("DCP00006", "服务商进件失败"),
    UPDATE_SERVICE_ONBOARD_STATUS_ERROR("DCP00007", "服务商进件状态更新失败"),


    /**
     * 商户进件 50-100
     */
    MERCHANT_ID_IS_NULL("DCP000050", "商户号不能为空"),
    MERCHANT_SHOW_NAME_IS_NULL("DCP00051", "商户对外经营名称不能为空"),
    MCC_IS_NULL("DCP00052", "经营类目（MCC）不能为空"),
    BUSS_CODE_IS_BULL("DCP00053", "业务类型不能为空"),
    BUSS_TYPE_IS_BULL("DCP00054", "业务种类不能为空"),
    MERCHANT_TYPE_IS_BULL("DCP00055", "商户类型不合法"),
    IS_SERVICE_IS_NULL("DCP00056", "是否服务商商户不能为空"),
    CONTACT_NAME_IS_NULL("DCP00057", "商户联系人不能为空"),
    SERVICE_PHONE_IS_NULL("DCP00058", "商户联系人电话不能为空"),
    CONTACT_PHONE_IS_NULL("DCP00059", "联系电话不能为空"),
    CONTACT_EMAIL_IS_NULL("DCP00060", "联系邮箱不能为空"),
    UNIFY_ENTRY_FLAG_IS_NULL("DCP00061", "统一入账标识不能为空"),
    ACC_TYPE_IS_NULL("DCP00062", "账户类型不能为空"),
    WALLET_ID_NOT_VALID("DCP00063", "结算钱包id不合法"),
    WALLET_NAME_NOT_VALID("DCP00064", "结算钱包id不合法"),
    MERCHANT_ONBOARD_FAIL("DCP00065", "商户进件失败"),
    MERCHANT_NAME_NOT_VALID("DCP00066", "商户名称非法"),
    ACC_NO_NOT_VALID("DCP00067", "结算账户号非法"),
    ACC_NAME_NOT_VALID("DCP00068", "结算账户名称非法"),
    ACC_BANK_CODE_NOT_VALID("DCP00069", "开户行行号非法"),
    ACC_BANK_NAME_NOT_VALID("DCP00070", "开户行名称非法"),
    MERCHANT_ONBOARD_QUERY_FAIL("DCP00071", "商户进件查询失败"),
    MERCHANT_INFO_NOT_EXIST("DCP00072", "商户信息不存在或已失效（商户已作废）"),
    MERCHANT_CANCEL_FAIL("DCP00073", "商户作废失败"),
    UPDATE_MERCHANT_STATUS_ERROR("DCP00074", "服务商进件状态更新失败"),
    RETURN_CODE_ERROR("DCP00075", "工行返回码不正确"),
    ONBOARD_RESPONSE_DATA_ERROR("DCP00076", "商户进件成功，返回协议伞顶钱包数据不正确"),
    SERVICE_ONBOARD_RESPONSE_DATA_ERROR("DCP00078", "服务商进件成功返回服务商号为空"),


    // 下单 DCP10000-DCP10199
    OUT_ORDER_NO_INVALID("DCP10000", "商户订单号非法"),
    PAYMENT_METHOD_INVALID("DCP10001", "支付方式非法"),
    PAYMENT_SCENARIO_INVALID("DCP10002", "支付场景非法"),
    ORDER_TOTAL_AMOUNT_INVALID("DCP10003", "订单金额非法"),
    PRODUCT_NAME_INVALID("DCP10004", "商品名称非法"),
    ORDER_DATE_INVALID("DCP10005", "订单日期非法"),
    ORDER_TIME_INVALID("DCP10006", "订单时间非法"),
    ORDER_EXPIRATION_TIME_INVALID("DCP10007", "订单失效时间非法"),
    BUSINESS_TYPE_INVALID("DCP10008", "业务类型非法"),
    TRADE_JRN_NO_INVALID("DCP10009", "交易流水号非法"),
    PAY_WAY_ERROR("DCP10010", "支付方式或支付场景错误"),
    OUT_ORDER_NO_REPEAT("DCP10011", "重复的订单号"),
    MERCHANT_ROUTE_NOT_EXIST("DCP10012", "交易请求失败，不支持的业务类型"),
    UNIFIED_ORDER_RESPONSE_NULL("DCP10013", "请求互联互通统一下单接口响应为空"),
    UNIFIED_ORDER_RESPONSE_FAIL("DCP10014", "请求互联互通统一下单接口响应受理失败"),
    UNIFIED_ORDER_RESPONSE_INVALID("DCP10015", "请求互联互通统一下单接口响应业务回执状态非法"),
    CHANNEl_CODE_INVALID("DCP10016", "渠道非法"),
    APP_CONTEXT_INVALID("DCP10017", "【APP拉起支付下单失败】业务信息转JSON字符串"),
    URL_ENCODE_ERROR("DCP10018", "【APP拉起支付下单失败】url encode 编码异常"),
    UNIFIED_ORDER_RESPONSE_FAULT("DCP10019", "请求互联互通统一下单接口失败"),

    //订单退款 DCP10200-DCP10299
    OUT_REFUND_NO_INVALID("DCP10200", "退款单号非法"),
    REFUND_AMOUNT_ERROR("DCP10201", "退款金额超过订单金额"),
    ORG_ORDER_NOT_EXISTS("DCP10202", "支付订单不存在"),
    REFUND_ORDER_ALREADY_EXIST("DCP10203", "退款订单已存在"),
    PAYMENT_ORDER_STATUS_CANNOT_REFUND("DCP10204", "支付订单状态不允许退款"),
    ORDER_REFUND_PARAM_TIME_OUT("DCP10205", "已超过退款时限，请联系商户走线下退款"),
    //该错误码和包会去做判断，退款订单不存在
    REFUND_ORDER_NOT_EXISTS("DCP10206", "退款订单不存在"),
    REFUND_ORDER_RESPONSE_NULL("DCP10207", "请求互联互通订单退款接口响应为空"),
    REFUND_ORDER_RESPONSE_FAIL("DCP10208", "请求互联互通订单退款接口响应受理失败"),
    REFUND_ORDER_RESPONSE_INVALID("DCP10209", "请求互联互通订单退款接口响应业务回执状态非法"),
    REFUND_ORDER_RESPONSE_FAULT("DCP10210", "请求互联互通订单退款接口异常"),
    REFUND_ORDER_REGISTER_FAIL("DCP10211", "退款登记异常"),


    //订单关闭DCP10300-DCP10349
    PAYMENT_ORDER_STATUS_CANNOT_CLOSE("DCP10300", "订单状态不能发起关单"),
    CLOSE_ORDER_RESPONSE_NULL("DCP10301", "请求互联互通订单关闭接口响应为空"),
    CLOSE_ORDER_RESPONSE_FAIL("DCP10302", "请求互联互通订单关闭接口响应失败"),
    CLOSE_ORDER_RESPONSE_FAULT("DCP10303", "请求互联互通订单关闭接异常"),


    //订单查询DCP10350
    ORDER_QUERY_RESPONSE_NULL("DCP10350", "请求互联互通支付结果查询接口响应为空"),
    ORDER_QUERY_BUSINESS_RESPONSE_NULL("DCP10351", "请求互联互通支付结果查询接口受理成功，返回应答的原业务信息为空"),

    REFUND_QUERY_RESPONSE_NULL("DCP10352", "请求互联互通退款结果查询接口响应为空"),
    REFUND_ORDER_QUERY_BUSINESS_RESPONSE_NULL("DCP10353", "请求互联互通退款结果查询接口受理成功，返回应答的原业务信息为空"),

    UPDATE_ORDER_SUCCESS_ERROR("DCP10354", "更新支付订单状态为'支付成功'异常"),

    UPDATE_ORDER_FAIL_ERROR("DCP10355", "更新支付订单状态为'支付失败'异常"),

    UPDATE_REFUND_SUCCESS_ERROR("DCP10356", "更新退款订单状态为'退款成功'异常"),

    UPDATE_REFUND_FAIL_ERROR("DCP10357", "更新退款订单状态为'退款失败'异常"),


    PAYMENT_ORDER_NOTIFY_STATUS_INVALID("DCP10358", "互联互通支付订单通知业务回执状态非法"),
    PAYMENT_ORDER_NOTIFY_AMOUNT_ERROR("DCP10359", "互联互通支付订单通知订单金额错误"),
    REFUND_ORDER_NOTIFY_STATUS_INVALID("DCP10360", "互联互通退款订单通知业务回执状态非法"),
    REFUND_ORDER_NOTIFY_AMOUNT_ERROR("DCP10361", "互联互通退款订单通知订单金额错误"),
    PAYMENT_ORDER_STATUS_INVALID("DCP10362", "互联互通支付订单通知处理失败，订单状态异常"),


    MESSAGE_TYPE_ERROR("DCP10380", "消息类型非法"),
    MESSAGE_SENDER_ERROR("DCP10381", "消息发送发非法"),
    MESSAGE_RECEIVER_ERROR("DCP10382", "消息接收方非法"),
    MESSAGE_CONTENT_ERROR("DCP10383", "消息内容非法"),
    BUSINESS_AUTHORITY_NOTIFY_ERROR("DCP10384", "互联互通平台业务权限变更通知处理失败"),
    FINANCIAL_CODE_CHANGE_NOTIFY_ERROR("DCP10385", "互联互通平台机构变更通知处理失败"),
    FINANCIAL_STATUS_CHANGE_NOTIFY_ERROR("DCP10386", "互联互通平台机构状态变更通知处理失败"),
    FREE_FORMAT_ERROR("DCP10387", "互联互通平台自由格式通知处理失败"),
    LOGIN_DCEP_RESPONSE_NULL("DCP10388", "登录互联互通平台失败：响应数据为空"),
    LOGIN_DCEP_ERROR("DCP10389", "登录互联互通平台失败"),
    LOGINOUT_DCEP_RESPONSE_NULL("DCP10390", "登出互联互通平台失败：响应数据为空"),
    LOGINOUT_DCEP_ERROR("DCP10391", "登出互联互通平台失败"),
    FREE_FORMAT_DCEP_ERROR("DCP10392", "发送自由格式至互联互通平台响应异常"),
    FREE_FORMAT_EXCEPTION("DCP10393", "发送自由格式消息至互联互通成功，登记业务信息表失败"),
    FREE_FORMAT_DCEP_FAIL("DCP10394", "发送自由格式消息至互联互通响应失败"),

    //账务差错处理10400
    ACCOUNT_LIST_NULL("DCP10400", "请求账务中心失败：账务数组为空"),
    ACCOUNT_CENTER_RESPONSE_ERROR("DCP10401", "请求账务中心做账返回失败"),
    CHECK_DATE_ERROR("DCP10402", "对账日期非法"),
    CHANNEL_CODE_ERROR("DCP10403", "渠道编号非法"),
    CHECK_ERROR_CANCEL_FAIL("DCP10404", "对账差错取消失败"),
    CHECK_ERROR_UPDATE_AMOUNT_FAIL("DCP10405", "对账差错修改金额失败：更新数据库数据异常"),
    CHECK_ERROR_CANCEL_REFUND_FAIL("DCP10406", "对账差错补单退款失败失败"),
    ACCOUNT_CANCEL_ERROR("DCP10407", "请求账务中心处理冲正失败"),
    ACCOUNT_REVERSE_ERROR("DCP10408", "请求账务中心处理撤销失败"),
    ACCOUNT_JRN_NO_NULL("DCP10409", "请求账务处理业务流水号不能为空"),
    ACCOUNT_ORDER_DATE_NULL("DCP10410", "请求账务处理交易日期不能为空"),
    ACCOUNT_ORDER_NO_NULL("DCP10411", "请求账务处理订单号不能为空"),
    ACCOUNT_CANCEL_STATUS_ERROR("DCP10412", "该账务暂未处理成功，无法进行账务冲正处理"),
    ACCOUNT_RESERVE_STATUS_ERROR("DCP10413", "该账务暂未处理成功，无法进行账务撤销处理"),
    PAYMENT_ACCOUNT_STATUS_WAIT("DCP10414", "支付成功账务处理中……"),

    REFUND_ACCOUNT_STATUS_WAIT("DCP10415", "退款成功账务处理中……"),
    ERROR_DATA_NOT_EXIST("DCP10416","差错数据不存在"),
    ERROR_STATUS_ERROR("DCP10417","差错非待处理状态"),
    ERROR_NOT_SUPPORT("DCP10418","差错不支持操作，请确认正确的操作方式"),


    TRANSACTION_REQUEST_FAIL("DCP10500", "请求网关返回异常"),
    TRANSACTION_REQUEST_ERROR_TIME_OUT("DCP10501", "请求超时"),
    TRANSACTION_REQUEST_SYS_ERROR("DCP10502", "系统异常"),


    WRITE_CHECK_FILE_ERROR("DCP20000", "生成对账文件异常"),
    WRITE_CHECK_FILE_BEFORE_ERROR("DCP20001", "生成对账文件异常，创建文件异常"),
    WRITE_CHECK_FILE_AFTER_ERROR("DCP20002", "生成对账文件异常，生成文件后处理异常"),
    CHECK_FILE_UPLOAD_FAILED("DCP20003", "对账文件上传失败"),
    CHECK_FILE_RE_PUSH_FAILED("DCP20004", "对账文件重推失败"),
    CHECK_FILE_NOTIFY_HANDLE_ERROR("DCP20100", "对账汇总核对通知处理失败"),
    CHECK_FILE_CGW_DOWNLOAD_FAILED("DCP20101", "请求网关对账文件下载失败"),
    CHECK_FILE_DOWNLOAD_FAILED("DCP20102", "从文件服务器下载对账文件失败"),
    PARSE_CHECK_DETAIL_FILE_ERROR("DCP20103", "解析对账明细文件异常"),
    INSERT_CHECK_DETAIL_FILE_ERROR("DCP20104", "插入对账明细文件异常"),

    //DCP900000网关使用

    SUCCESS("DCP00000", "成功");


    MsgCodeEnum(String msgCd, String msgInfo) {
        this.msgCd = msgCd;
        this.msgInfo = msgInfo;
    }

    private String msgCd;
    private String msgInfo;

    @Override
    public String getMsgCd() {
        return this.msgCd;
    }

    @Override
    public String getMsgInfo() {
        return this.msgInfo;
    }
}
