package com.cmpay.dceppay.enums.icbc;

/**
 * <AUTHOR>
 * @date 2024/8/28 14:39
 * 工行商户类型
 */
public enum MerchantTypeEnum {

    /**
     * 普通商户
     */
    NORMAL_MERCHANT(1),
    /**
     * 个人商户
     */
    PERSONAL_MERCHANT(2);


    private final int value;


    MerchantTypeEnum(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static MerchantTypeEnum getByValue(int value) {
        for (MerchantTypeEnum merchantTypeEnum : values()) {
            if (merchantTypeEnum.getValue() == value) {
                return merchantTypeEnum;
            }
        }
        return null;
    }

}


