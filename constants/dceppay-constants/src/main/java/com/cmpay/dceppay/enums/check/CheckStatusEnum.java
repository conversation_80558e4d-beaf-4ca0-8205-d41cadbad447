package com.cmpay.dceppay.enums.check;


/**
 * <AUTHOR>
 * @date 2024/9/17 08:42
 * 消息码枚举类
 */
public enum CheckStatusEnum {

	/**
	 * 对账完成
	 */
	COMPLETE("对账完成"),
	/**
	 * 待对账
	 */
	WAIT("待对账"),
	/**
	 * 存疑对平
	 */
	DOUBT_COMPLETE("存疑对平"),
	ERROR_CANCEL("差错取消"),
	AMOUNT_UPDATE("金额修改"),
	CANCEL_REFUND("补单退款");


	private String desc;

	CheckStatusEnum(String desc) {
		this.desc = desc;
	}

	public String getDesc() {
		return desc;
	}
}