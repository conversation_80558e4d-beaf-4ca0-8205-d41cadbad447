package com.cmpay.dceppay.enums.icbc;

public enum SettleCycleEnum {
    /**
     * 实时入账
     */
    REAL_TIME(1),
    /**
     * T+1日入账（暂不支持）
     */
    T_PLUS_ONE_DAY(2),
    /**
     * 指令入账
     */
    COMMAND(3);

    private final int value;

    SettleCycleEnum(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static SettleCycleEnum getByValue(int value) {
        for (SettleCycleEnum settleCycleEnum : values()) {
            if (settleCycleEnum.getValue() == value) {
                return settleCycleEnum;
            }
        }
        return null;
    }
}