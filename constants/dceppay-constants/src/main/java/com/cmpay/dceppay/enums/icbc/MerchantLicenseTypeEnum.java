package com.cmpay.dceppay.enums.icbc;
/**
 * <AUTHOR>
 * @date 2024/8/28 14:39
 * 商户证件类型
 */
public enum MerchantLicenseTypeEnum {
    /**
     * 全国组织机构代码证书
     */
    ORGANIZATION_CODE_CERTIFICATE(100),
    /**
     * 营业执照
     */
    BUSINESS_LICENSE(101),
    /**
     * 行政机关
     */
    ADMINISTRATIVE_ORG(102),
    /**
     * 社会团体法人登记证书
     */
    SOCIAL_GROUP_CORP_REG_CERTIFICATE(103),
    /**
     * 军队单位开户核准通知书
     */
    ARMY_UNIT_ACCOUNT_APPROVAL_NOTICE(104),
    /**
     * 武警部队单位开户核准通知书
     */
    ARMED_POLICE_UNIT_ACCOUNT_APPROVAL_NOTICE(105),
    /**
     * 下属机构（具有主管单位批文号）
     */
    SUBSIDIARY_ORG_WITH_AUTHORITY_DOC(106),
    /**
     * 其他
     */
    OTHER(107),
    /**
     * 商业登记证
     */
    COMMERCIAL_REGISTRATION_CERTIFICATE(108),
    /**
     * 公司注册证
     */
    COMPANY_REGISTRATION_CERTIFICATE(109),

    /**
     * 身份证
     */
    ID_CARD(0),
    /**
     * 护照
     */
    PASSPORT(1),
    /**
     * 军官证
     */
    OFFICER_CARD(2),
    /**
     * 士兵证
     */
    SOLDIER_CARD(3),
    /**
     * 港澳台居民往来通行证
     */
    HK_MC_TW_PASS(4),
    /**
     * 临时身份证
     */
    TEMPORARY_ID_CARD(5),
    /**
     * 户口本
     */
    HUKOU_BOOK(6),
    /**
     * GEREN-其他
     */
    PERSONAL_OTHER(7),
    /**
     * 警官证
     */
    POLICE_OFFICER_CARD(9),
    /**
     * 外国人永久居留证
     */
    FOREIGNER_PERMANENT_RESIDENCE(12);

    private final int value;

    MerchantLicenseTypeEnum(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static MerchantLicenseTypeEnum getByValue(int value) {
        for (MerchantLicenseTypeEnum merchantLicenseTypeEnum : values()) {
            if (merchantLicenseTypeEnum.getValue() == value) {
                return merchantLicenseTypeEnum;
            }
        }
        return null;
    }
}