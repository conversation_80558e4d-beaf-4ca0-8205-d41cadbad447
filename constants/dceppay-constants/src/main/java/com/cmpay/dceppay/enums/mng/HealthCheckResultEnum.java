package com.cmpay.dceppay.enums.mng;

/**
 * <AUTHOR>
 * @date 2024/12/23 10:00
 * 探活结果枚举
 */
public enum HealthCheckResultEnum {
    
    SUCCESS("SUCCESS", "成功"),
    FAIL("FAIL", "失败"),
    TIMEOUT("TIMEOUT", "超时");
    
    private final String code;
    private final String desc;
    
    HealthCheckResultEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDesc() {
        return desc;
    }
    
    public static HealthCheckResultEnum getByCode(String code) {
        for (HealthCheckResultEnum result : values()) {
            if (result.getCode().equals(code)) {
                return result;
            }
        }
        return null;
    }
}
