package com.cmpay.dceppay.constant.pay;

/**
 * <AUTHOR>
 * @date 2024/9/11 16:31
 * 账务处理常量类
 */
public class AccountConstants {
    /**
     *交易对手类型 0个人 1商户 2内部科目
     */
    public static final String OPP_TYPE_INNER_ACCOUNT="2";
    /**
     * 交易描述-备注
     */
    public static final String TRADE_DESC_REMARK="数币收单服务";

    /**
     * 业务渠道CAS 客户端
     */
    public static final String BUS_CHANNEL_CAS="CAS";

    /**
     * 账务来源 DCEP
     */
    public static final String ACCOUNT_FROM_DCEP="DCEP";
    /**
     * 业务类型 P101
     */
    public static final String BUS_TYPE_DCEP="P101";

    /**
     * 交易类型 P1
     */
    public static final String TRADE_TYPE_DCEP="P1";
    /**
     * 账务中心受理中
     */
    public static  final String ACM_EXISTS_FLG_WAIT="P";
    /**
     * 账务中心做账成功
     */
    public static  final String ACM_EXISTS_FLG_SUCCESS="N";
    /**
     * C冲正或撤销
     */
    public static  final String ACM_EXISTS_FLG_CANCEL_REVERSE_SUCCESS="C";
    /**
     * Z记录不存在
     */
    public static  final String ACM_NOT_EXISTS_FLG="Z";

}
