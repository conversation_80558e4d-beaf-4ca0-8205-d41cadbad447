package com.cmpay.dceppay.enums.common;

/**
 * <AUTHOR>
 * @date 2024/10/26 14:28
 * 工行-数研所 身份类型对应关系枚举类
 */
public enum LicenseTypeMappingEnum {
    /**
     * 全国组织机构代码证书
     */
    ORGANIZATION_CODE_CERTIFICATE(100, "IT12"),
    /**
     * 营业执照
     */
    BUSINESS_LICENSE(101, "IT11"),
    OTHER(107, "IT99"),

    /**
     * 身份证
     */
    ID_CARD(0, "IT01"),
    /**
     * 护照
     */
    PASSPORT(1, "IT03"),
    /**
     * 军官证
     */
    OFFICER_CARD(2, "IT02"),
    /**
     * 士兵证
     */
    SOLDIER_CARD(3, "IT05"),
//    /**
//     * 港澳台居民往来通行证
//     */
//    HK_MC_TW_PASS(4),
    /**
     * 临时身份证
     */
    TEMPORARY_ID_CARD(5, "IT08"),
    /**
     * 户口本
     */
    HUKOU_BOOK(6, "IT04"),
    /**
     * 其他
     */
    PERSONAL_OTHER(7, "IT99"),

    /**
     * 警官证
     */
    POLICE_OFFICER_CARD(9, "IT10"),
    /**
     * 外国人永久居留证
     */
    FOREIGNER_PERMANENT_RESIDENCE(12, "IT09");
    private int icbcValue;
    private String dcepValue;

    LicenseTypeMappingEnum(int icbcValue, String dcepValue) {
        this.icbcValue = icbcValue;
        this.dcepValue = dcepValue;
    }

    public static LicenseTypeMappingEnum getByIcbcValue(int value) {
        try {
            for (LicenseTypeMappingEnum licenseTypeMappingEnum : values()) {
                if (licenseTypeMappingEnum.getIcbcValue() == value) {
                    return licenseTypeMappingEnum;
                }
            }
            return OTHER;
        } catch (Exception e) {
            return OTHER;
        }
    }

    public int getIcbcValue() {
        return icbcValue;
    }

    public void setIcbcValue(int icbcValue) {
        this.icbcValue = icbcValue;
    }

    public String getDcepValue() {
        return dcepValue;
    }

    public void setDcepValue(String dcepValue) {
        this.dcepValue = dcepValue;
    }
}
