package com.cmpay.dceppay.enums.pay;

/**
 * <AUTHOR>
 * @date 2024/9/11 16:00
 */
public enum AccountOrderTypeEnum {
    PAYMENT("P1","支付"),
    REFUND("P2","退款");
    private final String value;

    private final String description;

    AccountOrderTypeEnum(String value,String description) {
        this.value=value;
        this.description = description;
    }
    public String getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }
}
