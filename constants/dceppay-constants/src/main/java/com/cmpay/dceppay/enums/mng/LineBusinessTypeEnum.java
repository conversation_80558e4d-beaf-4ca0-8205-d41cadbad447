package com.cmpay.dceppay.enums.mng;

/**
 * <AUTHOR>
 * @date 2024/12/23 10:00
 * 专线业务类型枚举
 */
public enum LineBusinessTypeEnum {
    
    API("API", "API业务"),
    SFTP("SFTP", "SFTP业务");
    
    private final String code;
    private final String desc;
    
    LineBusinessTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDesc() {
        return desc;
    }
    
    public static LineBusinessTypeEnum getByCode(String code) {
        for (LineBusinessTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
