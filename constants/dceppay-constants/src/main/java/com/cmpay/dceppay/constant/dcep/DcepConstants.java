package com.cmpay.dceppay.constant.dcep;

/**
 * <AUTHOR>
 * @date 2024/10/8 17:06
 */
public class DcepConstants {
    //业务种类编码 扩展位默认为0
    public static final String BUSINESS_TYPE_EXTEND_CODE = "0";
    //MP01:实体
    //MP02:网络
    //MP03:混合（实体+网络）
    public static final String MERCHANT_PROPERTY_MP03 = "MP03";
    //受理终端地理位置
    public static final String TERMINAL_DEVICE_INFORMATION_URL = "https://www.cmpay.com/";
    //商户经营地址

    public static final String  MERCHANT_BUSINESS_ADDRESS = "长沙市岳麓区东方红中路352号中移电子商务有限公司一期生产";

    //商户所在地代码
    public static final String  MERCHANT_LOCATION_CODE = "430100";

    public static final String  PLATFORM_NAME = "中移电商平台";

    //业务种类编码 扩展位默认为0
    public static final String BUSINESS_CATEGORY_TYPE_REFUND = "20400001";
    //BT02：外部App拉起支付
    public static final String BIZ_TYPE_APP= "BT02";
}
