package com.cmpay.dceppay.enums.pay;

/**
 * <AUTHOR>
 * @date 2024/9/11 15:55
 */
public enum AccountTradeCodeEnum {
    DCEP0001("数币支付"),
    DCEP0002("数币退款成功"),
    DCEP0003("数币对账"),
    DCEP0004("数币补单"),
    DCEP0005("数币退款（撤单）"),
    DCEP0006("数币退款申请");


    private final String description;

    AccountTradeCodeEnum(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }
}
