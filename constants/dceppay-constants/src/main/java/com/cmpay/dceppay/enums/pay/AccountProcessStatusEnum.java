package com.cmpay.dceppay.enums.pay;

/**
 * <AUTHOR>
 * @date 2024/9/5 11:48
 * 账务处理状态
 */
public enum AccountProcessStatusEnum {

    WAIT("处理中"),

    /**
     * 交易支付成功
     */
    SUCCESS("处理成功"),


    REVERSE("已撤销"),

    NORECORD("无记录");

    private String desc;

    AccountProcessStatusEnum(String desc) {
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }
}
