package com.cmpay.dceppay.enums.common;

/**
 * <AUTHOR>
 * @date 2024/9/6 17:34
 */
public enum IdGenKeyEnum {
    /**
     * 报文标识号序列号
     */
    MESSAGE_SEQ_NO,
    /**
     * 通讯级标识号序列号
     */
    COMMUNICATION_SEQ_NO,
    /**
     * 结算流水号
     */
    SETTLEMENT_JRN_NO,
    /**
     * 账务业务流水号
     */
    ACCOUNT_BUS_JRN_NO,
    REFUND_NO,
    //进件流水号
    ONBOARD_ID,
    //业务消息号
    BUS_MESSAGE_ID;

    public static final int MESSAGE_SEQ_NO_LENGTH = 8;

    public static final int COMMUNICATION_SEQ_NO_LENGTH = 4;
    public static final int ACCOUNT_BUS_JRN_NO_LENGTH = 4;
    public static final int SETTLEMENT_JRN_NO_LENGTH = 8;
    public static final int REFUND_NO_LENGTH = 8;
    public static final int ONBOARD_ID_LENGTH = 2;
    public static final int  BUS_MESSAGE_ID_LENGTH = 4;
}
