package com.cmpay.dceppay.enums.icbc;

/**
 * <AUTHOR>
 * @date 2024/8/29 9:20
 * 商户进件状态枚举类
 */
public enum MerchantOnboardStatusEnum {
    /**
     * 待进件
     */
    WAIT_ONBOARD("wait_onboard"),
    /**
     * 进件发起中
     */
    ONBOARDING("onboarding"),
    /**
     * 进件成功
     */
    ONBOARD_SUCCESS("onboard_success"),
    /**
     * 进件失败
     */
    ONBOARD_FAIL("onboard_fail"),
    /**
     * 进件作废中
     */
    ONBOARD_CANCELING("onboard_canceling"),
    /**
     * 进件作废
     */
    ONBOARD_CANCELED("onboard_canceled"),
    /**
     * 作废失败
     */
    CANCEL_FAIL("cancel_fail");

    private final String status;

    MerchantOnboardStatusEnum(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }
}

