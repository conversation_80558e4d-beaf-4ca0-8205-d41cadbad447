package com.cmpay.dceppay.enums.check;

/**
 * <AUTHOR>
 * @date 2024/9/23 11:00
 * 对账过程枚举
 */
public enum CheckFileProcessEnums {
    WAIT_NOTIFY("对账待通知"),

    WAIT_DOWNLOAD("对账文件待下载"),
    DOWNLOADING("对账文件下载中"),
    DOWNLOADED("对账文件下载完成"),

    WAIT_VERIFY("对账文件待核实"),
    IMPORTING("对账文件入库中"),
    IMPORTED("对账文件已入库"),
    CHECKING("对账处理中"),
    CHECK_FINISH("对账完成");

    private String desc;

    CheckFileProcessEnums(String desc) {
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }
}
