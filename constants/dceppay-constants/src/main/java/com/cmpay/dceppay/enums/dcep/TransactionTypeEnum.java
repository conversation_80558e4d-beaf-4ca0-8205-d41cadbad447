package com.cmpay.dceppay.enums.dcep;

/**
 * <AUTHOR>
 * @date 2024/10/8 16:44
 * 交易类型
 */
public enum TransactionTypeEnum {
    STANDARD_PAY("TT00", "普通汇款"),
    SCAN_PAY("TT01", "扫码支付"),
    TAP_TO_PAY("TT02", "碰一碰支付"),
    APP("TT03", "APP拉起支付"),
    H5("TT04", "H5拉起支付"),
    APP_H5("TT16", "App内H5订单页支付");

    private final String value;
    private final String desc;

    TransactionTypeEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }


    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }
}
