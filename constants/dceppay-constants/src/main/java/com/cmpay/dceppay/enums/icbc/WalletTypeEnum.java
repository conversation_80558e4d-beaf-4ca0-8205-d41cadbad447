package com.cmpay.dceppay.enums.icbc;

/**
 * <AUTHOR>
 * @date 2024/8/28 14:39
 * 钱包类型枚举类
 */
public enum WalletTypeEnum {
    /**
     * 钱包
     */
    WALLET(1),
    /**
     * 本行账户
     */
    SAME_BANK_ACCOUNT(2),
    /**
     * 他行账户
     */
    DIFFERENT_BANK_ACCOUNT(3);


    private final int value;


    WalletTypeEnum(int value) {
        this.value = value;
    }


    public int getValue() {
        return value;
    }
    public static WalletTypeEnum getByValue(int value) {
        for (WalletTypeEnum walletTypeEnum : values()) {
            if (walletTypeEnum.getValue() == value) {
                return walletTypeEnum;
            }
        }
        return null;
    }
}