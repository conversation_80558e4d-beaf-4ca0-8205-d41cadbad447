package com.cmpay.dceppay.enums.pay;

/**
 * <AUTHOR>
 * @date 2024/9/11 16:55
 * 科目信息
 */
public enum AccountInfoEnum {
    DIGITAL_CURRENCY_CHANNEL_CHARGE("3034001", "应收账款-渠道充值款-数字人民币"),
    OTHER_PAYABLES_TEMP_RECEIVABLES("********", "其他应付款-暂收款项-数字人民币"),
    ACCOUNTS_RECEIVABLE_SETTLEMENT("3024001", "应收账款-待结算款-数字人民币"),
    MERCHANT_ACCOUNT_CONSUMPTION_REFUND("********", "其他应付款-商户账户-消费退款"),
    ESCROW_REFUND_APPLICATION("********", "其他应付款-中转挂账-退款申请"),
    ESCROW_BANK_REFUND("********", "其他应付款-中转挂账-银行退款");

    private final String accountNo;
    private final String accountName;

    AccountInfoEnum(String accountNo, String accountName) {
        this.accountNo = accountNo;
        this.accountName = accountName;
    }

    public String getAccountNo() {
        return accountNo;
    }

    public String getAccountName() {
        return accountName;
    }

    // 根据账户编号获取账户描述
    public static String getAccountNameByAccountNo(String accountNo) {
        for (AccountInfoEnum account : AccountInfoEnum.values()) {
            if (account.getAccountNo().equals(accountNo)) {
                return account.getAccountName();
            }
        }
        return null;
    }
}
