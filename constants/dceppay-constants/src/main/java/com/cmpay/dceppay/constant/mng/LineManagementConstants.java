package com.cmpay.dceppay.constant.mng;

/**
 * <AUTHOR>
 * @date 2024/12/23 10:00
 * 专线管理常量
 */
public class LineManagementConstants {
    
    /**
     * 默认标识
     */
    public static final String IS_DEFAULT_YES = "Y";
    public static final String IS_DEFAULT_NO = "N";
    
    /**
     * 探活开关
     */
    public static final String HEALTH_CHECK_ENABLED = "Y";
    public static final String HEALTH_CHECK_DISABLED = "N";
    
    /**
     * 记录状态
     */
    public static final String STATUS_ACTIVE = "A";
    public static final String STATUS_INACTIVE = "I";
    
    /**
     * 网络状态
     */
    public static final String NETWORK_STATUS_NORMAL = "NORMAL";
    public static final String NETWORK_STATUS_WARNING = "WARNING";
    public static final String NETWORK_STATUS_FAULT = "FAULT";
    
    /**
     * 服务状态
     */
    public static final String SERVICE_STATUS_NORMAL = "NORMAL";
    public static final String SERVICE_STATUS_ABNORMAL = "ABNORMAL";
    
    /**
     * 业务类型
     */
    public static final String BUSINESS_TYPE_API = "API";
    public static final String BUSINESS_TYPE_SFTP = "SFTP";
    
    /**
     * 探活类型
     */
    public static final String CHECK_TYPE_PING = "PING";
    public static final String CHECK_TYPE_TELNET = "TELNET";
    public static final String CHECK_TYPE_API = "API";
    
    /**
     * 探活结果
     */
    public static final String CHECK_RESULT_SUCCESS = "SUCCESS";
    public static final String CHECK_RESULT_FAIL = "FAIL";
    public static final String CHECK_RESULT_TIMEOUT = "TIMEOUT";
    
    /**
     * 缓存相关
     */
    public static final String CACHE_KEY_PREFIX = "dcep:line:best:";
    public static final String HEALTH_CHECK_CACHE_PREFIX = "dcep:line:health:";
    public static final int CACHE_EXPIRE_MINUTES = 10;
    public static final int HEALTH_CHECK_CACHE_EXPIRE_MINUTES = 30;
    
    /**
     * 探活配置
     */
    public static final int PING_TIMEOUT_MS = 5000; // 5秒超时
    public static final int WARNING_FAIL_COUNT = 3; // 连续失败3次告警
    public static final int FAULT_FAIL_COUNT = 5; // 连续失败5次故障
    public static final int AVG_CALC_COUNT = 5; // 每5次重新计算平均值
    public static final int BATCH_CHECK_COUNT = 5; // 每5次探测后更新状态
    
    /**
     * 定时任务配置
     */
    public static final String HEALTH_CHECK_CRON = "0/5 * * * * ?"; // 每5秒执行一次
}
