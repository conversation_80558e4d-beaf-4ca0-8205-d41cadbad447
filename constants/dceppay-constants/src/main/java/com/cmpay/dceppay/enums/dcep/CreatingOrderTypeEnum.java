package com.cmpay.dceppay.enums.dcep;

/**
 * <AUTHOR>
 * @date 2024/10/8 17:01
 * 下单类型
 */
public enum CreatingOrderTypeEnum {

    APPLY_DYNAMIC_CODE("COT01", "申请商户动态码"),
    APPLY_API_ADDRESS("COT02", "申请调用地址"),
    APPLY_TRANSACTION_IDENTIFIER("COT03", "申请交易标识");

    private final String code;
    private final String description;

    CreatingOrderTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}