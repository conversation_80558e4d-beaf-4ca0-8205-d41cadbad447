package com.cmpay.dceppay.enums.common;

/**
 * <AUTHOR>
 * @date 2024/8/29 9:43
 * 状态枚举类
 */
public enum StatusEnum {
    /**
     * ENABLED: 启用 (Enabled)
     */
    ENABLED("enable"),
    /**
     * DISABLED: 禁用 (Disabled)
     */
    DISABLED("disable");
    private final String status;

    StatusEnum(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }
}
