package com.cmpay.dceppay.constant.dcep;

/**
 * <AUTHOR>
 * @date 2024/9/7 15:25
 */
public class DcepResponseStatusConstant {
    /**
     * 统一下单成功
     */
    public static final String UNIFIED_ORDER_RESPONSE_SUCCESS = "PR02";

    /**
     * 统一下单失败
     */
    public static final String UNIFIED_ORDER_RESPONSE_FAIL = "PR01";
    /**
     * 订单退款交易受理成功
     */
    public static final String REFUND_ORDER_RESPONSE_SUCCESS = "PR02";

    /**
     * 订单退款交易受理失败
     */
    public static final String REFUND_ORDER_RESPONSE_FAIL = "PR01";

    /**
     * 关闭订单成功
     */
    public static final String CLOSE_ORDER_SUCCESS = "PR00";

    /**
     *关闭订单失败
     */
    public static final String CLOSE_ORDER_FAIL = "PR01";


    /**
     * 支付订单结果查询成功
     */
    public static final String QUERY_ORDER_SUCCESS = "PR00";

    /**
     *支付订单结果查询失败
     */
    public static final String QUERY_ORDER_FAIL = "PR01";

    /**
     * 支付成功
     */
    public static final String PAY_ORDER_SUCCESS = "PR00";

    /**
     *支付失败
     */
    public static final String PAY_ORDER_FAIL = "PR01";
    /**
     * 支付受理成功
     */
    public static final String PAY_ORDER_WAIT = "PR02";



    /**
     * 退款订单结果查询成功
     */
    public static final String REFUND_ORDER_QUERY_SUCCESS = "PR00";

    /**
     *退款订单结果查询失败
     */
    public static final String  REFUND_ORDER_QUERY_FAIL = "PR01";

    /**
     * 退款成功
     */
    public static final String PREFUND_ORDER_SUCCESS = "PR00";

    /**
     *退款失败
     */
    public static final String REFUND_ORDER_FAIL = "PR01";
    /**
     * 退款受理成功
     */
    public static final String PREFUND_ORDER_WAIT = "PR02";

    public static final String  NOTIFY_HANDLE_SUCCESS="PR00";
    /**
     * 对账文件交易---成功
     */
    public static final String  CHECK_FILE_TRADE_SUCCESS="PR00";

    public static final String  LOGIN_SUCCESS="PR00";

    public static final String  LOGIN_OUT_SUCCESS="PR00";

    public static final String  SUCCESS="PR00";
}
