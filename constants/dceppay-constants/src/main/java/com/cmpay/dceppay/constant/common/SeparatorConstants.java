package com.cmpay.dceppay.constant.common;

/**
 * <AUTHOR>
 * @date 2024/10/9 9:40
 * 常见的分隔符常量
 */
public class SeparatorConstants {

    public static final char COMMA = ',';
    public static final char SEMICOLON = ';';
    public static final char COLON = ':';
    public static final char TAB = '\t';
    public static final char SPACE = ' ';
    public static final String COMMA_SPACE = ", ";
    public static final String SEMICOLON_SPACE = "; ";
    public static final String COLON_SPACE = ": ";
    public static final String PIPE = "\\|";
    public static final String NEWLINE = "\n";
    public static final String RETURN = "\r";
    public static final String LINE_SEPARATOR = System.lineSeparator();
    //文件路径分隔在 Windows系统中，它返回 \ 在Unix/Linux系统中，它返回 /
    public static final String FILE_SEPARATOR = System.getProperty("file.separator");
    //用于环境变量的路径分隔 在Windows系统中，它返回 ; 在Unix/Linux系统中，它返回 :
    public static final String PATH_SEPARATOR = System.getProperty("path.separator");
    public static final String UNDER_LINE = "_";
    public static final String BETWEEN_LINE = "-";

    public static final String NULL_STRING="";

}
