package com.cmpay.dceppay.enums.mng;

/**
 * <AUTHOR>
 * @date 2024/12/23 10:00
 * 专线网络状态枚举
 */
public enum LineNetworkStatusEnum {
    
    NORMAL("NORMAL", "正常"),
    WARNING("WARNING", "告警"),
    FAULT("FAULT", "故障");
    
    private final String code;
    private final String desc;
    
    LineNetworkStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDesc() {
        return desc;
    }
    
    public static LineNetworkStatusEnum getByCode(String code) {
        for (LineNetworkStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
}
