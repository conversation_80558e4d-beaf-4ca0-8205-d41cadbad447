package com.cmpay.dceppay.enums.check;

/**
 * <AUTHOR>
 * @date 2024/9/24 15:05
 */
public enum ErrorTypeEnum {

    AMOUNT_ERROR("金额差错"),
    PAYMENT_LONG_NOT_ORD("充值长款（未知订单）"),
    PAYMENT_LONG_STS_DIF("充值长款（状态差异）"),
    PAYMENT_SHORT("充值短款"),
    PAYMENT_SHORT_DOUBT("充值短款（存疑）"),
    REFUND_LONG("退款长款"),
    REFUND_LONG_DOUBT("退款长款（存疑）"),
    REFUND_SHORT_NOT_ORD("退款短款（未知订单）"),
    REFUND_SHORT_STS_DIF("退款短款（状态差异）");

    private String desc;

    ErrorTypeEnum(String desc) {
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }
}
