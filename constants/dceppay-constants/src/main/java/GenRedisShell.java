import com.cmpay.dceppay.enums.MsgCodeEnum;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2023/11/24 15:46
 */
public class GenRedisShell {
    public static void main(String[] args) throws IOException {
        genPubtmsg();
        genPubtmms();
    }


    private static void genPubtmsg() throws IOException {
        File redisShell = new File("E:\\WORK\\数字人民币收单服务\\redis\\pay_pubtmsg.sh");
        FileOutputStream fos = new FileOutputStream(redisShell);
        for (MsgCodeEnum m : MsgCodeEnum.values()) {
            String data =
                    "sh ./alert-redis_uat.sh 'insert' 'pubtmsg' '" + m.getMsgCd() + "' 'busCnl' 'busType' 'E' '" + m.getMsgInfo() + "' '0' ' '"+ "\n" ;
            byte[] bytes = data.getBytes();
            fos.write(bytes);
        }
    }

    private static void genPubtmms() throws IOException {
        File redisShell = new File("E:\\WORK\\数字人民币收单服务\\redis\\pay_pubtmms.sh");
        FileOutputStream fos = new FileOutputStream(redisShell);
        for (MsgCodeEnum m : MsgCodeEnum.values()) {
            String data = "sh ./alert-redis_uat.sh 'insert' 'pubttms' '" + m.getMsgCd() + "' '*' '*' 'msgType' '" + m.getMsgInfo() + "' 'monFlg' 'bmsgInf'"+ "\n" ;
            byte[] bytes = data.getBytes();
            fos.write(bytes);
        }

    }
}