package com.cmpay.igw.dceppay.client;

import com.cmpay.dceppay.dto.closeorder.CloseOrderReqDTO;
import com.cmpay.dceppay.dto.closeorder.CloseOrderRspDTO;
import com.cmpay.dceppay.dto.payment.UnifiedOrderReqDTO;
import com.cmpay.dceppay.dto.payment.UnifiedOrderRsqDTO;
import com.cmpay.dceppay.dto.paymentquery.PaymentOrderQueryReqDTO;
import com.cmpay.dceppay.dto.paymentquery.PaymentOrderQueryRspDTO;
import com.cmpay.dceppay.dto.refund.RefundOrderCheckReqDTO;
import com.cmpay.dceppay.dto.refund.RefundOrderRepDTO;
import com.cmpay.dceppay.dto.refund.RefundOrderRspDTO;
import com.cmpay.dceppay.dto.refundquery.RefundOrderQueryRspDTO;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.framework.data.NoBody;
import com.cmpay.lemon.igw.client.IGWClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @date 2024/8/13 14:45
 */
@IGWClient(
        name = "${igw.client.dceppay-igw.name:}",
        url = "${igw.client.dceppay-igw.url}",
        contextId = "DcepPayIGWClient"
)
public interface DcepPayIGWClient {
    @PostMapping("/dceppay-igw/v1/unifiedOrder")
    UnifiedOrderRsqDTO unifiedOrder(@Valid @RequestBody UnifiedOrderReqDTO unifiedOrderReqDTO);

    @PostMapping("/dceppay-igw/v1/refundOrder")
    RefundOrderRspDTO refundOrder(@Valid @RequestBody RefundOrderRepDTO refundOrderRepDTO);

    @PostMapping("/dceppay-igw/v1/closeOrder")
    CloseOrderRspDTO closeOrder(@Valid @RequestBody CloseOrderReqDTO closeOrderReqDTO);

    @PostMapping("/dceppay-igw/v1/paymentOrderQuery")
    PaymentOrderQueryRspDTO paymentOrderQuery(@Valid @RequestBody PaymentOrderQueryReqDTO orderQueryReqDTO);

    @PostMapping("/dceppay-igw/v1/refundOrderQuery")
    RefundOrderQueryRspDTO refundOrderQuery(@Valid @RequestBody RefundOrderQueryRspDTO refundOrderQueryRspDTO);

    @PostMapping("/dceppay-igw/v1/refundOrderCheck")
     GenericRspDTO<NoBody> refundOrderCheck(@Valid @RequestBody RefundOrderCheckReqDTO refundOrderCheckReqDTO);

}
