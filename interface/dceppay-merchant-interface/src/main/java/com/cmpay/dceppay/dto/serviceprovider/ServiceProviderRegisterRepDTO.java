package com.cmpay.dceppay.dto.serviceprovider;

import com.cmpay.dceppay.enums.icbc.MerchantLicenseTypeEnum;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/8/27 14:27
 * 服务商进件请求参数
 */
@Data
public class ServiceProviderRegisterRepDTO {
    /**
     * 智能收款协议编号
     */
    private String protocolId;
    /**
     * 服务商编号
     */
    @NotBlank(message = "DCP00001")
    private String outServiceId;
    /**
     * 服务商名称
     */
    @NotBlank(message = "DCP00002")
    private String serviceName;
    /**
     * 证件类型
     */
    @NotNull(message = "DCP00003")
    private MerchantLicenseTypeEnum licenseType;
    /**
     * 证件编号
     */
    @NotBlank(message = "DCP00004")
    private String licenseNo;
    /**
     * 场景ID
     */
    private String sceneId;
    /**
     * 场景描述
     */
    private String sceneDesc;
    /**
     * 备注信息
     */
    private String remark;
    private String bak1;
    private String bak2;
    /**
     * 运管操作id
     */
    private String operatorId;
}
