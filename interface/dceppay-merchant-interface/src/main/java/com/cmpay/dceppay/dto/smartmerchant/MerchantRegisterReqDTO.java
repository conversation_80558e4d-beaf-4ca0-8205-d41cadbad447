package com.cmpay.dceppay.dto.smartmerchant;

import com.cmpay.dceppay.enums.icbc.*;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/8/19 16:08
 */
@Data
public class MerchantRegisterReqDTO {
    /**
     * 协议号
     */
    private String protocolId;
    /**
     * 是否服务商 1-是，2-否
     */
    private Integer isServiceMerchant;
    /**
     * 运营机构侧服务商号
     */
    private String serviceId;
    /**
     * 合作方商户编号
     */
    @NotBlank(message = "DCP000050")
    private String outMerchantId;
    /**
     * 商户类型
     */
    @NotNull(message = "DCP00055")
    private MerchantTypeEnum merchantType;
    /**
     * 商户证件类型
     */
    @NotNull(message = "DCP00003")
    private String merchantLicenseType;
    /**
     * 商户名称  当为普通商户时必输，需填写营业执照的名称；当为个人商户时，无需填写
     */
    private String merchantName;
    /**
     * 商户证件编号
     */
    @NotBlank(message = "DCP000004")
    private String merchantLicense;
    /**
     * 商户对外经营名称
     */
    @NotBlank(message = "DCP00051")
    private String merchantShowName;
    /**
     * 商户简称
     */
    private String merchantShortName;
    /**
     * 商户经营类目
     */
    @NotBlank(message = "DCP00052")
    private String merchantCategory;
    /**
     * 商户联系人电话
     */
    @NotBlank(message = "DCP00058")
    private String servicePhone;
    /**
     * 商户联系人
     */
    @NotBlank(message = "DCP00057")
    private String contactName;
    /**
     * 联系电话
     */
    @NotBlank(message = "DCP00059")
    private String contactPhone;
    /**
     * 联系邮箱
     */
    @NotBlank(message = "DCP00060")
    private String contactEmail;
    /**
     * 业务种类编号
     */
    @NotBlank(message = "DCP00054")
    private String bussType;
    /**
     * 业务类型编
     */
    @NotBlank(message = "DCP00053")
    private String bussCode;
    /**
     * 备注
     */
    private String merchantRemark;
    /**
     * 统一入账标志 1-是，2-否
     * 当智能收款协议中的“是否支持统一入账”为开时，可送1-是，2-否；
     * 当智能收款协议中的“是否支持统一入账”为关时，只能送2-否。
     */
    @NotNull(message = "DCP00061")
    private UnifyEntryAccountFlagEnum unifyEntryFlag;
    /**
     * 结算账户/钱包类型。1-钱包；2-本行账户；3-他行账户；
     */
    @NotNull(message = "DCP00062")
    private WalletTypeEnum accType;
    /**
     * 结算钱包id。当acc_type为钱包时必输，当acc_type为账户时无需输入
     */
    private String walletId;
    /**
     * 结算钱包名称。当acc_type为钱包时必输；当acc_type为账户时无需输入
     */
    private String walletName;
    /**
     * 结算账户名称。当acc_type为本行账户和他行账户时必输；当acc_type为钱包时时无需输入
     */
    private String accName;
    /**
     * 结算账户号。当acc_type为本行账户和他行账户时必输；当acc_type为钱包时时无需输入
     */
    private String accNo;
    /**
     * 开户行名称。填写支行名称，当acc_type为本行账户和他行账户时必输；当acc_type为钱包时时无需输入
     */
    private String accBankName;
    /**
     * 开户行行号。联行号，当acc_type为本行账户和他行账户时必输；当acc_type为钱包时时无需输入。
     */
    private String accBankCode;
    /**
     * 结算周期。 1-实时入账；2-T+1日入账（暂不支持）；3-指令入账
     */
    private SettleCycleEnum settleCycle;
    /**
     * 运管操作id
     */
    private String operatorId;
}
