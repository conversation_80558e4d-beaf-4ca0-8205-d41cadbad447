package com.cmpay.dceppay.client;

import com.cmpay.dceppay.dto.serviceprovider.ServiceProviderRegisterRepDTO;
import com.cmpay.dceppay.dto.serviceprovider.ServiceProviderRegisterRspDTO;
import com.cmpay.dceppay.dto.smartmerchant.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @date 2024/8/13 14:46
 */
@FeignClient("dceppay-merchant")
public interface DceppayMerchantClient {
    /**
     * 服务商进件
     * @param serviceProviderRegisterRepDTO
     * @return
     */
    @PostMapping("/v1/outmerchant/register")
    ServiceProviderRegisterRspDTO merchantRegister(@RequestBody @Valid ServiceProviderRegisterRepDTO serviceProviderRegisterRepDTO);
    /**
     * 商户进件
     *
     * @param registerReqDTO
     * @return
     */
    @PostMapping("/v1/smartmerchant/register")
    MerchantRegisterRspDTO merchantRegister(@RequestBody @Valid MerchantRegisterReqDTO registerReqDTO);
    /**
     * 商户进件查询
     * @param registerQueryReqDTO
     * @return
     */
    @PostMapping("/v1/smartmerchant/registerQuery")
    MerchantRegisterQueryRspDTO merchantRegisterQuery(@RequestBody @Valid MerchantRegisterQueryReqDTO registerQueryReqDTO);
    /**
     * 进件作废
     * @param cancelReqDTO
     * @return
     */
    @PostMapping("/v1/smartmerchant/registerCancel")
    MerchantCancelRspDTO merchantCancel(@RequestBody @Valid MerchantCancelReqDTO cancelReqDTO);
}
