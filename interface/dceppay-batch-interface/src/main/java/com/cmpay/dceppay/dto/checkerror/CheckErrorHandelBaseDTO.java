package com.cmpay.dceppay.dto.checkerror;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2024/9/29 11:38
 */
@Data
public class CheckErrorHandelBaseDTO {
    //交易订单号
    @NotBlank(message = "DCP10000")
    private String outOrderNo;
    //退款订单号
    @NotBlank(message = "DCP10200")
    private String refundOrderNo;
    //操作人id
    private String updateId;
}
