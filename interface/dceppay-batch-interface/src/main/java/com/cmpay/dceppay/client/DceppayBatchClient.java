package com.cmpay.dceppay.client;

import com.cmpay.dceppay.dto.account.AccountHandleReqDTO;
import com.cmpay.dceppay.dto.checkerror.CheckAmountUpdateDTO;
import com.cmpay.dceppay.dto.checkerror.CheckErrorCancelDTO;
import com.cmpay.dceppay.dto.checkerror.CheckErrorHandelBaseDTO;
import com.cmpay.dceppay.dto.checkfile.CheckFileGenReqDTO;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.framework.data.NoBody;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @date 2024/8/13 14:43
 * 对账服务
 */

@FeignClient("dceppay-batch")
public interface DceppayBatchClient {
    @PostMapping("/v1/checkFile/genPayCenterFile")
    GenericRspDTO<NoBody> checkFileNotify(@Valid @RequestBody CheckFileGenReqDTO checkFileGenReqDTO);

    @PostMapping("/v1/checkErrorHandle/cancelError")
    GenericRspDTO<NoBody> cancelError(@Valid @RequestBody CheckErrorCancelDTO checkErrorCancelDTO);


    @PostMapping("/v1/checkErrorHandle/updateAmount")
    GenericRspDTO<NoBody> updateAmount(@Valid @RequestBody CheckAmountUpdateDTO checkAmountUpdateDTO);

    @PostMapping("/v1/checkErrorHandle/cancelRefund")
    GenericRspDTO<NoBody> cancelRefund(@Valid @RequestBody CheckErrorHandelBaseDTO checkErrorHandelBaseDTO);

    @PostMapping("/v1/accountHandle/cancel")
    GenericRspDTO<NoBody> cancel(@Valid @RequestBody AccountHandleReqDTO accountHandleReqDTO);

    @PostMapping("/v1/accountHandle/reverse")
    GenericRspDTO<NoBody> reverse(@Valid @RequestBody AccountHandleReqDTO handleReqDTO);
}
