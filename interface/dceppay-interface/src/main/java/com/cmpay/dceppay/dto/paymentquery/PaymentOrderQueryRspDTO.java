package com.cmpay.dceppay.dto.paymentquery;

import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.framework.data.NoBody;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/8/30 11:41
 * 支付订单查询响应对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PaymentOrderQueryRspDTO extends GenericRspDTO<NoBody> {
    private String outOrderNo;
    private BigDecimal totalAmount;
    private String finishDateTime;
    private String orderStatus;
    private String extra;
    private String bankOrderNo;
    private String errMsgCd;
    private String errMsgInfo;
}
