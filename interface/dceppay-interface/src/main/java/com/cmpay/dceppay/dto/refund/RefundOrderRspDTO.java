package com.cmpay.dceppay.dto.refund;

import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.framework.data.NoBody;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/8/30 11:32
 * 订单退款响应参数
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RefundOrderRspDTO extends GenericRspDTO<NoBody> {

    // 订单号
    private String outOrderNo;

    // 退款请求号
    private String outRefundNo;

    // 退款金额
    private BigDecimal refundAmount;

    // 支付机构退款请求号
    private String bankRefundNo;

    // 第三方支付错误码名称
    private String errMsgCd;

    // 第三方支付错误码信息
    private String errMsgInfo;

}
