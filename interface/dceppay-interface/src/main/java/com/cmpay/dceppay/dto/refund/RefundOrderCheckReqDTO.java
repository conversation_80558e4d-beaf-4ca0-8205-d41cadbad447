package com.cmpay.dceppay.dto.refund;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/11/22 13:56
 */
@Data
public class RefundOrderCheckReqDTO {
    // 订单号
    @NotBlank(message = "DCP10000")
    private String outOrderNo;

    // 退款请求号
    @NotBlank(message = "DCP10200")
    private String outRefundNo;

    // 退款请求日期
    @NotBlank(message = "DCP10005")
    private String tradeDate;

    // 退款金额
    @NotNull(message = "DCP10003")
    private BigDecimal refundAmount;
}
