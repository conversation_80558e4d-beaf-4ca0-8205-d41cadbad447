package com.cmpay.dceppay.dto.payment;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/8/30 9:47
 * 数币收单下单请求接口
 */
@Data
public class UnifiedOrderReqDTO {

    // 交易流水号
    @NotBlank(message = "DCP10009")
    private String tradeJrnNo;

    // 订单号
    @NotBlank(message = "DCP10000")
    private String outOrderNo;

    // 支付方式
    @NotBlank(message = "DCP10001")
    private String payWay;

    // 支付场景
    @NotBlank(message = "DCP10002")
    private String scene;

    // 订单总金额
    @NotNull(message = "DCP10003")
    private BigDecimal totalAmount;

    // 商品名称
    @NotBlank(message = "DCP10004")
    private String productName;

    // 商品描述
    private String productDesc;

    // 订单日期
    @NotBlank(message = "DCP10005")
    private String orderDate;

    // 订单时间
    @NotBlank(message = "DCP10006")
    private String orderTime;

    // 订单失效时间
    @NotBlank(message = "DCP10007")
    private String orderTimeExpire;

    // 业务类型
    @NotBlank(message = "DCP10008")
    private String busType;
    // 通知URL
    private String notifyUrl;
    // 页面通知URL
    private String pageNotifyUrl;
    // 保留字段
    private String extra;
    //渠道编号  模拟器运管请求必输运管‘BC0002’，模拟器‘BC0003’，支付中台不输默认‘BC0001’
    private String channelCode;
}
