package com.cmpay.dceppay.dto.notify;

import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.framework.data.NoBody;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/10/2 14:28
 */
@Data
public class NotifyBaseRspDTO extends GenericRspDTO<NoBody> {
    //填写原报文发起时间
    private String origSendDateTime;
    //填写原报文标识号
    private String originalMessageIdentification;
    //填写原报文发起机构
    private String originalInstructingParty;
    //填写原报文编号
    private String originalMessageType;
    //固定填写：PR00-报文接收成功
    private String processStatus;

    private String originalMessageSN;
    //备注信息
    private String remark;
}
