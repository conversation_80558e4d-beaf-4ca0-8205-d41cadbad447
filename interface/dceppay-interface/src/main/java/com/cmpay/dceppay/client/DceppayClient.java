package com.cmpay.dceppay.client;


import com.cmpay.dceppay.dto.mng.DcepLoginReqDTO;
import com.cmpay.dceppay.dto.mng.FreeFormatReqDTO;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.framework.data.NoBody;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @date 2024/8/13 14:43
 * 对账服务
 */

@FeignClient("dceppay")
public interface DceppayClient {
    @PostMapping("/v1/dcep/login")
     GenericRspDTO<NoBody> login(@Valid @RequestBody DcepLoginReqDTO dcepLoginReqDTO);

    @PostMapping("/v1/dcep/logout")
    GenericRspDTO<NoBody> loginOut(@Valid @RequestBody DcepLoginReqDTO dcepLoginReqDTO);

    @PostMapping("/v1/dcep/freeFormatSend")
    GenericRspDTO<NoBody> freeFormatSend(@Valid @RequestBody FreeFormatReqDTO freeFormatReqDTO) ;
}
