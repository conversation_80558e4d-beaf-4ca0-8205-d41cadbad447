package com.cmpay.dceppay.dto.refund;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/8/30 11:28
 * 订单退款请求参数
 */
@Data
public class RefundOrderRepDTO {
    // 订单号
    @NotBlank(message = "DCP10000")
    private String outOrderNo;

    // 退款请求号
    @NotBlank(message = "DCP10200")
    private String outRefundNo;

    // 退款请求日期
    @NotBlank(message = "DCP10005")
    private String tradeDate;

    // 退款金额
    @NotNull(message = "DCP10003")
    private BigDecimal refundAmount;

    // 退款原因
    private String refundReason;

    // 退款结果通知url
    private String notifyUrl;

    // 保留字段
    private String extra;

    // 撤单标识 默认N
    private String cancelFlag;
    //渠道编号  模拟器运管请求必输运管‘BC0002’，模拟器‘BC0003’，支付中台不输默认‘BC0001’
    private String channelCode;

}
