package com.cmpay.dceppay.dto.notify.busmessage;

import com.cmpay.dceppay.dto.notify.NotifyBaseDTO;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2024/10/30 16:24
 */
@Data
public class BusMessageNotifyReqDTO extends NotifyBaseDTO {
    //消息类型
    @NotBlank(message = "DCP10380")
    private String messageType;
    //消息发送时间
    private String messageTime;
    //发起方
    @NotBlank(message = "DCP10381")
    private String sender;
    //接收方
    @NotBlank(message = "DCP10382")
    private String receiver;
    //消息内容
    @NotBlank(message = "DCP10383")
    private String messageContent;

}
