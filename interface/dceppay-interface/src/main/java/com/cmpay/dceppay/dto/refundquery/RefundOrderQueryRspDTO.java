package com.cmpay.dceppay.dto.refundquery;

import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.lemon.framework.data.NoBody;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/8/30 11:41
 * 退款订单查询响应对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RefundOrderQueryRspDTO extends GenericRspDTO<NoBody> {
    private  String outOrderNo;
    private String outRefundNo;
    private String finishDateTime;
    private BigDecimal refundAmount;
    private  String bankRefundNo;
    private String refundStatus;
    private String errMsgCd;
    private String errMsgInfo;
    private String refundReason;
    private String extra;
}
