import com.cmpay.dceppay.util.NetworkConnectivityUtil;
import com.cmpay.dceppay.util.NetworkConnectivityUtil.ConnectivityResult;

/**
 * 网络连通性检测测试类
 */
public class NetworkConnectivityTest {
    
    public static void main(String[] args) {
        System.out.println("=== 网络连通性检测测试 ===\n");
        
        // 测试用例
        String[] testAddresses = {
            "127.0.0.1:8080",        // 本地地址，可能通也可能不通
            "www.baidu.com:80",      // 百度，应该ping通，telnet可能通
            "*******:53",           // Google DNS，应该ping通，telnet可能通
            "192.168.1.999:8080",   // 不存在的地址，应该不通
            "127.0.0.1:99999",      // 端口超出范围
            "invalid-address",       // 格式错误
            "",                      // 空地址
            "www.google.com:443"     // Google HTTPS，应该ping通，telnet应该通
        };
        
        for (String address : testAddresses) {
            System.out.println("测试地址: " + address);
            
            long startTime = System.currentTimeMillis();
            ConnectivityResult result = NetworkConnectivityUtil.checkConnectivity(address);
            long actualTime = System.currentTimeMillis() - startTime;
            
            System.out.println("检测结果: " + result);
            System.out.println("实际耗时: " + actualTime + "ms");
            System.out.println("---");
        }
        
        System.out.println("\n=== 单独测试IP和端口 ===\n");
        
        // 单独测试IP和端口
        testSingleIpPort("127.0.0.1", 8080);
        testSingleIpPort("www.baidu.com", 80);
        testSingleIpPort("*******", 53);
        
        System.out.println("\n=== 性能测试 ===\n");
        
        // 性能测试
        performanceTest("www.baidu.com:80", 5);
    }
    
    private static void testSingleIpPort(String ip, int port) {
        System.out.println("测试IP: " + ip + ", 端口: " + port);
        
        long startTime = System.currentTimeMillis();
        ConnectivityResult result = NetworkConnectivityUtil.checkConnectivity(ip, port);
        long actualTime = System.currentTimeMillis() - startTime;
        
        System.out.println("检测结果: " + result);
        System.out.println("实际耗时: " + actualTime + "ms");
        System.out.println("---");
    }
    
    private static void performanceTest(String address, int times) {
        System.out.println("性能测试: " + address + " (执行" + times + "次)");
        
        long totalTime = 0;
        int successCount = 0;
        
        for (int i = 0; i < times; i++) {
            long startTime = System.currentTimeMillis();
            ConnectivityResult result = NetworkConnectivityUtil.checkConnectivity(address);
            long endTime = System.currentTimeMillis();
            
            totalTime += (endTime - startTime);
            if (result.isPingSuccess() && result.isTelnetSuccess()) {
                successCount++;
            }
            
            System.out.println("第" + (i + 1) + "次: " + result);
        }
        
        System.out.println("平均耗时: " + (totalTime / times) + "ms");
        System.out.println("成功率: " + (successCount * 100 / times) + "%");
        System.out.println("---");
    }
}
